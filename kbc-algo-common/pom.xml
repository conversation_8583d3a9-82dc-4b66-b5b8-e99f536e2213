<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.kbao</groupId>
        <artifactId>kbc-sct-algo</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <groupId>com.kbao</groupId>
    <artifactId>kbc-algo-common</artifactId>
    <description>公共支撑</description>

    <properties>
        <core.version>1.0.0-SNAPSHOT</core.version>
        <java.version>1.8</java.version>
        <orika-version>1.5.2</orika-version>
        <modelmapper-version>2.3.3</modelmapper-version>
        <xss-version>1.5.5</xss-version>
        <aliyun.oss.version>3.5.0</aliyun.oss.version>
        <poi.version>3.17</poi.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>common-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>tool-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>
        <!--hutool -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${poi.version}</version>
        </dependency>

        <!-- 文件统一平台 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-ufs-web-client</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-bsc-web-client</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-ufs-api-client</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-bsc-api-client</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
