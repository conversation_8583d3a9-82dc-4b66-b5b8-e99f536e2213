package com.kbao.algo.redis;

import com.kbao.algo.thread.config.AlgoThreadPoolConfig;
import com.kbao.algo.util.AlgoConstants;
import com.kbao.kbcbsc.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RSemaphore;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Slf4j
@Component
public class RedissonUtil {
    @Resource(name = "customRedissonClient")
    private RedissonClient redissonClient;
    @Autowired
    private RedisUtil redisUtil;

    public RLock getLock(String key) {
        String lockKey = redisUtil.generateKey(key);
        return redissonClient.getLock(lockKey);
    }

    public RSemaphore getSemaphore(String key) {
        String semaphoreKey = redisUtil.generateKey(key);
        return redissonClient.getSemaphore(semaphoreKey);
    }

    public void releaseSemaphore(RSemaphore semaphore, int permits) {
        if (semaphore != null) {
            semaphore.release(permits);
        }
    }

    @PostConstruct
    public void init() {
        //项目启动时初始化信号量
        int collectDataSemaphore = 6;
        int calcIndicatorSemaphore = 2;
        RSemaphore semaphore = this.getSemaphore(AlgoConstants.LOCK_ALGO_SOURCE);
        if (semaphore.availablePermits() != collectDataSemaphore) {
            semaphore.delete();
            semaphore.trySetPermits(collectDataSemaphore);
        }
        semaphore = this.getSemaphore(AlgoConstants.LOCK_ALGO_INDICATOR);
        if (semaphore.availablePermits() != calcIndicatorSemaphore) {
            semaphore.delete();
            semaphore.trySetPermits(calcIndicatorSemaphore);
        }
        log.info("初始化信号量完成");
    }
}
