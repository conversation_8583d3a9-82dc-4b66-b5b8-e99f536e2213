package com.kbao.algo.redis;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName RedisLockUtils
 * @Description
 * @date 2024/11/15 15:31
 * @since V1.0
 */
@Component
@Slf4j
public class RedisLockUtils{
    /**
     * redis前缀
     */
    private static final String Header = "kbc-sct-web:algo";

    /**
     * redis key 各字段分隔符
     */
    public static final String Seg = ":";

    @Resource
    RedisTemplate<String, Object> redisJsonTemplate;

    public String getLockKey(String lockKey) {
        return setKey("lock", lockKey);
    }

    /**
     * @param lockKey
     * @param lockValue
     * @param expireTime
     * @return java.lang.Boolean
     * @description 加锁
     * <AUTHOR>
     * @date 2024/11/15 15:31
     */
    public Boolean lock(String lockKey, Object lockValue, Integer expireTime) {
        return redisJsonTemplate.opsForValue().setIfAbsent(lockKey, lockValue, expireTime, TimeUnit.SECONDS);
    }

    public Boolean isLock(String lockKey, Object lockValue, Integer expireTime) {
        lockKey = getLockKey(lockKey);
        Boolean lock = this.lock(lockKey, lockValue, expireTime);
        if (lock) {
            return lock;
        } else {
            Object obj = redisJsonTemplate.opsForValue().get(lockKey);
            return !areObjectsEqual(lockValue, obj);
        }
    }

    public boolean areObjectsEqual(Object obj1, Object obj2) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            String json1 = mapper.writeValueAsString(obj1);
            String json2 = mapper.writeValueAsString(obj2);
            return json1.equals(json2);
        } catch (Exception e) {
            // 处理异常
            log.error("redis锁比较对象出现异常={}", e.getMessage());
            return false;
        }
    }

    public Boolean lock(String lockKey, String tokenValue, Integer expireTime) {
        return redisJsonTemplate.opsForValue().setIfAbsent(lockKey, tokenValue, expireTime, TimeUnit.SECONDS);
    }

    public void unlock(String lockKey,String tokenValue) {
        String value = (String) redisJsonTemplate.opsForValue().get(lockKey);
        if(EmptyUtils.isNotEmpty(value) && tokenValue.equals(value)){
            redisJsonTemplate.delete(lockKey);
        }
    }
    /**
     * 设置 redis key
     * @param subKey
     * @return
     */
    public String setKey(String... subKey) {

        String key = Header + Seg;

        for (String str : subKey) {
            key += (str + Seg);
        }

        return key.substring(0, key.length() - 1);
    }

    /**
     * 设置 redis锁的 key
     * @param subKey
     * @return
     */
    public String setLockKey(String ... subKey ) {
        String key = Header + Seg + "lock" + Seg;
        for (String str : subKey) {
            key+=( str + Seg );
        }

        return key.substring(0, key.length()-1);
    }
}
