package com.kbao.algo.redis.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RedissonConfig {
    @Autowired
    private RedisProperties redisProperties;

    @Bean(name = "customRedissonClient")
    public RedissonClient redissonClient() {
        Config config = new Config();

        config.useSingleServer().setAddress("redis://" + redisProperties.getHost() + ":" + redisProperties.getPort())
                .setPassword(redisProperties.getPassword())
                .setTimeout((int) redisProperties.getTimeout().toMillis())
                .setRetryAttempts(3)
                .setRetryInterval(1000)
                .setPingConnectionInterval(30000)
                .setDatabase(redisProperties.getDatabase());
        return Redisson.create(config);
    }
}
