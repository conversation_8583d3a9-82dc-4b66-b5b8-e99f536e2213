package com.kbao.algo.enums;

import com.kbao.commons.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

public enum AlgoFieldTypeEnum {
    CHAR("char", "字符"),
    VARCHAR("varchar", "字符串"),
    INT("int", "整数"),
    DOUBLE("double", "浮点型"),
    DATETIME("datetime", "日期时间"),
    DATE("date", "日期"),
    TIME("time", "时间");
    
    private String code;
    private String name;
    
    AlgoFieldTypeEnum(String code, String name) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public static AlgoFieldTypeEnum getByCode(String code) {
        for (AlgoFieldTypeEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

    public static String buildFieldType(String fieldType, String fieldLength) {
        AlgoFieldTypeEnum type = AlgoFieldTypeEnum.getByCode(fieldType);
        if (type == null) {
            throw new BusinessException("字段类型不正确");
        }
        if ((type == AlgoFieldTypeEnum.INT || type == AlgoFieldTypeEnum.DATETIME || type == AlgoFieldTypeEnum.DATE || type == AlgoFieldTypeEnum.TIME)
                && StringUtils.isNotEmpty(fieldLength)) {
            throw new BusinessException("整数、时间类型不能设置长度");
        }
        int[] length = {0, 0};
        if (fieldLength != null && fieldLength.matches("^\\d+(,\\d+)?$")) {
            length = Arrays.stream(fieldLength.split(",")).mapToInt(Integer::parseInt).toArray();
        }
        switch (type) {
            case VARCHAR:
                if (length[0] <= 5) {
                    return CHAR.code + "(" + length[0] + ")";
                }
                return VARCHAR.code + "(" + length[0] + ")";
            case INT:
                return INT.code;
            case DOUBLE:
                return DOUBLE.code + "(" + length[0] + "," + length[1] + ")";
            case DATETIME:
                return DATETIME.code;
            case DATE:
                return DATE.code;
            case TIME:
                return TIME.code;
            default:
                throw new BusinessException("字段类型不正确");
        }
    }
}
