package com.kbao.algo.enums;

public enum AlgoIndicatorTypeEnum {
    CALC("1", "计算指标"),
    RESULT("2", "结果指标"),
    DATA_SPACE("3", "空间指标");

    private String code;
    private String name;

    AlgoIndicatorTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
