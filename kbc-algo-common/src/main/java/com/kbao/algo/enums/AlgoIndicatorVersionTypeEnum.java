package com.kbao.algo.enums;

public enum AlgoIndicatorVersionTypeEnum {
    CURRENT("1", "当前应用"),
    DRAFT("2", "草稿"),
    HISTORY("3", "历史版本");

    private String code;
    private String name;

    AlgoIndicatorVersionTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
