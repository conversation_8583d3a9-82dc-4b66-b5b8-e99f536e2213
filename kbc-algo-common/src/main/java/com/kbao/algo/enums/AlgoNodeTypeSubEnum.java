package com.kbao.algo.enums;

import lombok.Getter;

public enum AlgoNodeTypeSubEnum {
    CONNECT("connect", "数据连接"),
    GROUP("group", "分组聚合"),
    FILTER("filter", "数据过滤"),
    CASE("case", "条件函数"),
    SOURCE("source", "数据源"),
    INDICATOR("indicator", "计算指标");

    @Getter
    private String value;
    private String desc;

    AlgoNodeTypeSubEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static AlgoNodeTypeSubEnum getByValue(String value) {
        for (AlgoNodeTypeSubEnum algoNodeTypeSubEnum : AlgoNodeTypeSubEnum.values()) {
            if (algoNodeTypeSubEnum.getValue().equals(value)) {
                return algoNodeTypeSubEnum;
            }
        }
        return null;
    }
}
