package com.kbao.algo.enums;

public enum AlgoNodeTypeEnum {
    SOURCE("1", "数据源节点"),
    CALC_INDICATOR("2", "计算指标节点"),
    RESULT_INDICATOR("3", "结果指标节点");

    private String code;
    private String name;

    AlgoNodeTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
