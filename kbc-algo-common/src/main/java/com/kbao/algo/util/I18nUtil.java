package com.kbao.algo.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;

import java.util.Locale;

/**
 * <AUTHOR>
 * @Description 国际化工具类
 * @Date 2021-08-18
 */
@Component
public class I18nUtil {

    @Autowired
    private MessageSource messageSource;

    /**
     * 获取国际化文本
     * @param key 文本key
     * @return 国际化文本
     */
    public String getMsg(String key) {
        return messageSource.getMessage(key, null, Locale.SIMPLIFIED_CHINESE);
    }

    /**
     * 获取国际化文本
     * @param key 文本key
     * @param objects 动态参数集合
     * @return 国际化文本
     */
    public String getMsg(String key, Object[] objects) {
        return messageSource.getMessage(key, objects, Locale.SIMPLIFIED_CHINESE);
    }
}
