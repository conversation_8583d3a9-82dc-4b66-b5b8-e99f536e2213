package com.kbao.algo.util;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description LIST集合工具类
 * @Date 2021-08-16
 */
public class ListUtil {

    /**
     * 复制集合
     * @param list
     * @return
     */
    public static List copy(ArrayList list) {
        return (ArrayList) list.clone();
    }

    /**
     * 替换集合
     * @param sourceDataList
     * @param targetDataList
     */
    public static void replaceList(List sourceDataList, List targetDataList) {
        sourceDataList.clear();
        sourceDataList.addAll(targetDataList);
    }

}
