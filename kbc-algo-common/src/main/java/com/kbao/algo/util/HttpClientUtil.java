package com.kbao.algo.util;

import cn.hutool.core.text.UnicodeUtil;
import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.exception.BusinessException;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URLDecoder;
import java.util.Map;

/**
 * <AUTHOR> qiuzb
 * @Description: HTTP请求工具类
 * @create 2023/10/23 15:01
 */
@Slf4j
public class HttpClientUtil {

    private static final Logger logger =  LoggerFactory.getLogger(HttpClientUtil.class);

    private static final int MAX_TIME_OUT = 3* 60 * 1000;

    private static final String UTF_8 = "UTF-8";

    public static JSONObject postJson(String url, String jsonStr)  {
        log.info("postJson 请求入参 = {}", jsonStr);
        HttpClient httpClient = HttpClientBuilder.create().build();
        HttpPost method = new HttpPost(url);
        String result = null;
        try {
            RequestConfig requestConfig = RequestConfig.custom().setConnectionRequestTimeout(MAX_TIME_OUT)
                    .setConnectTimeout(MAX_TIME_OUT).setSocketTimeout(MAX_TIME_OUT).build();
            method.setConfig(requestConfig);
            method.addHeader("Content-type", "application/json; charset=utf-8");
            method.setHeader("avgx", "csbt34.ydhl12s");
            method.setHeader("zrt", "1dcypsz1/2jss1/2j#f00");
            method.setHeader("X-SPDT-SERVICE-KUAIBAO", "2018-04-03-spdt-service-kuaibao");
            method.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.142 Safari/537.36");
            method.setEntity(new StringEntity(jsonStr, "UTF-8"));
            HttpResponse response = httpClient.execute(method);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != HttpStatus.SC_OK) {
                log.error("服务器连接异常! URL:" + url + ",服务器状态:" + response.getStatusLine().getStatusCode());
                throw new BusinessException("服务器连接异常! URL:" + url + ",服务器状态:" + response.getStatusLine().getStatusCode());
            }
            result = EntityUtils.toString(response.getEntity(), "UTF-8");
            result = URLDecoder.decode(result);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BusinessException(url+"接口调用异常！");
        } finally {
            method.releaseConnection();
            log.info("postJson success...");
        }
        return JSONObject.parseObject(result);
    }
    /**
    * @Description: post请求
    * @Param: [url, json, header]
    * @return: java.lang.String
    * @Author: husw
    * @Date: 2024/6/14 9:51
    */
    public static String postJson(String url, String json, Map<String, Object> header) {
        long startTime = System.currentTimeMillis();
        HttpClient httpClient = HttpClientBuilder.create().build();
        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader("Content-type", "application/json; charset=utf-8");
        httpPost.setEntity(new StringEntity(json, HttpClientUtil.UTF_8));
        httpPost.setConfig(RequestConfig.custom().setConnectTimeout(MAX_TIME_OUT).setSocketTimeout(MAX_TIME_OUT).build());

        if (EmptyUtils.isNotEmpty(header)) {
            header.forEach((k, v) -> {
                httpPost.setHeader(k, String.valueOf(v));
            });
        }
        HttpResponse response;
        String result;
        try {
            response = httpClient.execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != HttpStatus.SC_OK) {
                log.error("服务器连接异常! URL:" + url + ",服务器状态:" + response.getStatusLine().getStatusCode());
                throw new BusinessException("服务器连接异常! URL:" + url + ",服务器状态:" + response.getStatusLine().getStatusCode());
            }
            result = EntityUtils.toString(response.getEntity(), "UTF-8");
            result = UnicodeUtil.toString(result);
        } catch (IOException e) {
            log.error("发送请求失败！{}",e.getMessage());
            throw new BusinessException("发送post请求失败!");
        }finally {
            httpPost.releaseConnection();
        }
        log.info("post域名{}请求结束,耗时{}ms", url, System.currentTimeMillis() - startTime);
        return result;
    }
}
