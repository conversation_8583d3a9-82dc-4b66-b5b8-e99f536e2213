package com.kbao.algo.util;

import java.math.BigDecimal;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Description 字符串工具类
 * @Date 2021-08-18
 */
public class StringUtil {

    /**
     * 格式化特殊字符正则表达式
     */
    private static final Pattern PATTERN = Pattern.compile("\\{([^}]*)\\}");

    /**
     * 转换成json字符串
     * @param str 原始字符串
     * @return json字符串
     */
    public static String convertToJsonString(String str) {
        str = str.trim();
        Matcher matcher = PATTERN.matcher(str);
        if(matcher.find()) {
            String matchStr = matcher.group();
            StringBuffer sb = new StringBuffer();
            sb.append(matchStr, 0, 1);
            String keyValueStr = matchStr.substring(1, matchStr.length() - 1);
            if(keyValueStr.contains(",")) {
                String[] keyValueArr = keyValueStr.split(",");
                for (int i = 0; i < keyValueArr.length; i++) {
                    String keyValueItem = keyValueArr[i];
                    sb.append(processLine(keyValueItem));
                }
            }
            sb.append(matchStr.substring(matchStr.length() - 1));
            return sb.toString();
        }else {
            return processLine(str);
        }
    }

    /**
     * 处理行数据
     * @param str 行数据
     * @return 处理后的行数据
     */
    private static String processLine(String str) {
        if(str.contains(":")) {
            String[] commentRowArr = str.split(":");
            String key = replaceSpecialChar(commentRowArr[0]);
            String value = replaceSpecialChar(commentRowArr[1]);

            if ("[".equals(value)) {
                return "'" + key + "':" + value;
            } else if ("{".equals(value)) {
                return "'" + key + "':" + value;
            } else {
                if (!value.endsWith(",")) {
                    return "'" + key + "':" + "'" + value + "',";
                }else {
                    return "'" + key + "':" + "'" + value.substring(0, value.length() - 1) + "',";
                }
            }
        }else {
            if(str.endsWith("]") || str.endsWith("}")) {
                str += ",";
            }
            return str;
        }

    }

    /**
     * 替换字符串内特殊字符
     * @param string 原始字符串
     * @return 替换后的字符串
     */
    private static String replaceSpecialChar(String string) {
        return string.trim().replaceAll("\"","")
                .replaceAll("“","")
                .replaceAll("'","")
                .replaceAll("‘","");
    }


    /**
     * 去除数字字符串中多余的零
     *
     * @param numberStr 表示数字的字符串，例如 "9.000" 或 "0.004500"
     * @return 去除多余零后的字符串，例如 "9" 或 "0.0045"
     */
    public static String removeTrailingZeros(String numberStr) {
        if (numberStr == null || numberStr.isEmpty()) {
            return "0";
        }
        BigDecimal bd = new BigDecimal(numberStr);
        String plainStr = bd.toPlainString();
        if (plainStr.indexOf('.') == -1) {
            return plainStr;
        }
        return plainStr.replaceAll("0+$", "").replaceAll("\\.$", "");
    }

}
