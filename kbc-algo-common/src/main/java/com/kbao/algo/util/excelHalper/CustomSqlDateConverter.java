package com.kbao.algo.util.excelHalper;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.sql.Date;
import java.text.SimpleDateFormat;

public class CustomSqlDateConverter implements Converter<Date> {

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public Class<?> supportJavaTypeKey() {
        return Date.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Date convertToJavaData(ReadConverterContext<?> context) throws Exception {
        return new Date(dateFormat.parse(context.getReadCellData().getStringValue()).getTime());
    }

    @Override
    public WriteCellData<?> convertToExcelData(Date date, ExcelContentProperty property, GlobalConfiguration configuration) throws Exception {
        return new WriteCellData<>(dateFormat.format(date));
    }
}
