package com.kbao.algo.util;

import com.kbao.feign.config.FeignRequestHeader;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @className FeignUtils
 * @description
 * @date 2023/7/5
 */
public class FeignUtils {

    /**
     * @Description 封装feign请求头
     * <AUTHOR>
     * @Date 10:57 2023/7/5
     **/
    public static void packageFeignHeaders() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        Map<String, String> headerMap = new HashMap<>(3);
        String accessToken = request.getHeader("access_token");
        if (StringUtils.isBlank(accessToken)) {
            accessToken = request.getParameter("access_token");
        }
        String tenantId = request.getHeader("tenantId");
        if (StringUtils.isBlank(tenantId)) {
            tenantId = request.getParameter("tenantId");
        }

        String funcId = request.getHeader("funcId");
        if (StringUtils.isBlank(funcId)) {
            funcId = request.getParameter("funcId");
        }
        headerMap.put("tenantId", tenantId);
        headerMap.put("access_token", accessToken);
        headerMap.put("funcId", funcId);
        FeignRequestHeader.Header.set(headerMap);
    }

    public static void baseFeignHeaders() {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("tenantId", AlgoContext.getTenantId());
        FeignRequestHeader.Header.set(headerMap);
    }
}
