package com.kbao.algo.util;

public class AlgoConstants {

    /*** algo数据库表名 ****/
    private static final String SOURCE_TABLE_PREFIX = "t_algo_source_";
    private static final String INDICATOR_TABLE_PREFIX = "t_algo_indicator_";
    private static final String DATA_SPACE_INDICATOR_PREFIX = "t_algo_space_ind_";
    private static final String PROCEDURE_PREFIX = "proc_";
    private static final String SOURCE_STORAGE_TABLE_SUFFIX = "_storage";

    // 数据源编码+数据空间ID
    public static String getSourceTableName(String bizCode, Integer dataSpaceId) {
        return SOURCE_TABLE_PREFIX + bizCode + "_" + dataSpaceId;
    }

    public static String getSourceStorageTableName(String sourceTableName) {
        return sourceTableName + SOURCE_STORAGE_TABLE_SUFFIX;
    }

    // 指标编码+策略批次ID
    public static String getIndicatorTableName(String indicatorBizCode, Integer dataSpaceId) {
        return INDICATOR_TABLE_PREFIX + indicatorBizCode + "_" + dataSpaceId;
    }

    public static String getProcedureName(String indicatorBizCode, Integer strategyBatchId) {
        return PROCEDURE_PREFIX + indicatorBizCode + "_" + strategyBatchId;
    }

    // 策略执行批次号字段名
    public static String STRATEGY_BATCH_ID_FIELD = "strategyBatchId";

    // 字段正则
    public static String FIELD_REGEX = "\\$\\{([^}]+)\\}";

    // 参数正则
    public static String PARAM_REGEX = "@param\\(([^)]+)\\)";

    // *********** redis key ***********
    public static String LOCK_ALGO_INDICATOR = "lock:executeCalcIndicator";
    public static String LOCK_ALGO_SOURCE = "lock:executeCollectData";

    public static String LOCK_ALGO_CREATE_TABLE = "lock:createTable:";

    public static String REK_INDICATOR_STRATEGY_BATCH = "indicator:strategyBatchCode";

    public static String REK_SOURCE_STRATEGY_BATCH = "source:strategyBatchCode";
}
