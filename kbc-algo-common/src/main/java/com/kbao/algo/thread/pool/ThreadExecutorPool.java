package com.kbao.algo.thread.pool;

import com.kbao.algo.thread.config.AlgoThreadPoolConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class ThreadExecutorPool {

    @Autowired
    private AlgoThreadPoolConfig algoPoolConfig;

    @Bean(name = "algoCalcThreadPool", destroyMethod = "shutdown")
    public ThreadPoolTaskExecutor algoCalcThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(algoPoolConfig.getCorePoolSize());
        executor.setMaxPoolSize(algoPoolConfig.getMaxPoolSize());
        executor.setQueueCapacity(algoPoolConfig.getQueueCapacity());
        executor.setKeepAliveSeconds(algoPoolConfig.getKeepAliveSeconds());
        executor.setThreadNamePrefix("AlgoCalcPool");

        // 如果QueueCapacity数不足,直接丢弃任务，抛出异常
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        executor.initialize();
        return executor;
    }
}
