package com.kbao.algo.thread.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

@Component
@PropertySource(value = "classpath:/application.yml")
@ConfigurationProperties(prefix = "thread.executor.algo")
@Data
public class AlgoThreadPoolConfig {

    /**
     * 核心线程数
     */
    private int corePoolSize;

    /**
     * 最大线程数
     */
    private int maxPoolSize;

    /**
     * 线程最大空闲时间
     */
    private int keepAliveSeconds;

    /**
     * 线程等待队列大小
     */
    private int queueCapacity;

    // 抽取数据源信号总量
    private int collectDataSemaphore;
    // 计算指标信号总量
    private int calcIndicatorSemaphore;
}
