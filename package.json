{"name": "kbc-sct-algo-web", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service  serve  --mode dev --open", "sta": "vue-cli-service  build  --mode sta", "uat": "vue-cli-service  build  --mode uat", "prod": "vue-cli-service build --mode prod"}, "dependencies": {"@riophae/vue-treeselect": "^0.4.0", "axios": "^0.19.2", "core-js": "^3.4.3", "crypto-js": "^4.0.0", "echarts": "^5.2.0", "element-ui": "^2.15.12", "js-file-download": "^0.4.12", "jsplumb": "^2.15.6", "lodash": "^4.17.15", "nprogress": "^0.2.0", "panzoom": "^9.4.3", "quill": "^1.3.7", "vue": "^2.6.10", "vue-click-outside": "^1.1.0", "vue-contextmenujs": "^1.4.11", "vue-json-viewer": "^2.2.22", "vue-router": "^3.1.3", "vuedraggable": "^2.23.2", "vuex": "^3.1.2", "vuex-along": "^1.2.11"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.1.0", "@vue/cli-plugin-eslint": "^4.1.0", "@vue/cli-plugin-router": "^4.1.0", "@vue/cli-plugin-unit-mocha": "^4.1.0", "@vue/cli-plugin-vuex": "^4.1.0", "@vue/cli-service": "^4.1.0", "@vue/eslint-config-prettier": "^5.0.0", "@vue/test-utils": "1.0.0-beta.29", "babel-eslint": "^10.0.3", "babel-plugin-import": "^1.13.0", "chai": "^4.1.2", "eslint": "^5.16.0", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-vue": "^5.0.0", "less": "^3.0.4", "less-loader": "^5.0.0", "lint-staged": "^9.4.3", "postcss-px-to-viewport": "^1.1.1", "prettier": "^1.19.1", "vue-template-compiler": "^2.6.10"}, "gitHooks": {"pre-commit": ""}, "lint-staged": {"*.{js,vue}": ["vue-cli-service lint", "git add"]}, "description": "```\r npm install\r ```", "main": ".eslintrc.js", "directories": {"test": "tests"}, "repository": {"type": "git", "url": "https://git.dtinsure.com/kbcs-app/sts/kbc-sct-algo-web.git"}, "author": "", "license": "ISC"}