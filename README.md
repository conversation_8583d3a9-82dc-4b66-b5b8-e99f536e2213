
## Project setup
```
npm install
```

### Compiles and hot-reloads for development
```
npm run dev
```

### Compiles and minifies for production
```
npm run prod
```

### Compiles and minifies for uat
```
npm run uat
```

### Compiles and minifies for sta
```
npm run sta
```

### Run your unit tests
```
npm run test:unit
```

### Lints and fixes files
```
npm run lint
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/)

### 内嵌iframe父子系统的通讯

```
1.accessToken 过期时通讯方法

@/utils/httpService.js中写入方法
if (res.data.resp_code == 401) {
  if (window.parent && window.parent.kbcChangeToken) {
      await window.parent.kbcChangeToken(currentLoginUser.access_token)
      let lastApiUrl = res.config.url;
      let lastApiData = res.config.data;
      const access_token = sessionStorage.getItem("LoginAccessToken");
      const tenantId = currentLoginUser.tenantId;
      const funcId = currentLoginUser.funcId;
      store.commit("layoutStore/setCurrentLoginUser", {access_token,tenantId,funcId})
      const lastData = await Axios.post(lastApiUrl, lastApiData);
      if (lastData) {
        return lastData;
      };
  }
}

2.父系统切主题时子系统获取新主题并更改主题
@/utils/globalParam 中新增主系统域名
let mainSysUrl = ""
if (process.env.NODE_ENV == "dev") {
  mainSysUrl= "https://kbc-sta.kbao123.com/"
} else if (process.env.NODE_ENV == "uat") {
  mainSysUrl= "https://kbc-uat.kbao123.com/"
} else if (process.env.NODE_ENV == "sta") {
  mainSysUrl= "https://kbc-sta.kbao123.com/"
} else if (process.env.NODE_ENV == "prod") {
  mainSysUrl= "https://kbc.dtinsure.com/"
}
export const kbcPath = mainSysUrl;



main.js中引入公共js
import { kbcPath } from "./utils/globalParam";
document.write(`<script language=javascript src='${kbcPath}vender/dtPublic.js' charset='utf-8'></script>`)






@/app.vue 中引入
const version = require("element-ui/package.json").version; // 获取elment-ui版本
const ORIGINAL_THEME = "#D7A256"; // elment 默认主题

created() {
  this.init();    
},

methods:{
  init() {
    let funcId = getParamString("funcId");
    let tenantId = getParamString("tenantId");
    let userName = getParamString("userName");
    let accessToken = getParamString("access_token");
    sessionStorage.setItem("LoginAccessToken", accessToken);
    this.$store.commit("layoutStore/setCurrentLoginUser", {accessToken, tenantId, funcId, userName});
    //获取登录用户在该应用下权限位等信息
    this.$store.dispatch('getWebUserInfo');
    this.getTenantUsers()
  },
  handleTheme(color, themeObj) {
    if (window.dtHandleTheme) {
      window.dtHandleTheme(color, ORIGINAL_THEME, version)
    }
    this.$store.commit("layoutStore/setThemeObj", themeObj);
  },
  // 获取用户信息 
  async getTenantUsers() {
    if(!localStorage.getItem("tenantUsers")){
      let res = await getTenantUsers()
      if (res) {
        localStorage.setItem("tenantUsers", JSON.stringify(res))
      }
    }
  }
}


// 初始化调用从路由传过来的颜色主题
mounted() {
    let themeObj = {
      color: getParamString("themeColor"),
      tableBtnActiveColor: getParamString("themeColor"),
      navTagUnselectedColor: getParamString("navTagColor"),
      text: ""
    }
    if (themeObj.color) {
      setTimeout(() => {
        this.handleTheme(themeObj.color, themeObj)
      }, 700);
    }    
    window.addEventListener('message', (e) =>{
      if (e.data.theme) {
        //在此处标识是父系统嵌入的子系统，需要子系统隐藏左侧菜单栏，头部的Tas栏目，去掉主体内容的padding样式
        window.dtHandleTheme(e.data.theme.color, ORIGINAL_THEME, version)
        this.$store.commit("layoutStore/setThemeObj", e.data.theme);
      }
    })
    
}




```

### 全局字典方法

```
@/store/index.js中增加全局存储字典的方法，目的是为了缓存字典值，如下：
const state = {
  dicMap: {}
};
const mutations = {
  mapDicData(state, data) {
    state.dicMap[data.dicCode] = data.dicItems;
  }
};

@/config/tool.js 引入公共的获取字典的方法
// 获取字典
// dicCode 字典类型   force 是否刷新字典，默认一直刷新请求字典
// 该方法会将已经获取的接口存在store,为了不频繁调用接口查字典，可以直接从store里面取,现在默认都是从接口取
export const getDicItemList = async (dicCode,force=true) => {
  let arr = [];
  let dicParam = {
    pageNum: 0,
    pageSize: 0,
    param: {
      dicCode: dicCode
    }
  };
  let dicMap = store.state.dicMap
  if(_.has(dicMap,dicCode)&&dicMap[dicCode]&&!force){
    return dicMap[dicCode]
  }
  // 调用获取字典的接口  接口地址：https://kbc-sta.kbao123.com/gateway/kbc-bsc/api/dic/item/getDicItems

  let res = await dicByCodeApi(dicParam);
  if (res && res.list) {
    arr = res.list;
    store.commit("mapDicData",{dicCode:dicCode,dicItems:arr})
  }
  return arr;
}
用法：
在需要获取字典的页面调用该方法，一般进入页面我会将该页面所有用到的字典都获取一遍：
async getDicFun() {
  this.dicList1 = await getDicItemList("sys.dicType")
  this.dicList2 = await getDicItemList("sys.status")
  this.dicList3 = await getDicItemList("sys.gender")
  .....
},
因为字典涉及到翻译，所以在页面的初始化渲染之前一定要等字典列表都加载完，否则翻译器可能没法翻译成功，处理方式：
在created方法中等页面字典方法调用完再走init接口，如下：
// created 前面增加 async关键字 
async created(){
  await this.getDicFun()
  // 在这里调用初始化方法
  this.init()
  ....
}

main.js 引入如下过滤器

// 该方法主要针对表格里面的相关字典的展示，用来翻译字典
// dicType 传入字典类型 同获取字典的接口传参一致
Vue.filter("getDicItemName", function (val, dicType) {
  let dicList = store.state.dicMap[dicType];
  if (dicList) {
    let dicItemObj = _.find(dicList, el=>{return el.dicItemCode == val});
    if (dicItemObj) {
      val = dicItemObj.dicItemName;
      return val;
    }
    return val;
  }
  return val;
})
用法：
<el-table-column align="center" prop="status" label="xxx">
  <template slot-scope="scope">
    {{scope.row.status|getDicItemName("sys.status")}}
  </template>
</el-table-column>





```

### 用户姓名获取

```
main.js 引入如下过滤器

// 用户名称获取  
## 注意   localStorage获取tenantUsers的前提是父子系统的域名必须保持一致，否则拿不到localStorage  
##  云服  https://kbc-sta.kbao123.com      子系统：https://kbc-sta.kbao123.com/ucs-web/


Vue.filter("getNickName", function (userId) {
  if(!localStorage.getItem("tenantUsers")){return userId}
  let tenantUsers = JSON.parse(localStorage.getItem("tenantUsers"))
  let userObj = _.find(tenantUsers,el=>{return el.userId == userId})
  if(userObj){
    return userObj.nickName
  }
  return userId
})

用法：
<el-table-column align="center" prop="createId" label="xxx">
  <template slot-scope="scope">
    {{scope.row.createId|getNickName(scope.row.createId)}}
  </template>
</el-table-column>




如果子系统有提供查询用户列表的接口 

@/app.vue 中

定义方法：

async getTenantUsers() {
  if(!localStorage.getItem("tenantUsers")){
    let res = await getTenantUsers()
    if (res) {
      localStorage.setItem("tenantUsers", JSON.stringify(res))
    }
  }
}

created(){
  this.getTenantUsers()
}

