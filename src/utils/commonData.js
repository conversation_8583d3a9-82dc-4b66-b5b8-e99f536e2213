/** *公共数据*/
const data = {};
// 是否
const whether = [{
    dicItemCode: 1,
    dicItemName: "是"
  },
  {
    dicItemCode: 1,
    dicItemName: "否"
  },
];

// 属性类型
const attributes = [{
    dicItemCode: 1,
    dicItemName: "指标属性"
  },
  {
    dicItemCode: 1,
    dicItemName: "匹配属性"
  },
  {
    dicItemCode: 1,
    dicItemName: "权力属性"
  }
];


// 数据类型
const dataTypes = [{
    dicItemCode: 1,
    dicItemName: "字符串"
  },
  {
    dicItemCode: 1,
    dicItemName: "整数"
  },
  {
    dicItemCode: 1,
    dicItemName: "浮点"
  }
];

//优先权规则
const rules = [{
  dicItemCode: 1,
  dicItemName: "从高到底"
}, {
  dicItemCode: 2,
  dicItemName: "从低到高"
}, {
  dicItemCode: 3,
  dicItemName: "等于"
}, {
  dicItemCode: 4,
  dicItemName: "包含"
}, {
  dicItemCode: 5,
  dicItemName: "政策扶持"
}]


data.whether = whether;
data.attributes = attributes;
data.dataTypes = dataTypes;
data.rules = rules;
export default data;