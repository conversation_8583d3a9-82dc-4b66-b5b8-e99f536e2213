import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import Pagination from "@/components/layouts/Pagination";
import DtPopup from "@/components/layouts/DtPopup";


import domainObj from "@/utils/globalParam";

import {
  getDicItemList
} from "@/config/tool.js";
import {
  validate,
  validateAlls
} from "@/config/validation";
import _ from "lodash";

export const baseComponent = {
  components: {
    TableToolTemp,
    SearchForm,
    Pagination,
    DtPopup
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = []
      this._.each(val, item => {
        this.multipleSelection.push(item.attributeName || null)
      })
    },
    normalSearch(data) {

      console.log(data)
      if (data) {
        this.initParam = data;
      }
      // this.initParam.pageNum = 1
      // this.initParam.pageSize = 20
      if (this.paramObj) {
        let param = this._.cloneDeep(this.initParam)
        _.each(param, (value, key) => {
          if (!_.includes(["pageNum", "pageSize"], key)) {
            this.paramObj[key] = value
          }
        })
        // this.paramObj = this._.cloneDeep(this.initParam.param)
      }

      this.initList();
    },
    normalResetQuery() {
      this.initParam = this.$options.data().initParam
      if (this.paramObj) {
        this.paramObj = this.$options.data().paramObj
      }
      this.initList();
    },
    handleSizeChange(val) {
      this.initParam.pageSize = val;
      this.initList();
    },
    handleCurrentChange(val) {
      this.initParam.pageNum = val;
      this.initList();
    },
    //初始化查询下拉框
    setSearchFormTemp(name, res) {
      this._.each(this.searchFormTemp, el => {
        if (el.name == name) {
          el.list = res
        }
      })
    },
    setQueryParam(obj,key) {
      this.$store.commit("layoutStore/setQueryParam", {key:key,val:this._.cloneDeep(obj)});
    },
    getQueryParam(obj,key) {
      let cloneQueryParam = this._.cloneDeep(this.$store.state.layoutStore.queryParam)[key]
      if(cloneQueryParam){
        this._.each(cloneQueryParam, (val, k) => {
          if (this._.has(obj, k)) {
            obj[k] = cloneQueryParam[k]
          }
        })
        this.$store.commit("layoutStore/setQueryParam",  {key:key,val:{}});
      }
      
    },
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    currentLoginUser() {
      return this.$store.getters["layoutStore/getCurrentLoginUser"];
    }
  }
}


