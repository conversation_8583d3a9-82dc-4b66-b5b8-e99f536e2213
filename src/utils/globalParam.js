/**
 * 运行环境定义
 * dev  - 本地开发环境  --默认
 * sta  - 对应sta部署环境
 * uat  - 对应uat部署环境
 * prod - 对应生产部署环境
 */

let domainObj = {
  publicUrl: "/mock",
  baseUrl: "/dataMock",
  kbcUrl: "https://kbc-sta.kbao123.com/"
};

if (process.env.NODE_ENV == "sta") {
  domainObj.publicUrl = "https://kbc-sta.kbao123.com/gateway/kbc-bsc";
  domainObj.baseUrl = "https://kbc-sta.kbao123.com/gateway/kbc-sct-algo";
  domainObj.kbcUrl = "https://kbc-sta.kbao123.com/";
} else if (process.env.NODE_ENV == "uat") {
  domainObj.publicUrl = "https://kbc-uat.kbao123.com/gateway/kbc-bsc";
  domainObj.baseUrl = "https://kbc-uat.kbao123.com/gateway/kbc-sct-algo";
  domainObj.kbcUrl = "https://kbc-uat.kbao123.com/";
} else if (process.env.NODE_ENV == "prod") {
  domainObj.publicUrl = "https://kbc.dtinsure.com/gateway/kbc-bsc";
  domainObj.baseUrl = "https://kbc.dtinsure.com/gateway/kbc-sct-algo";
  domainObj.kbcUrl = "https://kbc.dtinsure.com/";
}

export default domainObj;
