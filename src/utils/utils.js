export const dateFormat = (fmt, date) => {
  let ret;
  date = new Date(date)
  const opt = {
    "Y+": date.getFullYear().toString(), // 年
    "m+": (date.getMonth() + 1).toString(), // 月
    "d+": date.getDate().toString(), // 日
    "H+": date.getHours().toString(), // 时
    "M+": date.getMinutes().toString(), // 分
    "S+": date.getSeconds().toString() // 秒
    // 有其他格式化字符需求可以继续添加，必须转化成字符串
  };
  for (let k in opt) {
    ret = new RegExp("(" + k + ")").exec(fmt);
    if (ret) {
      fmt = fmt.replace(
        ret[1],
        ret[1].length === 1 ? opt[k] : opt[k].padStart(ret[1].length, "0")
      );
    };
  };
  return fmt;
}

export const getParamString = (param) => {
  let strHref = window.location.href;     //获取Url字串
  let intPos = strHref.indexOf("?");      // 参数开始位置
  let strRight = strHref.substr(intPos + 1);
  let arrTmp = strRight.split("&"); //参数分割符
  for (let i = 0; i < arrTmp.length; i++) {
    let arrTemp = arrTmp[i].split("=");
    if (arrTemp[0].toUpperCase() == param.toUpperCase()) {
      return arrTemp[1];
    }
  }
  return "";
}

/** 
 * 根据币种类型获取币种单位名称
 * @param 
 * type代表数字
*/
export const getCurrencyUnit = (type)=> {    
  let name = "元";
  if(type=="1"){
      name = "元"
  }else if(type=="2"){
      name = "港元";
  }else if(type=="3"){
      name = "美元";
  }else if(type=="4"){
      name = "澳门元";
  }    
  return name;
}


/** 生成uuid */
export const guid = () => {
  function s4() {
    return Math.floor((1 + Math.random()) * 0x10000)
      .toString(16)
      .substring(1);
  }
  return (s4() + s4() + '-' + s4() + '-' + s4() + '-' + s4() + '-' + s4() + s4() + s4());
}

/** 生成uuid */
export const getguid = () => {
  function s4() {
    return Math.floor((1 + Math.random()) * 0x10000)
      .toString(16)
      .substring(1);
  }
  // console.log(s4())
  return (s4()+ s4());
}