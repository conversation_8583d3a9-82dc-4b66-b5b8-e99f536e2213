import axios from "axios";
import {
  Loading
} from "element-ui";
import store from "../store/index";
import Vue from "vue";
let _this = new Vue();
let axiosConfig = {
  baseURL: "",
  timeout: 10000,
  responseType: "json",
  withCredentials: true, // 是否允许带cookie这些
  headers: {
    "Content-Type": "application/json;charset=utf-8"
  }
}
let loadingInstance = null; //自定义不需要loading
const Axios = axios.create(axiosConfig);
//添加请求拦截器
Axios.interceptors.request.use(
  config => {
    if (!config.hideLoading) {
      loadingInstance = Loading.service({
        fullscreen: true,
        lock: true,
        text: "加载中...",
        target: document.getElementsByTagName("body")[0]
      });
    }
    if (sessionStorage.getItem("LoginAccessToken") && sessionStorage.getItem("LoginAccessToken") != "null" && sessionStorage.getItem("LoginAccessToken") != "undefined") {
      config.headers["access_token"] = sessionStorage.getItem("LoginAccessToken");
    }
    // if (store.state.layoutStore.currentLoginUser) {}
    config.headers["tenantId"] = store.state.layoutStore.currentLoginUser.tenantId || sessionStorage.getItem("tenantId")
    config.headers["funcId"] = store.state.layoutStore.currentLoginUser.funcId || sessionStorage.getItem("funcId")


    if (sessionStorage.getItem("tenantId"))
      if (config.ContentType) {
        config.headers["Content-Type"] = config.ContentType;
      }
    // 导入文件 需要转换
    if (config.isImport) {
      config.headers["Content-Type"] = "multipart/form-data";
      //处理文件上传 使用FormData处理 Blob, File，string类型
      let fd = new FormData();
      for (let key in config.data) {
        if (config.data.hasOwnProperty(key)) {
          fd.append(key, config.data[key]);
        }
      }
      // fd.append("file", config.data);
      config.data = fd;
    }
    return config;
  },
  error => {
    console.log("request：error", error);
    return Promise.reject(error.data.error.message);
  }
);

//返回状态判断(添加响应拦截器)
Axios.interceptors.response.use(
  async res => {
    if (loadingInstance) {
      loadingInstance.close();
    };
    //resp_code == 0 接口正常返回 
    if (res.data.resp_code == 0) {
      //如果有些接口 成功后是不返还任何值的（例如登出接口）前端赋值，以免没有datas
      if (!res.data.datas) {
        res.data.datas = res.data.resp_msg ? {
          resp_msg: res.data.resp_msg
        } : 'ok';
      };
      //如果设置接口(noError)不拦截error 返回参数
      if (res.config.noError) {
        return res.data;
      };
      return res.data.datas
    }
    //resp_code == 1 接口有返回 但是有相应的错误resp_msg

    if (res.data.resp_code == 1) {
      if (!res.config.noError && !res.config.hideMsg) {
        _this.$message({
          showClose: true,
          message: res.data.resp_msg || "服务异常",
          type: 'error'
        });
        return "";
      } else {
        return res.data;
      };
    }
    // resp_code == 401 登录信息过期 重新获取accessToken
    if (res.data.resp_code == 401) {
      const currentLoginUser = store.state.layoutStore.currentLoginUser;
      if (window.parent && window.parent.kbcChangeToken) {
        await window.parent.kbcChangeToken(currentLoginUser.access_token)
        let lastApiUrl = res.config.url;
        let lastApiData = res.config.data;
        const access_token = sessionStorage.getItem("LoginAccessToken");
        const tenantId = currentLoginUser.tenantId;
        const funcId = currentLoginUser.funcId;
        store.commit("layoutStore/setCurrentLoginUser", {
          access_token,
          tenantId,
          funcId
        })
        const lastData = await Axios.post(lastApiUrl, lastApiData);
        if (lastData) {
          return lastData;
        };
      }
    }
  },
  error => {
    //服务器状态码不是200的情况
    //处理404 500之类
    if (loadingInstance) {
      loadingInstance.close();
    };
    if (error.response.status) {
      _this.$message({
        showClose: true,
        message: "请求报错或请求超时！",
        type: "error"
      });
    };
    // 返回 response 里的错误信息
    return Promise.reject(error.response.data);
  }
);

// 对axios的实例重新封装成一个plugin ,方便 Vue.use(xxxx)
/* eslint-disable */
export default {
  Axios,
  install: function (Vue, Option) {
    Object.defineProperty(Vue.prototype, "$http", {
      value: Axios
    });
  }
};