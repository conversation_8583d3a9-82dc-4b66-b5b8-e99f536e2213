import Vue from "vue";
import VueRouter from "vue-router";
import home from "./home";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
// import { checkUrl, isLogin } from "@/utils/auth";
import store from "@/store/layoutStore.js";
import gpRule from "./gpRule";
// import allocation from "./allocation"
import templateEngine from "./templateEngine"
import dataSpace from './dataSpace';

Vue.use(VueRouter);

const routes = [{
    path: "/",
    name: "index",
    redirect: "/home",
    component: () =>
      import(/* webpackChunkName: "layouts" */ "@/views/index.vue"),
    // children: [...home]
  },
  {
    path: "*",
    name: "404",
    component: () =>
      import( /* webpackChunkName: "exception" */ "@/views/exception/404")
  },
  {
    path: "/home",
    name: "home",
    component: () => import("@/views/home")
  },
  ...gpRule,
  ...templateEngine,
  ...dataSpace
];

// 解决路由重复点击报错
const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err);
};

const router = new VueRouter({
  mode: "history",
  base: process.env.BASE_URL,
  routes
});

router.beforeEach((to, from, next) => {
  if (to.path !== from.path) {
    NProgress.start();
    let el = document.getElementById("nprogress");
    if (el) {
      el.querySelector(".bar").style.background = store.state.themeObj.color;
      el.querySelector(".spinner-icon").style.borderTopColor =
        store.state.themeObj.color;
      el.querySelector(".spinner-icon").style.borderLeftColor =
        store.state.themeObj.color;
    }
  }
  // if (!isLogin() && to.path !== "/login") {
  //   next({
  //     path: "/login"
  //   });
  // }
  // if (
  //   to.path !== "/404" &&
  //   to.path !== "/403" &&
  //   (checkUrl(to.path) == "404" || checkUrl(to.path) == "403")
  // ) {
  //   next({
  //     path: "/" + checkUrl(to.path)
  //   });
  // }
  next();
});

router.afterEach(() => {
  NProgress.done();
});

export default router;