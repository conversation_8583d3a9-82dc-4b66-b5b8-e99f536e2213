export default [
  {
    path: "/strategyMatchList",
    name: "strategyMatchList",
    meta: {},
    component: () => import(/* webpackChunkName: "index" */ "@/views/strategyMatchManage/strategyMatchList.vue")
  },
  {
    path: "/strategyMatchUpdate",
    name: "strategyMatchUpdate",
    meta: {},
    component: () => import(/* webpackChunkName: "index" */ "@/views/strategyMatchManage/strategyMatchUpdate.vue")
  },
  {
    path: "/sourceMatchFieldConfig",
    name: "sourceMatchFieldConfig",
    meta: {},
    component: () => import(/* webpackChunkName: "index" */ "@/views/strategyMatchManage/sourceMatchField/sourceMatchFieldConfig.vue")
  },
  {
    path: "/sourceMatchFieldConfigView",
    name: "sourceMatchFieldConfigView",
    meta: {},
    component: () => import(/* webpackChunkName: "index" */ "@/views/strategyMatchManage/sourceMatchFieldView/sourceMatchFieldConfigView.vue")
  },
  {
    path: "/strategyMatchIndicatorList",
    name: "strategyMatchIndicatorList",
    meta: {},
    component: () => import(/* webpackChunkName: "index" */ "@/views/strategyMatchManage/strategyMatchIndicator/strategyMatchIndicatorList.vue")
  },
  {
    path: "/strategyMatchIndicatorUpdate",
    name: "strategyMatchIndicatorUpdate",
    meta: {},
    component: () => import(/* webpackChunkName: "index" */ "@/views/strategyMatchManage/strategyMatchIndicator/strategyMatchIndicatorUpdate.vue")
  },
  {
    path: "/strategyMatchIndicatorConfig",
    name: "strategyMatchIndicatorConfig",
    meta: {},
    component: () => import(/* webpackChunkName: "index" */ "@/views/strategyMatchManage/strategyMatchIndicator/strategyMatchIndicatorConfig.vue")
  },


  {
    path: "/executedRecordPage",
    name: "executedRecordPage",
    meta: {},
    component: () => import(/* webpackChunkName: "index" */ "@/views/strategyMatchManage/strategyExecutedRecord/executedRecordPage.vue")
  },
  {
    path: "/executedRecordInfoView",
    name: "executedRecordInfoView",
    meta: {},
    component: () => import(/* webpackChunkName: "index" */ "@/views/strategyMatchManage/strategyExecutedRecord/executedRecordInfoView.vue")
  } 
];