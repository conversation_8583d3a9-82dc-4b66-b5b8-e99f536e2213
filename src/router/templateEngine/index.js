export default [
  // 数据源列表
  {
    path: "/matchSourceIndex",
    name: "matchSourceIndex",
    component: () => import("@/views/sourceManage/index.vue")
  },
  // 数据源新增
  {
    path: "/sourceManageUpdate",
    name: "sourceManageUpdate",
    component: () => import("@/views/sourceManage/sourceManageUpdate.vue")
  },
  // 数据源配置
  {
    path: "/sourceInfo",
    name: "sourceInfo",
    component: () => import("@/views/sourceManage/sourceInfo.vue")
  },
  // 数据源参数列表新增
  {
    path: "/paramUpdate",
    name: "paramUpdate",
    component: () => import("@/views/sourceManage/paramUpdate.vue")
  },
  // 数据源字段列表新增
  {
    path: "/filedUpdate",
    name: "filedUpdate",
    component: () => import("@/views/sourceManage/filedUpdate.vue")
  },
  // 指标配置
  {
    path: "/matchSource",
    name: "matchSource",
    component: () => import("@/views/indexManage/matchSource.vue")
  },
  // 指标新增
  {
    path: "/strAdd",
    name: "strAdd",
    component: () => import("@/views/indexManage/strAdd.vue")
  },
  // 指标列表
  {
    path: "/indexManage",
    name: "indexManage",
    component: () => import("@/views/indexManage/index.vue")
  },
  // 策略管理列表
  {
    path: "/strategicManage",
    name: "strategicManage",
    meta: {},
    component: () =>
      import(
        /* webpackChunkName: "index" */ "@/views/strategicManage/index.vue"
      )
  },
  // 新增策略
  {
    path: "/stryUpdate",
    name: "stryUpdate",
    meta: {},
    component: () =>
      import(
        /* webpackChunkName: "index" */ "@/views/strategicManage/stryUpdate.vue"
      )
  },
  // 策略配置
  {
    path: "/confiGuration",
    name: "confiGuration",
    meta: {},
    component: () =>
      import(
        /* webpackChunkName: "index" */ "@/views/strategicManage/confiGuration.vue"
      )
  },
  // 执行记录，数据明细
  {
    path: "/sourceDetail",
    name: "sourceDetail",
    meta: {},
    component: () =>
      import(
        /* webpackChunkName: "index" */ "@/views/strategicManage/sourceDetail.vue"
      )
  },
  // 策略执行批次
  {
    path: "/strategyBach",
    name: "strategyBach",
    meta: {},
    component: () =>
      import(
        /* webpackChunkName: "index" */ "@/views/strategicManage/strategyBach.vue"
      )
  },
  // 执行记录，批次明细
  {
    path: "/bachDetail",
    name: "bachDetail",
    meta: {},
    component: () =>
      import(
        /* webpackChunkName: "index" */ "@/views/strategicManage/bachDetail.vue"
      )
  },
  // 数据流转图
  {
    path: "/flowChart",
    name: "flowChart",
    meta: {},
    component: () =>
      import(
        /* webpackChunkName: "index" */ "@/views/strategicManage/components/flowChart.vue"
      )
  },
  // 版本管理列表
  {
    path: "/visionList",
    name: "visionList",
    component: () => import("@/views/indexManage/visionList.vue")
  },
  // 指标配置查看
  {
    path: "/indicator-config-view",
    name: "indicatorConfigView",
    component: () => import("@/views/indexManage/configView.vue")
  },
];
