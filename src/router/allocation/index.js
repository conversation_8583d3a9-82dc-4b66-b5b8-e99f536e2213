export default [{
    path: "/allocationlist",
    name: "allocationlist",
    component: () =>
      import("@/views/allocation/index.vue")
  },
  {
    path: "/allocationUpdate",
    name: "allocationUpdate",
    component: () =>
      import("@/views/allocation/update.vue")
  },
  {
    path: "/matchinglist",
    name: "matchinglist",
    component: () =>
      import("@/views/matching/index.vue")
  }, {
    path: "/matchingUpdate",
    name: "matchingUpdate",
    component: () =>
      import("@/views/matching/update.vue")
  },
  {
    path: "/matchingPriority",
    name: "matchingPriority",
    component: () =>
      import("@/views/matching/priority.vue")
  },
];