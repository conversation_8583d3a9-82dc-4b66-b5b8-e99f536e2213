<!--
/*
 * @Author: thomas
 * @Date: 2019-05-27 13:40:47
 * @Description: 封装table搜索条件(双输入框)
 -->
<template>
  <div>
    <el-date-picker v-if="elType=='DateTimePicker'" v-model="value"  :picker-options="pickerOptions" type="datetimerange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" @change="getValue()" value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
    <el-date-picker v-model="value" v-if="elType=='DatePicker'" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" @change="getValue()"></el-date-picker>
    <el-time-picker is-range v-if="elType=='TimePicker'" v-model="value" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" value-format="HH:mm:ss" @change="getValue()"></el-time-picker>
  </div>
</template>

<script>
export default {
  name: "elType",
  props: {
    elType: {
      type: String,
      default: "DateTimePicker"
    },
    options: {
      type: Array,
      default: () => []
    },
    name: {
      type: String
    },
    pickerOptions:{
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      value: ""
    };
  },
  created() { },
  watch: {
    options: {
      immediate: true,
      deep: true,
      handler: function(val, oldVal) {
        if (val[0].value == "") {
          this.value = "";
        } else if (val.length == 2 && val[0].value != "") {
          this.value = [];
          this.value[0] = val[0].value;
          this.value[1] = val[1].value;
        }
      }
    }
  },
  methods: {
    getValue(val) {
      this.$emit("editParams", this.value, this.name);
    }
  }
};
</script>

<style scoped lang="less">
.query-conditions-container {
  background: #eee;
  padding: 18px 0 0;
  border: 1px solid #d3dce6;

  .el-input,
  .el-select {
    width: 190px;
  }

  .search-btn {
    margin-left: 20px;
  }
}
</style>