<template>
  <div>
    <SearchForm
      :searchForm="initParam"
      :labelWidth="'120px'"
      :searchFormTemp="searchFormTemp"
      @normalSearch="normalSearch"
      @normalResetQuery="normalResetQuery"
    ></SearchForm>
    <el-table
      :data="tableData"
      class="dt-table"
      style="width: 100%"
      :height="popStatus == 3 || popStatus == 4 ? '30vh' : '68vh'"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        v-if="popStatus == 3 || popStatus == 4"
        type="selection"
        width="55"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="fieldId"
        width="150px"
        label="字段编码"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.fieldId }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="name"
        width="250px"
        label="字段名称"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="matchRule" label="数据类型">
        <template slot-scope="scope">
          <span>{{
            scope.row.dataType | getDicItemName("sct.source.dataType")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="isDic" label="是否为字典">
        <template slot-scope="scope">
          <span>{{ scope.row.isDic | getDicItemName("gen.yesorno.num") }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="isCalc" label="是否参与计算">
        <template slot-scope="scope">
          <span>{{
            scope.row.isCalc | getDicItemName("gen.yesorno.num")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="propertyType" label="属性类型">
        <template slot-scope="scope">
          <span>{{
            scope.row.propertyType | getDicItemName("sct.source.propertyType")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="操作"
        fixed="right"
        width="150"
        v-if="popStatus == 1 || popStatus == 2"
      >
        <template slot-scope="scope">
          <div>
            <el-button type="text" size="mini" @click="handleUpdate(scope.row)"
              >编辑</el-button
            >
            <el-button type="text" size="mini" @click="handleDel(scope.row)"
              >删除</el-button
            >
            <el-button
              type="text"
              v-if="popStatus == 2"
              size="mini"
              @click="addPriority(scope.row)"
              >优先权配置</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :pageData="initParam"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
    ></Pagination>
    <DtPopup
      :isShow.sync="showPopup"
      @close="showPopup = false"
      @confirm="confirmUpdate"
      :isSmall="true"
    >
      <div class="popup-text">
        删除后无法恢复，请确认是否删除？
      </div>
    </DtPopup>
  </div>
</template>

<script>
import {
  getAssignPage,
  getMatchPage,
  deleteAssign,
  getAssignPageById,
  getMatchPageById,
  saveAssignFbId,
  saveMatchFbId,
} from "@/api/allocation/index.js";
import { getDicItemList } from "@/config/tool";
import sourceTemp from "@/mixins/sourceTemp";
import { baseComponent } from "@/utils/common";
export default {
  name: "sourceTemp",
  mixins: [sourceTemp, baseComponent],
  props: {
    popStatus: {
      type: String,
      default: "", //1分配源列表，2匹配源列表，3分配源弹框，4匹配源弹框
    },
    strategyId: {
      type: String,
      default: "", //策略id
    },
  },
  data() {
    return {
      listFbId: [],
    };
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
  },
  watch: {},
  created() {
    if (this.strategyId) {
      this.initParam.param.strategyId = this.strategyId;
    }
    this.initData();
  },
  methods: {
    //初始化数据
    async initData() {
      await this.getDictList();
      this.initList();
    },
    async initList() {
      let res;
      if (this.popStatus == "1") {
        res = await getAssignPage(this.initParam);
      } else if (this.popStatus == "2") {
        res = await getMatchPage(this.initParam);
      } else if (this.popStatus == "3") {
        res = await getAssignPageById(this.initParam);
      } else {
        res = await getMatchPageById(this.initParam);
      }
      if (!res) {
        return;
      }
      this.tableData = res.list;
      this.initParam.pageNum = res.pageNum;
      this.initParam.pageSize = res.pageSize;
      this.total = Number(res.total);
    },
    // 获取字典/
    async getDictList() {
      let res = await getDicItemList("sct.source.dataType");
      this.setSearchFormTemp("dataType", res);
      res = await getDicItemList("sct.source.propertyType");
      this.setSearchFormTemp("propertyType", res);
      res = await getDicItemList("gen.yesorno.num");
      this.setSearchFormTemp("isCalc", res);
      this.setSearchFormTemp("isDic", res);
    },
    handleUpdate(row) {
      if (this.popStatus == "1") {
        this.$router.push({
          name: "allocationUpdate",
          query: {
            type: "edit",
            fbId: row.fbId,
          },
        });
      } else if (this.popStatus == "2") {
        this.$router.push({
          name: "matchingUpdate",
          query: {
            type: "edit",
            fbId: row.fbId,
          },
        });
      }
    },
    handleDel(row) {
      this.fbId = row.fbId;
      this.showPopup = true;
    },
    async confirmUpdate() {
      let res = await deleteAssign({ fbId: this.fbId });
      if (!res) {
        return;
      }
      this.initList();
      this.showPopup = false;
    },
    addPriority(row) {
      this.$router.push({
        name: "matchingPriority",
        query: {
          fbId: row.fbId,
        },
      });
    },
    handleSelectionChange(val) {
      this.listFbId = val;
    },
    async saveFbId() {
      let res;
      let fbIds = [];
      let listFbIds = this.listFbId;
      for (let i = 0; i < listFbIds.length; i++) {
        fbIds.push(listFbIds[i].fbId);
      }
      let obj = {
        strategyId: this.strategyId,
        listFbId: fbIds,
      };
      if (fbIds.length == 0) {
        this.$message({
          message: "请至少选择一条数据源",
          type: "error",
        });
        return;
      }
      if (this.popStatus == "3") {
        res = await saveAssignFbId(obj);
      } else {
        res = await saveMatchFbId(obj);
      }
      if (!res) {
        return;
      }
      this.$emit("confirm");
    },
  },
};
</script>

<style lang="less"></style>
