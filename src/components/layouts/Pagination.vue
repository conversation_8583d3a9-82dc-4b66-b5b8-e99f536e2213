<template>
  <div class="pagination clearfix">
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page.sync="pageData.pageNum"
      :page-sizes="pageSizes"
      :page-size="pageData.pageSize"
      layout="total,slot, sizes, prev, pager, next, jumper"
      :total="total"
    >
      <span class="add-select c-666" v-if="showSelect">
        已选：{{selNum}}条
        <span class="dt-fz14 checkbox">
          仅显示已选
          <el-switch v-model="value" @change="change"></el-switch>
        </span>
      </span>
    </el-pagination>
  </div>
</template>

<script>
export default {
  data() {
    return {
      pageSizes: [5,10, 20, 30],
      value: false
    };
  },
  props: {
    pageData: {
      type: Object,
      default: function() {
        return {};
      }
    },
    showSelect: {
      type: Boolean,
      default: false
    },
    selNum: {
      type: Number,
      default: 0
    },
    total: {
      type: Number,
      default: 0
    }
  },
  beforeDestroy() {
    console.log('aaa')
  },
  methods: {
    handleSizeChange(val) {
      this.$emit("size-change", val);
    },
    handleCurrentChange(val) {
      this.$emit("current-change", val);
    },
    change(data) {
      this.$emit("changeSel", data);
    }
  }
};
</script>

<style lang="less">
.pagination {
  text-align: right;
  padding-left: 10px;
  padding-right: 10px;
  .el-pagination__total {
    float: left;
  }
  .el-pagination__sizes {
    /*float: left;*/
  }
  .add-select {
    /*float: left;*/
    font-weight: 400;
    .el-switch__core {
      height: 20px;
      line-height: 20px;
    }
    .checkbox {
      margin: 0 10px;
    }
  }
}
</style>
