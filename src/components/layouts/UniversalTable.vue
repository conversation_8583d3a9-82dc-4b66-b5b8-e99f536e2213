<template>
  <div class="universal-table-container">
    <!-- 页面标题和操作区域 -->
    <div class="header-section">
      <div class="header-top">
        <div class="title-section">
          <h1 class="page-title">
            <div class="title-icon">
              <i :class="titleIcon || 'el-icon-data-analysis'"></i>
            </div>
            <div class="title-text">
              <span class="main-title">{{ title }}</span>
              <p class="page-subtitle">{{ subtitle }}</p>
            </div>
          </h1>
        </div>
        <div class="action-section">
          <slot name="headerActions">
            <el-button
              v-if="showAddButton"
              type="primary"
              @click="handleAdd"
              class="add-btn primary-btn"
            >
              <i class="el-icon-plus"></i>
              {{ addButtonText }}
            </el-button>
          </slot>
        </div>
      </div>
      <BreadcrumbNav :items="breadcrumbItems" v-if="breadcrumbItems && breadcrumbItems.length > 0" />
      <!-- 搜索表单区域 -->
      <div class="search-section" v-if="showSearchForm">
        <SearchForm
          :searchForm="searchParams"
          :labelWidth="searchLabelWidth"
          :searchFormTemp="searchFormConfig"
          @normalSearch="handleSearch"
          @normalResetQuery="handleReset"
        />
      </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <div v-if="tableData.length > 0" class="table-wrapper">
        <el-table
          :data="tableData"
          stripe
          v-hover
          class="modern-table"
          style="width: 100%"
          v-loading="loading"
          :element-loading-text="loadingText"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(251, 246, 238, 0.8)"
          @selection-change="handleSelectionChange"
          @sort-change="handleSortChange"
          @row-click="handleRowClick"
        >
          <!-- 选择列 -->
          <el-table-column
            v-if="showSelection"
            type="selection"
            width="55"
            align="center"
          />
          
          <!-- 动态生成的列 -->
          <el-table-column
            v-for="column in columns"
            :key="column.prop"
            :align="column.align || 'center'"
            :prop="column.prop"
            :width="column.width"
            :min-width="column.minWidth"
            :label="column.label"
            :sortable="column.sortable"
            :fixed="column.fixed"
            :show-overflow-tooltip="column.showOverflowTooltip"
          >
            <template slot-scope="scope">
              <!-- 自定义列内容 -->
              <slot 
                :name="column.prop" 
                :row="scope.row" 
                :column="column" 
                :$index="scope.$index"
              >
                <!-- 默认渲染 -->
                <span v-if="!column.render">{{ getColumnValue(scope.row, column) }}</span>
                
                <!-- 自定义渲染函数 -->
                <component 
                  v-else
                  :is="getRenderComponent(scope.row, column, scope.$index)"
                />
              </slot>
            </template>
          </el-table-column>
          
          <!-- 操作列 -->
          <el-table-column
            v-if="showActions && actions.length > 0"
            align="center"
            header-align="center"
            label="操作"
            :width="actionColumnWidth"
            :fixed="actionColumnFixed"
          >
            <template slot-scope="scope">
              <div class="action-buttons">
                <template v-for="action in actions">
                  <el-button
                    v-if="!action.hidden || !action.hidden(scope.row)"
                    :key="action.key"
                    :size="action.size || 'mini'"
                    :plain="action.plain !== false"
                    :disabled="action.disabled && action.disabled(scope.row)"
                    @click="handleAction(action, scope.row, scope.$index)"
                    :class="['action-btn', action.class]"
                  >
                    <i v-if="action.icon" :class="action.icon"></i>
                    {{ action.label }}
                  </el-button>
                </template>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && tableData.length === 0" class="empty-state">
        <div class="empty-content">
          <div class="empty-icon">
            <i :class="emptyIcon || titleIcon || 'el-icon-data-analysis'"></i>
          </div>
          <h3>{{ emptyTitle || '暂无数据' }}</h3>
          <p>{{ emptyDescription || '暂时没有相关数据' }}</p>
        </div>
      </div>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section" v-if="showPagination && total > 0">
      <Pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :pageData="paginationData"
        :total="total"
        :layout="paginationLayout"
      />
    </div>
  </div>
</template>

<script>
import SearchForm from './SearchForm';
import Pagination from './Pagination';
import BreadcrumbNav from '@/components/common/BreadcrumbNav.vue';

export default {
  name: 'UniversalTable',
  components: {
    SearchForm,
    Pagination,
    BreadcrumbNav
  },
  props: {
    // 页面标题相关
    title: {
      type: String,
      default: '数据列表'
    },
    subtitle: {
      type: String,
      default: ''
    },
    titleIcon: {
      type: String,
      default: 'el-icon-data-analysis'
    },
    
    // 表格数据
    tableData: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    loadingText: {
      type: String,
      default: '加载中...'
    },
    
    // 列配置
    columns: {
      type: Array,
      required: true
    },
    
    // 操作按钮配置
    actions: {
      type: Array,
      default: () => []
    },
    showActions: {
      type: Boolean,
      default: true
    },
    actionColumnWidth: {
      type: [String, Number],
      default: 320
    },
    actionColumnFixed: {
      type: String,
      default: 'right'
    },
    
    // 搜索表单配置
    showSearchForm: {
      type: Boolean,
      default: true
    },
    searchFormConfig: {
      type: Array,
      default: () => []
    },
    searchParams: {
      type: Object,
      default: () => ({})
    },
    searchLabelWidth: {
      type: String,
      default: '120px'
    },
    
    // 分页配置
    showPagination: {
      type: Boolean,
      default: true
    },
    paginationData: {
      type: Object,
      default: () => ({
        pageSize: 10,
        pageNum: 1
      })
    },
    total: {
      type: Number,
      default: 0
    },
    paginationLayout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper'
    },
    
    // 其他功能配置
    showSelection: {
      type: Boolean,
      default: false
    },
    showAddButton: {
      type: Boolean,
      default: true
    },
    addButtonText: {
      type: String,
      default: '新增'
    },
    showAddInEmpty: {
      type: Boolean,
      default: true
    },
    
    // 空状态配置
    emptyTitle: {
      type: String,
      default: ''
    },
    emptyDescription: {
      type: String,
      default: ''
    },
    emptyIcon: {
      type: String,
      default: ''
    },
    // 面包屑配置
    breadcrumbItems: {
      type: Array,
      default: () => []
    }
  },
  
  methods: {
    // 获取列值
    getColumnValue(row, column) {
      const keys = column.prop.split('.');
      let value = row;
      for (let key of keys) {
        value = value ? value[key] : '';
      }
      
      // 应用格式化函数
      if (column.formatter && typeof column.formatter === 'function') {
        return column.formatter(value, row, column);
      }
      
      return value;
    },
    
    // 获取渲染组件
    getRenderComponent(row, column, index) {
      if (column.render && typeof column.render === 'function') {
        return column.render(row, column, index);
      }
      return null;
    },
    
    // 处理操作按钮点击
    handleAction(action, row, index) {
      this.$emit('action-click', {
        action: action.key,
        row,
        index,
        actionConfig: action
      });
    },
    
    // 处理搜索
    handleSearch(searchData) {
      this.$emit('search', searchData);
    },
    
    // 处理重置
    handleReset() {
      this.$emit('reset');
    },
    
    // 处理新增
    handleAdd() {
      this.$emit('add');
    },
    
    // 处理分页大小变化
    handleSizeChange(size) {
      this.$emit('size-change', size);
    },
    
    // 处理页码变化
    handleCurrentChange(page) {
      this.$emit('current-change', page);
    },
    
    // 处理选择变化
    handleSelectionChange(selection) {
      this.$emit('selection-change', selection);
    },
    
    // 处理排序变化
    handleSortChange(sortInfo) {
      this.$emit('sort-change', sortInfo);
    },
    
    // 处理行点击
    handleRowClick(row, column, event) {
      this.$emit('row-click', row, column, event);
    }
  }
};
</script>

<style lang="less" scoped>
@import "~@/views/indexManage/components/shared-styles.less";
@success-color: #67c23a;
.universal-table-container {
  min-height: 100vh;
  background: white;
  overflow: hidden;

  // 页面头部和搜索区域
  .header-section {
    .header-top {
      background-color: @bg-light;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 28px 28px;
      border: 1px solid @border-light;
      .title-section {
        .page-title {
          margin: 0;
          display: flex;
          align-items: center;
          gap: 12px;

          .title-icon {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, @primary-color 0%, @primary-hover 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px @primary-shadow;

            i {
              color: white;
              font-size: 18px;
            }
          }

          .title-text {
            .main-title {
              font-size: 20px;
              font-weight: 600;
              color: @text-primary;
              margin: 0 0 2px 0;
              display: block;
            }

            .page-subtitle {
              margin: 0;
              color: @text-secondary;
              font-size: 12px;
              font-weight: 400;
            }
          }
        }
      }

      .action-section {
        .primary-btn {
          height: 32px;
          padding: 0 16px;
          border-radius: 6px;
          font-weight: 500;
          font-size: 13px;
          background: linear-gradient(135deg, @primary-color 0%, @primary-hover 100%);
          border: none;
          box-shadow: 0 2px 8px @primary-shadow;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px @primary-shadow;
          }

          i {
            margin-right: 6px;
            font-size: 12px;
          }
        }
      }
    }

    .search-section {
      padding: 24px 0;
      border-top: 1px solid @border-light;
      border-bottom: 1px solid @border-light;
    }
  }

  // 表格区域
  .table-section {
    .table-wrapper {
      .modern-table {
        /deep/ .el-table__header-wrapper {
          .el-table__header {
            th {
              color: @text-primary;
              font-weight: 600;
              font-size: 14px;
            }
          }
        }

        /deep/ .el-table__body-wrapper {
          .el-table__row {
            transition: all 0.3s ease;

            &:hover {
              background: rgba(@primary-color, 0.05) !important;
              transform: translateY(-1px);
              box-shadow: 0 2px 8px rgba(@primary-color, 0.1);
            }

            td {
              border-bottom: 1px solid @border-light;
            }
          }
        }

        /deep/ .el-table--striped .el-table__body tr.el-table__row--striped td {
          background: @bg-light;
        }
      }

      // 操作按钮样式
      .action-buttons {
        display: flex;
        gap: 4px;
        justify-content: center;
        align-items: center;

        .action-btn {
          border-radius: 4px;
          font-size: 11px;
          padding: 4px 8px;
          transition: all 0.3s ease;
          font-weight: 500;
          min-width: 56px;
          height: 28px;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          }

          i {
            margin-right: 3px;
            font-size: 11px;
          }

          &.config-btn {
            color: @primary-color;
            border-color: @border-light;
            background: @bg-light;

            &:hover {
              background: @primary-color;
              color: white;
              border-color: @primary-color;
            }
          }

          &.edit-btn {
            color: @primary-color;
            border-color: rgba(@primary-color, 0.3);
            background: rgba(@primary-color, 0.1);

            &:hover {
              background: @primary-color;
              color: white;
              border-color: @primary-color;
            }
          }

          &.version-btn {
            color: @primary-color;
            border-color: rgba(@primary-color, 0.3);
            background: rgba(@primary-color, 0.1);

            &:hover {
              background: @primary-color;
              color: white;
              border-color: @primary-color;
            }
          }

          &.delete-btn {
            color: @error-color;
            border-color: rgba(@error-color, 0.3);
            background: rgba(@error-color, 0.1);

            &:hover {
              background: @error-color;
              color: white;
              border-color: @error-color;
            }
          }

          &.primary-btn {
            color: @success-color;
            border-color: rgba(@success-color, 0.3);
            background: rgba(@success-color, 0.1);

            &:hover {
              background: @success-color;
              color: white;
              border-color: @success-color;
            }
          }
        }
      }
    }

    // 空状态
    .empty-state {
      padding: 80px 20px;
      text-align: center;

      .empty-content {
        .empty-icon {
          width: 80px;
          height: 80px;
          background: rgba(@primary-color, 0.1);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 20px;

          i {
            font-size: 40px;
            color: @primary-color;
          }
        }

        h3 {
          margin: 0 0 8px 0;
          color: @text-primary;
          font-size: 18px;
          font-weight: 600;
        }

        p {
          margin: 0 0 24px 0;
          color: @text-secondary;
          font-size: 14px;
        }

        .primary-btn {
          background: linear-gradient(135deg, @primary-color 0%, @primary-hover 100%);
          border: none;
          box-shadow: 0 4px 12px @primary-shadow;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px @primary-shadow;
          }
        }
      }
    }
  }

  // 分页区域
  .pagination-section {
    padding: 20px 28px;
    border-top: 1px solid @border-light;
  }
}
</style> 