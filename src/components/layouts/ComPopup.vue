<template>
  <DtPopup :isShow.sync="showComGroup" @close="closeComGroup" :title="title" class="no-padding" width="1100px" :footer="false">
    <div>
      <div>
        <SearchForm :searchForm="comParam" :searchFormTemp="searchTemp" @normalSearch="normalSearch" @normalResetQuery="normalResetQuery" class="comSearch"></SearchForm>
        <el-table :data="list"  stripe class="dt-table com-popup-table" ref="multipleTable" style="width: 100%" height="50vh" v-hover @selection-change="handleSelectionChange">
          <el-table-column  v-if="tableHead.length>0" type="selection" width="55" align="center">
          </el-table-column>
          <template v-for="(item,index) in tableHead">
            <el-table-column :prop="item.column_name" :label="item.column_comment" :key="index">
              <template slot-scope="scope">
                <div v-if="isTranslate(item.column_name)">
                  {{scope.row[item.column_name] | getDicItemName(isTranslate(item.column_name))}}
                </div>
                <div v-else>
                  {{scope.row[item.column_name]}}
                </div>
              </template>
            </el-table-column>
          </template>
        </el-table>
      </div>
      <div class="df-cc ptb-20 userButton">
        <el-button type="primary" plain :style="{color:themeObj.color}" @click="closeComGroup">关闭</el-button>
        <el-button type="primary" @click="save">保存</el-button>
      </div>
    </div>
  </DtPopup>
</template>

<script>
import DtPopup from "@/components/layouts/DtPopup";
import SearchForm from "@/components/layouts/SearchForm";
import Pagination from "@/components/layouts/Pagination";
import { getDicItemList } from "@/config/tool.js";
import { fuzzyQuery } from "@/config/tool.js";

export default {
  name: "ComPopup",
  components: { DtPopup, SearchForm, Pagination },
  props: {
    showComGroup: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ""
    },
    tableHead: {
      type: Array,
      default: () => {
        return []
      }
    },
    tableData: {
      type: Array,
      default: () => {
        return []
      }
    },
    searchTemp: {
      type: Array,
      default: () => {
        return []
      }
    },
    selectList: {
      type: Array,
      default: () => {
        return []
      }
    },
    translateArr: {
      type: Array,
      default: () => {
        return []
      }
    },
    comGroupSearchVal: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      multipleSelection: [],
      list: [],
      comParam: {
        param: {
          name: ""
        }
      },

    };
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    }
  },
  watch: {
    showComGroup(val) {
      if (val) {
        this.list = this.tableData
        this.echo()
      } else {
        this.comParam.param.name = ""
      }
    }

  },
  methods: {
    closeComGroup() {
      this.$emit("closeDialog")
    },
    normalSearch(data) {
      this.comParam = data;
      this.list = fuzzyQuery(this.tableData, this.comParam.param.name, this.comGroupSearchVal)
      this.echo()
    },
    isTranslate(name) {
      let obj = this._.find(this.translateArr, o => { return o.key == name })
      if (obj) {
        return obj.dic
      }
      return false
    },
    normalResetQuery() {
      this.comParam = this.$options.data().comParam
      this.list = this.tableData
      this.echo()
    },
    save() {
      if (this.multipleSelection.length == 0) {
        this.$message({
          message: "请至少选择一条",
          type: "error"
        });
        return
      }
      this.$emit("select", this.multipleSelection)
      this.$emit("closeDialog")
    },
    handleSelectionChange(val) {
      this.multipleSelection = this._.map(val, o => {
        return {
          fid: o.fid,
          name: o[this.comGroupSearchVal]
        }
      })
    },

    echo() {
      if (this.selectList.length == 0) { return }
      this.$nextTick(() => {
        this._.each(this.list, item => {
          if (this._.find(this.selectList, ["fid", item.fid])) {
            this.$refs.multipleTable.toggleRowSelection(item, true)
          }
        })
      })
      console.log(1)
    }
  }
};
</script>

<style lang="less" >
.no-padding {
  .dt-popup {
    .el-dialog {
      padding: 0;
    }
  }
}

.com-popup-table {
  &.dt-table.el-table tr th .cell {
    padding-left: 10px;
  }
}

.comSearch {
  &.form-block {
    padding-left: 20px;
    padding-top: 0;
  }
}

.roleGroupName {
  height: 38px;
  line-height: 38px;
  padding-left: 30px;
}

.pagination-wrap {
  padding-left: 16px;
  padding-right: 8px;
}

.userButton {
  button {
    width: 180px;
  }
}
</style>
