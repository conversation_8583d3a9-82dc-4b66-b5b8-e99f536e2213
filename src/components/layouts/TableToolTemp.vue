<template>
  <div>
    <div class="tab-tool" :class="{ 'fix-height': toolList.length == 0 }">
      <div class="sub-title" v-if="level === 'l2'">
        <i :style="{ background: themeObj.color }"></i>
        {{ toolListProps.toolTitle }}
      </div>
      <div class="tab-title dt-fz18 dt-cursor-pointer" v-else>
        <i :style="{ background: themeObj.color }"></i>
        {{ toolListProps.toolTitle }}
      </div>
      <div class="tab-wrap" v-for="(item, index) in toolList" :key="index">
        <el-button type="text" :icon="item.icon" @click="handleTool(item)">
          <span v-if="isIncludeDownload(item.name)">{{ item.name }}</span>
          <span v-else>
            <a
              :href="item.downloadURL"
              :download="Date.parse(new Date())"
              class="download-btn"
              :style="{ color: themeObj.color }"
              >{{ item.name }}</a
            >
          </span>
        </el-button>
      </div>
    </div>
    <div class="tool-content">
      <slot name="default"> </slot>
    </div>
  </div>
</template>

<script>
import { hasRights } from "@/config/tool";
export default {
  name: "",
  props: {
    level: {
      type: String,
      default: "l1",
    },
    toolListProps: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      mapToolIcon: {
        新增: { icon: "iconfont icondt8" },
        导出: { icon: "iconfont icondt10" },
        下载: { icon: "iconfont icondt10" },
        导入: { icon: "iconfont icondt21" },
        清空: { icon: "iconfont icondt24" },
        删除: { icon: "iconfont icondt24" },
        清除: { icon: "iconfont icondt24" },
        清理: { icon: "iconfont icondt24" },
        关联租户: { icon: "iconfont icondt20" },
        批量分配: { icon: "iconfont icondt20" },
        保存: { icon: "iconfont icondt-1381" },
        查询: { icon: "iconfont icondt8" },
        流程查看: { icon: "iconfont icondt8" },
        显示: { icon: "iconfont icondt8" },
        标签管理: { icon: "iconfont icondt20" },
        新增字段: { icon: "iconfont icondt-99" },
        复制字段: { icon: "iconfont icondt19" },
        页面预览: { icon: "iconfont icondt-72" },
        同步公司资料: { icon: "iconfont icondt-1381" },
        下架: { icon: "iconfont icondt21" },
        上架: { icon: "iconfont icondt10" },
        返回: { icon: "iconfont icondt12" },
        同步: { icon: "iconfont icondt-1381" },
      },
    };
  },
  computed: {
    toolList() {
      let toolListProps = this._.cloneDeep(this.toolListProps);
      this._.each(toolListProps.toolList, (item, key) => {
        this._.each(this.mapToolIcon, (el, p) => {
          if (item.name.indexOf(p) > -1) {
            item.icon = el.icon;
            return false;
          }
        });
      });

      toolListProps.toolList = this._.filter(toolListProps.toolList, (item) => {
        return hasRights(item.btnCode);
      });
      return toolListProps.toolList;
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
  },
  watch: {},
  created() {},
  methods: {
    isIncludeDownload(name) {
      return name.indexOf("下载") === -1;
    },
    handleTool(item) {
      if (item.action) item.action();
      console.log(item)
      this.$emit("handleTool", item);
    },
  },
};
</script>

<style lang="less">
.tab-tool {
  background-color: #fff; // padding-left: 10px;
  // padding-top: 20px;
  .tab-title {
    display: inline-block;
    font-weight: 600;
    margin-right: 10px;
    font-family: PingFang SC;
    i {
      display: inline-block;
      width: 4px;
      height: 21px;
      margin-right: 10px;
      position: relative;
      top: 3px;
    }
  }

  .tab-wrap {
    display: inline-block;
    margin-right: 15px;
    .el-button--medium {
      padding-top: 12px;
      padding-bottom: 12px;
    }
  }
  .iconfont {
    position: relative;
    top: 2px;
    font-size: 22px;
  }
  .icondt12 {
    top: 3px !important;
  }
  a {
    text-decoration: none;
  }
  .sub-title {
    font-size: 15px;
    font-weight: bold;
    display: inline-block;
    margin-right: 10px;
    i {
      display: inline-block;
      width: 8px;
      height: 8px;
      margin-right: 10px;
      border-radius: 50%;
    }
  }
}
.tool-content {
  width: 100%;
  box-sizing: border-box;
  padding: 0 20px;
}
.fix-height {
  padding: 0;
  line-height: 48px;
}
</style>
