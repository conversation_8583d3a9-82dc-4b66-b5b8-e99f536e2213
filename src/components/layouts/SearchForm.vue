<template>
  <div class="form-block" :class="{expand:foldType}">
    <el-container class="wh100 dt-menusList-wrap">
      <div>
        <el-form ref="form" :model="searchForm" :label-width="labelWidth" :inline="true" :label-position="position" v-if="hasParam">
          <el-form-item v-for="(item, index) in searchFormTemp" :key="index"  :label="item.label" v-show="item.fixedShow || item.tempShow || !foldType">
            <el-input v-if="item.type == 'input'" v-model="searchForm.param[item.name]" :placeholder="item.placeholder || '请输入'"></el-input>
            <el-select v-if="item.type == 'select'" v-model="searchForm.param[item.name]" :multiple="item.multiple" collapse-tags  filterable clearable @change="handleChange(item.name, ...arguments)" :disabled="item.disabled" :placeholder="item.placeholder || '请选择'">
              <el-option v-for="(el, idx) in item.list" :key="idx" :label="el.dicItemName" :value="el.dicItemCode"></el-option>
            </el-select>
            <el-cascader :props="item.props" class="dt-cascader" ref="dt-cascader" v-if="item.type == 'cascader'" v-model="searchForm.param[item.name]" @change="handleCascader(item.name, ...arguments)" filterable clearable></el-cascader>
            <el-date-picker v-if="item.type == 'datePicker'" v-model="searchForm.param[item.name]" format="yyyy-MM-dd" value-format="yyyy-MM-dd" clearable type="date" :placeholder="item.placeholder || '请选择'" class="dt-picker"></el-date-picker>
            <doubleDate v-if="item.type == 'doubleDate'" :elType="item.elType" :options="item.options" :name="item.name ? item.name : ''" @editParams="getDateTime"></doubleDate>
          </el-form-item>
          <el-form-item v-show="!foldType">
            <el-button type="primary" :style="{'background-color':themeObj.color,'border-color':themeObj.color}" @click="normalSearch()">
              搜索
            </el-button>
            <el-button type="primary" plain :style="{'color':themeObj.color,'border-color':themeObj.color}" @click="normalResetQuery()">
              重置
            </el-button> 
          </el-form-item>
        </el-form>
        <!-- 兼容处理 -->
        <el-form ref="form" :model="searchForm" :label-width="labelWidth" :inline="true" :label-position="position" v-else>
          <el-form-item v-for="(item, index) in searchFormTemp" :key="index" :label="item.label" v-show="item.fixedShow || item.tempShow || !foldType">
            <el-input v-if="item.type == 'input'"  :type="item.inputType" v-model="searchForm[item.name]" :placeholder="item.placeholder || '请输入'"></el-input>
            <el-select v-if="item.type == 'select'" :multiple='item.multiple' v-model="searchForm[item.name]" collapse-tags filterable clearable @change="handleChange(item.name, ...arguments)" :disabled="item.disabled" :placeholder="item.placeholder || '请选择'">
              <el-option v-for="(el, idx) in item.list" :key="idx" :label="el.dicItemName||el[item.dicItemName]" :value="el.dicItemCode||el[item.dicItemCode]"></el-option>
            </el-select>
            <el-cascader :props="item.props" class="dt-cascader" ref="dt-cascader" v-if="item.type == 'cascader'" v-model="searchForm[item.name]" @change="handleCascader(item.name, ...arguments)" filterable clearable></el-cascader>
            <el-date-picker v-if="item.type == 'datePicker'" v-model="searchForm[item.name]" format="yyyy-MM-dd" value-format="yyyy-MM-dd" clearable type="date" :placeholder="item.placeholder || '请选择'" class="dt-picker"></el-date-picker>
            <doubleDate v-if="item.type == 'doubleDate'" :elType="item.elType" :options="item.options" :name="item.name ? item.name : ''" @editParams="getDateTime" ref="doubleDate"></doubleDate>
            <div v-if="item.type == 'block'">{{item.value}}</div>
          </el-form-item>
          <el-form-item v-show="!foldType">
            <el-button type="primary" :style="{'background-color':themeObj.color,'border-color':themeObj.color}" @click="normalSearch()" v-if="isSearch">
              搜索
            </el-button>
            <el-button type="primary" plain :style="{'color':themeObj.color,'border-color':themeObj.color}" @click="normalResetQuery()" v-if="isReset">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <el-aside :width="sideWidth" class="dt-menusList-tree" v-show="foldType">
        <div style="align-items: center;justify-content: center;display: flex;height: 95%;margin-top:0%;border-left: 1px dotted #ccc;">
          <el-button type="primary" :style="{'background-color':themeObj.color,'border-color':themeObj.color}" @click="normalSearch()">
            搜索
          </el-button>
          <el-button type="primary" plain :style="{'color':themeObj.color,'border-color':themeObj.color}" @click="normalResetQuery()">
            重置
          </el-button>
          <el-button type="primary" plain :style="{'color':themeObj.color,'border-color':themeObj.color}" @click="changeExpand()">
            {{expand?"精确搜索":"精简搜索"}}
          </el-button>
        </div>
      </el-aside>
    </el-container>
  </div>
</template>
<script>
import doubleDate from "@/components/doubleDate";
export default {
  name: "",
  components: {
    doubleDate
  },
  props: {
    searchForm: {
      type: Object,
      default: function() {
        return {};
      }
    },
    searchFormTemp: {
      type: Array,
      default: function() {
        return [];
      }
    },
    labelWidth: {
      type: String,
      default: "auto"
    },
    foldType: {
      type: Boolean,
      default: false
    },
    isSearch:{
      type:Boolean,
      default:true,
    },
    isReset:{
      type:Boolean,
      default:true,
    },
    position:{
      type:String,
      default:'left'
    }

  
  },
  data() {
    return {
      sideWidth: "300px",
      expand: true,
      hasParam:false //判断当前传入的是两层对象
    };
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
  },
  created() {
    console.log(this.searchForm.hasOwnProperty('param'))
    this.hasParam = this.searchForm.hasOwnProperty('param')
  },
  mounted() {

  },
  methods: {
    normalSearch() {
      this.handleSearchForm();
      this.$emit("normalSearch", this.searchForm);
    },
    normalResetQuery() {
      this.$refs.form.resetFields();
      if(!this.hasParam&&this.searchForm.doubleDate){
        this.$refs.doubleDate[0].value = '' // 重置时间选择器
      }
      this.$emit("normalResetQuery");
    },
    getDateTime(arr, name) {
      this.searchFormTemp.forEach(item => {
        if(!this.hasParam && item.name == name){
            if(!this._.isArray(arr))  arr =[]
            this.searchForm[item.options[0].name] =  arr[0] ? arr[0] : '';
            this.searchForm[item.options[1].name] =  arr[1] ? arr[1] : '';
            return 
        }
       if (item.type == "doubleDate" && item.name == name) {
         if(!this._.isArray(arr))  arr =[]
          item.options[0].value = arr[0] ? arr[0] : '';
          item.options[1].value = arr[1] ? arr[1] : '';
        }
      });
    },
    handleSearchForm() {
      let tempObj = {};
      for (let item of this.searchFormTemp) {
        if (item.type == "doubleDate") {
          for (let li of item.options) {
            tempObj[li.name] = li.value;
          }
          break
        }
      }
      if(this.hasParam){
        Object.assign(this.searchForm.param,tempObj)
      }
    },
    changeExpand() {
      this.searchFormTemp.forEach((item) => {
        item.tempShow = this.expand
      })
      this.expand = !this.expand
    },
    handleChange(name, val) {
      this.$emit("handleChange", name, true, val);
    },
    handleCascader(name, val) {
      const nodesObj = this.$refs["dt-cascader"][0].getCheckedNodes()[0];
      this.$refs['dt-cascader'][0].dropDownVisible = false
      this.$emit("handleCascader", name, val,nodesObj);
    }
  }
};
</script>
<style lang="less">
.form-block {
  background: #fff;
  padding-top: 10px;
  padding-left: 11px;

  .el-input--medium {
    width: 215px !important;
  }

  .el-input__inner,
  .el-button--medium {
    border-radius: 6px;
  }

  .el-form-item__label {
    color: #333;
  }

  .el-form-item {
    margin-bottom: 13px;
  }


  .dt-cascader {
    .el-input--medium {
      width: 600px !important;
    }
  }
  .ml0{
    .el-form-item__label-wrap{
      margin-left: 0 !important;
    }
  }
 
}
</style>