<template>
  <div class="secondary-page-header">
    <!-- 页面头部区域 -->
    <div class="header-section">
      <div class="header-top">
        <div class="title-section">
          <div class="page-title">
            <div class="title-icon" v-if="icon">
              <i :class="icon"></i>
            </div>
            <div class="title-text">
              <h2 class="main-title">{{ title }}</h2>
              <p class="page-subtitle" v-if="subtitle">{{ subtitle }}</p>
            </div>
          </div>
        </div>
      </div>

      <BreadcrumbNav :items="breadcrumbItems" v-if="breadcrumbItems && breadcrumbItems.length > 0" />
    </div>
  </div>
</template>

<script>
import BreadcrumbNav from '@/components/common/BreadcrumbNav.vue';

export default {
  name: "SecondaryPageHeader",
  components: { BreadcrumbNav },
  props: {
    // 页面标题
    title: {
      type: String,
      required: true
    },
    // 页面副标题
    subtitle: {
      type: String,
      default: ""
    },
    // 标题图标
    icon: {
      type: String,
      default: ""
    },
    // 是否显示操作区域
    showActions: {
      type: Boolean,
      default: true
    },
    // 是否显示返回按钮
    showBackButton: {
      type: Boolean,
      default: true
    },
    // 返回按钮文字
    backButtonText: {
      type: String,
      default: "返回上级"
    },
    // 返回路由
    backRoute: {
      type: [String, Object],
      default: null
    },
    // 是否显示面包屑
    showBreadcrumb: {
      type: Boolean,
      default: true
    },
    // 面包屑数据
    breadcrumbItems: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    // 处理返回操作
    handleBack() {
      this.$emit('back');
      
      if (this.backRoute) {
        if (typeof this.backRoute === 'string') {
          this.$router.push({ name: this.backRoute });
        } else {
          this.$router.push(this.backRoute);
        }
      } else {
        // 默认返回上一页
        this.$router.go(-1);
      }
    },
    
    // 处理面包屑点击
    handleBreadcrumbClick(item) {
      this.$emit('breadcrumb-click', item);
      
      if (item.to) {
        if (typeof item.to === 'string') {
          this.$router.push({ name: item.to });
        } else {
          this.$router.push(item.to);
        }
      }
    }
  }
};
</script>

<style lang="less" scoped>
@import "~@/views/indexManage/components/shared-styles.less";
.secondary-page-header {
  background-color: #fbf6ee;
  // 页面头部区域
  .header-section {
    .header-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 28px;
      border-bottom: 1px solid @border-light;

      .title-section {
        .page-title {
          margin: 0;
          display: flex;
          align-items: center;
          gap: 12px;

          .title-icon {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, @primary-color 0%, @primary-hover 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px @primary-shadow;

            i {
              color: white;
              font-size: 18px;
            }
          }

          .title-text {
            .main-title {
              font-size: 20px;
              font-weight: 600;
              color: @text-primary;
              margin: 0 0 2px 0;
              display: block;
            }

            .page-subtitle {
              margin: 0;
              color: @text-secondary;
              font-size: 12px;
              font-weight: 400;
            }
          }
        }
      }
    }

    // 面包屑区域样式已交由BreadcrumbNav组件维护
  }
}
</style> 