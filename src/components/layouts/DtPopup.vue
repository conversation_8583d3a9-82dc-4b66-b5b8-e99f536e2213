<template>
  <div class="">
    <el-dialog
      :title="title"
      :visible.sync="show"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="isShowClose"
      :width="width"
      :center="isCenter"
      :fullscreen="isFullscreen"
      class="dt-popup"
      @close="closePopup"
      :class="{
        isCenter: center
      }"
    >
      <slot></slot>
      <span slot="footer" class="dialog-footer" v-if="footer">
        <el-button
          v-if="isShowClose"
          class="dt-btn"
          @click="show = false"
          type="primary"
          plain
          :style="{ color: $store.state.layoutStore.themeObj.color }"
          >取 消</el-button
        >
        <el-button type="primary" class="dt-btn" @click="confirm"
          >确 认</el-button
        >
      </span>
      <span
        v-if="isShowClose"
        class="iconfont icondt25"
        @click="show = false"
        style="cursor: pointer;"
      ></span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: "系统提示"
    },
    isShow: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: "670px"
    },
    footer: {
      type: Boolean,
      default: true
    },
    center: {
      type: Boolean,
      default: false
    },
    isCenter: {
      type: Boolean,
      default: true
    },
    isShowClose: {
      type: Boolean,
      default: true
    },
    isFullscreen: {
      type: Boolean,
      default: false
    },
    confirmBeforeClose: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: this.isShow
    };
  },
  watch: {
    isShow(newVal, oldVal) {
      this.show = newVal;
    }
  },
  methods: {
    closePopup() {
      this.$emit("close");
    },
    confirm() {
      if (!this.confirmBeforeClose) this.show = false;
      this.$emit("confirm");
    }
  }
};
</script>

<style lang="less">
.dt-popup {
  .el-dialog {
    min-width: 300px;
    min-height: 150px;
    border-radius: 10px;
    position: absolute;
    margin: 0 !important;
    width: auto;
    padding: 0 40px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    .el-dialog__body {
      padding: 0;
    }
    .el-dialog__header {
      padding-bottom: 20px;
    }
    .el-dialog__footer {
      padding-bottom: 60px;
      padding-top: 20px;
      .el-button {
        // width: 140px;
      }
    }
  }
  &.isCenter {
    .el-dialog__body {
      display: flex;
      justify-content: center;
    }
  }
  &.small {
    .el-dialog {
      width: 700px;
    }
  }
  &.mini {
    .el-dialog {
      width: 400px;
    }
  }
  &.large {
    .el-dialog {
      width: 1000px;
    }
  }
  .icondt25 {
    position: absolute;
    font-size: 65px !important;
    top: -14px;
    right: -11px;
    color: #b8b8b8;
  }
  .dialog-footer {
    .el-button {
      // font-weight: 600;
    }
  }
  .el-dialog__title {
    font-weight: bold;
  }
}
</style>
