<template>
  <div class="breadcrumb-nav">
    <template v-for="(item, index) in items">
      <el-button
        v-if="item.to"
        :key="'btn-' + index"
        class="breadcrumb-btn clickable-btn"
        size="mini"
        @click="handleClick(item)"
      >
        <i v-if="item.icon" :class="item.icon"></i>
        {{ item.text }}
      </el-button>
      <el-button
        v-else
        :key="'current-' + index"
        class="breadcrumb-btn current-btn"
        size="mini"
        disabled
      >
        <i v-if="item.icon" :class="item.icon"></i>
        {{ item.text }}
      </el-button>
      <i
        v-if="index < items.length - 1"
        :key="'separator-' + index"
        class="el-icon-arrow-right breadcrumb-separator"
      ></i>
    </template>
  </div>
</template>

<script>
export default {
  name: 'BreadcrumbNav',
  props: {
    items: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    handleClick(item) {
      if (item.to) {
        if (typeof item.to === 'string') {
          this.$router.push({ name: item.to });
        } else {
          this.$router.push(item.to);
        }
      }
    }
  }
};
</script>

<style lang="less" scoped>
@import "~@/views/indexManage/components/shared-styles.less";
.breadcrumb-nav {
  background-color: @bg-light;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  padding: 12px 28px;
  .breadcrumb-btn {
    height: 28px;
    padding: 0 12px;
    border-radius: 6px;
    font-weight: 500;
    font-size: 13px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    i {
      font-size: 12px;
    }
    &.clickable-btn {
      background: #fff;
      border: 1px solid @border-light;
      color: @primary-color;
      &:hover {
        background: @primary-color !important;
        border-color: @primary-color !important;
        color: #fff !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px @primary-shadow;
      }
      &:active {
        transform: translateY(0);
      }
    }
    &.current-btn {
      background: linear-gradient(135deg, @primary-color 0%, @primary-hover 100%) !important;
      border: 1px solid @primary-color !important;
      color: #fff !important;
      cursor: default;
      box-shadow: 0 2px 8px @primary-shadow;
      &:hover {
        background: linear-gradient(135deg, @primary-color 0%, @primary-hover 100%) !important;
        border-color: @primary-color !important;
        color: #fff !important;
        transform: none !important;
      }
    }
  }
  .breadcrumb-separator {
    color: #c0c4cc;
    font-size: 12px;
    margin: 0 4px;
    display: flex;
    align-items: center;
    opacity: 0.6;
  }
}
</style> 