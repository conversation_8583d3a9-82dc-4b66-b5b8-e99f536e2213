/* eslint-disable no-empty */
/*
 * @Author: shizy
 * @Date: 2023-9-19
 * @LastEditors: shizy
 * @LastEditTime: 2023-9-19 
 * @Description: 
 */

const sourceTemp = {
  data() {
    return {
      searchFormTemp: [{
          label: "字段编码",
          name: "fieldId",
          type: "input",
        },
        {
          label: "字段名称",
          name: "name",
          type: "input",
        },
        {
          label: "数据类型",
          name: "dataType",
          type: "select",
          list: [],
        },
        {
          label: "是否为字典",
          name: "isDic",
          type: "select",
          list: [],
        },
        {
          label: "是否参与计算",
          name: "isCalc",
          type: "select",
          list: [],
        },
        {
          label: "属性类型",
          name: "propertyType",
          type: "select",
          list: [],
        },
      ],
      tableData: [],
      initParam: {
        param: {
          fieldId: "",
          name: "",
          dataType: "",
          isDic: "",
          isCalc: "",
          propertyType: "",
        },
        pageSize: 10,
        pageNum: 1,
      },
      total: 0,
      showPopup: false,
      fbId: "",
    }
  },
  methods: {

  },
  created() {

  },
  computed: {

  }
}
export default sourceTemp