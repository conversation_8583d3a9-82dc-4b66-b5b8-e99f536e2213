import http from "@/utils/httpService";
import domainObj from "@/utils/globalParam";
// import gpruleCfg from "./mock/sc0002.json";
export const agreementSelectProduct = data =>
  http.Axios.post(
    domainObj.kbcUrl +
      "/gateway/kbc-cbs-ism/web/gpagreementproduct/agreementSelectProduct",
    data
  );

// 根据产品编码批量查询产品信息
export const getProductsById = async listUnifiedProductIds => {
  const res = await http.Axios.post(
    domainObj.kbcUrl + "/gateway/kbc-cbs-ism/web/gpproduct/propertyPage",
    {
      pageNum: 1,
      pageSize: 10,
      param: { listUnifiedProductIds }
    }
  );
  const retData = {};
  listUnifiedProductIds.forEach(e => (retData[e] = e));
  if (res && res.list && Array.isArray(res.list)) {
    res.list.forEach(e => (retData[e.unifiedProductId] = {
      gpProductId:e.id,
      productName:e.productName
    }));
  }
  return retData;
};

export const getSourceData = async sourceId => {
  const ret = await http.Axios.post(
    domainObj.baseUrl + "/web/sourcecalcfield/getDetailList",
    { sourceId }
  );
  if (ret && Array.isArray(ret)) return ret;
  return [];
};

const cfgProductIds2Name = async data => {
  const productIds = [];
  if (data.sourceFilterList) {
    data.sourceFilterList.forEach((e, idx) => {
      if (
        (e.fieldId === "mainProductId" || e.fieldId === "kbcProductId") &&
        e.matchValue
      )
        productIds.push(
          ...e.matchValue.split(",").map((m, idx1) => {
            return {
              key: data.sourceFilterList[idx],
              index: idx1,
              id: m
            };
          })
        );
    });
  }
  if (data.variableList) {
    data.variableList.forEach((e, idx) => {
      if (e.valueList) {
        e.valueList.forEach((e1, idx1) => {
          if (e1.conditionList) {
            e1.conditionList.forEach((e2, idx2) => {
              if (
                (e2.fieldId === "mainProductId" ||
                  e2.fieldId === "kbcProductId") &&
                e2.matchValue
              )
                productIds.push(
                  ...e2.matchValue.split(",").map((m, idx3) => {
                    return {
                      key:
                        data.variableList[idx].valueList[idx1].conditionList[
                          idx2
                        ],
                      index: idx3,
                      id: m
                    };
                  })
                );
            });
          }
          if (e1.filterList) {
            e1.filterList.forEach((e2, idx2) => {
              if (
                (e2.fieldId === "mainProductId" ||
                  e2.fieldId === "kbcProductId") &&
                e2.matchValue
              )
                productIds.push(
                  ...e2.matchValue.split(",").map((m, idx3) => {
                    return {
                      key:
                        data.variableList[idx].valueList[idx1].filterList[idx2],
                      index: idx3,
                      id: m
                    };
                  })
                );
            });
          }
        });
      }
    });
  }
  if (data.decisionList) {
    data.decisionList.forEach((e, idx) => {
      if (e.valueList) {
        e.valueList.forEach((e1, idx1) => {
          if (e1.conditionList) {
            e1.conditionList.forEach((e2, idx2) => {
              if (
                (e2.fieldId === "mainProductId" ||
                  e2.fieldId === "kbcProductId") &&
                e2.matchValue
              )
                productIds.push(
                  ...e2.matchValue.split(",").map((m, idx3) => {
                    return {
                      key:
                        data.decisionList[idx].valueList[idx1].conditionList[
                          idx2
                        ],
                      index: idx3,
                      id: m
                    };
                  })
                );
            });
          }
          if (e1.filterList) {
            e1.filterList.forEach((e2, idx2) => {
              if (
                (e2.fieldId === "mainProductId" ||
                  e2.fieldId === "kbcProductId") &&
                e2.matchValue
              )
                productIds.push(
                  ...e2.matchValue.split(",").map((m, idx3) => {
                    return {
                      key:
                        data.decisionList[idx].valueList[idx1].filterList[idx2],
                      index: idx3,
                      id: m
                    };
                  })
                );
            });
          }
        });
      }
    });
  }
  if (data.resultFilterList) {
    data.resultFilterList.forEach((e, idx) => {
      if (
        (e.fieldId === "mainProductId" || e.fieldId === "kbcProductId") &&
        e.matchValue
      )
        productIds.push(
          ...e.matchValue.split(",").map((m, idx1) => {
            return {
              key: data.resultFilterList[idx],
              index: idx1,
              id: m
            };
          })
        );
    });
  }
  const listUnifiedProductIds = [...new Set(productIds.map(e => e.id))];
  if (listUnifiedProductIds.length > 0) {
    const ret = await getProductsById(listUnifiedProductIds);

    productIds.forEach(e => {
      if (!e.key.matchValueProduct) {
        e.key.matchValueProduct = [];
      }
      e.key.matchValueProduct.push({
        gpProductId: ret[e.id].gpProductId,
        unifiedProductId: e.id,
        productName: ret[e.id].productName
      });
    });
  }
};
export const getConfig = async strategyId => {
  const resData = {
    decisionList: [],
    sourceFilterList: [],
    variableList: [],
    resultFilterList: [],
    strategyType: ""
  };
  if (!strategyId) return resData;
  const ret = await http.Axios.post(
    domainObj.baseUrl + "/web/strategycalc/getConfig",
    { strategyId }
  );
  // test data
  // const ret = gpruleCfg.datas;

  if (ret) {
    await cfgProductIds2Name(ret);
    resData.decisionList = ret.decisionList || [];
    resData.sourceFilterList = ret.sourceFilterList || [];
    resData.variableList = ret.variableList || [];
    resData.resultFilterList = ret.resultFilterList || [];
    resData.strategyType = ret.strategyType || "1";
  }
  return resData;
};

export const saveConfig = async data => {
  const ret = await http.Axios.post(
    domainObj.baseUrl + "/web/strategycalc/saveConfig",
    data
  );
  if (ret) return true;
  return false;
};
