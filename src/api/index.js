import http from "@/utils/httpService";
import domainObj from "@/utils/globalParam";


export const getWebUserInfo = data =>
  http.Axios.get(domainObj.kbcUrl + "gateway/kbc-pmc/common/info/user", data, {
    hideLoading: true
  });


export const getTenantUsers = data =>
  http.Axios.post(domainObj.publicUrl + "/api/bsc/tenant/getTenantUsers", data, {
    hideLoading: true
  });

//根据字典编码获取数据字典项
export const getDicItems = (data) =>
  http.Axios.post(domainObj.publicUrl + "/api/dic/item/getDicItems", data, {
    hideLoading: true
  });



//获取通用条件列表
export const getConditionList = (data) =>
  http.Axios.post(domainObj.publicUrl + "/api/dic/item/getConditionList", data, {
    hideLoading: true
  });