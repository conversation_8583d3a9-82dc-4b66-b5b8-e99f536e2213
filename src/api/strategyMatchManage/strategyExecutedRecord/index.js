import http from "@/utils/httpService";
import domainObj from "@/utils/globalParam";


//执行列表
export const getStrategyExecutedRecordList = data =>
http.Axios.post(domainObj.baseUrl + "/web/strategyexecutedrecord/page", data);




//查询分配/匹配源数据
export const getSourceDataPageList = data =>
http.Axios.post(domainObj.baseUrl + "/web/strategyexecutedrecord/sourceDataPage", data);


//查询最终匹配数据
export const getMatchResultDataPageList = data =>
http.Axios.post(domainObj.baseUrl + "/web/strategyexecutedrecord/matchResultDataPage", data);


//执行匹配策略
export const executeStrategyRecord = data =>
http.Axios.post(domainObj.baseUrl + "/web/strategyexecutedrecord/execute", data);




//查询分配数据源列表
export const getSourceAssignFieldList = data =>
http.Axios.post(domainObj.baseUrl + "/web/sourceassignfield/list", data,{
  hideLoading:true
});



//查询匹配数据源列表
export const getSourceMatchFieldList = data =>
http.Axios.post(domainObj.baseUrl + "/web/sourcematchfield/list", data,{
  hideLoading:true
});


//查询匹配数据源列表
export const getProcessAnalysisList = data =>
http.Axios.post(domainObj.baseUrl + "/web/strategyexecutedrecord/getProcessAnalysis", data);






