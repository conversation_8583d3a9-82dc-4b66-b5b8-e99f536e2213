import http from "@/utils/httpService";
import domainObj from "@/utils/globalParam";


//分页查询
export const getStrategyMatchIndicator = data =>
http.Axios.post(domainObj.baseUrl + "/web/strategymatchindicator/page", data);



//删除指标
export const deleteStrategyMatchIndicator = data =>
http.Axios.post(domainObj.baseUrl + "/web/strategymatchindicator/delete", data);



//上移
export const strategyMatchIndicatorUp = data =>
http.Axios.post(domainObj.baseUrl + "/web/strategymatchindicator/up", data);



//下移
export const strategyMatchIndicatorDown = data =>
http.Axios.post(domainObj.baseUrl + "/web/strategymatchindicator/down", data);



//编辑指标
export const updateStrategyMatchIndicator = data =>
http.Axios.post(domainObj.baseUrl + "/web/strategymatchindicator/update", data);



//新增指标
export const addStrategyMatchIndicator = data =>
http.Axios.post(domainObj.baseUrl + "/web/strategymatchindicator/add", data);



//获取策略数据源模板
export const getSourceTemplate = data =>
http.Axios.post(domainObj.baseUrl + "/web/strategymatchindicator/getSourceTemplate", data,{
  hideLoading:true
});


//获取指标配置
export const getConfig = data =>
http.Axios.post(domainObj.baseUrl + "/web/strategymatchindicator/getConfig", data,{hideMsg:true,hideLoading:true});


//保存指标配置
export const saveConfig = data =>
http.Axios.post(domainObj.baseUrl + "/web/strategymatchindicator/saveConfig", data);





