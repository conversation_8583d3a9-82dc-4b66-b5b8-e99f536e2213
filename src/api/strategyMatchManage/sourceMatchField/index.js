import http from "@/utils/httpService";
import domainObj from "@/utils/globalParam";


//分页查询 -分配
export const getSourceAssignField = data =>
http.Axios.post(domainObj.baseUrl + "/web/sourceassignfield/page", data);



//查询字段详情-分配
export const getSourceAssignFieldDetail = data =>
http.Axios.post(domainObj.baseUrl + "/web/sourceassignfield/detail", data);



//分页查询-匹配
export const getSourceMatchField = data =>
http.Axios.post(domainObj.baseUrl + "/web/sourcematchfield/page", data);



//查询字段详情-匹配
export const getSourceMatchFieldDetail = data =>
http.Axios.post(domainObj.baseUrl + "/web/sourcematchfield/detail", data);



//获取优先权配置列表
export const getPriorityConfigList = data =>
http.Axios.post(domainObj.baseUrl + "/web/strategymatch/getPriorityConfigList", data);





