import http from "@/utils/httpService";
import domainObj from "@/utils/globalParam";


//分页查询
export const getStrategymatch = data =>
http.Axios.post(domainObj.baseUrl + "/web/strategymatch/page", data);


//新增策略
export const addStrategymatch = data =>
http.Axios.post(domainObj.baseUrl + "/web/strategymatch/add", data);

//编辑策略
export const updateStrategymatch = data =>
http.Axios.post(domainObj.baseUrl + "/web/strategymatch/update", data);


//复制策略
export const copyStrategymatch = data =>
http.Axios.post(domainObj.baseUrl + "/web/strategymatch/copy", data);


//删除策略
export const deleteStrategymatch = data =>
http.Axios.post(domainObj.baseUrl + "/web/strategymatch/delete", data);



//脑图分析
export const getProcessMind = data =>
http.Axios.post(domainObj.baseUrl + "/web/strategyexecutedrecord/getProcessMind", data);

// 获取指标节点字段
export const getIndicatorNodeFields = data =>
http.Axios.post(domainObj.baseUrl + '/web/algoStrategyBatchNode/getIndicatorNodeFields',data);


// 获取指标节点数据
export const getIndicatorNodeData = data =>
http.Axios.post(domainObj.baseUrl + '/web/algoStrategyBatchNode/getIndicatorNodeData', data, {
  timeout: 30000 // 设置30秒超时
});





