import http from "@/utils/httpService";
import domainObj from "@/utils/globalParam";

//数据源分页查询
export const algoSourcePage = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoSource/list", data);

//数据源新增加
export const algoSourceSave = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoSource/save", data);

//数据源删除
export const algoSourceDel = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoSource/delete", data);

//数据源参数列表
export const algoSourceParamList = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoSourceParam/list", data);

//数据源字段列表
export const algoSourceFieldList = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoSourceField/list", data);

//数据源字段新增
export const algoSourceFieldAdd = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoSourceField/add", data);

//数据源字段修改
export const algoSourceFieldUpdate = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoSourceField/update", data);

//数据源字段删除
export const algoSourceFieldDel = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoSourceField/delete", data);

//数据源-参数新增
export const algoSourceParamAdd = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoSourceParam/add", data);

//数据源-参数修改
export const algoSourceParamUpdate = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoSourceParam/update", data);

//数据源-详情
export const algoSourceById = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoSource/getById", data);

//数据源-参数删除
export const algoSourceParamDel = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoSourceParam/delete", data);

//指标列表
export const algoindicatorPage = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoindicator/page", data);

//指标删除
export const algoindicatorDel = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoindicator/delete", data);
//指标新增
export const algoindicatorAdd = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoindicator/save", data);

//指标编辑
export const algoindicatorUpdate = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoindicator/save", data);

//指标左侧node
export const algoindicatorGetNodeList = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoindicator/getNodeList", data);

//指标左侧node
export const algoindicatorSave = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoindicator/save", data);

//指标查询明细
export const algoindicatorGetById = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoindicator/getById", data);

//指标查询JSON配置
export const algoindicatorGetJSONConfig = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoindicator/getJSONConfig", data);

//  策略列表
export const algostrategyPage = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoStrategy/page", data);

//  策略删除
export const algostrategyDel = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoStrategy/delete", data);

//   批次列表-明细
export const algostrategybatchnodePage = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoStrategyBatchNode/list", data);
//   批次列表-明细
export const algoStrategyBatchNodeRe = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoStrategyBatchNode/retry", data);

//  批次列表
export const algostrategybatchPage = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoStrategyBatch/page", data);

//   数据明细列表
export const algostrategybatchnodeDetailPage = data =>
  http.Axios.post(
    domainObj.baseUrl + "/web/algoStrategyBatchNode/detail/page",
    data
  );
//   数据明细列表表头查询
export const algoSourceFieldData = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoSource/data/fields", data);
//  参数节点查询
export const algoSourceFieldParams = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoSourceField/params", data);
// 参数字段查询
export const algoSourceField = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoSourceField/fields", data, {
    hideLoading: true
  });
//  新增策略
export const algoStrategyAdd = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoStrategy/add", data);
//  编辑策略
export const algoStrategyUpdate = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoStrategy/update", data);
// 策略详情
export const algoStrategyFind = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoStrategy/find", data);
// 指标配置
export const algoindicatorConfig = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoindicator/config", data);
// // 策略配置查询
// export const algoStrategyConfigFind = data =>
//   http.Axios.post(domainObj.baseUrl + "/web/algoStrategy/config/find", data);
// 策略配置
// export const algoStrategyConfig = data =>
//   http.Axios.post(domainObj.baseUrl + "/web/algoStrategy/config", data);
// 策略配置导出
export const algoSourceExport = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoSource/data/export", data);
// 策略配置
export const algoStrategyConfig = data =>
  http.Axios.post(
    domainObj.baseUrl + "/web/algoStrategy/node/flow/config/save",
    data
  );
// 策略配置查询
export const algoStrategyConfigFind = data =>
  http.Axios.post(
    domainObj.baseUrl + "/web/algoStrategy/node/flow/config/find",
    data
  );
// 指标-版本列表
export const versionPage = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoindicator/versionPage", data);
// 指标-检查
export const algoindicatorCheck = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoindicator/check", data);

// 指标-保存草稿
export const algoindicatorSaveDraft = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoindicator/saveDraft", data);

// 指标-版本更新
export const algoindicatorVersionUpdate = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoindicator/versionUpdate", data);

// 指标-版本删除
export const algoindicatorVersionDel = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoindicator/versionDel", data);
//版本配置
export const versionConfig = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoindicator/versionConfig", data);
//指标查询明细
export const algoindicatorGetConfig = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoindicator/getConfig", data);

//版本JSON配置查询
export const algoindicatorGetVersionJSONConfig = data =>
  http.Axios.post(domainObj.baseUrl + "/web/algoindicator/getVersionJSONConfig", data);

// 获取指标配置
export const getIndicatorConfig = data =>
  http.Axios.post(
    domainObj.baseUrl + "/web/algoStrategyBatchNode/indicatorConfig",
    data
  );
