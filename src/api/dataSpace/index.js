import http from "@/utils/httpService";
import domainObj from "@/utils/globalParam";

// 获取数据空间列表
export const getList = ({ code, current, size }) =>
  http.Axios.post(domainObj.baseUrl + "/web/algoDataSpace/list", {
    pageNum: current,
    pageSize: size,
    param: { code }
  });

// 保存数据空间（假设后端未实现，暂不处理）
// export const save = data =>
//   http.Axios.post(domainObj.baseUrl + "/web/algoDataSpace/save", data);

// 删除数据空间
export const remove = row =>
  http.Axios.post(domainObj.baseUrl + "/web/algoDataSpace/delete", row);

// 清除计算数据
export const cleanData = ({ dataSpaceId }) =>
  http.Axios.post(domainObj.baseUrl + "/web/algoStrategyBatch/clean", { dataSpaceId });

// 新增数据空间
export const createDataSpace = ({ name, remark }) =>
  http.Axios.post(domainObj.baseUrl + "/web/algoDataSpace/create", { name, remark });

// 编辑数据空间
export const editDataSpace = ({ id, name, memoryLimit, remark }) =>
  http.Axios.post(domainObj.baseUrl + "/web/algoDataSpace/update", { id, name, memoryLimit, remark });

// 数据空间概览
export const overviewDataSpace = ({ dataSpaceId, dataCode, dataName, dataType, pageNum, pageSize }) =>
  http.Axios.post(domainObj.baseUrl + "/web/algoDataSpace/overview", {
    pageNum,
    pageSize,
    param: { dataSpaceId, dataCode, dataName, dataType }
  }); 

// 查询显示字段
export const getDataSpaceFields = ({ nodeType, bizCode, dataSpaceId }) =>
  http.Axios.post(domainObj.baseUrl + "/web/algoDataSpace/fields", {
    nodeType,
    bizCode,
    dataSpaceId
  });

// 查询数据
export const getDataSpaceData = ({ nodeType, bizCode, dataSpaceId, dataType, pageNum, pageSize, searchParamList }) =>
  http.Axios.post(domainObj.baseUrl + "/web/algoDataSpace/data", {
    pageNum,
    pageSize,
    param: { nodeType, bizCode, dataSpaceId, dataType, searchParamList }
  }); 