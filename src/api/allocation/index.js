import http from "@/utils/httpService";
import domainObj from "@/utils/globalParam";

//分配数据源分页查询
export const getAssignPage = data =>
  http.Axios.post(domainObj.baseUrl + "/web/sourcefieldbase/assignPage", data, {
    hideLoading: true
  });

//分配数据源删除
export const deleteAssign = data =>
  http.Axios.post(domainObj.baseUrl + "/web/sourcefieldbase/delete", data, {
    hideLoading: true
  });

//分配数据源保存
export const saveAssign = data =>
  http.Axios.post(domainObj.baseUrl + "/web/sourcefieldbase/assignSave", data, {
    hideLoading: true
  });

//分配数据源查看
export const getAssign = data =>
  http.Axios.post(domainObj.baseUrl + "/web/sourcefieldbase/view", data, {
    hideLoading: true
  });


//匹配数据源分页查询
export const getMatchPage = data =>
  http.Axios.post(domainObj.baseUrl + "/web/sourcefieldbase/matchPage", data, {
    hideLoading: true
  });

//匹配数据源删除
export const deleteMatch = data =>
  http.Axios.post(domainObj.baseUrl + "/web/sourcefieldbase/delete", data, {
    hideLoading: true
  });

//匹配数据源保存
export const saveMatch = data =>
  http.Axios.post(domainObj.baseUrl + "/web/sourcefieldbase/matchSave", data, {
    hideLoading: true
  });

//匹配数据源查看
export const getMatch = data =>
  http.Axios.post(domainObj.baseUrl + "/web/sourcefieldbase/view", data, {
    hideLoading: true
  });


//匹配数据源优先权查看
export const getPriority = data =>
  http.Axios.post(domainObj.baseUrl + "/web/sourcefieldpriorityconfigbase/detail", data, {
    hideLoading: true
  });

//匹配数据源优先权保存
export const savePriority = data =>
  http.Axios.post(domainObj.baseUrl + "/web/sourcefieldpriorityconfigbase/save", data, {
    hideLoading: true
  });


//匹配策略待选择分配数据源查询
export const getAssignPageById = data =>
  http.Axios.post(domainObj.baseUrl + "/web/sourcefieldbase/pageAllocateAssignField", data, {
    hideLoading: true
  });


//匹配策略待选择匹配数据源查询
export const getMatchPageById = data =>
  http.Axios.post(domainObj.baseUrl + "/web/sourcefieldbase/pageAllocateMatchField", data, {
    hideLoading: true
  });


//匹配策略分配数据源确认分配
export const saveAssignFbId = data =>
  http.Axios.post(domainObj.baseUrl + "/web/sourcefieldbase/assignConfirmAllocate", data, {
    hideLoading: true
  });

//匹配策略匹配数据源确认分配
export const saveMatchFbId = data =>
  http.Axios.post(domainObj.baseUrl + "/web/sourcefieldbase/matchConfirmAllocate", data, {
    hideLoading: true
  });

//匹配数据源优先权匹配数据源字段选择
export const getMatchKeys = data =>
  http.Axios.post(domainObj.baseUrl + "/web/sourcefieldbase/priorityMatchSource", data, {
    hideLoading: true
  });


//同步Br系统
export const syncToBr = data =>
  http.Axios.post(domainObj.baseUrl + "/web/strategymatch/syncToBr", data, {
    hideLoading: true
  });