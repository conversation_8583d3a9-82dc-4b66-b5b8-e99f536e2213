import Vue from "vue";
import uplode from "./uplode.vue";

const dt_upload = (option) => {
  let { actionUrl,formData,onError,onSuccess,fileSuffix } = option;
  if(!onError) onError = ()=>{}
  if(!onSuccess) onSuccess = ()=>{}
  const InputFile = Vue.extend(uplode)
  const $vm = new InputFile({
    propsData: {
      actionUrl,
      formData,
      onError,
      onSuccess,
      fileSuffix
    },
  }).$mount();

  document.body.appendChild($vm.$el);
  return $vm;
};

const install = function(Vue, options) {
  Vue.prototype.$dt_upload = {
    show: (options) => {
      const $vm = dt_upload(options);
      $vm.$refs.file.click();
    },
  };
};

export default {
    install, 
}
