<template>
  <div>
    <input
      type="file"
      style="display:none"
      multiple="true"
      ref="file"
      name="termFile"
      @change="fileChange($event)"
    />
  </div>
</template>
<script>
import http from "@/utils/httpService";
import domainObj from "@/utils/globalParam";
export default {
  props: {
    actionUrl: {
      type: String,
      required: true,
      default: "" /*【必填】处理成功后的回调函数 */,
    },
    formData: {
      type: Object,
      required: false,
      default() {
        return {};
      },
    },
    onError: {
      type: Function,
      required: false,
      default: () => {} /*失败回调 */,
    },
    onSuccess:{
      type: Function,
      required: false,
      default: () => {} /*成功回调 */,
    },
    fileSuffix:{
      type: Array,
      required: false,
      default() {
        return ["xls","xlsx"];
      },
    }
  },
  data() {
    return {};
  },
  methods: {
    fileChange(e) {
      const ctx = this
      let files = this.$refs.file.files[0];
      let suffix =  files.name.split('.').pop().toLowerCase()
      let pass = this._.includes(this.fileSuffix,suffix)
      if(!pass){
        let msg = this.fileSuffix.join("或")
        this.$message({
          type: "error",
          message: `上传格式不正确，请上传${msg}格式`,
        });
        return false;
      }



      let formData = this.formData || {};
      formData.file = files;
      const actionUrl = domainObj.baseUrl + this.actionUrl;
      console.log(actionUrl)
      http.Axios.post(actionUrl, formData, {
        isImport: true,
        noError:true
      }).then((res) => {
        if (res.resp_code == 1) {
          ctx.onError(res.resp_msg)
        }else if(res.resp_code == 0){
          ctx.onSuccess(res.datas)
        }
        e.target.value = "";
        ctx.destroy();
      });
    },
    remove() {
      setTimeout(() => {
        this.destroy();
      }, 100);
    },
    destroy() {
      this.$destroy();
      document.body.removeChild(this.$el);
    },
  },
};
</script>
<style lang="less">
.is-display {
  display: none;
}
</style>
