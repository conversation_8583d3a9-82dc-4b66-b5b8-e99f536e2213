import {
  getDicItems
} from "@/api/index";
import store from "@/store/index.js";

import _ from "lodash";


/**
 * 1.判断对象是否为空
 * 2.判断字符串是否有值
 * @param o {Object}
 * @return 不为空返回true 为空返回false
 */
export const isNotEmpty = o => {
  if (o === "" || o === undefined || o === null) return false;

  if (typeof o == "String" && o.trim().length == 0) return false;

  if(_.isArray(o)&&o.length==0) return false;

  return true;
};

/**
 * 将source和target共有属性，由source赋值至target
 * @param target {Object}
 * @param source {Object}
 * @param ignoreProperties {Array} 忽略的属性
 * @param isDeep {Boolean} 是否进行深层赋值
 */
export const mergeProperties = function (
  target,
  source,
  ignoreProperties,
  isDeep
) {
  if (!target || !source) return target;

  if (isDeep == null || isDeep == "undefined") isDeep = false;

  ignoreProperties = ignoreProperties || [];

  for (let i in target) {
    if (contains(ignoreProperties, i)) continue;

    if (i in source) {
      target[i] = source[i];
    } else if (typeof target[i] == "object" && isDeep) {
      mergeProperties(target[i], source, ignoreProperties, isDeep);
    }
  }
  return target;
};

/**
 * 将source属性及值复制至target
 * @param target {Object}
 * @param source {Object}
 * @param ignoreProperties {Array} 忽略的属性
 */
export const unionProperties = function (target, source, ignoreProperties) {
  if (!target || !source) return target;

  ignoreProperties = ignoreProperties || [];

  for (let i in source) {
    if (contains(ignoreProperties, i)) continue;

    target[i] = source[i];
  }

  return target;
};

/**
 * 判断数组是否包含指定元素
 * @param arr {Array}
 * @param obj {Object}
 */
export const contains = function (arr, obj) {
  let i = arr.length;
  while (i--) {
    if (arr[i] === obj) {
      return true;
    }
  }
  return false;
};


// 获取字典数据项
// dicCode 字典类型   force 是否强制刷新字典
export const getDicItemList =  async (dicCode, force = false) => {
  let arr = [];
  let dicParam = {
    pageNum: 0,
    pageSize: 0,
    param: {
      dicCode: dicCode
    }
  };
  let dicMap = store.state.dicMap
  if (_.has(dicMap, dicCode) && dicMap[dicCode] && !force) {
    return dicMap[dicCode]
  }
  let res = await getDicItems(dicParam);
  if (res && res.list) {
    arr = res.list;

    // _.each(arr,item=>{
    //   item.dicItemCode =  Number(item.dicItemCode) 
    // })

    store.commit("mapDicData", {
      dicCode: dicCode,
      dicItems: arr
    })
  }

  return arr;
};




/**
 * 转码通用方法
 * @param arr {Array} 字典配置
 * @param obj {Object} 转码对象,可以是数组或对象
 * @param isReverse {Boolean} 是否反转,默认fale
 */
export const dicFormatter = function (dicConfig, obj, isReverse) {
  if (obj instanceof Array) {
    for (let i = 0; i < obj.length; i++) {
      objFormatter(obj[i]);
    }
  } else if (obj instanceof Object) {
    objFormatter(obj);
  }

  function objFormatter(o) {
    dicConfig.forEach(element => {
      if (o.hasOwnProperty(element.property)) {
        let items = store.state.dicMap.get(element.id) || [];
        items.forEach(function (value, key, mapObj) {
          if (isReverse) {
            if (o[element.property] == value.dicItemName) {
              o[element.property] = value.dicItemCode + "";
            }
          } else {
            if (o[element.property] == value.dicItemCode) {
              o[element.property] = value.dicItemName;
            }
          }
        });
      }
    });
  }
};




export const hasRights = btn => {
  let authSet = store.state["layoutStore"].authSet
  // 部分权限
  if (btn == "" || btn == undefined) {
    return true;
  }
  for (let i = 0; i < authSet.length; i++) {
    if (btn == authSet[i]) {
      return true;
    }
  }
  return false
};


/**
 * @param list {Array} 原数组
 * @param keyword {String} 关键字
 * @param key {String} 数组对象中的匹配属性
 */
export const fuzzyQuery = (list,keyword,key="") => {
  let arr = []
  // keyword = keyword.toLowerCase().trim()
  for (let i = 0; i < list.length; i++) {
    let item = key?list[i][key]:list[i]
    if(item.indexOf(keyword)!=-1){
      arr.push(list[i])
    }
  }
  return arr
}


//今天的日期 YYYY-MM-DD
export const Today = () => {
  let date = new Date();
  let seperator1 = "-";
  let year = date.getFullYear();
  let month = date.getMonth() + 1;
  let strDate = date.getDate();
  if (month >= 1 && month <= 9) {
    month = "0" + month;
  }
  if (strDate >= 0 && strDate <= 9) {
    strDate = "0" + strDate;
  }
  let currentdate = year + seperator1 + month + seperator1 + strDate;
  return currentdate;
};



