import Vue from "vue";
import Vuex from "vuex";
import createVuexAlong from "vuex-along";
import * as api from "@/api/index";
import layoutStore from "./layoutStore.js"; //公共状态
import strategyMatchManage from "./strategyMatchManage"; //产品管理
import gpRule from "./gpRule";
import templateEngine from "./templateEngine";
Vue.use(Vuex);

const state = {
  dicMap: {},
  userTenants: [],
  conditions: {}
};
const mutations = {
  mapDicData(state, data) {
    state.dicMap[data.dicCode] = data.dicItems;
  },
  setUserTenants(state, data) {
    state.userTenants = data;
  },
  clearDicData(state) {
    state.dicMap = {};
  }
};

const actions = {
  getWebUserInfo({ commit }) {
    //获取登录用户在该应用下的租户
    api.getWebUserInfo({}).then(data => {
      if (data) {
        let authsetArr = [];
        if (data.funcAuthDTO) {
          data.funcAuthDTO.forEach(item => {
            authsetArr.push(item.authCode);
          });
        }
        commit("layoutStore/setAuthSet", authsetArr);
      }
    });
  }
};

export default new Vuex.Store({
  state,
  mutations,
  modules: {
    layoutStore,
    strategyMatchManage,
    gpRule,
    templateEngine
  },
  plugins: [createVuexAlong()],
  actions
});
