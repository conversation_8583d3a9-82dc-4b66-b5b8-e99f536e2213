const state = {
  strategyMatchObj:{},
  strategyMatchIndicatorObj:{},
  executedRecordCount:0
};

const mutations = {
  setStrategyMatchObj(state, data) {
    state.strategyMatchObj = data;
  },
  setStrategyMatchIndicatorObj(state,data){
    state.strategyMatchIndicatorObj = data
  },
  updatesExecutedRecordList(state,data){
    state.executedRecordCount++
  }
};

export default {
  namespaced: true,
  state,
  mutations
};