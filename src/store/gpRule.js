const state = {
  variableKeys: [],
  // 普通变量集合
  variableList: [],
  // 全局变量集合
  GVariableList: []
};
const getters = {
  getVariableList: state => (type, sort = null) => {
    const variables = type === "2" ? state.GVariableList : state.variableList;
    return variables.filter(e => {
      if (sort !== null) {
        const index = state.variableKeys.findIndex(k => k === e.key);
        return index < sort && e.code !== "" && e.name !== "";
      } else return e.code !== "" && e.name !== "";
    });
  },
  checkVariable: state => (type, code, index) => {
    const variable = type === "2" ? state.GVariableList : state.variableList;
    return (
      variable.findIndex((e, idx) => index !== idx && e.code === code) === -1
    );
  }
};
const mutations = {
  setVariableList(state, data) {
    const variable =
      data.type === "2" ? state.GVariableList : state.variableList;

    variable.push({
      key: data.key,
      name: data.name,
      code: data.code
    });
    state.variableKeys.push(data.key);
  },
  updateVariableList(state, data) {
    const variableList =
      data.type === "2" ? state.GVariableList : state.variableList;
    const variable = variableList[data.index];
    if (variable) {
      variable.name = data.name;
      variable.code = data.code;
    }
  },
  delVariableList(state, data) {
    const variable =
      data.type === "2" ? state.GVariableList : state.variableList;
    const v = variable.splice(data.index, 1);
    const k = state.variableKeys.findIndex(e => e === v.key);
    if (k !== -1) state.variableKeys(k, 1);
  },
  clearVariableList(state) {
    state.GVariableList = [];
    state.variableList = [];
    state.variableKeys = [];
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations
};
