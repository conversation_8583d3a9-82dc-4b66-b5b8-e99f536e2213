import _ from "lodash";

const state = {
  //待缓存的页面路由名称
  cacheArrName: [],
  //不需要缓存的页面路由名称
  notCacheArrName: [],
  themeObj: {
    color: "#D7A256",
    tableBtnActiveColor: "#D7A256",
    navTagUnselectedColor: "#FFF6E8",
    text: "主题一"
  },
  //权限控制
  authSet: [],  
  //后台登录用户信息
  currentLoginUser: {},
  queryParam:{}
};

const getters = {
  getThemeObj: state => {
    return state.themeObj;
  },
  getAuthSet: state => {
    return state.authSet;
  },
  getCurrentLoginUser: state => {
    return state.currentLoginUser;
  },
};

const mutations = {
  setAuthSet(state, data) {
    state.authSet = data
  },
  setThemeObj(state, data) {
    state.themeObj = data;
  },
  setCurrentLoginUser(state, data) {
    state.currentLoginUser = data
  },
  //动态设置需要缓存的页面
  setCacheArr(state, data) {
    // data包含两个字段  statue add为添加 del为删除 clear清空缓存列表(主要针对切换用户)
    if (data.status == "add") {
      if (!_.find(state.cacheArrName, ["name", data.routeName])) {
        state.cacheArrName.push(data.routeName)
        _.remove(state.notCacheArrName, function (n) {
          return n == data.routeName;
        });
      }
    } else if (data.status == "del") {
      if (!_.find(state.notCacheArrName, ["name", data.routeName])) {
        state.notCacheArrName.push(data.routeName)
        _.remove(state.cacheArrName, function (n) {
          return n == data.routeName;
        });
      }
    } else if (data.status == "clear") {
      state.notCacheArrName = [];
      state.cacheArrName = [];
    }
  },
  // 缓存搜索条件
  setQueryParam(state,data){
    if(_.isEmpty(data)){
      state.queryParam = {}
      return 
    }
    state.queryParam[data.key] = data.val
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations
};