const state = {
  filedUpdateObj: {},
  nodeItemList: {},
  indexManage:{},
  gurationList:{},
  visionList:{},
};

const mutations = {
  filedUpdateObj(state, data) {
    state.filedUpdateObj = data;
  },
  indexManage(state, data) {
    state.indexManage = data;
  },
  nodeItemList(state, data) {
    state.nodeItemList = data;
  },
  gurationList(state, data) {
    state.gurationList = data;
  },
  visionList(state, data) {
    state.visionList = data;
  }
};

export default {
  namespaced: true,
  state,
  mutations
};
