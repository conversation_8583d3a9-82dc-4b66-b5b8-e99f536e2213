<template>
  <div class="data-space-container">
    <UniversalTable
      title="数据空间列表"
      subtitle="管理和维护数据空间，支持空间内策略执行与资源监控"
      title-icon="el-icon-folder"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :actions="tableActions"
      :search-form-config="searchFormTemp"
      :search-params="searchForm"
      :pagination-data="page"
      :total="page.total"
      add-button-text="新增数据空间"
      empty-title="暂无数据空间"
      empty-description="点击上方新增数据空间按钮开始创建"
      @search="handleSearch"
      @reset="resetSearch"
      @add="handleAdd"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @action-click="handleAction"
      :action-column-width="450"
    >
      <template #headerActions>
        <el-button type="primary" size="small" @click="handleAdd" class="add-btn primary-btn">
          <i class="el-icon-plus"></i> 新增数据空间
        </el-button>
        <el-button type="primary" size="small" @click="handleMemoryLimit" class="add-btn primary-btn" style="margin-left: 8px;">
          <i class="el-icon-setting"></i> 内存上限设置
        </el-button>
      </template>
      <template #dataSpaceCode="{ row }">
        <div class="code-cell">
          <div class="code-icon">
            <i class="el-icon-folder"></i>
          </div>
          <span class="code-text">{{ row.dataSpaceCode }}</span>
        </div>
      </template>
      <template #strategyStatus="{ row }">
        <div class="strategy-status-cell">
          <el-button size="mini" type="success" plain round class="status-btn" @click="handleBatchStatus(row, '2')">
            成功: {{ row.strategyFinishedNum || 0 }}
          </el-button>
          <el-button size="mini" type="info" plain round class="status-btn" @click="handleBatchStatus(row, '0')">
            未执行: {{ row.strategyUnRunningNum || 0 }}
          </el-button>
          <el-button size="mini" type="warning" plain round class="status-btn" @click="handleBatchStatus(row, '1')">
            执行中: {{ row.strategyRunningNum || 0 }}
          </el-button>
          <el-button size="mini" type="danger" plain round class="status-btn" @click="handleBatchStatus(row, '3')">
            失败: {{ row.strategyFailedNum || 0 }}
          </el-button>
        </div>
      </template>
      <template #memory="{ row }">
        <span class="memory-badge">{{ (row.memoryUsage || 0).toFixed(2) }}/{{ row.memoryLimit || 0 }} G</span>
      </template>
      <template #remark="{ row }">
        <el-tooltip v-if="row.remark" :content="row.remark" effect="dark" placement="top">
          <span class="remark-text">{{ row.remark }}</span>
        </el-tooltip>
        <span v-else class="no-remark">
          <i class="el-icon-info"></i> 暂无备注
        </span>
      </template>
      <template #createTime="{ row }">
        <div class="time-cell">
          <i class="el-icon-time"></i>
          <span>{{ row.createTime | formatDate }}</span>
        </div>
      </template>
    </UniversalTable>

    <!-- 新增/编辑弹窗 -->
    <UniversalFormDialog
      v-model="showEditDialog"
      :form-data="editForm"
      :form-fields="formFields"
      :form-rules="formRules"
      :is-edit="editForm.isEdit"
      :loading="saveLoading"
      add-title="新增数据空间"
      edit-title="编辑数据空间"
      @confirm="handleSaveDataSpace"
      @cancel="handleCancel"
      @close="handleDialogClose"
    />

    <!-- 内存上限设置弹窗 -->
    <UniversalFormDialog
      v-model="showMemoryDialog"
      :form-data="memoryForm"
      :form-fields="memoryFields"
      :form-rules="memoryRules"
      :loading="memorySaveLoading"
      add-title="内存上限设置"
      edit-title="内存上限设置"
      confirm-text="保存"
      @confirm="handleSaveMemoryLimit"
      @cancel="handleCancelMemory"
      @close="handleDialogCloseMemory"
    />

    <!-- 删除确认弹窗 -->
    <ConfirmDialog
      ref="confirmDialog"
      :title="confirmTitle"
      :message="confirmMessage"
      :icon="confirmIcon"
      confirm-text="确定"
      cancel-text="取消"
      @confirm="handleConfirm"
    />
  </div>
</template>

<script>
import UniversalTable from '@/components/layouts/UniversalTable.vue';
import UniversalFormDialog from '@/components/layouts/UniversalFormDialog.vue';
import ConfirmDialog from '@/components/layouts/ConfirmDialog.vue';
import { getList, createDataSpace, editDataSpace, remove, cleanData } from '@/api/dataSpace';
import { getSetting, saveSetting } from '@/api/setting';

export default {
  name: 'DataSpace',
  components: { UniversalTable, UniversalFormDialog, ConfirmDialog },
  data() {
    return {
      searchFormTemp: [
        {
          label: '数据空间编码',
          name: 'dataSpaceCode',
          type: 'input',
          placeholder: '请输入数据空间编码'
        }
      ],
      searchForm: {
        dataSpaceCode: ''
      },
      loading: false,
      tableData: [],
      page: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      tableColumns: [
        { prop: 'dataSpaceCode', label: '数据空间编码', minWidth: 150, align: 'center' },
        { prop: 'name', label: '数据空间名称', minWidth: 150, align: 'center' },
        { prop: 'strategyStatus', label: '策略执行情况', minWidth: 280, align: 'center' },
        { prop: 'memory', label: '占用内存(G)', minWidth: 130, align: 'center' },
        { prop: 'remark', label: '备注', minWidth: 150, align: 'center' },
        { prop: 'createTime', label: '创建时间', minWidth: 180, align: 'center' }
      ],
      tableActions: [
        { key: 'overview', label: '数据概览', icon: 'el-icon-view', class: 'config-btn' },
        { key: 'clean', label: '清除计算数据', icon: 'el-icon-delete', class: 'config-btn' },
        { key: 'edit', label: '编辑', icon: 'el-icon-edit', class: 'config-btn' },
        { key: 'delete', label: '删除', icon: 'el-icon-delete', class: 'delete-btn' }
      ],
      // 新增/编辑弹窗
      showEditDialog: false,
      saveLoading: false,
      editForm: {
        id: null,
        dataSpaceCode: '',
        name: '',
        remark: '',
        isEdit: false
      },
      get formFields() {
        const baseFields = [
          { type: 'input', prop: 'name', label: '数据空间名称', placeholder: '请输入数据空间名称', maxlength: 100 },
          { type: 'textarea', prop: 'remark', label: '备注', placeholder: '请输入备注', maxlength: 200, showWordLimit: true }
        ];
        if (this.editForm && this.editForm.isEdit) {
          baseFields.splice(1, 0, { type: 'number', prop: 'memoryLimit', label: '内存上限(G)', min: 1, max: 100, precision: 0, placeholder: '请输入内存上限', inputStyle: 'width: 100%' });
        }
        return baseFields;
      },
      formRules: {
        dataSpaceCode: [
          { required: true, message: '请输入数据空间编码', trigger: 'blur' },
          { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入数据空间名称', trigger: 'blur' },
          { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
        ]
      },
      // 内存上限弹窗
      showMemoryDialog: false,
      memorySaveLoading: false,
      memoryForm: { value: 6 },
      memoryFields: [
        { type: 'number', prop: 'value', label: '内存上限(G)', min: 1, max: 100, precision: 0, placeholder: '请输入内存上限'}
      ],
      memoryRules: {
        value: [
          { required: true, message: '请输入内存上限', trigger: 'blur' }
        ]
      },
      // 删除确认弹窗
      confirmTitle: '',
      confirmMessage: '',
      confirmIcon: '',
      confirmCallback: null
    };
  },
  created() {
    this.fetchData();
    this.fetchMemoryLimit();
  },
  methods: {
    async fetchData() {
      this.loading = true;
      try {
        const { pageNum, pageSize } = this.page;
        const res = await getList({
          code: this.searchForm.dataSpaceCode,
          current: pageNum,
          size: pageSize
        });
        this.tableData = (res.list || res.records || []).map(item => ({ ...item, id: item.id }));
        this.page.total = res.total || 0;
      } catch (error) {
        console.error('获取数据失败', error);
      }
      this.loading = false;
    },
    async fetchMemoryLimit() {
      try {
        const res = await getSetting('DATA_SPACE_MEMORY_LIMIT');
        if (res) {
          this.memoryForm.value = parseInt(res);
        }
      } catch (error) {
        console.error('获取内存上限设置失败', error);
      }
    },
    handleSearch() {
      this.page.pageNum = 1;
      this.fetchData();
    },
    resetSearch() {
      this.searchForm = { dataSpaceCode: '' };
      this.handleSearch();
    },
    handleAdd() {
      this.editForm = { id: null, dataSpaceCode: '', name: '', remark: '', isEdit: false };
      this.showEditDialog = true;
    },
    handleEdit(row) {
      this.editForm = { id: row.id, name: row.name, memoryLimit: row.memoryLimit, remark: row.remark, isEdit: true };
      this.showEditDialog = true;
    },
    async handleSaveDataSpace({ formData, isEdit }) {
      try {
        this.saveLoading = true;
        if (isEdit) {
          await editDataSpace({ id: formData.id, name: formData.name, memoryLimit: formData.memoryLimit, remark: formData.remark });
          this.$message.success('编辑成功');
        } else {
          await createDataSpace({ name: formData.name, remark: formData.remark });
          this.$message.success('新增成功');
        }
        this.showEditDialog = false;
        this.fetchData();
      } catch (e) {
        this.$message.error('操作失败');
      } finally {
        this.saveLoading = false;
      }
    },
    handleCancel() {
      this.showEditDialog = false;
    },
    handleDialogClose() {
      // 可选：重置表单
    },
    handleMemoryLimit() {
      this.showMemoryDialog = true;
    },
    async handleSaveMemoryLimit({ formData }) {
      try {
        this.memorySaveLoading = true;
        await saveSetting({ code: 'DATA_SPACE_MEMORY_LIMIT', value: formData.value });
        this.$message.success('设置成功');
        this.showMemoryDialog = false;
      } catch (e) {
        this.$message.error('设置失败');
      } finally {
        this.memorySaveLoading = false;
      }
    },
    handleCancelMemory() {
      this.showMemoryDialog = false;
    },
    handleDialogCloseMemory() {
      // 可选：重置表单
    },
    handleOverview(row) {
      this.$router.push({ name: 'dataSpaceOverview', query: { dataSpaceId: row.id } });
    },
    handleBatch(row) {
      this.$router.push({ name: 'strategyBach', query: { dataSpaceCode: row.dataSpaceCode } });
    },
    handleCleanData(row) {
      this.confirmTitle = '清除计算数据';
      this.confirmMessage = `确定要清除数据空间 ${row.dataSpaceCode} 的计算数据吗？`;
      this.confirmIcon = 'el-icon-warning';
      this.confirmCallback = async () => {
        try {
          await cleanData({ dataSpaceId: row.id });
          this.$message.success('清除成功');
          this.fetchData();
        } catch (error) {
          console.error('清除失败', error);
        }
      };
      this.$refs.confirmDialog.show();
    },
    handleDelete(row) {
      this.confirmTitle = '删除数据空间';
      this.confirmMessage = `确定要删除数据空间 ${row.dataSpaceCode} 吗？`;
      this.confirmIcon = 'el-icon-error';
      this.confirmCallback = async () => {
        try {
          await remove({ id: row.id });
          this.$message.success('删除成功');
          this.fetchData();
        } catch (error) {
          console.error('删除失败', error);
        }
      };
      this.$refs.confirmDialog.show();
    },
    handleConfirm() {
      if (this.confirmCallback) this.confirmCallback();
      this.$refs.confirmDialog.hide();
    },
    handleSizeChange(val) {
      this.page.pageSize = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.page.pageNum = val;
      this.fetchData();
    },
    handleAction({ action, row }) {
      if (action === 'overview') this.handleOverview(row);
      else if (action === 'clean') this.handleCleanData(row);
      else if (action === 'edit') this.handleEdit(row);
      else if (action === 'delete') this.handleDelete(row);
    },
    handleBatchStatus(row, status) {
      const query = { dataSpaceCode: row.dataSpaceCode, fromDataSpace: 1 };
      if (status !== '') query.executeStatus = status;
      this.$router.push({ name: 'strategyBach', query });
    },
    formatDate(val) {
      if (!val) return '';
      const d = new Date(val);
      return d.getFullYear() + '-' + String(d.getMonth() + 1).padStart(2, '0') + '-' + String(d.getDate()).padStart(2, '0') + ' ' + String(d.getHours()).padStart(2, '0') + ':' + String(d.getMinutes()).padStart(2, '0') + ':' + String(d.getSeconds()).padStart(2, '0');
    }
  },
  filters: {
    formatDate(val) {
      if (!val) return '';
      const d = new Date(val);
      return d.getFullYear() + '-' + String(d.getMonth() + 1).padStart(2, '0') + '-' + String(d.getDate()).padStart(2, '0') + ' ' + String(d.getHours()).padStart(2, '0') + ':' + String(d.getMinutes()).padStart(2, '0') + ':' + String(d.getSeconds()).padStart(2, '0');
    }
  }
};
</script>

<style lang="less">
@import "~@/views/indexManage/components/shared-styles.less";
.data-space-container {
  background: white;
  min-height: 100vh;
  padding: 0;
  .header-section {
    .header-top {
      background: @bg-light;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 28px 28px;
      .title-section {
        .page-title {
          margin: 0;
          display: flex;
          align-items: center;
          gap: 12px;
          .title-icon {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, @primary-color 0%, @primary-hover 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px @primary-shadow;
            i {
              color: white;
              font-size: 18px;
            }
          }
          .title-text {
            .main-title {
              font-size: 20px;
              font-weight: 600;
              color: @text-primary;
              margin: 0 0 2px 0;
              display: block;
            }
            .page-subtitle {
              margin: 0;
              color: @text-secondary;
              font-size: 12px;
              font-weight: 400;
            }
          }
        }
      }
      .action-section {
        .add-btn.primary-btn {
          height: 32px;
          padding: 0 16px;
          border-radius: 6px;
          font-weight: 500;
          font-size: 13px;
          background: @primary-color;
          color: white;
          border: none;
          box-shadow: 0 2px 8px @primary-shadow;
          transition: all 0.3s ease;
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px @primary-shadow;
            background: @primary-hover;
          }
          i {
            margin-right: 6px;
            font-size: 12px;
          }
        }
      }
    }
    .search-section {
      padding: 24px 0;
      border-top: 1px solid @border-light;
      border-bottom: 1px solid @border-light;
      margin-top: 0;
    }
  }
  .table-section {
    .table-wrapper {
      width: 100%;
      .modern-table {
        width: 100%;
        border-radius: 0;
        overflow: hidden;
        border: none;
        font-size: 14px;
        /deep/ .el-table__header-wrapper {
          .el-table__header {
            th {
              background: @bg-light !important;
              font-weight: 600;
              font-size: 13px;
              color: @text-primary;
              border-bottom: 2px solid @border-light;
              padding: 12px 0;
              .cell {
                padding: 0 16px;
              }
            }
            th:last-child {
              background: @bg-light !important;
            }
          }
        }
        /deep/ .el-table__body-wrapper {
          .el-table__body {
            tr {
              transition: all 0.2s ease;
              &:hover {
                background: @hover-bg !important;
              }
              td {
                padding: 12px 0;
                border-bottom: 1px solid #f5f7fa;
                .cell {
                  padding: 0 16px;
                }
              }
            }
          }
        }
        /deep/ .el-table--striped .el-table__body tr.el-table__row--striped td {
          background: #fefdfb;
        }
      }
      .code-cell {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;

        .code-icon {
          width: 24px;
          height: 24px;
          background: rgba(215, 162, 86, 0.1);
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;

          i {
            color: #D7A256;
            font-size: 12px;
          }
        }

        .code-text {
          font-family: 'Courier New', monospace;
          font-weight: 500;
          color: #2c3e50;
          font-size: 13px;
        }
      }
      .strategy-status-cell {
        display: flex;
        align-items: center;
        gap: 4px;
        flex-wrap: wrap;
        justify-content: center;
        .status-btn {
          font-size: 12px;
          padding: 0 6px;
          height: 22px;
          min-width: 50px;
          font-weight: 500;
          margin-right: 0;
          margin-left: 0;
        }
      }
      .remark-text {
        cursor: pointer;
        color: #666;
      }
      .no-remark {
        color: #bbb;
        font-size: 13px;
      }
      .time-cell {
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: center;
        i {
          color: #909399;
        }
      }
      .action-buttons {
        display: flex;
        gap: 4px;
        justify-content: center;
        align-items: center;
        .action-btn {
          border-radius: 4px;
          font-size: 11px;
          padding: 4px 8px;
          transition: all 0.3s ease;
          font-weight: 500;
          min-width: 56px;
          height: 28px;
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          }
          i {
            margin-right: 3px;
            font-size: 11px;
          }
          &.config-btn {
            color: @primary-color;
            border-color: @primary-shadow;
            background: @primary-light;
            &:hover {
              background: @primary-color;
              color: white;
              border-color: @primary-color;
            }
          }
          &.edit-btn {
            color: @primary-color;
            border-color: @primary-shadow;
            background: @primary-light;
            &:hover {
              background: @primary-color;
              color: white;
              border-color: @primary-color;
            }
          }
          &.delete-btn {
            color: @error-color;
            border-color: rgba(245, 108, 108, 0.3);
            background: @error-bg;
            &:hover {
              background: @error-color;
              color: white;
              border-color: @error-color;
            }
          }
        }
      }
      .memory-badge {
        background: @primary-light;
        color: @primary-color;
        border-radius: 12px;
        padding: 2px 12px;
        font-weight: 600;
        font-size: 13px;
        display: inline-block;
      }
      .empty-state {
        padding: 80px 20px;
        text-align: center;
        .empty-content {
          .empty-icon {
            width: 80px;
            height: 80px;
            background: @primary-light;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            i {
              font-size: 40px;
              color: @primary-color;
            }
          }
          h3 {
            margin: 0 0 8px 0;
            color: @text-primary;
            font-size: 18px;
            font-weight: 600;
          }
          p {
            margin: 0 0 24px 0;
            color: @text-secondary;
            font-size: 14px;
          }
        }
      }
      .pagination-container {
        padding: 16px;
        text-align: right;
      }
    }
  }
}
</style> 