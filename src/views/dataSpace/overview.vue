<template>
  <div class="overview-container">
    <UniversalTable
      title="数据空间概览"
      subtitle="数据空间内各类数据的分布与资源占用情况"
      title-icon="el-icon-data-analysis"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :search-form-config="searchFormTemp"
      :search-params="searchParams"
      :pagination-data="pageInfo"
      :total="total"
      empty-title="暂无数据概览数据"
      empty-description="暂无相关数据"
      :show-add-button="false"
      :breadcrumb-items="breadcrumbItems"
      @search="handleSearch"
      @reset="handleReset"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :actions="actions"
      @action-click="handleAction"
    >
      <template #dataCode="{ row }">
        <div class="code-cell">
          <div class="code-icon">
            <i class="el-icon-document-copy"></i>
          </div>
          <span class="code-text">{{ row.dataCode }}</span>
        </div>
      </template>
      <template #dataType="{ row }">
        <div class="type-cell">
          <el-tag
            :class="getDataTypeClass(row.dataType)"
            size="medium"
            effect="plain"
          >
            <i :class="getDataTypeIcon(row.dataType)"></i>
            {{ dataTypeText(row.dataType) }}
          </el-tag>
        </div>
      </template>
      <template #strategyName="{ row }">
        <div class="strategy-name-cell">
          <span v-if="row.strategyName" class="strategy-name-text">{{ row.strategyName }}</span>
          <span v-else class="no-strategy">
            <i class="el-icon-info"></i> 暂无关联策略
          </span>
        </div>
      </template>
      <template #memoryUsage="{ row }">
        <div class="memory-cell">
          <span v-if="row.dataType == 1" class="memory-status">
            <el-tag size="mini" type="success">当前: {{ (row.memoryUsage || 0).toFixed(2) }} M</el-tag>
            <el-tag size="mini" type="info">全量: {{ (row.storageMemoryUsage || 0).toFixed(2) }} M</el-tag>
          </span>
          <span v-else class="memory-value">
            <el-tag size="mini" type="info">全量: {{ (row.memoryUsage || 0).toFixed(2) }} M</el-tag>
          </span>
        </div>
      </template>
    </UniversalTable>
  </div>
</template>

<script>
import UniversalTable from '@/components/layouts/UniversalTable.vue';
import { overviewDataSpace } from '@/api/dataSpace';

export default {
  name: 'DataSpaceOverview',
  components: { UniversalTable },
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      searchFormTemp: [
        { label: '数据编码', name: 'dataCode', type: 'input', placeholder: '请输入数据编码' },
        { label: '数据名称', name: 'dataName', type: 'input', placeholder: '请输入数据名称' },
        { label: '数据类型', name: 'dataType', type: 'select', placeholder: '请选择数据类型', list: [
          { dicItemCode: 1, dicItemName: '数据源' },
          { dicItemCode: 2, dicItemName: '计算指标' },
          { dicItemCode: 3, dicItemName: '结果指标' }
        ] }
      ],
      searchParams: {
        dataCode: '',
        dataName: '',
        dataType: ''
      },
      pageInfo: {
        pageNum: 1,
        pageSize: 10
      },
      tableColumns: [
        { prop: 'dataCode', label: '数据编码', minWidth: 120, align: 'center' },
        { prop: 'dataName', label: '数据名称', minWidth: 150, align: 'center' },
        { prop: 'dataType', label: '数据类型', minWidth: 100, align: 'center' },
        { prop: 'strategyName', label: '所属策略', minWidth: 150, align: 'center' },
        { prop: 'batchNum', label: '执行批次数', minWidth: 100, align: 'center' },
        { prop: 'memoryUsage', label: '占用内存(M)', minWidth: 160, align: 'center' }
      ],
      actions: [
        {
          key: 'viewCurrent',
          label: '查看当前数据',
          icon: 'el-icon-view',
          class: 'config-btn',
          hidden: row => row.dataType != 1
        },
        {
          key: 'viewAll',
          label: '查看全量数据',
          icon: 'el-icon-tickets',
          class: 'config-btn'
        }
      ]
    };
  },
  computed: {
    breadcrumbItems() {
      return [
        {
          text: '数据空间列表',
          icon: 'el-icon-back',
          to: { name: 'dataSpace' }
        },
        {
          text: '数据空间概览',
          icon: 'el-icon-data-analysis'
        }
      ];
    },
    themeObj() {
      return this.$store.state.layoutStore.themeObj;
    }
  },
  created() {
    this.fetchData();
  },
  methods: {
    dataTypeText(type) {
      switch (Number(type)) {
        case 1: return '数据源';
        case 2: return '计算指标';
        case 3: return '结果指标';
        default: return '-';
      }
    },
    getDataTypeClass(type) {
      switch (Number(type)) {
        case 1: return 'source-tag';
        case 2: return 'calculate-tag';
        case 3: return 'result-tag';
        default: return '';
      }
    },
    getDataTypeIcon(type) {
      switch (Number(type)) {
        case 1: return 'el-icon-coin';
        case 2: return 'el-icon-cpu';
        case 3: return 'el-icon-data-line';
        default: return 'el-icon-info';
      }
    },
    async fetchData() {
      this.loading = true;
      try {
        const { pageNum, pageSize } = this.pageInfo;
        const params = {
          ...this.searchParams,
          dataSpaceId: this.$route.query.dataSpaceId,
          pageNum,
          pageSize
        };
        
        const res = await overviewDataSpace(params);
        if (res && res.list) {
          this.tableData = res.list;
          this.total = res.total || 0;
          this.pageInfo.pageNum = res.pageNum;
          this.pageInfo.pageSize = res.pageSize;
        } else {
          this.tableData = [];
          this.total = 0;
        }
      } catch (e) {
        console.error('获取数据概览失败', e);
        this.tableData = [];
        this.total = 0;
      }
      this.loading = false;
    },
    handleSearch(data) {
      this.searchParams = data;
      this.pageInfo.pageNum = 1;
      this.fetchData();
    },
    handleReset() {
      this.searchParams = { dataCode: '', dataName: '', dataType: '' };
      this.pageInfo.pageNum = 1;
      this.fetchData();
    },
    handleSizeChange(size) {
      this.pageInfo.pageSize = size;
      this.fetchData();
    },
    handleCurrentChange(page) {
      this.pageInfo.pageNum = page;
      this.fetchData();
    },
    handleDetail(row) {
      // 详情操作，后续可扩展
      this.$message.info('详情功能待实现');
    },
    handleViewCurrentData(row) {
      // 跳转到数据空间明细页面，dataType=1
      this.$router.push({
        name: 'dataSpaceDetail',
        query: {
          nodeType: row.dataType, // 这里假设row.dataType就是nodeType，如有不同请调整
          bizCode: row.dataCode,
          dataSpaceId: this.$route.query.dataSpaceId,
          dataType: 1
        }
      });
    },
    handleViewAllData(row) {
      // 跳转到数据空间明细页面，dataType=2
      this.$router.push({
        name: 'dataSpaceDetail',
        query: {
          nodeType: row.dataType, // 这里假设row.dataType就是nodeType，如有不同请调整
          bizCode: row.dataCode,
          dataSpaceId: this.$route.query.dataSpaceId,
          dataType: 2
        }
      });
    },
    handleAction({ action, row }) {
      if (action === 'viewCurrent') this.handleViewCurrentData(row);
      else if (action === 'viewAll') this.handleViewAllData(row);
    }
  }
};
</script>

<style lang="less" scoped>
@import "~@/views/indexManage/components/shared-styles.less";

.overview-container {
  background: #fff;
  min-height: 100vh;

  // 编码单元格样式
  .code-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    .code-icon {
      width: 24px;
      height: 24px;
      background: rgba(215, 162, 86, 0.1);
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        color: #D7A256;
        font-size: 12px;
      }
    }

    .code-text {
      font-family: 'Courier New', monospace;
      font-weight: 500;
      color: #2c3e50;
      font-size: 13px;
    }
  }

  // 数据类型单元格样式
  .type-cell {
    .source-tag {
      background: rgba(230, 162, 60, 0.1);
      color: #E6A23C;
      border: 1px solid rgba(230, 162, 60, 0.3);

      i {
        margin-right: 4px;
      }
    }

    .calculate-tag {
      background: rgba(64, 158, 255, 0.1);
      color: #409eff;
      border: 1px solid rgba(64, 158, 255, 0.3);

      i {
        margin-right: 4px;
      }
    }

    .result-tag {
      background: rgba(103, 194, 58, 0.1);
      color: #67c23a;
      border: 1px solid rgba(103, 194, 58, 0.3);

      i {
        margin-right: 4px;
      }
    }
  }

  // 所属策略单元格样式
  .strategy-name-cell {
    .strategy-name-text {
      font-weight: 500;
      color: #2c3e50;
    }
    
    .no-strategy {
      color: #c0c4cc;
      font-style: italic;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;

      i {
        font-size: 12px;
      }
    }
  }

  // 内存占用单元格样式
  .memory-cell {
    .memory-status {
      display: flex;
      align-items: center;
      gap: 8px;
      flex-wrap: wrap;
      justify-content: center;
    }
    
    .memory-value {
      font-weight: 500;
      color: #409eff;
    }
  }

  // 内存占用样式
  .memory-badge {
      background: @primary-light;
      color: @primary-color;
      border-radius: 12px;
      padding: 2px 12px;
      font-weight: 600;
      font-size: 13px;
      display: inline-block;
  }

  // 操作按钮样式 - 与数据空间列表保持一致
  .action-buttons {
    display: flex;
    gap: 4px;
    justify-content: center;
    align-items: center;
    
    .action-btn {
      border-radius: 4px;
      font-size: 11px;
      padding: 4px 8px;
      transition: all 0.3s ease;
      font-weight: 500;
      min-width: 56px;
      height: 28px;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }
      
      i {
        margin-right: 3px;
        font-size: 11px;
      }
      
      &.config-btn {
        color: @primary-color;
        border-color: @primary-shadow;
        background: @primary-light;
        
        &:hover {
          background: @primary-color;
          color: white;
          border-color: @primary-color;
        }
      }
    }
  }
}
</style> 