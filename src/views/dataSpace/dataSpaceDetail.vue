<template>
  <div class="strateg-match-list">
    <SecondaryPageHeader
      title="数据明细"
      subtitle="数据明细"
      icon="el-icon-files"
      back-button-text="返回"
      back-route="dataSpaceOverview"
      :breadcrumb-items="breadcrumbItems"
      @back="goBack"
    />
    <Search
      :searchForm="initParam"
      class="search-bar"
      :searchFormTemp="searchFormTemp"
      @normalSearch="normalSearch"
      @addTemp="addTemp"
      @del="del"
      @handleChange="handleChange"
      @normalResetQuery="normalResetQuery"
      @export="exportUrl"
    ></Search>
    <div class="table-section">
      <div class="table-wrapper">
        <el-table
          :data="tableData"
          stripe
          v-hover
          class="modern-table"
          style="width: 100%"
        >
          <el-table-column
            v-for="(item, index) in FiledList"
            :key="item.fieldName || index"
            align="center"
            :prop="item.fieldName"
            :label="item.fieldDesc"
            :min-width="item.fieldName === 'strategyBatchParams' ? 250 : 100"
          >
            <template slot-scope="scope">
              <template v-if="item.fieldName === 'strategyBatchParams' && scope.row[item.fieldName]">
                <div style="white-space: pre-line; text-align: center;">
                  <span v-for="(line, idx) in scope.row[item.fieldName].split(',')" :key="idx">{{ line }}<br v-if="idx !== scope.row[item.fieldName].split(',').length - 1"/></span>
                </div>
              </template>
              <template v-else>
                <span>{{ scope.row[item.fieldName] }}</span>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <Pagination
          class="pagination-section"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :pageData="initParam"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
        ></Pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { getDataSpaceFields, getDataSpaceData } from '@/api/dataSpace';
import Search from '@/views/strategicManage/components/Search.vue';
import SecondaryPageHeader from '@/components/layouts/SecondaryPageHeader';
import Pagination from '@/components/layouts/Pagination.vue';
import fileDownload from 'js-file-download';
import Axios from 'axios';
import domainObj from '@/utils/globalParam';
import store from '../../store/index';
export default {
  name: 'dataSpaceDetail',
  components: { Search, SecondaryPageHeader, Pagination },
  data() {
    return {
      downloadURL: "/web/algoDataSpace/data/export",
      searchFormTemp: [
        {
          // 只保留字段、匹配符、输入字段，strategyBatchParams字段不出现
          strategyId: {
            label: '字段',
            name: 'strategyId',
            type: 'select',
            list: [],
            value: ''
          },
          match: {
            label: '匹配符',
            name: 'match',
            value: '=',
            type: 'select',
            list: [
              { dicItemCode: '=', dicItemName: '=' },
              { dicItemCode: '>', dicItemName: '>' },
              { dicItemCode: '>=', dicItemName: '>=' },
              { dicItemCode: '<', dicItemName: '<' },
              { dicItemCode: '<=', dicItemName: '<=' }
            ]
          },
          filed: {
            label: '输入字段',
            name: 'filed',
            type: 'input',
            value: ''
          },
          btn: {
            label: '删除',
            name: 'btn'
          }
        }
      ],
      tableData: [],
      FiledList: [],
      total: 0,
      initParam: {
        param: {
          nodeType: this.$route.query.nodeType,
          bizCode: this.$route.query.bizCode,
          dataSpaceId: this.$route.query.dataSpaceId,
          dataType: this.$route.query.dataType,
          searchParamList: [
            {
              fieldName: '',
              operator: '=',
              value: ''
            }
          ]
        },
        pageSize: 10,
        pageNum: 1
      }
    };
  },
  computed: {
    breadcrumbItems() {
      return [
        {
          text: '数据空间列表',
          icon: 'el-icon-back',
          to: { name: 'dataSpace' }
        },
        {
          text: '数据空间概览',
          icon: 'el-icon-data-analysis',
          to: { name: 'dataSpaceOverview', query: { dataSpaceId: this.$route.query.dataSpaceId } }
        },
        {
          text: '数据明细',
          icon: 'el-icon-files'
        }
      ];
    }
  },
  created() {
    this.initData();
  },
  methods: {
    goBack() {
      this.$router.back();
    },
    async initData() {
      await this.initFiledList();
      await this.initList();
    },
    async initFiledList() {
      // 查询显示字段
      const res = await getDataSpaceFields({
        nodeType: this.initParam.param.nodeType,
        bizCode: this.initParam.param.bizCode,
        dataSpaceId: this.initParam.param.dataSpaceId
      });
      if (res && res.length > 0) {
        this.FiledList = res;
        // 设置搜索下拉字段，过滤掉strategyBatchCode和strategyBatchParams
        const filterList = res.filter(f => f.fieldName !== 'strategyBatchCode' && f.fieldName !== 'strategyBatchParams');
        for (let i = 0; i < this.searchFormTemp.length; i++) {
          if (this.searchFormTemp[i].strategyId.name === 'strategyId') {
            this.searchFormTemp[i].strategyId.list = filterList;
          }
        }
      }
    },
    async initList() {
      // 查询数据
      const res = await getDataSpaceData({
        nodeType: this.initParam.param.nodeType,
        bizCode: this.initParam.param.bizCode,
        dataSpaceId: this.initParam.param.dataSpaceId,
        dataType: this.initParam.param.dataType,
        pageNum: this.initParam.pageNum,
        pageSize: this.initParam.pageSize,
        searchParamList: this.initParam.param.searchParamList
      });
      if (res && res.list) {
        this.tableData = res.list;
        this.total = res.total || 0;
      } else {
        this.tableData = [];
        this.total = 0;
      }
    },
    handleSizeChange(size) {
      this.initParam.pageSize = size;
      this.initList();
    },
    handleCurrentChange(page) {
      this.initParam.pageNum = page;
      this.initList();
    },
    normalSearch(data) {
      // 组装搜索参数
      let arr = [];
      for (let i = 0; i < data.length; i++) {
        arr.push({
          fieldName: data[i].strategyId.value,
          operator: data[i].match.value,
          value: data[i].filed.value
        });
      }
      this.initParam.param.searchParamList = arr;
      this.initList();
    },
    addTemp() {
      this.searchFormTemp.push(JSON.parse(JSON.stringify({
        strategyId: {
          label: '字段',
          name: 'strategyId',
          type: 'select',
          list: this.FiledList,
          value: ''
        },
        match: {
          label: '匹配符',
          name: 'match',
          value: '=',
          type: 'select',
          list: [
            { dicItemCode: '=', dicItemName: '=' },
            { dicItemCode: '>', dicItemName: '>' },
            { dicItemCode: '>=', dicItemName: '>=' },
            { dicItemCode: '<', dicItemName: '<' },
            { dicItemCode: '<=', dicItemName: '<=' }
          ]
        },
        filed: {
          label: '输入字段',
          name: 'filed',
          type: 'input',
          value: ''
        },
        btn: {
          label: '删除',
          name: 'btn'
        }
      })));
    },
    del(i) {
      this.searchFormTemp.splice(i, 1);
    },
    normalResetQuery() {
      this.initParam.pageNum = 1;
      this.initParam.pageSize = 10;
      this.initParam.param.searchParamList = [
        {
          fieldName: '',
          operator: '=',
          value: ''
        }
      ];
      this.initList();
    },
    handleChange(val, num, type, index) {
      // 可根据需要扩展
    },
    async exportUrl() {
      let fileurl = domainObj.baseUrl + this.downloadURL;
      const loadingInstance = this.$loading({
        fullscreen: true,
        lock: true,
        text: "加载中...",
        target: document.getElementsByTagName("body")[0]
      });
      Axios({
        method: "post",
        url: fileurl,
        headers: {
          access_token: sessionStorage.getItem("LoginAccessToken"),
          tenantId:
            store.state.layoutStore.currentLoginUser.tenantId ||
            sessionStorage.getItem("tenantId"),
          funcId:
            store.state.layoutStore.currentLoginUser.funcId ||
            sessionStorage.getItem("funcId")
        },
        data: this.initParam.param,
        responseType: "blob"
      }).then(res => {
        if (res.status === 200) {
          fileDownload(res.data, decodeURI(res.headers["file-name"] || "数据明细导出.xlsx"));
          setTimeout(function() {
            loadingInstance.close();
          }, 1000);
        } else {
          loadingInstance.close();
        }
      }).catch(() => {
        loadingInstance.close();
        this.$message.error("导出失败");
      });
    },
  }
};
</script>
<style lang="less" scoped>
@import "~@/views/indexManage/components/shared-styles.less";
.search-bar {
  padding: 24px;
  border-top: 1px solid @border-light;
  border-bottom: 1px solid @border-light;
}
</style> 