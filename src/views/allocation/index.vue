<template>
  <div class="strateg-match-list">
    <TableToolTemp
      :toolListProps="toolListProps"
      @handleTool="handleTool"
    ></TableToolTemp>
    <sourceTemp popStatus="1" />
  </div>
</template>
<script>
import sourceTemp from "@/components/sourceTemp";
import { baseComponent } from "@/utils/common";
export default {
  name: "allocationlist",
  mixins: [baseComponent],
  components: {
    sourceTemp,
  },
  data() {
    return {
      toolListProps: {
        toolTitle: "分配数据源管理",
        toolList: [
          {
            name: "新增数据源",
            btnCode: "",
            type: "add",
          },
        ],
      },
    };
  },
  created() {},
  methods: {
    handleTool(item) {
      if (item.type == "add") {
        this.$router.push({
          name: "allocationUpdate",
          query: {
            type: item.type,
          },
        });
      }
    },
  },
};
</script>

<style lang="less"></style>
