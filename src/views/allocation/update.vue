<template>
  <div class="strategy-match-update">
    <el-breadcrumb separator-class="el-icon-arrow-right" class="dt-bread">
      <el-breadcrumb-item :to="{ name: 'allocationlist' }"
        >分配数据源管理</el-breadcrumb-item
      >
      <el-breadcrumb-item
        :style="{ color: $store.state.layoutStore.themeObj.color }"
        >{{ getText }}</el-breadcrumb-item
      >
    </el-breadcrumb>
    <TableToolTemp :toolListProps="titleListPros"></TableToolTemp>
    <el-form
      :model="updateForm"
      label-width="160px"
      :rules="rules"
      ref="updateForm"
      label-position="left"
      class="pdl-20"
    >
      <el-form-item label="字段编码" prop="fieldId">
        <el-input
          :disabled="baseDisabled"
          v-model="updateForm.fieldId"
          class="dt-input-width"
          placeholder="请输入字段编码"
        ></el-input>
      </el-form-item>
      <el-form-item label="字段名称" prop="name">
        <el-input
          v-model="updateForm.name"
          class="dt-input-width"
          placeholder="请输入字段名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="数据类型" prop="dataType">
        <el-select
          v-model="updateForm.dataType"
          placeholder="请选择"
          class="dt-select"
        >
          <el-option
            v-for="(item, index) in commonData.dataType"
            :key="index"
            :label="item.dicItemName"
            :value="item.dicItemCode"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否参与计算" prop="isCalc">
        <el-select
          v-model="updateForm.isCalc"
          placeholder="请选择"
          class="dt-select"
        >
          <el-option
            v-for="(item, index) in commonData.status"
            :key="index"
            :label="item.dicItemName"
            :value="item.dicItemCode"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="属性类型" prop="propertyType">
        <el-select
          v-model="updateForm.propertyType"
          placeholder="请选择"
          class="dt-select"
        >
          <el-option
            v-for="(item, index) in commonData.propertyType"
            :key="index"
            :label="item.dicItemName"
            :value="item.dicItemCode"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否为字典" prop="isDic">
        <el-select
          v-model="updateForm.isDic"
          placeholder="请选择"
          class="dt-select"
        >
          <el-option
            v-for="(item, index) in commonData.status"
            :key="index"
            :label="item.dicItemName"
            :value="item.dicItemCode"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <div class="zidian-box">
        <div>
          字典配置
          <el-button type="text" size="mini" @click="addDictionary"
            >新增</el-button
          >
        </div>
        <el-table :data="updateForm.listFieldDicBaseSave" style="width: 100%">
          <el-table-column prop="date" label="字典编码" width="400">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.dicKey"
                class="dt-input-width"
                placeholder="请输入字典编码"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="字典名称" width="400">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.dicValue"
                class="dt-input-width"
                placeholder="请输入字典名称"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="address" label="操作">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="mini"
                @click="handleDel(scope.$index, scope.row)"
                >移除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-form-item>
        <el-button type="primary" @click="submit">保存</el-button>
        <el-button
          @click="goBack"
          type="primary"
          plain
          :style="{ color: $store.state.layoutStore.themeObj.color }"
          >取消</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import { validate, validateAlls } from "@/config/validation";
import { getDicItemList } from "@/config/tool.js";
import { saveAssign, getAssign } from "@/api/allocation/index.js";

export default {
  name: "allocationUpdate",
  data() {
    return {
      titleListPros: {
        toolTitle: "",
      },
      rules: {
        fieldId: [
          {
            required: true,
            validator: validate,
            trigger: "blur",
            min: 1,
            max: 30,
          },
        ],
        name: [
          {
            required: true,
            min: 1,
            max: 100,
            validator: validate,
            trigger: "blur",
          },
        ],
        dataType: [{ required: true, validator: validate, trigger: "blur" }],
        isCalc: [{ required: true, validator: validate, trigger: "blur" }],
        isDic: [{ required: true, validator: validate, trigger: "blur" }],
        propertyType: [
          { required: true, validator: validate, trigger: "blur" },
        ],
      },
      updateForm: {
        fbId: "",
        fieldId: "",
        name: "",
        dataType: "",
        isCalc: "",
        isDic: "",
        propertyType: "",
        listFieldDicBaseSave: [],
      },
      commonData: {
        dataType: [],
        propertyType: [],
        status: [],
      },
      baseDisabled: false,
    };
  },
  components: {
    TableToolTemp,
  },
  computed: {
    getText() {
      if (this.type == "add") {
        return "新增分配数据源";
      }
      if (this.type == "edit") {
        return "编辑分配数据源";
      }
    },
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      this.type = this.$route.query.type;
      this.titleListPros.toolTitle = this.getText;
      await this.getDictList();
      if (this.type == "edit") {
        this.baseDisabled = true;
        this.initData();
      }
    },
    async initData() {
      let res = await getAssign({
        fbId: this.$route.query.fbId,
      });
      if (!res) {
        return;
      }
      this.updateForm = res;
      this.updateForm.listFieldDicBaseSave = res.listSourceFieldDic || [];
      delete this.updateForm.listSourceFieldDic;
    },
    // 获取字典/
    async getDictList() {
      this.commonData.dataType = await getDicItemList("sct.source.dataType");
      this.commonData.propertyType = await getDicItemList(
        "sct.source.propertyType"
      );
      this.commonData.status = await getDicItemList("gen.yesorno.num");
    },
    goBack() {
      this.$router.go(-1);
    },
    async submit() {
      if (!validateAlls(this.$refs.updateForm)) {
        return;
      }
      let param = {};
      let res;
      res = await saveAssign(this.updateForm);
      if (!res) {
        return;
      }
      this.goBack();
    },
    addDictionary() {
      this.updateForm.listFieldDicBaseSave.push({
        dicKey: "",
        dicValue: "",
      });
    },
    handleDel(index, row) {
      console.log(index, row);
      this.updateForm.listFieldDicBaseSave.splice(index, 1);
    },
  },
};
</script>

<style lang="less">
.strategy-match-update {
  .table-tool {
    padding-bottom: 10px;
  }
  .draggable-wrap {
    display: inline-block;
  }
  .tags {
    margin-right: 6px;
  }
  .zidian-box {
    margin-bottom: 20px;
  }
}
</style>
