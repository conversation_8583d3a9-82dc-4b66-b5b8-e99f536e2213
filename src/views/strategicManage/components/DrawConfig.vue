<template>
  <!-- 策略配置抽屉 -->
  <div class="draw-config" :class="{ 'drawer-open': visible }">
    <!-- 抽屉头部 -->
    <div class="drawer-header">
      <div class="drawer-title">
        <i :class="getNodeIcon(selectedNode)"></i>
        <span>{{ selectedNode ? selectedNode.nodeName : '节点配置' }}</span>
      </div>
      <div class="drawer-actions">
        <el-button
          type="text"
          size="small"
          @click="handleClose"
          class="close-btn"
        >
          <i class="el-icon-close"></i>
          确定
        </el-button>
      </div>
    </div>

    <!-- 抽屉内容 -->
    <div class="drawer-content" v-if="selectedNode">
      <!-- 配置内容容器 -->
      <div class="config-content">
        <!-- 基本配置 -->
        <div class="config-panel">
          <div class="node-config-section">
            <el-form
              :model="nodeForm"
              label-width="120px"
              label-position="right"
              class="node-form"
              ref="nodeForm"
            >
              <el-form-item label="节点名称" prop="nodeName">
                <el-input
                  v-model="nodeForm.nodeName"
                  placeholder="请输入节点名称"
                  @change="handleNodeNameChange"
                  clearable
                />
              </el-form-item>
              
              <el-form-item 
                label="是否更新数据" 
                prop="isUpdate"
                v-if="showUpdateOption"
              >
                <el-select
                  v-model="nodeForm.isUpdate"
                  placeholder="请选择"
                  clearable
                  @change="handleUpdateChange"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in updateOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="节点描述" prop="description">
                <el-input
                  v-model="nodeForm.description"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入节点描述"
                  @change="handleDescriptionChange"
                  maxlength="200"
                  show-word-limit
                />
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </div>

    <!-- 未选择节点时的提示 -->
    <div class="drawer-content" v-if="!selectedNode">
      <div class="no-selection">
        <div class="empty-state">
          <div class="empty-icon">
            <i class="el-icon-mouse"></i>
          </div>
          <h4>选择节点开始配置</h4>
          <p>点击画布上的任意节点来查看和编辑其配置</p>
          <div class="help-tips">
            <div class="tip-item">
              <i class="el-icon-info"></i>
              <span>双击节点可快速编辑名称</span>
            </div>
            <div class="tip-item">
              <i class="el-icon-link"></i>
              <span>拖拽连接点可连接节点</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "DrawConfig",
  props: {
    // 是否显示抽屉
    visible: {
      type: Boolean,
      default: false
    },
    // 选中的节点
    selectedNode: {
      type: Object,
      default: null
    },
    // 当前活动标签页
    activeTab: {
      type: String,
      default: '1'
    },
    // 节点类型
    typeGet: {
      type: String,
      default: null
    },
    // 类型标题
    typeTitle: {
      type: String,
      default: '基本信息'
    },
    // 配置相关数据
    idKey: {
      type: String,
      default: ''
    },
    idName: {
      type: String,
      default: ''
    },
    dataItem: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 节点基本配置表单
      nodeForm: {
        nodeName: '',
        isUpdate: '',
        description: ''
      },
      // 更新选项
      updateOptions: [
        { label: '是', value: '1' },
        { label: '否', value: '0' }
      ]
    };
  },
  computed: {
    // 是否显示更新选项
    showUpdateOption() {
      return this.selectedNode && this.selectedNode.nodeType !== '0';
    }
  },
  watch: {
    selectedNode: {
      handler(newNode) {
        if (newNode) {
          this.initNodeForm();
        }
      },
      immediate: true
    }
  },
  methods: {
    // 初始化节点表单数据
    initNodeForm() {
      if (!this.selectedNode) return;
      
      this.nodeForm = {
        nodeName: this.selectedNode.nodeName || '',
        isUpdate: this.selectedNode.isUpdate || '',
        description: this.selectedNode.description || ''
      };
    },
    
    // 关闭抽屉
    handleClose() {
      this.$emit('close');
    },
    
    // 切换标签页
    handleTabClick(tab) {
      this.$emit('tab-change', tab);
    },
    
    // 获取节点图标
    getNodeIcon(node) {
      if (!node) return 'el-icon-setting';
      
      // 如果节点已经有logImg属性，直接使用
      if (node.logImg) {
        return node.logImg;
      }
      
      // 根据节点类型获取图标
      const iconMap = {
        '-1': 'el-icon-video-play',     // 开始节点
        '-2': 'el-icon-video-pause',    // 结束节点
        '0': 'el-icon-coin',            // 数据源
        '1': 'el-icon-files',           // 聚合
        '2': 'el-icon-link',            // 连接
        '3': 'el-icon-switch-button',   // 条件
        '4': 'el-icon-search',          // 过滤
        '5': 'el-icon-setting'          // 自定义
      };
      
      return iconMap[node.nodeType] || 'el-icon-setting';
    },
    
    // 节点名称变更
    handleNodeNameChange() {
      this.emitChange('nodeName', this.nodeForm.nodeName);
    },
    
    // 更新选项变更
    handleUpdateChange() {
      this.emitChange('isUpdate', this.nodeForm.isUpdate);
    },
    
    // 描述变更
    handleDescriptionChange() {
      this.emitChange('description', this.nodeForm.description);
    },
    
    // 发射变更事件
    emitChange(field, value) {
      this.$emit('changefiled', {
        field,
        value,
        nodeId: this.idKey,
        node: this.selectedNode
      });
    }
  }
};
</script>

<style lang="less" scoped>
.draw-config {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 12px 12px 0 0;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1300;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
  resize: vertical;
  overflow: hidden;

  &.drawer-open {
    transform: translateY(0);
  }

  .drawer-header {
    background-color: #D7A256;
    color: white;
    padding: 12px 20px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: row-resize;
    
    .drawer-title {
      font-size: 15px;
      display: flex;
      align-items: center;
      gap: 10px;
      
      i {
        font-size: 18px;
        color: white;
      }
      
      span {
        font-weight: 500;
      }
    }
    
    .drawer-actions .close-btn {
      font-size: 13px;
      height: 30px;
      padding: 0 12px;
      color: white;
      
      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }
    }
  }
  
  .drawer-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    background: #fcfaf7;
    

    
    .config-content {
      padding: 24px;
      
      .config-panel {
        .node-config-section {
          margin-bottom: 32px;
          
          .node-form {
            /deep/ .el-form-item {
              margin-bottom: 22px;
              
              .el-form-item__label {
                color: #606266;
                font-weight: 500;
              }
              
              .el-input__inner,
              .el-textarea__inner {
                border-radius: 6px;
                border: 1px solid #e4d5c7;
                
                &:focus {
                  border-color: #D7A256;
                  box-shadow: 0 0 0 2px rgba(215, 162, 86, 0.1);
                }
              }
            }
          }
        }
      }
    }
    
    .no-selection {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      min-height: 300px;
      
      .empty-state {
        text-align: center;
        color: #909399;
        
        .empty-icon {
          margin-bottom: 24px;
          
          i {
            font-size: 64px;
            color: #D7A256;
            opacity: 0.6;
          }
        }
        
        h4 {
          font-size: 18px;
          color: #606266;
          margin: 0 0 12px 0;
          font-weight: 500;
        }
        
        p {
          font-size: 14px;
          color: #909399;
          margin: 0 0 24px 0;
        }
        
        .help-tips {
          .tip-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-bottom: 8px;
            font-size: 13px;
            color: #C0C4CC;
            
            i {
              color: #D7A256;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}
</style> 