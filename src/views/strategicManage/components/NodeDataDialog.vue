<template>
  <el-dialog
    :visible.sync="visible"
    :show-close="false"
    width="80%"
    :before-close="handleClose"
    class="node-data-dialog modern-dialog"
    :custom-class="'custom-dialog'"
  >
    <!-- 自定义标题区域 -->
    <div slot="title" class="dialog-header">
      <div class="dialog-title">
        <i :class="nodeIcon"></i>
        <span class="title-text">{{ nodeName }} - 数据</span>
      </div>
      <div class="dialog-actions">
        <el-button
          type="text"
          size="small"
          @click="handleClose"
          class="close-btn"
        >
          <i class="el-icon-close"></i>
          关闭
        </el-button>
      </div>
    </div>

    <!-- 查询条件区域 -->
    <div class="search-section">
      <div class="search-conditions">
        <div v-for="(condition, index) in searchConditions" :key="index" class="search-row">
          <el-select
            v-model="condition.fieldName"
            placeholder="请选择字段"
            class="field-select"
            @change="handleFieldChange(index)"
          >
            <el-option
              v-for="field in fields"
              :key="field.fieldName"
              :label="field.fieldDesc"
              :value="field.fieldName"
            />
          </el-select>
          
          <el-select
            v-model="condition.operator"
            placeholder="请选择匹配符"
            class="operator-select"
          >
            <el-option
              v-for="op in operators"
              :key="op.value"
              :label="op.label"
              :value="op.value"
            />
          </el-select>
          
          <el-input
            v-model="condition.value"
            placeholder="请输入值"
            class="value-input"
          />
          
          <el-button
            v-if="index !== 0 && searchConditions.length > 1"
            type="text"
            icon="el-icon-delete"
            class="delete-btn"
            @click="removeCondition(index)"
          >删除</el-button>

          <!-- 将操作按钮放在第一行 -->
          <template v-if="index === 0">
            <el-button
              type="primary"
              icon="el-icon-plus"
              @click="addCondition"
              class="action-btn"
            >添加条件</el-button>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="handleSearch"
              class="action-btn"
            >查询</el-button>
            <el-button
              icon="el-icon-refresh"
              @click="resetSearch"
              class="action-btn"
            >重置</el-button>
          </template>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        border
        stripe
        v-hover
        class="modern-table"
        v-loading="loading"
      >
        <el-table-column
          v-for="field in fields"
          :key="field.fieldName"
          :prop="field.fieldName"
          :label="field.fieldDesc"
          min-width="180"
          align="center"
        />
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { getIndicatorNodeFields, getIndicatorNodeData } from '@/api/strategyMatchManage/index'

export default {
  name: 'NodeDataDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    batchNodeId: {
      type: [String, Number],
      default: ''
    },
    nodeId: {
      type: [String, Number],
      default: ''
    },
    nodeName: {
      type: String,
      default: '节点'
    },
    nodeType: {
      type: [String, Number],
      default: ''
    },
    nodeIcon: {
      type: String,
      default: 'el-icon-coin'
    }
  },
  data() {
    return {
      loading: false,
      fields: [],
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      operators: [
        { value: '=', label: '=' },
        { value: '>', label: '>' },
        { value: '>=', label: '>=' },
        { value: '<', label: '<' },
        { value: '<=', label: '<=' }
      ],
      searchConditions: [
        {
          fieldName: '',
          operator: '=',
          value: ''
        }
      ]
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initData()
      }
    }
  },
  methods: {
    async initData() {
      try {
        await this.getFields()
        await this.getData()
      } catch (error) {
        console.error('初始化数据失败：', error)
        this.$message.error('获取数据失败')
      }
    },
    async getFields() {
      this.loading = true
      try {
        const res = await getIndicatorNodeFields({
          batchNodeId: this.batchNodeId,
          indicatorNodeId: this.nodeId
        })
        if (res && Array.isArray(res)) {
          this.fields = res
        }
      } finally {
        this.loading = false
      }
    },
    async getData() {
      this.loading = true
      try {
        const res = await getIndicatorNodeData({
          param: {
            batchNodeId: this.batchNodeId,
            indicatorNodeId: this.nodeId,
            searchParamList: this.searchConditions.filter(condition => condition.fieldName && condition.value)
          },
          pageNum: this.currentPage,
          pageSize: this.pageSize
        })
        if (res) {
          this.tableData = res.list || []
          this.total = res.total || 0
        }
      } finally {
        this.loading = false
      }
    },
    handleFieldChange(index) {
      // 字段改变时可以添加一些额外的逻辑
    },
    addCondition() {
      this.searchConditions.push({
        fieldName: '',
        operator: '=',
        value: ''
      })
    },
    removeCondition(index) {
      this.searchConditions.splice(index, 1)
    },
    handleSearch() {
      this.currentPage = 1
      this.getData()
    },
    resetSearch() {
      this.searchConditions = [{
        fieldName: '',
        operator: '=',
        value: ''
      }]
      this.currentPage = 1
      this.getData()
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData()
    },
    handleClose() {
      this.searchConditions = [{
        fieldName: '',
        operator: '=',
        value: ''
      }]
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="less" scoped>
.node-data-dialog {
  /deep/ .el-dialog {
    border-radius: 8px;
    overflow: hidden;
    
    .el-dialog__header {
      margin: 0;
      padding: 0;
    }

    .el-dialog__body {
      padding: 0;
    }
  }
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background-color: #fbf6ee;
  border-bottom: 1px solid #f7ecdd;

  .dialog-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #262626;

    i {
      margin-right: 8px;
      font-size: 20px;
      color: #d7a256;
    }

    .title-text {
      font-weight: 600;
    }
  }

  .dialog-actions {
    .close-btn {
      color: #666;
      font-size: 14px;

      &:hover {
        color: #d7a256;
      }

      i {
        margin-right: 4px;
      }
    }
  }
}

.search-section {
  padding: 20px;
  background-color: #fff;
  
  .search-conditions {
    .search-row {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .field-select,
      .operator-select {
        width: 180px;
        margin-right: 16px;
      }
      
      .value-input {
        width: 200px;
        margin-right: 16px;
      }
      
      .delete-btn {
        color: #ff4d4f;
        padding: 0;
        margin-right: 16px;
        
        &:hover {
          color: #ff7875;
        }
      }

      .action-btn {
        margin-right: 16px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}

.table-container {
  background-color: #fff;
  padding: 20px;
  
  .modern-table {
    margin-bottom: 20px;
    
    &::v-deep .el-table__header-wrapper {
      th {
        background-color: #fafafa;
        color: #262626;
        font-weight: 600;

        .cell {
          font-weight: 600;
        }
      }
    }
    
    &::v-deep .el-table__row {
      &:hover {
        td {
          background-color: #f5f5f5;
        }
      }
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}

// 添加悬浮效果
.custom-dialog {
  border-radius: 8px;
  overflow: hidden;
  
  /deep/ .el-dialog__body {
    padding: 0;
  }
}
</style> 