<template>
  <div>
    <!-- 节点配置 -->
    <div class="title">节点配置</div>
    <el-form
      label-width="150px"
      label-position="right"
      :model="editForm"
      class="editUserForm"
      ref="editUserForm"
      :inline="false"
    >
      <el-form-item label="节点名称" prop="nodeName">
        <el-input
          style="width: 150px;"
          v-model="editForm.nodeName"
          :disabled="nodeObjList.nodeType !== '5' && nodeObjList.children"
          @change="blur1"
          placeholder="请输入渠道名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="是否更新数据" prop="isUpdate">
        <el-select
          v-model="editForm.isUpdate"
          clearable
          placeholder="请选择"
          filterable
          @change="blur2"
          size="large"
        >
          <el-option
            v-for="option in optionList2"
            :key="option.id"
            :label="option.name"
            :value="option.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <!-- 新增 -->
    <DtPopup
      :isShow.sync="showAdd"
      @close="closePopup()"
      title="新增分组字段"
      size="small"
      :footer="false"
    >
      <el-form
        :model="addForm"
        label-width="120px"
        ref="addUserForm"
        label-position="left"
      >
        <el-form-item label="参数名称" prop="name">
          <el-input v-model="addForm.name" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="参数字段" prop="value">
          <el-input v-model="addForm.value" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="字段类型" prop="value">
          <el-input v-model="addForm.value" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="value">
          <el-input v-model="addForm.value" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            class="dt-btn"
            plain
            :style="{ color: $store.state.layoutStore.themeObj.color }"
            @click="closePopup"
            >取消</el-button
          >
          <el-button type="primary" class="dt-btn" @click="toAddForm"
            >保存</el-button
          >
        </el-form-item>
      </el-form>
    </DtPopup>
  </div>
</template>

<script>
import ClickOutside from "vue-click-outside";
import TableToolTemp from "@/components/layouts/TableToolTemp";
import DtPopup from "@/components/layouts/DtPopup";
export default {
  name: "basic",
  components: {
    TableToolTemp,
    DtPopup
  },
  directives: {
    ClickOutside
  },
  props: {
    idKey: String,
    idName: String,
    dataItem: Object
  },
  watch: {
    dataItem(newValue, oldValue) {
      console.log(newValue, oldValue);
      this.nodeObjList = newValue;
      this.editForm.isUpdate = this.nodeObjList.isUpdate
        ? this.nodeObjList.isUpdate
        : "1";
    },
    idName(newVal, oldVal) {
      this.editForm.nodeName = newVal;
    },
    idKey(newVal, oldVal) {
      // if (newVal != oldVal) {
      //   this.changeName();
      // }
    }
  },
  data() {
    return {
      titleListPros: {
        toolTitle: "参数列表",
        toolList: [
          {
            name: "新增",
            btnCode: "",
            type: "add"
          }
        ]
      },
      titleListPros1: {
        toolTitle: "参数绑定"
      },
      mouseEnter: false,
      isActive: false,
      isSelected: false,
      editForm: {
        nodeName: "",
        isUpdate: "1"
      },
      showAdd: false,
      fileNameList: [],
      addForm: {
        name: "",
        value: ""
      },
      optionList2: [
        {
          name: "否",
          id: "0"
        },
        {
          name: "是",
          id: "1"
        }
      ]
    };
  },
  computed: {},
  created() {
    console.log(this.idName, this.dataItem);
    this.nodeObjList = this.dataItem;
    this.editForm.nodeName = this.idName;
    this.editForm.isUpdate = this.nodeObjList.isUpdate
      ? this.nodeObjList.isUpdate
      : "1";
    // if (this.nodeObjList.length > 0) {
    //   this.changeName();
    // }
  },
  methods: {
    handleEdit(row) {
      console.log(row);
      this.showEdit = true;
      this.title = "编辑";
      this.editForm.robot_id = row.robot_id;
      this.editForm.robot_type = row.robot_type;
      this.editForm.id = row.id;
    },
    blur1(val) {
      console.log(this.editForm.nodeName);
      let data = {
        name: "nodeName",
        key: this.idKey,
        value: this.editForm.nodeName
      };
      this.$emit("changefiled", data);
    },
    blur2(val) {
      console.log(val);
      let data = {
        name: "isUpdate",
        key: this.idKey,
        value: val
      };
      this.$emit("changefiled", data);
    },
    onContextmenu() {
      this.$contextmenu({
        items: [
          {
            label: "删除",
            disabled: false,
            icon: "",
            onClick: () => {
              this.deleteNode();
            }
          }
        ],
        event,
        customClass: "custom-class",
        zIndex: 9999,
        minWidth: 180
      });
    },
    // 新增切片
    async toAddForm() {
      const data = {
        ...this.addForm
      };
      //   await api.bsChannelAdd(data);
      this.showAdd = false;
      //   this.$refs["addUserForm"].resetFields();
    },
    //关闭新增弹窗
    closePopup() {
      this.showAdd = false;
      this.addForm = this.$options.data().addForm;
      this.$nextTick(() => {
        this.$refs.addUserForm.clearValidate();
      });
    },
    handleTool() {
      this.title = "新增分组字段";
      this.showAdd = true;
    }
  }
};
</script>

<style lang="less" scoped></style>
