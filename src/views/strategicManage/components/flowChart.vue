<template>
  <div class="flow-chart-container">
    <SecondaryPageHeader
      title="执行批次列表"
      subtitle="数据流转图"
      icon="el-icon-share"
      back-button-text="返回执行批次列表"
      back-route="strategyBach"
      :breadcrumb-items="breadcrumbItems"
      @back="goBack"
    />
    
    <div class="flow-content">
      <div id="flowWrap" ref="flowWrap" class="flow-canvas">
        <div id="flow" class="flow-canvas-inner">
          <!-- 节点列表 -->
          <nodeItem
            v-for="item in nodeList"
            :id="item.nodeId"
            :key="item.nodeId"
            :node="item"
            :active-node-id="activeNodeId"
            @nodeClick="handleNodeClick"
          />

          <!-- 空画布提示 -->
          <div v-if="nodeList.length === 0" class="empty-canvas">
            <div class="empty-content">
              <i class="el-icon-edit-outline empty-icon"></i>
              <h3>暂无数据流转信息</h3>
              <p>当前批次暂无数据流转信息</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 节点数据弹窗 -->
    <NodeDataDialog
      :visible.sync="showNodeDataDialog"
      :batch-node-id="$route.query.batchNodeId"
      :node-id="activeNodeId"
      :node-name="activeNodeName"
      :node-type="activeNodeType"
      :node-icon="activeNodeIcon"
    />
  </div>
</template>

<script>
import { jsPlumb } from "jsplumb";
import SecondaryPageHeader from "@/components/layouts/SecondaryPageHeader";
import nodeItem from "@/components/canvas/nodeItem.vue";
import NodeDataDialog from "./NodeDataDialog.vue";
import { getIndicatorConfig } from "@/api/template/index.js";
import panzoom from "panzoom";

export default {
  name: "FlowChart",
  components: {
    SecondaryPageHeader,
    nodeItem,
    NodeDataDialog
  },
  data() {
    return {
      jsPlumb: null,
      jsplumbSetting: {
        grid: [10, 10],
        Anchors: ["TopCenter", "RightMiddle", "BottomCenter", "LeftMiddle"],
        Container: "flow",
        Connector: [
          "Flowchart",
          { cornerRadius: 8, alwaysRespectStubs: true, stub: 10 }
        ],
        ConnectionsDetachable: false,
        DeleteEndpointsOnDetach: false,
        Endpoint: ["Dot", { radius: 5 }],
        EndpointStyle: {
          fill: "rgba(255,255,255,0)",
          outlineWidth: 1
        },
        PaintStyle: {
          stroke: "#ccc",
          strokeWidth: 2,
          outlineStroke: "transparent",
          outlineWidth: 2
        },
        HoverPaintStyle: {
          stroke: "#D7A256",
          strokeWidth: 4,
          outlineStroke: "rgba(215, 162, 86, 0.2)",
          outlineWidth: 4
        },
        Overlays: [
          [
            "Arrow",
            {
              width: 14,
              length: 14,
              location: 1,
              foldback: 0.8,
              cssClass: "connection-arrow"
            }
          ]
        ],
        RenderMode: "svg"
      },
      jsplumbSourceOptions: {
        filter: ".anchor-point, .anchor-dot, .anchor-ring",
        filterExclude: false,
        anchor: ["TopCenter", "RightMiddle", "BottomCenter", "LeftMiddle"],
        allowLoopback: false,
        maxConnections: -1
      },
      jsplumbTargetOptions: {
        filter: ".anchor-point, .anchor-dot, .anchor-ring",
        filterExclude: false,
        anchor: ["TopCenter", "RightMiddle", "BottomCenter", "LeftMiddle"],
        allowLoopback: false,
        maxConnections: -1
      },
      jsplumbConnectOptions: {
        isSource: true,
        isTarget: true,
        anchor: ["TopCenter", "RightMiddle", "BottomCenter", "LeftMiddle"],
        endpoint: "Blank",
        paintStyle: {
          stroke: "#ccc",
          strokeWidth: 2
        },
        hoverPaintStyle: {
          stroke: "#D7A256",
          strokeWidth: 4
        }
      },
      nodeList: [],
      activeNodeId: null,
      activeNodeName: '',
      activeNodeType: '',
      activeNodeIcon: '',
      panzoomInstance: null,
      showNodeDataDialog: false,
    };
  },
  computed: {
    breadcrumbItems() {
      if (this.$route.query.fromDataSpace) {
        return [
          { text: '数据空间', to: { name: 'dataSpace' } },
          { text: '策略执行情况', to: { name: 'strategyBach', query: { fromDataSpace: 1 } } },
          { text: '执行记录-批次明细', icon: 'el-icon-document', to: { name: 'bachDetail', query: { batchId: this.$route.query.batchId, fromDataSpace: 1 } } },
          { text: '数据流转图', icon: 'el-icon-share' }
        ];
      }
      return [
        { text: '执行批次列表', icon: 'el-icon-back', to: { name: 'strategyBach' } },
        { text: '执行记录-批次明细', icon: 'el-icon-document', to: { name: 'bachDetail', query: { batchId: this.$route.query.batchId } } },
        { text: '数据流转图', icon: 'el-icon-share' }
      ];
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initJsPlumb();
      this.initPanzoom();
      this.loadData();
    });
  },
  beforeDestroy() {
    if (this.panzoomInstance) {
      this.panzoomInstance.dispose();
    }
    if (this.jsPlumb) {
      this.jsPlumb.reset();
    }
  },
  methods: {
    initJsPlumb() {
      this.jsPlumb = jsPlumb.getInstance();
      if (!this.jsPlumb) return;
      this.jsPlumb.importDefaults(this.jsplumbSetting);
    },
    initPanzoom() {
      const container = document.getElementById("flow");
      if (!container) return;
      this.panzoomInstance = panzoom(container, {
        smoothScroll: false,
        bounds: true,
        zoomDoubleClickSpeed: 1,
        minZoom: 0.5,
        maxZoom: 2,
        beforeMouseDown: e => {
          // 与指标配置页面一致：按住 Ctrl 键时禁用画布拖动
          return e.ctrlKey; // 返回 true 表示忽略，false 表示允许拖动
        }
      });
      // 缩放同步jsPlumb
      this.panzoomInstance.on("zoom", e => {
        const { scale } = e.getTransform();
        if (this.jsPlumb) {
          this.jsPlumb.setZoom(scale);
        }
      });
      // 拖动/缩放后重绘连线
      this.panzoomInstance.on("pan", () => {
        if (this.jsPlumb) this.jsPlumb.repaintEverything();
      });

      // 设置鼠标样式
      const wrap = document.getElementById("flowWrap");
      if (wrap) {
        wrap.style.cursor = "grab";
        wrap.addEventListener("mousedown", () => {
          wrap.style.cursor = "grabbing";
        });
        wrap.addEventListener("mouseup", () => {
          wrap.style.cursor = "grab";
        });
        wrap.addEventListener("mouseleave", () => {
          wrap.style.cursor = "grab";
        });
      }
    },
    async loadData() {
      try {
        const { batchNodeId } = this.$route.query;
        const res = await getIndicatorConfig({ batchNodeId });
        if (res && res.nodeList) {
          this.nodeList = res.nodeList.map(n => ({ ...n, nodeId: n.id || n.nodeId }));
          await this.$nextTick();
          this.renderNodesAndLines();
        }
      } catch (err) {
        console.error(err);
        this.$message.error("加载数据失败");
      }
    },
    renderNodesAndLines() {
      if (!this.jsPlumb) return;
      this.jsPlumb.batch(() => {
        this.jsPlumb.deleteEveryConnection();
        this.nodeList.forEach(node => {
          const el = document.getElementById(node.nodeId);
          if (el) {
            el.style.left = node.positionLeft;
            el.style.top = node.positionTop;
          }
          this.jsPlumb.makeSource(node.nodeId, this.jsplumbSourceOptions);
          this.jsPlumb.makeTarget(node.nodeId, this.jsplumbTargetOptions);
        });
        // 根据 childIds 连线
        this.nodeList.forEach(targetNode => {
          if (targetNode.childIds && targetNode.childIds.length) {
            targetNode.childIds.forEach(sourceId => {
              this.jsPlumb.connect(
                {
                  source: sourceId,
                  target: targetNode.nodeId,
                  id: `${sourceId}_${targetNode.nodeId}`
                },
                this.jsplumbConnectOptions
              );
            });
          }
        });
      });
      this.jsPlumb.repaintEverything();
    },
    getNodeIcon(node) {
      if (!node) return 'el-icon-setting';
      
      // 如果节点已经有logImg属性，直接使用
      if (node.logImg) {
        return node.logImg;
      }
      
      // 根据节点类型获取图标
      const iconMap = {
        '-1': 'el-icon-video-play',     // 开始节点
        '-2': 'el-icon-video-pause',    // 结束节点
        '0': 'el-icon-coin',            // 数据源
        '1': 'el-icon-files',           // 聚合
        '2': 'el-icon-link',            // 连接
        '3': 'el-icon-switch-button',   // 条件
        '4': 'el-icon-search',          // 过滤
        '5': 'el-icon-setting'          // 自定义
      };
      
      return iconMap[node.nodeType] || 'el-icon-setting';
    },
    handleNodeClick(node) {
      if (typeof node === 'object') {
        this.activeNodeId = node.nodeId || node.id;
        this.activeNodeName = node.nodeName || node.name || '节点';
        this.activeNodeType = node.nodeType || '';
        this.activeNodeIcon = node.logImg || this.getNodeIcon(node);
      } else {
        this.activeNodeId = node;
        this.activeNodeName = '节点';
        this.activeNodeType = '';
        this.activeNodeIcon = 'el-icon-setting';
      }
      this.showNodeDataDialog = true;
    },
    goBack() {
      this.$router.push({ name: "bachDetail", query: { batchId: this.$route.query.batchId, fromDataSpace: this.$route.query.fromDataSpace } });
    }
  }
};
</script>

<style lang="less" scoped>
.flow-chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .flow-content {
    flex: 1;
    padding: 20px;
    background: #fbf6ee;
    position: relative;
    overflow: hidden;
    
    .flow-canvas {
      position: relative;
      width: 100%;
      height: 100%;
      min-height: 600px;
      background: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
      overflow: hidden;

      // 深度渐变和边框，与指标配置页面一致
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: 
          radial-gradient(ellipse at center, transparent 0%, rgba(215,162,86,0.01) 50%, rgba(215,162,86,0.02) 100%),
          radial-gradient(circle at 20% 80%, rgba(255,255,255,0.2) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(255,255,255,0.2) 0%, transparent 50%);
        pointer-events: none;
        z-index: 1;
      }

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border: 1px solid rgba(215, 162, 86, 0.04);
        border-radius: 12px;
        box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.3), inset 0 -1px 0 rgba(215, 162, 86, 0.02);
        pointer-events: none;
        z-index: 1;
      }

      .flow-canvas-inner {
        position: absolute;
        width: 2001px;
        height: 1501px;
        z-index: 2;
        transform-origin: 0 0;
        background: linear-gradient(135deg, 
          #fefdfb 0%, 
          #ffffff 25%, 
          #ffffff 50%, 
          #ffffff 75%, 
          #fefdfb 100%);

        background-image: 
          linear-gradient(to right, rgba(128, 128, 128, 0.06) 1px, transparent 1px),
          linear-gradient(to bottom, rgba(128, 128, 128, 0.06) 1px, transparent 1px),
          linear-gradient(to right, rgba(215, 162, 86, 0.03) 1px, transparent 1px),
          linear-gradient(to bottom, rgba(215, 162, 86, 0.03) 1px, transparent 1px),
          radial-gradient(circle, rgba(215, 162, 86, 0.04) 0.5px, transparent 0.5px);

        background-size: 
          100px 100%,
          100% 100px,
          20px 100%,
          100% 20px,
          20px 20px;

        background-position: 
          0 0,
          0 0,
          0 0,
          0 0,
          0 0;
      }
    }
  }
}

.empty-canvas {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 10;
  
  .empty-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 40px 32px;
    border: 1px solid rgba(128, 128, 128, 0.1);
    box-shadow: 
      0 8px 32px rgba(215, 162, 86, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
    max-width: 360px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 
        0 12px 40px rgba(215, 162, 86, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    }
    
    .empty-icon {
      font-size: 56px;
      margin-bottom: 20px;
      background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      position: relative;
      
      &::before {
        content: attr(class);
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        filter: blur(8px);
        opacity: 0.3;
        z-index: -1;
      }
    }

    h3 {
      margin: 0 0 12px 0;
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    p {
      margin: 0 0 24px 0;
      font-size: 14px;
      color: #7f8c8d;
      line-height: 1.6;
    }
  }
}

:global(.connection-label) {
  padding: 2px 4px;
  background: rgba(215, 162, 86, 0.1);
  border-radius: 2px;
  color: #D7A256;
  font-size: 12px;
}

:global(.jtk-connector) {
  z-index: 10;
}

:global(.jtk-endpoint) {
  z-index: 11;
}

:global(.jtk-overlay) {
  z-index: 12;
}
</style> 