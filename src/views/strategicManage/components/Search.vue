<template>
  <div class="form-block" :class="{ expand: foldType }">
    <el-container class="wh100 dt-menusList-wrap">
      <div>
        <el-form
          ref="form"
          :model="searchForm"
          :label-width="labelWidth"
          :inline="true"
          :label-position="position"
          v-if="hasParam"
        >
          <el-form-item>
            <div
              style="margin-top: 20px;"
              v-for="(item, index) in searchFormTemp"
              :key="index"
            >
              <el-form-item
                v-if="item.strategyId.name == 'strategyId'"
                :label="item.strategyId.label"
              >
                <el-select
                  v-model="item.strategyId.value"
                  :multiple="item.strategyId.multiple"
                  collapse-tags
                  filterable
                  clearable
                  @change="
                    handleChange(item.strategyId.name, ...arguments, index)
                  "
                  :disabled="item.strategyId.disabled"
                  :placeholder="item.strategyId.placeholder || '请选择'"
                >
                  <el-option
                    v-for="(el, idx) in item.strategyId.list"
                    :key="idx"
                    :label="el.fieldDesc"
                    :value="el.fieldName"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="item.match.name == 'match'"
                :label="item.match.label"
              >
                <el-select
                  v-model="item.match.value"
                  :multiple="item.match.multiple"
                  collapse-tags
                  filterable
                  clearable
                  @change="handleChange(item.match.name, ...arguments, index)"
                  :disabled="item.match.disabled"
                  :placeholder="item.match.placeholder || '请选择'"
                >
                  <el-option
                    v-for="(el, idx) in item.match.list"
                    :key="idx"
                    :label="el.dicItemCode"
                    :value="el.dicItemCode"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="item.filed.name == 'filed'"
                :label="item.filed.label"
              >
                <el-input
                  v-model="item.filed.value"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
              <el-form-item v-if="item.btn.name == 'btn'">
                <el-button
                  type="primary"
                  plain
                  :style="{
                    color: themeObj.color,
                    'border-color': themeObj.color
                  }"
                  @click="del(index)"
                >
                  {{ item.btn.label }}
                </el-button>
              </el-form-item>
            </div>
          </el-form-item>
          <el-form-item style="margin-top: 20px;" v-show="!foldType">
            <el-button
              type="primary"
              plain
              :style="{ color: themeObj.color, 'border-color': themeObj.color }"
              @click="addTemp()"
            >
              新增条件
            </el-button>
            <el-button
              type="primary"
              :style="{
                'background-color': themeObj.color,
                'border-color': themeObj.color
              }"
              @click="normalSearch()"
            >
              搜索
            </el-button>
            <el-button
              type="primary"
              plain
              :style="{ color: themeObj.color, 'border-color': themeObj.color }"
              @click="normalResetQuery()"
            >
              重置
            </el-button>
            <el-button
              type="primary"
              icon="el-icon-download"
              @click="$emit('export')"
            >
              导出
            </el-button>
          </el-form-item>
          <!-- <el-form-item
            v-for="(item, index) in searchFormTemp"
            :key="index"
            :label="item.label"
          >
            <el-row>
              <el-col span="8">
                <el-input
                  v-model="searchForm.param[item.name]"
                  :placeholder="item.placeholder || '请输入'"
                ></el-input>
              </el-col>
              <el-col span="8">
                <el-select
                  v-model="searchForm.param[item.name]"
                  :multiple="item.multiple"
                  collapse-tags
                  filterable
                  clearable
                  @change="handleChange(item.name, ...arguments)"
                  :disabled="item.disabled"
                  :placeholder="item.placeholder || '请选择'"
                >
                  <el-option
                    v-for="(el, idx) in item.list"
                    :key="idx"
                    :label="el.dicItemName"
                    :value="el.dicItemCode"
                  ></el-option>
                </el-select>
              </el-col>
              <el-col span="8">
                <el-button
                  v-if="item.type='btn'"
                  type="primary"
                  plain
                  :style="{ color: themeObj.color, 'border-color': themeObj.color }"
                  @click="normalResetQuery()"
                >
                  删除
                </el-button>
              </el-col>
            </el-row>
          </el-form-item> -->
        </el-form>
      </div>
      <el-aside :width="sideWidth" class="dt-menusList-tree" v-show="foldType">
        <div
          style="align-items: center;justify-content: center;display: flex;height: 95%;margin-top:0%;border-left: 1px dotted #ccc;"
        >
          <el-button
            type="primary"
            :style="{
              'background-color': themeObj.color,
              'border-color': themeObj.color
            }"
            @click="normalSearch()"
          >
            搜索
          </el-button>
          <el-button
            type="primary"
            plain
            :style="{ color: themeObj.color, 'border-color': themeObj.color }"
            @click="normalResetQuery()"
          >
            重置
          </el-button>
          <el-button
            type="primary"
            plain
            :style="{ color: themeObj.color, 'border-color': themeObj.color }"
            @click="addTemp()"
          >
            新增
          </el-button>
          <el-button
            type="primary"
            plain
            :style="{ color: themeObj.color, 'border-color': themeObj.color }"
            @click="changeExpand()"
          >
            {{ expand ? "精确搜索" : "精简搜索" }}
          </el-button>
        </div>
      </el-aside>
    </el-container>
  </div>
</template>
<script>
export default {
  name: "search",
  props: {
    searchForm: {
      type: Object,
      default: function() {
        return {};
      }
    },
    searchFormTemp: {
      type: Array,
      default: function() {
        return [];
      }
    },
    labelWidth: {
      type: String,
      default: "auto"
    },
    foldType: {
      type: Boolean,
      default: false
    },
    isSearch: {
      type: Boolean,
      default: true
    },
    isReset: {
      type: Boolean,
      default: true
    },
    position: {
      type: String,
      default: "left"
    }
  },
  data() {
    return {
      sideWidth: "300px",
      expand: true,
      hasParam: false //判断当前传入的是两层对象
    };
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    }
  },
  created() {
    this.hasParam = this.searchForm.hasOwnProperty("param");
    console.log(this.hasParam);
  },
  mounted() {},
  methods: {
    normalSearch() {
      // this.handleSearchForm();
      this.$emit("normalSearch", this.searchFormTemp);
    },
    normalResetQuery() {
      // this.$refs.form.resetFields();
      for (let item of this.searchFormTemp) {
        item.match.value = "";
        item.strategyId.value = "";
        item.filed.value = "";
      }
      this.$emit("normalResetQuery", this.searchFormTemp);
    },
    del(val) {
      this.$emit("del", val);
    },
    addTemp() {
      this.$emit("addTemp", 1);
    },
    handleSearchForm() {
      let tempObj = {};
      // for (let item of this.searchFormTemp) {
      //   if (item.type == "doubleDate") {
      //     for (let li of item.options) {
      //       tempObj[li.name] = li.value;
      //     }
      //     break;
      //   }
      // }
      if (this.hasParam) {
        Object.assign(this.searchForm.param, tempObj);
      }
    },
    changeExpand() {
      this.searchFormTemp.forEach(item => {
        item.tempShow = this.expand;
      });
      this.expand = !this.expand;
    },
    handleChange(name, val, index) {
      this.$emit("handleChange", name, true, val, index);
    }
  }
};
</script>
<style lang="less">
.form-block {
  background: #fff;
  padding-top: 10px;
  padding-left: 11px;

  .el-input--medium {
    width: 215px !important;
  }

  .el-input__inner,
  .el-button--medium {
    border-radius: 6px;
  }

  .el-form-item__label {
    color: #333;
  }

  .el-form-item {
    margin-bottom: 13px;
  }

  .dt-cascader {
    .el-input--medium {
      width: 600px !important;
    }
  }
  .ml0 {
    .el-form-item__label-wrap {
      margin-left: 0 !important;
    }
  }
}
</style>
