<template>
  <div class="strateg-match-list">
    <!-- 使用通用表格组件 -->
    <UniversalTable
      title="策略管理"
      subtitle="管理和配置算法策略，支持策略的创建、编辑和配置"
      title-icon="el-icon-s-management"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :actions="tableActions"
      :search-form-config="searchFormTemp"
      :search-params="initParam"
      :pagination-data="initParam"
      :total="total"
      add-button-text="新增策略"
      empty-title="暂无策略数据"
      empty-description="点击上方新增策略按钮开始创建"
      @search="normalSearch"
      @reset="normalResetQuery"
      @add="handleAdd"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @action-click="handleAction"
    >
      <!-- 自定义列内容插槽 -->
      <template #bizCode="{ row }">
        <div class="code-cell">
          <div class="code-icon">
            <i class="el-icon-document-copy"></i>
          </div>
          <span class="code-text">{{ row.bizCode }}</span>
        </div>
      </template>

      <template #name="{ row }">
        <div class="name-cell">
          <span class="name-text">{{ row.name }}</span>
        </div>
      </template>

      <template #remark="{ row }">
        <div class="remark-cell">
          <el-tooltip
            v-if="row.remark"
            :content="row.remark"
            :enterable="false"
            effect="dark"
            placement="top"
            popper-class="modern-tooltip"
          >
            <span class="remark-text">{{ row.remark }}</span>
          </el-tooltip>
          <span v-else class="no-remark">
            <i class="el-icon-info"></i>
            暂无备注
          </span>
        </div>
      </template>

      <template #updateTime="{ row }">
        <div class="time-cell">
          <i class="el-icon-time"></i>
          <span>{{ row.updateTime }}</span>
        </div>
      </template>

      <template #updateId="{ row }">
        <div class="user-cell">
          <div class="user-avatar">
            <i class="el-icon-user"></i>
          </div>
          <span>{{ row.updateId | getNickName("row.updateId") }}</span>
        </div>
      </template>
    </UniversalTable>

    <!-- 新增/编辑策略弹窗 -->
    <UniversalFormDialog
      v-model="showEditDialog"
      :form-data="editForm"
      :form-fields="strategyFormFields"
      :form-rules="editRules"
      :is-edit="editForm.isEdit"
      :loading="saveLoading"
      add-title="新增策略"
      edit-title="编辑策略"
      @confirm="handleSaveStrategy"
      @cancel="handleCancel"
      @close="handleDialogClose"
    />

    <!-- 删除确认弹窗 -->
    <ConfirmDialog
      ref="confirmDialog"
      title="确认删除"
      message="删除后无法恢复，请确认是否删除该策略？"
      icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
      @confirm="confirmUpdate"
    />
  </div>
</template>
<script>
import { algostrategyPage, algostrategyDel, algoStrategyAdd, algoStrategyUpdate, algoStrategyFind } from "@/api/template/index.js";
import { getDicItemList } from "@/config/tool";
import { baseComponent } from "@/utils/common";
import { validate, validateAlls } from "@/config/validation";
import UniversalTable from "@/components/layouts/UniversalTable.vue";
import UniversalFormDialog from "@/components/layouts/UniversalFormDialog.vue";
import ConfirmDialog from "@/components/layouts/ConfirmDialog.vue";

export default {
  name: "strategyMatchList",
  mixins: [baseComponent],
  components: {
    UniversalTable,
    UniversalFormDialog,
    ConfirmDialog
  },
  data() {
    return {
      toolListProps: {
        toolTitle: "策略列表",
        toolList: [
          {
            name: "新增",
            btnCode: "",
            type: "add"
          }
        ]
      },
      searchFormTemp: [
        {
          label: "策略名称",
          name: "name",
          type: "input",
          placeholder: "请输入策略名称"
        }
      ],
      rules: {
        name: [
          {
            required: true,
            validator: validate,
            trigger: "blur",
            min: 1,
            max: 30,
            regax: [
              {
                message: "请输入30个字符以内不能包含特殊字符",
                ruleFormat: /^[a-zA-Z0-9\u4e00-\u9fa5]+$/i
              }
            ]
          }
        ]
      },
      tableData: [],
      initParam: {
        param: {
          name: ""
        },
        pageSize: 10,
        pageNum: 1
      },
      total: 0,
      delStrategyId: "",
      loading: false,
      // 新增/编辑弹窗相关
      showEditDialog: false,
      saveLoading: false,
      editForm: {
        strategyId: "",
        bizCode: "",
        name: "",
        remark: "",
        isEdit: false
      },
      editRules: {
        bizCode: [
          { required: true, message: "请输入策略编码", trigger: "blur" },
          { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" }
        ],
        name: [
          { required: true, message: "请输入策略名称", trigger: "blur" },
          { min: 1, max: 100, message: "长度在 1 到 100 个字符", trigger: "blur" }
        ]
      },
      // 表格列配置
      tableColumns: [
        {
          prop: 'bizCode',
          label: '策略编码',
          width: 150,
          align: 'center'
        },
        {
          prop: 'name',
          label: '策略名称',
          width: 250,
          align: 'center'
        },
        {
          prop: 'remark',
          label: '备注',
          minWidth: 200,
          align: 'center'
        },
        {
          prop: 'updateTime',
          label: '修改时间',
          width: 205,
          align: 'center'
        },
        {
          prop: 'updateId',
          label: '最后一次修改人',
          width: 150,
          align: 'center'
        }
      ],
      // 表格操作按钮配置
      tableActions: [
        {
          key: 'config',
          label: '配置',
          icon: 'el-icon-setting',
          class: 'config-btn'
        },
        {
          key: 'edit',
          label: '编辑',
          icon: 'el-icon-edit',
          class: 'edit-btn'
        },
        {
          key: 'delete',
          label: '删除',
          icon: 'el-icon-delete',
          class: 'delete-btn'
        }
      ],
      // 表单字段配置
      strategyFormFields: [
        {
          type: 'input',
          prop: 'bizCode',
          label: '策略编码',
          placeholder: '请输入策略编码',
          maxlength: 50,
          disabledOnEdit: true
        },
        {
          type: 'input',
          prop: 'name',
          label: '策略名称',
          placeholder: '请输入策略名称',
          maxlength: 100
        },
        {
          type: 'textarea',
          prop: 'remark',
          label: '备注',
          placeholder: '请输入备注信息',
          maxlength: 200,
          showWordLimit: true
        }
      ]
    };
  },
  created() {
    this.initData();
  },
  methods: {
    //初始化数据
    async initData() {
      //await this.getDictList();
      this.initList();
    },
    async initList() {
      this.loading = true;
      try {
        let res = await algostrategyPage(this.initParam);
        if (!res) {
          return;
        }
        if (res.list && res.list.length > 0) {
          this.tableData = res.list;
          this.initParam.pageNum = res.pageNum;
          this.initParam.pageSize = res.pageSize;
          this.total = Number(res.total);
        } else {
          this.tableData = [];
          this.total = Number(res.total);
        }
      } finally {
        this.loading = false;
      }
    },
    handleTool(item) {
      if (item.type == "add") {
        this.$router.push({
          name: "stryUpdate",
          query: {
            type: item.type
          }
        });
      }
    },
    // 处理新增按钮
    handleAdd() {
      this.resetEditForm();
      this.editForm.isEdit = false;
      this.showEditDialog = true;
    },
    // 处理表格操作按钮点击
    handleAction(actionData) {
      const { action, row } = actionData;
      switch (action) {
        case 'config':
          this.handleSourceConfig(row);
          break;
        case 'edit':
          this.handleUpdate(row);
          break;
        case 'delete':
          this.handleDel(row);
          break;
        default:
          break;
      }
    },
    handleUpdate(row) {
      this.handleEdit(row);
    },
    // 编辑策略
    async handleEdit(row) {
      this.resetEditForm();
      this.editForm = {
        strategyId: row.strategyId,
        bizCode: row.bizCode,
        name: row.name,
        remark: row.remark || "",
        isEdit: true
      };
      this.showEditDialog = true;
    },
    // 重置表单
    resetEditForm() {
      this.editForm = {
        strategyId: "",
        bizCode: "",
        name: "",
        remark: "",
        isEdit: false
      };
      // 使用nextTick确保DOM更新后再清除验证状态
      this.$nextTick(() => {
        if (this.$refs.editFormRef) {
          this.$refs.editFormRef.clearValidate();
        }
      });
    },
    // 对话框关闭时的处理
    handleDialogClose() {
      this.resetEditForm();
    },
    // 取消按钮处理
    handleCancel() {
      this.showEditDialog = false;
      this.resetEditForm();
    },
    // 保存策略
    async handleSaveStrategy(data) {
      try {
        this.saveLoading = true;
        
        const apiData = {
          bizCode: data.formData.bizCode,
          name: data.formData.name,
          remark: data.formData.remark
        };
        
        if (data.isEdit) {
          apiData.strategyId = data.formData.strategyId;
        }
        
        // 调用后台API
        let res = data.isEdit ? 
          await algoStrategyUpdate(apiData) : 
          await algoStrategyAdd(apiData);
        
        this.$message.success(data.isEdit ? '更新成功' : '新增成功');
        this.showEditDialog = false;
        this.initList(); // 刷新列表
        
      } catch (error) {
        console.error('保存失败:', error);
      } finally {
        this.saveLoading = false;
      }
    },

    handleDel(row) {
      this.delStrategyId = row.strategyId;
      this.$refs.confirmDialog.show();
    },
          async confirmUpdate() {
        let res = await algostrategyDel({ strategyId: this.delStrategyId });
        if (!res) {
          return;
        }
        this.initList();
        this.$refs.confirmDialog.hide();
      },
    normalSearch(data) {
      this.initParam = data;
      this.initList();
    },
    normalResetQuery() {
      this.initParam.pageNum = 1;
      this.initParam.pageSize = 10;
      this.initParam.param = {
        name: ""
      };
      this.initList();
    },
    handleView(row) {
      this.setCacheArr("del");
      this.handleRoute(row, "sourceMatchFieldConfigView");
    },
    handleStart(row) {
      this.$store.commit("layoutStore/setCacheArr", {
        status: "del",
        routeName: "executedRecordPage"
      });
      this.handleRoute(row, "executedRecordPage");
    },
    handleSourceConfig(row) {
      this.$router.push({
        name: "confiGuration",
        query: {
          strategyId: row.strategyId
        }
      });
    },
    handleRoute(row, name) {
      this.setQueryParam(this.initParam, "strategyMatchParam");
      this.$store.commit("strategyMatchManage/setStrategyMatchObj", row);
      this.$router.push({
        name: name,
        query: {
          strategyId: row.strategyId
        }
      });
    },
    setCacheArr(type) {
      let arr = [
        "baseInfoConfig",
        "strategyExecutedRecordList",
        "strategyMatchIndicatorListView",
        "sourceAssignFieldList",
        "sourceMatchFieldList",
        "priorityConfigList"
      ];
      this._.each(arr, item => {
        this.$store.commit("layoutStore/setCacheArr", {
          status: type,
          routeName: item
        });
      });
    },
    // 获取字典/标签/分组
    // async getDictList() {
    //   let res = await getDicItemList("sct.match.rule");
    //   this.setSearchFormTemp("matchRule", res);
    //   res = await getDicItemList("sct.source.config.status");
    //   this.setSearchFormTemp("remark", res);
    //   this.setSearchFormTemp("indicatorConfigStatus", res);
    //   res = await getDicItemList("gen.yesorno.num");
    //   this.setSearchFormTemp("status", res);
    // }
  }
};
</script>

<style lang="less" scoped>
.strateg-match-list {
  // 表格单元格样式 - 自定义插槽样式
  .code-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    .code-icon {
      width: 24px;
      height: 24px;
      background: rgba(215, 162, 86, 0.1);
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        color: #D7A256;
        font-size: 12px;
      }
    }

    .code-text {
      font-family: 'Courier New', monospace;
      font-weight: 500;
      color: #2c3e50;
      font-size: 13px;
    }
  }

  .name-cell {
    .name-text {
      font-weight: 500;
      color: #2c3e50;
    }
  }

  .remark-cell {
    .remark-text {
      color: #606266;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
    }

    .no-remark {
      color: #c0c4cc;
      font-style: italic;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;

      i {
        font-size: 12px;
      }
    }
  }

  .time-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    color: #606266;
    font-size: 13px;

    i {
      color: #D7A256;
      font-size: 14px;
    }
  }

  .user-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #606266;
    font-size: 13px;

    .user-avatar {
      width: 24px;
      height: 24px;
      background: rgba(215, 162, 86, 0.1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        color: #D7A256;
        font-size: 12px;
      }
    }
  }


}

// 全局样式
/deep/ .modern-tooltip {
  background: #2c3e50 !important;
  color: white !important;
  border-radius: 8px !important;
  padding: 8px 12px !important;
  font-size: 12px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}
</style>
