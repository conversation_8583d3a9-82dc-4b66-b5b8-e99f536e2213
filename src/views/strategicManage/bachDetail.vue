<template>
  <div class="strateg-match-list">
    <!-- 页面头部和导航区域 -->
    <SecondaryPageHeader
          title="执行批次列表"
          subtitle="执行批次列表管理"
          icon="el-icon-files"
          back-button-text="执行记录-批次明细"
          back-route="strategyBach"
          :breadcrumb-items="breadcrumbItems"
          @back="goBack"
        />
    <SearchForm
      style="padding:24px;border-top: 1px solid #f7ecdd;
       border-bottom: 1px solid #f7ecdd;"
      :searchForm="initParam"
      :searchFormTemp="searchFormTemp"
      :hasParam="false"
      @normalSearch="normalSearch"
      @normalResetQuery="normalResetQuery"
    ></SearchForm>
    <div class="table-section">
      <div class="table-wrapper">
        <el-table
          :data="tableData"
          stripe
               v-hover
              class="modern-table"
          style="width: 100%; font-size: 14px;"
        >
          <el-table-column
            align="center"
            prop="bizCode"
            width="150px"
            label="批次编码"
          >
            <template slot-scope="scope">
              <div class="version-cell">
                    <div class="version-icon">
                      <i class="el-icon-document"></i>
                    </div>
                    <span class="version-text">{{ scope.row.bizCode }}</span>
                  </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            prop="nodeType"
            width="250px"
            label="类型"
          >
            <template slot-scope="scope">
              <el-tag v-if="scope.row.nodeType == '1'" :type="getTypeTagType(scope.row.type)" class="current-tag"> <i :class="getTypeIcon(scope.row.type)"></i>数据源</el-tag>
              <el-tag v-if="scope.row.nodeType == '2'" :type="getTypeTagType(scope.row.type)"  class="draft-tag"> <i :class="getTypeIcon(scope.row.type)"></i>计算指标</el-tag>
              <el-tag v-if="scope.row.nodeType == '3'" :type="getTypeTagType(scope.row.type)" class="history-tag"> <i :class="getTypeIcon(scope.row.type)"></i>结果指标</el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="nodeName" label="名称">
            <template slot-scope="scope">
              <span>{{ scope.row.nodeName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="nodeValue" label="执行节点编码">
            <template slot-scope="scope">
              <!-- <span>{{ scope.row.nodeValue }}</span> -->
              <div class="version-cell">
                    <div class="version-icon">
                      <i class="el-icon-document"></i>
                    </div>
                    <span class="version-text">{{ scope.row.nodeValue }}</span>
                  </div>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="executeTime" label="执行时间">
            <template slot-scope="scope">
              <div class="time-cell">
                <i class="el-icon-time"></i>
                <span>{{ scope.row.executeTime }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="nodeName" label="执行参数">
            <template slot-scope="scope">
              <div
                v-if="scope.row.params && scope.row.params.length > 0"
                v-for="(item, index) in scope.row.params"
                :key="index"
              >
                <span> {{ item }} </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="executeStatus" label="执行状态">
            <template slot-scope="scope">
              <span v-if="scope.row.executeStatus == 0">未执行</span>
              <span v-else-if="scope.row.executeStatus == 1">执行中</span>
              <span v-else-if="scope.row.executeStatus == 2">执行完成</span>
              <el-tooltip v-else-if="scope.row.executeStatus == 3" :content="scope.row.failMessage || '无失败原因'" effect="dark" placement="top">
                <span style="color:#f56c6c;cursor:pointer;">执行失败</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="finishTime" label="完成时间">
            <template slot-scope="scope">
              <div class="time-cell">
                <i class="el-icon-time"></i>
                <span>{{ scope.row.finishTime }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            header-align="center"
            label="操作"
            width="200"
          >
            <template slot-scope="scope">
              <div class="action-buttons">
                <el-button
                  size="mini"
                  plain
                  @click="handleSourceConfig(scope.row)"
                  class="action-btn config-btn"
                >
                  <i class="el-icon-setting"></i>
                  查看结果
                </el-button>
                <el-button
                  v-if="scope.row.nodeType === '2' || scope.row.nodeType === '3'"
                  size="mini"
                  plain
                  @click="handleViewFlow(scope.row)"
                  class="action-btn config-btn"
                  :class="{'flow-btn-disabled': scope.row.executeStatus != 2}"
                  :disabled="scope.row.executeStatus != 2"
                >
                  <i class="el-icon-share"></i>
                  数据流转
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      </div>
  </div>
</template>
<script>
import {
  algostrategybatchnodePage,
  algoStrategyBatchNodeRe
} from "@/api/template/index.js";

import { getDicItemList } from "@/config/tool";
import { baseComponent } from "@/utils/common";
import SecondaryPageHeader from "@/components/layouts/SecondaryPageHeader";

export default {
  name: "bachDetail",
  mixins: [baseComponent],
  components: {
    SecondaryPageHeader,
  },
  data() {
    return {
      toolListProps: {
        toolTitle: "执行批次明细列表"
        // toolList: [
        //   {
        //     name: "新增",
        //     btnCode: "",
        //     type: "add"
        //   }
        // ]
      },
      searchFormTemp: [
        {
          label: "批次编码",
          name: "bizCode",
          type: "input",
          placeholder: "请输入批次编码"
        },
        {
          label: "备注",
          name: "remark",
          type: "input"
        }
      ],
      tableData: [],
      initParam: {
        batchId: "",
        bizCode: ""
        // remark: ""
      },
      total: 0,
      showPopup: false,
      showCopyPopup: false,
      delStrategyId: ""
    };
  },
  created() {
    this.initData();
  },
  methods: {
    //初始化数据
    async initData() {
      // await this.getDictList();
      this.initList();
    },
    async initList() {
      this.initParam.batchId = this.$route.query.batchId;
      // this.initParam.param.bizCode = this.$route.query.bizCode;
      let res = await algostrategybatchnodePage(this.initParam);
      if (!res) {
        return;
      }
      if (res && res.length > 0) {
        this.tableData = res;
      } else {
        this.tableData = [];
      }
      // this.tableData = res;
      // this.initParam.pageNum = res.pageNum;
      // this.initParam.pageSize = res.pageSize;
      // this.total = res.total ? Number(res.total) : 0;
    },
     // 返回指标管理
     goBack() {
      this.$router.push({ name: 'strategyBach' });
    },
    handleTool(item) {
      if (item.type == "add") {
        this.$router.push({
          name: "strategyMatchUpdate",
          query: {
            type: item.type
          }
        });
      }
    },
    normalSearch(data) {
      this.initParam = data;
      this.initList();
    },
    normalResetQuery() {
      // this.initParam.pageNum = 1;
      // this.initParam.pageSize = 10;
      this.initParam = {
        batchId: "",
        bizCode: ""
        // remark: ""
      };
      this.initList();
    },
    // 获取类型标签类型
    getTypeTagType(type) {
      const typeMap = {
        1: 'success',
        2: 'warning', 
        3: 'info'
      };
      return typeMap[type] || 'info';
    },

    // 获取类型标签样式类
    getTypeTagClass(type) {
      const classMap = {
        1: 'current-tag',
        2: 'draft-tag',
        3: 'history-tag'
      };
      return classMap[type] || 'history-tag';
    },

    // 获取类型图标
    getTypeIcon(type) {
      const iconMap = {
        1: 'el-icon-check',
        2: 'el-icon-edit-outline',
        3: 'el-icon-time'
      };
      return iconMap[type] || 'el-icon-time';
    },
    handleUpdate(row) {
      this.$store.commit("strategyMatchManage/setStrategyMatchObj", row);
      this.$router.push({
        name: "strategyMatchUpdate",
        query: {
          type: "edit",
          bizCode: row.bizCode
        }
      });
    },
    handleDel(row) {
      // this.showPopup = true;
      this.confirmUpdate(row.bizCode);
    },
    async confirmUpdate(bizCode) {
      let res = await algoStrategyBatchNodeRe({ bizCode: bizCode });
      if (!res) {
        return;
      }
      this.initList();
    },
    handleSourceConfig(row) {
      // this.handleRoute(row, "sourceDetail");
      this.$router.push({
        name: "sourceDetail",
        query: {
          nodeType: row.nodeType,
          batchId: this.$route.query.batchId,
          nodeValue: row.nodeValue,
          fromDataSpace: this.$route.query.fromDataSpace
        }
      });
    },
    handleIndicatorConfig(row) {
      this.handleRoute(row, "strategyMatchIndicatorList");
    },
    handleRoute(row, name) {
      this.setQueryParam(this.initParam, "strategyMatchParam");
      this.$store.commit("strategyMatchManage/setStrategyMatchObj", row);
      this.$router.push({
        name: name,
        query: {
          bizCode: row.bizCode
        }
      });
    },
    setCacheArr(type) {
      let arr = [
        "baseInfoConfig",
        "strategyExecutedRecordList",
        "strategyMatchIndicatorListView",
        "sourceAssignFieldList",
        "sourceMatchFieldList",
        "priorityConfigList"
      ];
      this._.each(arr, item => {
        this.$store.commit("layoutStore/setCacheArr", {
          status: type,
          routeName: item
        });
      });
    },
    // 获取字典/标签/分组
    async getDictList() {
      let res = await getDicItemList("sct.match.rule");
      this.setSearchFormTemp("matchRule", res);
      res = await getDicItemList("sct.source.config.status");
      this.setSearchFormTemp("sourceConfigStatus", res);
      this.setSearchFormTemp("indicatorConfigStatus", res);
      res = await getDicItemList("gen.yesorno.num");
      this.setSearchFormTemp("status", res);
    },
    handleViewFlow(row) {
      this.$router.push({
        name: "flowChart",
        query: {
          batchNodeId: row.batchNodeId,
          batchId: row.batchId || this.$route.query.batchId,
          fromDataSpace: this.$route.query.fromDataSpace
        }
      });
    },
  },
  computed: {
    breadcrumbItems() {
      if (this.$route.query.fromDataSpace) {
        return [
          { text: '数据空间', to: { name: 'dataSpace' } },
          { text: '策略执行情况', to: { name: 'strategyBach', query: { fromDataSpace: 1 } } },
          { text: '执行记录-批次明细' }
        ];
      }
      return [
        { text: '执行批次列表', to: { name: 'strategyBach' } },
        { text: '执行记录-批次明细' }
      ];
    }
  }
};
</script>

<style lang="less" scoped>
 // 表格区域
 .table-section {
        .table-wrapper {
          .modern-table {
            /deep/ .el-table__header-wrapper {
              .el-table__header {
                th {
                  color: #2c3e50;
                  font-weight: 600;
                  font-size: 14px;
                }
              }
            }

            /deep/ .el-table__body-wrapper {
              .el-table__row {
                transition: all 0.3s ease;

                &:hover {
                  background: rgba(215, 162, 86, 0.05) !important;
                  transform: translateY(-1px);
                  box-shadow: 0 2px 8px rgba(215, 162, 86, 0.1);
                }

                td {
                  border-bottom: 1px solid #f7ecdd;
                }
              }
            }

            /deep/ .el-table--striped .el-table__body tr.el-table__row--striped td {
              background: #fefdfb;
            }
          }

          // 表格单元格样式
          .version-cell {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;

            .version-icon {
              width: 24px;
              height: 24px;
              background: rgba(215, 162, 86, 0.1);
              border-radius: 6px;
              display: flex;
              align-items: center;
              justify-content: center;

              i {
                color: #D7A256;
                font-size: 12px;
              }
            }

            .version-text {
              font-family: 'Courier New', monospace;
              font-weight: 500;
              color: #2c3e50;
              font-size: 13px;
            }
          }

          .time-cell {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            color: #7f8c8d;
            font-size: 13px;

            i {
              color: #c0c4cc;
            }
          }

          .user-cell {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;

            .user-avatar {
              width: 20px;
  height: 20px;
              background: rgba(215, 162, 86, 0.1);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;

              i {
                color: #D7A256;
                font-size: 10px;
              }
            }
          }

          .remark-cell {
            .remark-text {
  display: block;
              max-width: 180px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
              color: #2c3e50;
            }

            .empty-remark {
              color: #c0c4cc;
              font-style: italic;
              font-size: 12px;
            }
          }

          // 类型标签样式
          .current-tag {
            background: rgba(103, 194, 58, 0.1);
            color: #67c23a;
            border: 1px solid rgba(103, 194, 58, 0.3);

            i {
              margin-right: 4px;
            }
          }

          .draft-tag {
            background: rgba(64, 158, 255, 0.1);
            color: #409eff;
            border: 1px solid rgba(64, 158, 255, 0.3);

            i {
              margin-right: 4px;
            }
          }

          .history-tag {
            background: rgba(144, 147, 153, 0.1);
            color: #909399;
            border: 1px solid rgba(144, 147, 153, 0.3);

            i {
              margin-right: 4px;
            }
          }

          // 操作按钮样式
          .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;

            .action-btn {
              font-size: 11px;
              padding: 4px 8px;
              border-radius: 4px;
              font-weight: 500;
              min-width: 56px;
              height: 28px;
              transition: all 0.3s ease;

              i {
                margin-right: 3px;
                font-size: 10px;
              }

              &.config-btn, &.edit-btn {
                background: rgba(215, 162, 86, 0.1);
                border: 1px solid rgba(215, 162, 86, 0.3) !important;
                color: #D7A256;

                &:hover {
                  background: #D7A256 !important;
                  color: white !important;
                  border-color: #D7A256 !important;
                  transform: translateY(-1px);
                  box-shadow: 0 2px 8px rgba(215, 162, 86, 0.3);
                }
              }

              &.delete-btn {
                background: rgba(245, 108, 108, 0.1);
                border: 1px solid rgba(245, 108, 108, 0.3) !important;
                color: #f56c6c;

                &:hover {
                  background: #f56c6c !important;
                  color: white !important;
                  border-color: #f56c6c !important;
                  transform: translateY(-1px);
                  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
                }
              }
            }
          }
        }

        // 空状态样式
        .empty-state {
          padding: 80px 20px;
          text-align: center;

          .empty-content {
            .empty-icon {
              width: 80px;
              height: 80px;
              margin: 0 auto 24px;
              background: rgba(215, 162, 86, 0.1);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;

              i {
                font-size: 36px;
                color: rgba(215, 162, 86, 0.6);
              }
            }

            h3 {
              margin: 0 0 12px 0;
              font-size: 18px;
              font-weight: 600;
              color: #2c3e50;
            }

            p {
              margin: 0;
              color: #7f8c8d;
              font-size: 14px;
              line-height: 1.5;
            }
          }
        }
  }

.flow-btn-disabled {
  color: #999 !important;
  border-color: #dcdfe6 !important;
  background-color: #f5f7fa !important;
  
  &:hover {
    color: #999 !important;
    border-color: #dcdfe6 !important;
    background-color: #f5f7fa !important;
  }
  
  i {
    color: #999 !important;
  }
}
</style>
