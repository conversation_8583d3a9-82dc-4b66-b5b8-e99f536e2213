<template>
  <div class="strateg-match-list">
    <!-- <TableToolTemp
      :toolListProps="toolListProps"
      @handleTool="handleTool"
    ></TableToolTemp> -->
    <!-- <SearchForm
      :searchForm="initParam"
      :labelWidth="'120px'"
      :searchFormTemp="searchFormTemp"
      @normalSearch="normalSearch"
      @normalResetQuery="normalResetQuery"
    ></SearchForm> -->
    <UniversalTable
      title="执行批次列表"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :actions="tableActions"
      :search-form-config="searchFormTemp"
      :search-params="initParam"
      :pagination-data="initParam"
      :showAddButton="false"
      :total="total"
      empty-title="暂无批次数据"
      empty-description="暂无数据，请调整筛选条件"
      :breadcrumb-items="breadcrumbItems"
      @search="normalSearch"
      @reset="normalResetQuery"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @action-click="handleAction"
    >
      <!-- 批次编码插槽 -->
      <template #bizCode="{ row }">
        <div class="code-cell">
          <div class="code-icon">
            <i class="el-icon-document"></i>
          </div>
          <span class="code-text">{{ row.bizCode }}</span>
        </div>
      </template>
      <!-- 数据空间编码插槽 -->
      <template #dataSpaceCode="{ row }">
        <div class="code-cell">
          <div class="code-icon">
            <i class="el-icon-coin"></i>
          </div>
          <span class="code-text">{{ row.dataSpaceCode }}</span>
        </div>
      </template>
      <!-- 执行时间插槽 -->
      <template #executeTime="{ row }">
        <div class="code-cell">
          <div class="code-icon">
            <i class="el-icon-time"></i>
          </div>
          <span class="code-text">{{ row.executeTime }}</span>
        </div>
      </template>
      <!-- 完成时间插槽 -->
      <template #finishTime="{ row }">
        <div class="code-cell">
          <div class="code-icon">
            <i class="el-icon-date"></i>
          </div>
          <span class="code-text">{{ row.finishTime }}</span>
        </div>
      </template>
      <!-- 自定义列内容插槽 -->
      <template #executeStatus="{ row }">
        <span v-if="row.executeStatus == 0">未执行</span>
        <span v-else-if="row.executeStatus == 1">执行中</span>
        <span v-else-if="row.executeStatus == 2">执行完成</span>
        <span v-else-if="row.executeStatus == 3">执行失败</span>
      </template>
      <template #params="{ row }">
        <div v-if="row.params && row.params.length > 0">
          <div v-for="(item, index) in row.params" :key="index">
            <span>{{ item }}</span>
          </div>
        </div>
      </template>
    </UniversalTable>
    <!-- <Pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :pageData="initParam"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
    ></Pagination> -->
    <DtPopup
      :isShow.sync="showPopup"
      @close="showPopup = false"
      @confirm="confirmUpdate"
      :isSmall="true"
    >
      <div class="popup-text">
        删除后无法恢复，请确认是否删除？
      </div>
    </DtPopup>
  </div>
</template>
<script>
import { algostrategybatchPage } from "@/api/template/index.js";
import { getDicItemList } from "@/config/tool";
import { baseComponent } from "@/utils/common";
import { validate, validateAlls } from "@/config/validation";
import UniversalTable from "@/components/layouts/UniversalTable.vue";

export default {
  name: "strategyBach",
  mixins: [baseComponent],
  components: {
    UniversalTable
  },
  data() {
    return {
      toolListProps: {
        toolTitle: "执行批次列表"
        // toolList: [
        //   {
        //     name: "新增",
        //     btnCode: "",
        //     type: "add"
        //   }
        // ]
      },
      searchFormTemp: [
        {
          label: "批次编码",
          name: "bizCode",
          type: "input",
          placeholder: "请输入批次编码"
        },
        {
          label: "数据空间编码",
          name: "dataSpaceCode",
          type: "input",
          placeholder: "请输入数据空间编码"
        },
        {
          label: "参数值",
          name: "paramValue",
          type: "input",
          placeholder: "请输入参数值"
        },
        {
          label: "执行状态",
          name: "executeStatus",
          type: "select",
          placeholder: "请输入执行状态",
          list: [
            {
              dicItemCode: "0",
              dicItemName: "未执行"
            },
            {
              dicItemCode: "1",
              dicItemName: "执行中"
            },
            {
              dicItemCode: "2",
              dicItemName: "执行完成"
            },
            {
              dicItemCode: "3",
              dicItemName: "执行失败"
            }
          ]
        }
        // {
        //   label: "备注",
        //   name: "remark",
        //   type: "input",
        //    placeholder: "请输入备注"
        // }
      ],
      rules: {
        name: [
          {
            required: true,
            validator: validate,
            trigger: "blur",
            min: 1,
            max: 30,
            regax: [
              {
                message: "请输入30个字符以内不能包含特殊字符",
                ruleFormat: /^[a-zA-Z0-9\u4e00-\u9fa5]+$/i
              }
            ]
          }
        ]
      },
      tableData: [],
      initParam: {
        param: {
          bizCode: "",
          remark: "",
          dataSpaceCode: ""
        },
        pageSize: 10,
        pageNum: 1
      },
      total: 0,
      showPopup: false,
      showCopyPopup: false,
      delStrategyId: "",
      copyObj: {
        name: "",
        bizCode: ""
      },
      loading: false,
      tableColumns: [
        {
          prop: "bizCode",
          label: "批次编码",
          width: "140px",
          slotName: "bizCode"
        },
        {
          prop: "dataSpaceCode",
          label: "数据空间编码",
          width: "120px",
          slotName: "dataSpaceCode"
        },
        {
          prop: "name",
          label: "来源类型",
          width: "120px"
        },
        {
          prop: "executeStatus",
          label: "执行状态",
          width: "100px",
          slotName: "executeStatus"
        },
        {
          prop: "params",
          label: "入参",
          width: "220px",
          slotName: "params"
        },
        {
          prop: "remark",
          label: "备注",
          width: "220px"
        },
        {
          prop: "executeTime",
          label: "执行时间",
          width: "220px",
          slotName: "executeTime"
        },
        {
          prop: "finishTime",
          label: "完成时间",
          width: "220px",
          slotName: "finishTime"
        },
        // {
        //   prop: "id",
        //   label: "操作",
        //   width: "200px",
        //   slotName: "actions"
        // }
      ],
      tableActions: [
        {
          key: "detail",
          label: "查看明细",
          icon: "el-icon-view",
          class: "config-btn"
        }
      ]
    };
  },
  computed: {
    breadcrumbItems() {
      if (this.$route.query.breadcrumb) {
        try {
          return JSON.parse(this.$route.query.breadcrumb);
        } catch (e) { return []; }
      }
      if (this.$route.query.fromDataSpace) {
        return [
          { text: '数据空间', to: { name: 'dataSpace' } },
          { text: '策略执行情况' }
        ];
      }
      return [];
    }
  },
  created() {
    // 自动带参查询
    if (this.$route.query.dataSpaceCode) {
      this.initParam.param.dataSpaceCode = this.$route.query.dataSpaceCode;
    }
    if (this.$route.query.executeStatus !== undefined) {
      this.initParam.param.executeStatus = this.$route.query.executeStatus;
    }
    this.initData();
  },
  methods: {
    //初始化数据
    async initData() {
      // await this.getDictList();
      this.initList();
    },
    async initList() {
      let res = await algostrategybatchPage(this.initParam);
      if (!res) {
        return;
      }
      if (res.list && res.list.length > 0) {
        this.tableData = res.list;
        this.initParam.pageNum = res.pageNum;
        this.initParam.pageSize = res.pageSize;
        this.total = Number(res.total);
      } else {
        this.tableData = [];
        this.total = Number(res.total);
      }
    },
    handleTool(item) {
      if (item.type == "add") {
        this.$router.push({
          name: "strategyMatchUpdate",
          query: {
            type: item.type
          }
        });
      }
    },
    handleUpdate(row) {
      this.$store.commit("strategyMatchManage/setStrategyMatchObj", row);
      this.$router.push({
        name: "strategyMatchUpdate",
        query: {
          type: "edit",
          bizCode: row.bizCode
        }
      });
    },
    handleCopy(row) {
      this.copyObj.bizCode = row.bizCode;
      this.showCopyPopup = true;
    },
    cancle() {
      this.copyObj = {
        bizCode: "",
        name: ""
      };
      this.showCopyPopup = false;
    },
    async confirmCopy() {
      if (!validateAlls(this.$refs.copyForm)) {
        return;
      }
      let res = await copyStrategymatch(this.copyObj);
      if (!res) {
        return;
      }
      this.initList();
      this.showCopyPopup = false;
    },
    normalSearch(data) {
      this.initParam = data;
      this.initList();
    },
    normalResetQuery() {
      this.initParam.pageNum = 1;
      this.initParam.pageSize = 10;
      this.initParam.param = {
        bizCode: "",
        remark: "",
        dataSpaceCode: ""
      };
      this.initList();
    },
    handleDel(row) {
      this.delStrategyId = row.bizCode;
      this.showPopup = true;
    },
    async confirmUpdate() {
      let res = await deleteStrategymatch({ bizCode: this.delStrategyId });
      if (!res) {
        return;
      }
      this.initList();
      this.showPopup = false;
    },
    handleView(row) {
      this.setCacheArr("del");
      this.handleRoute(row, "sourceMatchFieldConfigView");
      this.$router.push({
        name: "sourceMatchFieldConfigView",
        query: {
          bizCode: row.bizCode,
          fromDataSpace: this.$route.query.fromDataSpace
        }
      });
    },
    handleStart(row) {
      this.$store.commit("layoutStore/setCacheArr", {
        status: "del",
        routeName: "executedRecordPage"
      });
      this.handleRoute(row, "executedRecordPage");
      this.$router.push({
        name: "executedRecordPage",
        query: {
          bizCode: row.bizCode,
          fromDataSpace: this.$route.query.fromDataSpace
        }
      });
    },
    handleSourceConfig(row) {
      this.handleRoute(row, "bachDetail");
      this.$router.push({
        name: "bachDetail",
        query: {
          batchId: row.id,
          fromDataSpace: this.$route.query.fromDataSpace
        }
      });
    },
    handleIndicatorConfig(row) {
      this.handleRoute(row, "strategyMatchIndicatorList");
      this.$router.push({
        name: "strategyMatchIndicatorList",
        query: {
          bizCode: row.bizCode,
          fromDataSpace: this.$route.query.fromDataSpace
        }
      });
    },
    handleRoute(row, name) {
      this.setQueryParam(this.initParam, "strategyMatchParam");
      this.$store.commit("strategyMatchManage/setStrategyMatchObj", row);
      this.$router.push({
        name: name,
        query: {
          bizCode: row.bizCode,
          fromDataSpace: this.$route.query.fromDataSpace
        }
      });
    },
    setCacheArr(type) {
      let arr = [
        "baseInfoConfig",
        "strategyExecutedRecordList",
        "strategyMatchIndicatorListView",
        "sourceAssignFieldList",
        "sourceMatchFieldList",
        "priorityConfigList"
      ];
      this._.each(arr, item => {
        this.$store.commit("layoutStore/setCacheArr", {
          status: type,
          routeName: item
        });
      });
    },
    // 获取字典/标签/分组
    async getDictList() {
      let res = await getDicItemList("sct.match.rule");
      this.setSearchFormTemp("matchRule", res);
      res = await getDicItemList("sct.source.config.status");
      this.setSearchFormTemp("sourceConfigStatus", res);
      this.setSearchFormTemp("indicatorConfigStatus", res);
      res = await getDicItemList("gen.yesorno.num");
      this.setSearchFormTemp("status", res);
    },
    handleAction({ action, row }) {
      if (action === 'detail') {
        // 组装面包屑链路
        let breadcrumb = this.breadcrumbItems.slice();
        breadcrumb.push({ text: '执行记录-批次明细' });
        this.$router.push({
          name: 'bachDetail',
          query: {
            batchId: row.id,
            fromDataSpace: this.$route.query.fromDataSpace
          }
        });
      }
    }
  }
};
</script>

<style lang="less">
.strateg-match-list {
  // 表格单元格样式 - 自定义插槽样式
  .code-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    .code-icon {
      width: 24px;
      height: 24px;
      background: rgba(215, 162, 86, 0.1);
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        color: #D7A256;
        font-size: 12px;
      }
    }

    .code-text {
      font-family: 'Courier New', monospace;
      font-weight: 500;
      color: #2c3e50;
      font-size: 13px;
    }
  }

  .name-cell {
    .name-text {
      font-weight: 500;
      color: #2c3e50;
    }
  }

  .remark-cell {
    .remark-text {
      color: #606266;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
    }

    .no-remark {
      color: #c0c4cc;
      font-style: italic;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;

      i {
        font-size: 12px;
      }
    }
  }

  .time-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    color: #606266;
    font-size: 13px;

    i {
      color: #D7A256;
      font-size: 14px;
    }
  }

  .user-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #606266;
    font-size: 13px;

    .user-avatar {
      width: 24px;
      height: 24px;
      background: rgba(215, 162, 86, 0.1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        color: #D7A256;
        font-size: 12px;
      }
    }
  }
}
</style>
