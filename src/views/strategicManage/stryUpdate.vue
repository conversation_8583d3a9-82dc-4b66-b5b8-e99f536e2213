<template>
  <div class="strategy-match-update">
    <el-breadcrumb separator-class="el-icon-arrow-right" class="dt-bread">
      <el-breadcrumb-item :to="{ name: 'strategicManage' }"
        >策略列表</el-breadcrumb-item
      >
      <el-breadcrumb-item
        :style="{ color: $store.state.layoutStore.themeObj.color }"
        >{{ getText }}</el-breadcrumb-item
      >
    </el-breadcrumb>
    <TableToolTemp :toolListProps="titleListPros"></TableToolTemp>
    <el-form
      :model="updateForm"
      label-width="160px"
      :rules="rules"
      ref="updateForm"
      label-position="left"
      class="pdl-20"
    >
      <el-form-item label="策略编码" prop="bizCode">
        <el-input
          v-model="updateForm.bizCode"
          class="dt-input-width"
          placeholder="请输入策略编码"
        ></el-input>
      </el-form-item>
      <el-form-item label="策略名称" prop="name">
        <el-input
          v-model="updateForm.name"
          class="dt-input-width"
          placeholder="请输入策略名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="updateForm.remark"
          class="dt-input-width"
          placeholder="请输入备注"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submit">保存</el-button>
        <el-button
          @click="goBack"
          type="primary"
          plain
          :style="{ color: $store.state.layoutStore.themeObj.color }"
          >取消</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import { validate, validateAlls } from "@/config/validation";
import { getDicItemList } from "@/config/tool.js";
import { algoStrategyUpdate, algoStrategyAdd,algoStrategyFind } from "@/api/template/index.js";

export default {
  name: "stryUpdate",
  data() {
    return {
      titleListPros: {
        toolTitle: ""
      },
      rules: {
        name: [
          {
            required: true,
            min: 1,
            max: 100,
            validator: validate,
            trigger: "blur"
          }
        ],
        bizCode: [{ required: true, validator: validate, trigger: "blur" }]
        // sourceValue: [{ required: true, validator: validate, trigger: "blur" }]
        // remark: [{ required: true, validator: validate, trigger: "blur" }]
      },
      updateForm: {
        name: "",
        bizCode: "",
        remark: ""
      },
      commonData: {
        sourceType: []
      },
      baseDisabled: false
    };
  },
  components: {
    TableToolTemp
  },
  computed: {
    getText() {
      if (this.type == "add") {
        return "新增策略";
      }
      if (this.type == "edit") {
        return "编辑策略";
      }
    }
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      this.type = this.$route.query.type;
      this.titleListPros.toolTitle = this.getText;
      await this.getDictList();
      if (this.type == "edit") {
        this.baseDisabled = true;
        this.initData();
      }
    },
    async initData() {
      let res = await algoStrategyFind({
        strategyId: this.$route.query.strategyId
      });
      if (!res) {
        return;
      }
      this.updateForm.bizCode = res.bizCode;
      this.updateForm.remark = res.remark;
      this.updateForm.name = res.name;
    },
    // 获取字典/
    async getDictList() {
      this.commonData.sourceType = await getDicItemList(
        "sct.algo.source.status"
      );
    },
    goBack() {
      this.$router.go(-1);
    },
    async submit() {
      if (!validateAlls(this.$refs.updateForm)) {
        return;
      }
      let res;
      if (this.type == "edit") {
        this.updateForm.strategyId = this.$route.query.strategyId;
        res = await algoStrategyUpdate(this.updateForm);
      } else {
        res = await algoStrategyAdd(this.updateForm);
      }

      if (!res) {
        return;
      }
      this.goBack();
    }
  }
};
</script>

<style lang="less">
.strategy-match-update {
  .table-tool {
    padding-bottom: 10px;
  }
  .draggable-wrap {
    display: inline-block;
  }
  .tags {
    margin-right: 6px;
  }
  .zidian-box {
    margin-bottom: 20px;
  }
}
</style>
