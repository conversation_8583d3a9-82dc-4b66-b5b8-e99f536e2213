<template>
  <!-- 策略配置页面 -->
  <div class="strategy-config-container">
    <!-- 页面头部和导航区域 -->
    <SecondaryPageHeader
      title="策略配置"
      subtitle="拖拽组件构建策略计算逻辑"
      icon="el-icon-s-management"
      :show-actions="false"
      :breadcrumb-items="breadcrumbItems"
      @back="goBack"
    />
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 画布区域最大化 -->
      <div class="canvas-container">
        <!-- 左侧组件面板 -->
        <ComponentPanel
          :visible="showComponentDrawer"
          :input-data-list="paneList1"
          :process-data-list="paneList"
          @close="showComponentDrawer = false"
          @drag-start="drag"
          @drag-end="handleDragEnd"
          @item-hover="handleItemHover"
          @section-toggle="handleSectionToggle"
        />

        <div
          id="flowWrap"
          ref="flowWrap"
          class="flow-canvas"
          @drop="drop($event)"
          @dragover="allowDrop($event)"
        >
          <!-- 悬浮标题 -->
          <div class="floating-title">
            <div class="title-content">
              <span class="subtitle-text">拖拽组件构建策略</span>
            </div>
          </div>

          <div id="flow" class="flow-content">
            <!-- 辅助线 -->
            <div
              v-show="auxiliaryLine.isShowXLine"
              :style="{
                borderColor: themeObj.color,
                width: auxiliaryLinePos.width,
                top: auxiliaryLinePos.y + 'px',
                left: auxiliaryLinePos.offsetX + 'px'
              }"
              class="auxiliary-line-x"
            />
            <div
              v-show="auxiliaryLine.isShowYLine"
              :style="{
                borderColor: themeObj.color,
                height: auxiliaryLinePos.height,
                left: auxiliaryLinePos.x + 'px',
                top: auxiliaryLinePos.offsetY + 'px'
              }"
              class="auxiliary-line-y"
            />
            
            <!-- 节点列表 -->
            <nodeItem
              v-for="item in data.nodeList"
              :id="item.nodeId"
              :key="item.nodeId"
              :node="item"
              :active-node-id="activeNodeId"
              @deleteNode="deleteNode"
              @changeLineState="changeLineState"
              @nodeClick="handleNodeClick"
            />

            <!-- 空画布提示 -->
            <div v-if="data.nodeList.length === 0" class="empty-canvas">
              <div class="empty-content">
                <i class="el-icon-edit-outline empty-icon"></i>
                <h3>开始构建您的策略流程</h3>
                <p>从左侧拖拽组件到画布中开始配置</p>
              </div>
            </div>
          </div>

          <!-- 底部悬浮工具栏 -->
          <div class="floating-toolbar">
            <div class="toolbar-content">
              <el-button
                type="text"
                class="toolbar-icon"
                :class="{ active: showComponentDrawer }"
                @click="toggleComponentDrawer"
                :title="showComponentDrawer ? '关闭组件面板 (Esc)' : '打开组件面板 (Ctrl+M)'"
              >
                <i class="el-icon-menu"></i>
              </el-button>
              
              <el-button
                type="text"
                class="toolbar-icon"
                @click="clearCanvas"
                title="清空画布"
              >
                <i class="el-icon-delete"></i>
              </el-button>
              
              <el-button
                type="text"
                class="toolbar-icon"
                @click="autoLayout"
                title="自动布局"
              >
                <i class="el-icon-s-grid"></i>
              </el-button>
              
              <el-button
                type="text"
                class="toolbar-icon primary save-btn"
                @click="handleTool({ type: 'add' })"
                title="保存"
              >
                <i class="el-icon-check"></i>
              </el-button>
            </div>
          </div>
        </div>

        <!-- 底部配置抽屉组件 -->
        <DrawConfig
          :visible="showConfigDrawer"
          :selected-node="selectedNode"
          :active-tab="select"
          :type-get="typeGet"
          :type-title="typeTitle"
          :id-key="idKey"
          :id-name="idName"
          :data-item="dataItem"
          @close="closeConfigDrawer"
          @tab-change="handleTabChange"
          @changefiled="filedChange"
        />

      </div>
    </div>
    <!-- 删除确认弹窗 -->
    <ConfirmDialog
      ref="confirmDialog"
      title="确认删除"
      message="删除后无法恢复，请确认是否删除？"
      icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
      @confirm="confirmUpdate"
    />

    <!-- 清空画布确认弹窗 -->
    <ConfirmDialog
      ref="clearCanvasDialog"
      title="确认清空画布"
      message="确认清空画布吗？此操作将删除所有节点和连线，但会保留开始和结束节点。"
      icon="el-icon-warning"
      confirm-text="确定"
      cancel-text="取消"
      @confirm="confirmClearCanvas"
    />
  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import { getDicItemList } from "@/config/tool.js";
import { guid, getguid } from "@/utils/utils";
import panzoom from "panzoom";
import { nodeTypeList } from "../indexManage/components/init";
import nodeItem from "@/components/canvas/nodeItem";
import basic from "./components/basic";
import { jsPlumb } from "jsplumb";
import SecondaryPageHeader from "@/components/layouts/SecondaryPageHeader";
import ComponentPanel from "../indexManage/components/ComponentPanel";
import DrawConfig from "./components/DrawConfig";
import ConfirmDialog from "@/components/layouts/ConfirmDialog";
import {
  algoindicatorGetNodeList,
  algoStrategyConfig,
  algoStrategyConfigFind,
  algoSourceField
} from "@/api/template/index.js";
export default {
  name: "confiGuration",
  components: {
    TableToolTemp,
    nodeItem,
    basic,
    SecondaryPageHeader,
    ComponentPanel,
    DrawConfig,
    ConfirmDialog
  },
  provide() {
    return {
      baseInfoConfig: this
    };
  },
  data() {
    return {
      titleListPros: {
        toolTitle: "基础信息"
      },
      titleListPros2: {
        toolTitle: "策略配置",
        toolList: [
          {
            name: "保存",
            btnCode: "",
            type: "add"
          }
        ]
      },
      jsPlumb: null,
      delConn: "", // 删除的连线状态
      data: {
        nodeList: [
          // {
          //   logImg: "el-icon-video-play",
          //   nodeName: "开始",
          //   nodeType: "0",
          //   type: "0",
          //   positionTop: "200px",
          //   positionLeft: "300px"
          // },
          // {
          //   logImg: "el-icon-video-pause",
          //   nodeName: "结束",
          //   nodeType: "1",
          //   type: "0",
          //   positionTop: "200px",
          //   positionLeft: "600px"
          // }
        ],
        // lineList: [],
        strategyId: ""
      },
      paneList: [
        {
          logImg: "el-icon-video-play",
          nodeName: "开始",
          nodeType: "-1",
          type: "-1"
        },
        {
          logImg: "el-icon-video-pause",
          nodeName: "结束",
          nodeType: "-2",
          type: "-2"
        },
        {
          logImg: "el-icon-s-unfold",
          nodeName: "自定义节点",
          nodeType: "5",
          type: "1"
        }
        // {
        //   logImg: "el-icon-remove-outline",
        //   nodeName: "节点（计算指标）",
        //   // nodeType: "3",
        //   type: "1"
        // }
      ],
      paneListUp: true,
      paneList1: [
        // {
        //   logImg: "el-icon-video-play",
        //   name: "数据源-互联网长险续期结算明细",
        //   nodeType: "2",
        //   type: "2"
        // },
        // {
        //   logImg: "el-icon-video-play",
        //   name: "数据源-互联网短险结算明细",
        //   nodeType: "2",
        //   type: "2"
        // },
        // {
        //   logImg: "el-icon-video-play",
        //   name: "计算指标-顾问-寿险首单价值",
        //   nodeType: "3",
        //   type: "2"
        // },
        // {
        //   logImg: "el-icon-video-play",
        //   name: "计算指标-顾问-寿险续期价值",
        //   nodeType: "3",
        //   type: "2"
        // }
      ],
      tabsIndex: "0",
      componentName: "",
      currentItem: null,
      currentTab: {},
      tableData: [],
      auxiliaryLine: { isShowXLine: false, isShowYLine: false }, // 对齐辅助线是否显示
      auxiliaryLinePos: {
        width: "100%",
        height: "100%",
        offsetX: 0,
        offsetY: 0,
        x: 20,
        y: 20
      },
      lastPaneList: [],
      selectedList: [],
      commonGrid: [5, 5], // 节点移动最小距离
      jsplumbSetting: {
        grid: [10, 10],
        // 动态锚点、位置自适应
        Anchors: ["TopCenter", "RightMiddle", "BottomCenter", "LeftMiddle"],
        Container: "flow",
        // 连线的样式 StateMachine、Flowchart,有四种默认类型：Bezier（贝塞尔曲线），Straight（直线），Flowchart（流程图），State machine（状态机）
        Connector: [
          "Flowchart",
          { cornerRadius: 8, alwaysRespectStubs: true, stub: 10 }
        ],
        // 鼠标不能拖动删除线
        ConnectionsDetachable: false,
        // 删除线的时候节点不删除
        DeleteEndpointsOnDetach: false,
        // 连线的端点
        Endpoint: ["Dot", {radius: 5}],
        // Endpoint: [
        //   "Rectangle",
        //   {
        //     height: 10,
        //     width: 10
        //   }
        // ],
        // 线端点的样式
        EndpointStyle: {
          fill: "rgba(255,255,255,0)",
          outlineWidth: 1
        },
        LogEnabled: false, // 是否打开jsPlumb的内部日志记录
        // 绘制线
        PaintStyle: {
          stroke: "#ccc",
          strokeWidth: 2,
          outlineStroke: "transparent",
          outlineWidth: 2
        },
        HoverPaintStyle: { 
          stroke: "#D7A256", 
          strokeWidth: 4,
          outlineStroke: "rgba(215, 162, 86, 0.2)",
          outlineWidth: 4
        },
        // 绘制箭头
        Overlays: [
          [
            "Arrow",
            {
              width: 14,
              length: 14,
              location: 1,
              foldback: 0.8,
              cssClass: "connection-arrow"
            }
          ],
          [
            "Label",
            {
              label: "点击删除",
              location: 0.5,
              cssClass: "connection-label",
              visible: false,
              id: "connection-label"
            }
          ]
        ],
        RenderMode: "svg"
      },
      jsplumbConnectOptions: {
        isSource: true,
        isTarget: true,
        // 动态锚点、提供了4个方向 Continuous、AutoDefault
        anchor: ["TopCenter", "RightMiddle", "BottomCenter", "LeftMiddle"],
        // 设置连线上面的label样式
        labelStyle: {
          cssClass: "flowLabel"
        }
      },
      jsplumbSourceOptions: {
        // 使用锚点及其子元素选择器
        filter: ".anchor-point, .anchor-dot, .anchor-ring", 
        filterExclude: false,
        anchor: ["TopCenter", "RightMiddle", "BottomCenter", "LeftMiddle"],
        allowLoopback: false,
        maxConnections: -1, // 允许无限连接
        onMaxConnections: function(info, e) {
          console.log("Maximum connections (" + info.maxConnections + ") reached");
        }
      },
      jsplumbTargetOptions: {
        // 使用锚点及其子元素选择器
        filter: ".anchor-point, .anchor-dot, .anchor-ring",
        filterExclude: false,
        anchor: ["TopCenter", "RightMiddle", "BottomCenter", "LeftMiddle"],
        allowLoopback: false,
        maxConnections: -1, // 允许无限连接
        onMaxConnections: function(info, e) {
          console.log("Maximum connections (" + info.maxConnections + ") reached");
        }
      },

      typeTitle: "基本信息",
      type: null,
      typeGet: null,
      select: "1",
      idKey: "",
      idName: "",
      dataItem: {}, //节点缓存
      ids:[],
      // 新增的属性
      showComponentDrawer: false,
      showConfigDrawer: false,
      activeNodeId: null,
      selectedNode: null,
      toolType: "add", // 工具类型
      outPutList: [], // 基本信息输出字段
      filedOutPutList: [], // 字段输出列表
      childList: [], // 子节点集合
      filedList: [], // 连接字段列表
      breadcrumbItems: [
        { text: '策略管理', to: { name: 'strategicManage' } },
        { text: '策略配置' }
      ]
    };
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    strategyMatchObj() {
      return this.$store.state.strategyMatchManage.strategyMatchObj;
    },
    cacheArr() {
      return this.$store.state["layoutStore"].cacheArrName;
    },
    notCacheArr() {
      return this.$store.state["layoutStore"].notCacheArrName;
    }
  },
  created() {
    this.height = document.body.offsetHeight - 20 - 43 - 48;
    this.jsplumbSetting.HoverPaintStyle.stroke = this.themeObj.color;
    this.data.strategyId = this.$route.query.strategyId;
    this.data.nodeList = [];
    this.$store.commit("templateEngine/gurationList", this.data.nodeList);
    this.initData();
    document.oncontextmenu = function() {
      event.returnValue = false;
    };
  },
  mounted() {
    // this.jsPlumb = jsPlumb.getInstance();
    // this.$nextTick(() => {
    //   this.init();
    // });
    this.initNode();
  },
  watch: {
    tabsIndex: {
      immediate: true,
      handler(newValue, oldValue) {
        let idx = parseInt(newValue);
        this.currentTab = this.paneList[idx];
        this.componentName = this.currentTab.component;
      }
    }
  },
  methods: {
    up() {
      this.paneListUp = false;
    },
    down() {
      this.paneListUp = true;
    },
    async handleTool() {
      let data = this.data.nodeList;
      if (data.length == 0) {
        return this.$message.error("请将节点连线");
      }
      if (!this._.find(this.data.nodeList, ["nodeType", "-1"])) {
        this.$message.error("请连接开始节点");
        return false;
      }
      if (!this._.find(this.data.nodeList, ["nodeType", "-2"])) {
        this.$message.error("请连接结束节点");
        return false;
      }
      console.log(this.data.nodeList);
      let res = await algoStrategyConfig({
        strategyId: this.data.strategyId,
        nodeList: this.data.nodeList
      });
      if (!res) {
        return;
      } else {
        // this.initData();
        // this.initNode();
        // window.reload()
        this.$message.success("保存成功");
      }
    },
    handleTabsClick1() {
      this.select = "1";
    },
    handleTabsClick2() {
      this.select = "2";
    },
    // 基本信息输出
    filedChange(val) {
      console.log(val);
      for (let i = 0; i < this.data.nodeList.length; i++) {
        let that = this;
        that.traverseTree(that.data.nodeList[i], function(node) {
          // console.log(val.key, node.nodeId);
          if (val.nodeId == node.nodeId) {
            console.log('val',val);
            if (val.field == "isUpdate") {
              node.isUpdate = val.value;
            }
            if (val.field == "nodeName") {
              node.nodeName = val.value;
            }
            if (val.field == "description") {
              node.description = val.value;
            }
          }
        });
      }
      console.log(this.data.nodeList)
      this.$store.commit("templateEngine/gurationList", this.data.nodeList);
    },
    async initData() {
      // 基础节点回显
      let res = await algoindicatorGetNodeList();
      if (!res) {
        return;
      }
      if (res && res.length > 0) {
        for (let i = 0; i < res.length; i++) {
          let logImg;
          let nodeTypeSub;
          if (res[i].nodeType == "1") {
            //设置数据源图标
            logImg = "el-icon-menu";
            nodeTypeSub = "source";
          } else {
            logImg = "el-icon-s-data";
            nodeTypeSub = "indicator";
          }
          this.paneList1.push({
            // nodeValue: res[i].bizCode,
            nodeName: res[i].name,
            nodeType: res[i].nodeType,
            type: "0",
            logImg: logImg,
            nodeTypeSub: nodeTypeSub
          });
        }
      }
    },
    // 初始化节点回显
    async initNode() {
      this.jsPlumb = jsPlumb.getInstance();
      let res = await algoStrategyConfigFind({
        strategyId: this.data.strategyId
      });
      if (!res) {
        return;
      }
      if (res && res.nodeList && res.nodeList.length > 0) {
        res.nodeList.map(v => {
          // v.logImg = this.nodeTypeObj[v.nodeType].logImg;
          // v.templateId = v.templateId || "";
          // v.templateCode = v.templateCode || "";
          // v.remark = v.remark || "";
          this.data.nodeList.push(v);
        });
      }
      this.$nextTick(() => {
        this.init();
      });
    },
    //回显回调
    traverseTree(node, callback) {
      // 对当前节点执行回调函数
      callback(node);
      // 如果节点有子节点，则递归遍历子节点
      if (node.children) {
        let that = this;
        node.children.forEach(function(child) {
          that.traverseTree(child, callback);
        });
      }
    },
    init() {
      this.jsPlumb.ready(() => {
        // 导入默认配置
        this.jsPlumb.importDefaults(this.jsplumbSetting);
        // 完成连线前的校验
        this.jsPlumb.bind("beforeDrop", evt => {
          const res = () => {}; // 此处可以添加是否创建连接的校验， 返回 false 则不添加；
          return res;
        });
        // 连线创建成功后，维护本地数据
        this.jsPlumb.bind("connection", evt => {
          this.addLine(evt);
        });
        // 连线点击事件
        // contextmenu
        this.jsPlumb.bind("click", (conn, originalEvent) => {
          // originalEvent.preventDefault(); // 阻止默认的右键点击行为
          this.handleLine(conn, originalEvent);
        });
        // 断开连线后，维护本地数据
        this.jsPlumb.bind("connectionDetached", evt => {
          this.deleLine(evt);
        });
        this.loadEasyFlow();
        // 会使整个jsPlumb立即重绘。
        this.jsPlumb.setSuspendDrawing(false, true);
      });
      this.initPanZoom();
    },
    // 加载流程图
    loadEasyFlow() {
      // 初始化节点
      for (let i = 0; i < this.data.nodeList.length; i++) {
        const node = this.data.nodeList[i];
        // 设置源点，可以拖出线连接其他节点
        this.jsPlumb.makeSource(node.nodeId, this.jsplumbSourceOptions);
        // // 设置目标点，其他源点拖出的线可以连接该节点
        this.jsPlumb.makeTarget(node.nodeId, this.jsplumbTargetOptions);
        // this.jsPlumb.draggable(node.nodeId);
        this.draggableNode(node.nodeId);
      }

      // 初始化连线
      this.jsPlumb.unbind("connection"); // 取消连接事件
      this._.each(this.data.nodeList, item => {
        this._.each(item.transferRuleList, el => {
          this.jsPlumb.connect(
            {
              source: item.nodeId,
              target: el.nextNodeId,
              label: el.name || "",
              id: el.id
            },
            this.jsplumbConnectOptions
          );
        });
      });
      console.log("-33333");
      this.jsPlumb.bind("connection", evt => {
        // if (this.ids.includes(evt.target.id)) {
        //   return
        // }
        const from = evt.source.id;
        const to = evt.target;
        if (evt.target.innerText == "开始") {
          this.$message.error("开始节点只允许在全流程的起点");
          this.jsPlumb.deleteConnection(evt.connection);
          return false;
        }
        if (evt.source.innerText == "结束") {
          this.$message.error("该节点流程以结束");
          this.jsPlumb.deleteConnection(evt.connection);
          return false;
        }
        // this.ids.push(evt.target.id);
        // if (ids.includes(evt.target.id)) {
        this.setLine(from, to, evt);
        // }
      });
    },
    deleLine(line) {
      // this.data.lineList.forEach((item, index) => {
      //   if (item.from === line.sourceId && item.to === line.targetId) {
      //     this.data.lineList.splice(index, 1)
      //   }
      // })
      this._.each(this.data.nodeList, item => {
        this._.each(item.transferRuleList, (el, index) => {
          if (item.nodeId == line.sourceId && el.nextNodeId == line.targetId) {
            item.transferRuleList.splice(index, 1);
          }
        });
      });
      console.log(this.data.nodeList);
    },
    setLine(from, to, line) {
      let node = this._.find(this.data.nodeList, ["nodeId", from]);
      console.log(node)
      if (!node) {
        return;
      }
      let obj = {
        // sort: 1,
        nextNodeId: to.id,
        nodeName: to.innerText.replace(/\n.*$/gm, ""),
        nodeId: from,
        label: ""
        // id: guid()
      };
      if (this._.has(node, "transferRuleList")) {
        obj.sort = node.transferRuleList.length + 1;
        node.transferRuleList.push(obj);
      } else {
        node.transferRuleList = [obj];
      }
      this.$store.commit("templateEngine/gurationList", this.data.nodeList);
      console.log(this.data.nodeList);
    },
    menuClick(index) {
      this.tabsIndex = index;
    },
    drag(ele, item, type) {
      this.type = type;
      console.log(item);
      this.currentItem = item;
    },
    drop(event) {
      const containerRect = this.jsPlumb.getContainer().getBoundingClientRect();
      const scale = this.getScale();
      const left = (event.pageX - containerRect.left - 60) / scale;
      const top = (event.pageY - containerRect.top - 20) / scale;

      var temp = {
        ...this.currentItem,
        nodeId: getguid(),
        positionTop: Math.round(top / 20) * 20 + "px",
        positionLeft: Math.round(left / 20) * 20 + "px"
      };
      this.addNode(temp);
    },
    // dragover默认事件就是不触发drag事件，取消默认事件后，才会触发drag事件
    allowDrop(event) {
      event.preventDefault();
    },
    getScale() {
      let scale1;
      if (this.jsPlumb.pan) {
        const { scale } = this.jsPlumb.pan.getTransform();
        scale1 = scale;
      } else {
        const matrix = window.getComputedStyle(this.jsPlumb.getContainer())
          .transform;
        scale1 = matrix.split(", ")[3] * 1;
      }
      this.jsPlumb.setZoom(scale1);
      return scale1;
    },
    draggableNode(nodeId) {
      this.jsPlumb.draggable(nodeId, {
        grid: this.commonGrid,
        drag: params => {
          this.alignForLine(nodeId, params.pos);
        },
        start: () => {},
        stop: params => {
          this.auxiliaryLine.isShowXLine = false;
          this.auxiliaryLine.isShowYLine = false;
          this.changeNodePosition(nodeId, params.pos);
          this.changeFlag = false;

          setTimeout(() => {
            this.changeFlag = true;
          }, 20);
        }
      });
    },
    // 移动节点时，动态显示对齐线
    alignForLine(nodeId, position) {
      let showXLine = false,
        showYLine = false;
      this.data.nodeList.some(el => {
        if (el.nodeId !== nodeId && el.positionLeft == position[0] + "px") {
          this.auxiliaryLinePos.x = position[0] + 60;
          showYLine = true;
        }
        if (el.nodeId !== nodeId && el.positionTop == position[1] + "px") {
          this.auxiliaryLinePos.y = position[1] + 20;
          showXLine = true;
        }
      });
      this.auxiliaryLine.isShowYLine = showYLine;
      this.auxiliaryLine.isShowXLine = showXLine;
    },
    changeNodePosition(nodeId, pos) {
      this.data.nodeList.some(v => {
        if (nodeId == v.nodeId) {
          v.positionLeft = pos[0] + "px";
          v.positionTop = pos[1] + "px";
          return true;
        } else {
          return false;
        }
      });
    },
    addLine(line) {
      const from = line.source.id;
      const to = line.target.id;
      // this.setLine(from, to);
      this.setLine(from, to, line);
    },
    handleLine(conn, originalEvent) {
      console.log("conn", conn);
      this.delConn = conn;
      this.$refs.confirmDialog.show();
    },
    confirmUpdate(originalEvent) {
      if (this.jsPlumb) {
        this.jsPlumb.deleteConnection(this.delConn);
      }
      this.$refs.confirmDialog.hide();
    },
    async getDictList() {
      await getDicItemList("sct.source.config.status");
      await getDicItemList("gen.yesorno.num");
    },
    // 添加新的节点
    addNode(temp) {
      if (
        !this._.find(this.data.nodeList, ["nodeType", "-1"]) &&
        temp.nodeType != "-1"
      ) {
        this.$message.error("请先添加开始节点");
        return false;
      }
      if (
        this._.find(this.data.nodeList, ["nodeType", "-1"]) &&
        temp.nodeType == "-1"
      ) {
        this.$message.error("只能有一个开始节点");
        return false;
      }
      var node = {
        ...temp,
        isUpdate: temp.isUpdate || false,
        description: temp.description || ''
      };
      this.idKey = node.nodeId;
      this.idName = node.nodeName;
      this.dataItem = node;
      this.data.nodeList.push(node);
      this.$store.commit("templateEngine/gurationList", this.data.nodeList);
      console.log(this.data.nodeList);
      this.typeGet = this.type;
      this.$nextTick(() => {
        this.jsPlumb.makeSource(node.nodeId, this.jsplumbSourceOptions);
        this.jsPlumb.makeTarget(node.nodeId, this.jsplumbTargetOptions);
        this.draggableNode(node.nodeId);
      });
    },
    initPanZoom() {
      const mainContainer = this.jsPlumb.getContainer();
      const mainContainerWrap = mainContainer.parentNode;
      const pan = panzoom(mainContainer, {
        smoothScroll: false,
        bounds: true,
        // autocenter: true,
        zoomDoubleClickSpeed: 1,
        minZoom: 0.5,
        maxZoom: 2,
        // 设置滚动缩放的组合键，默认不需要组合键
        beforeWheel: e => {
          console.log(e);
          // let shouldIgnore = !e.ctrlKey
          // return shouldIgnore
        },
        beforeMouseDown: function(e) {
          // allow mouse-down panning only if altKey is down. Otherwise - ignore
          var shouldIgnore = e.ctrlKey;
          return shouldIgnore;
        }
      });
      this.jsPlumb.mainContainerWrap = mainContainerWrap;
      this.jsPlumb.pan = pan;
      // 缩放时设置jsPlumb的缩放比率
      pan.on("zoom", e => {
        const { x, y, scale } = e.getTransform();
        this.jsPlumb.setZoom(scale);
        // 根据缩放比例，缩放对齐辅助线长度和位置
        this.auxiliaryLinePos.width = (1 / scale) * 100 + "%";
        this.auxiliaryLinePos.height = (1 / scale) * 100 + "%";
        this.auxiliaryLinePos.offsetX = -(x / scale);
        this.auxiliaryLinePos.offsetY = -(y / scale);
      });
      pan.on("panend", e => {
        const { x, y, scale } = e.getTransform();
        this.auxiliaryLinePos.width = (1 / scale) * 100 + "%";
        this.auxiliaryLinePos.height = (1 / scale) * 100 + "%";
        this.auxiliaryLinePos.offsetX = -(x / scale);
        this.auxiliaryLinePos.offsetY = -(y / scale);
      });

      // 平移时设置鼠标样式
      mainContainerWrap.style.cursor = "grab";
      mainContainerWrap.addEventListener("mousedown", function wrapMousedown() {
        this.style.cursor = "grabbing";
        mainContainerWrap.addEventListener("mouseout", function wrapMouseout() {
          this.style.cursor = "grab";
        });
      });
      mainContainerWrap.addEventListener("mouseup", function wrapMouseup() {
        this.style.cursor = "grab";
      });
    },
    // 删除节点
    deleteNode(node) {
      // 开始节点(-1)和结束节点(-2)不允许删除
      if (node.nodeType === "-1" || node.nodeType === "-2") {
        this.$message.warning('开始和结束节点不允许删除');
        return;
      }
      
      this.typeGet = "";
      this.type = "";
      console.log(this.typeGet);
      this.data.nodeList.some((v, index) => {
        if (v.nodeId === node.nodeId) {
          this.data.nodeList.splice(index, 1);
          this.jsPlumb.remove(v.nodeId);
          this.$nextTick(() => {
            this.init();
          });
          return true;
        } else {
          return false;
        }
      });
    },

    // 更改连线状态
    changeLineState(node, val) {
      const lines = this.jsPlumb.getAllConnections();
      lines.forEach(line => {
        if (line.targetId === node.nodeId || line.sourceId === node.nodeId) {
          if (val) {
            line.canvas.classList.add("active");
          } else {
            line.canvas.classList.remove("active");
          }
        }
      });

      console.log(node, val);
      this.idKey = node.nodeId;
      this.idName = node.nodeName;
      this.dataItem = node;
      this.typeGet = node.nodeType;
      if (val && this.changeFlag) {
        this.getEditInfo(node);
      }
    },
    getEditInfo(node) {
      if (node.nodeType != "2") {
        return;
      }
      this._.each(node.children, item => {
        const o = this._.find(this.data.nodeList, el => {
          return el.nodeId == item.nextNodeId;
        });
        if (o) {
          item.name = o.name;
        }

        this._.each(item.conditionList, el => {
          if (!this._.isArray(el.fieldId)) {
            el.fieldId = el.fieldId.split(",");
          }
        });
      });
    },
    // 新增的方法
    goBack() {
      this.$router.push({ name: 'strategicManage' });
    },
    toggleComponentDrawer() {
      this.showComponentDrawer = !this.showComponentDrawer;
    },
    handleNodeClick(node) {
      // 开始节点(-1)和结束节点(-2)不显示编辑框
      if (node.nodeType === "-1" || node.nodeType === "-2") {
        // 只更新激活状态，不更改选中节点
        this.activeNodeId = node.nodeId;
        // 如果当前编辑框是打开的，则隐藏它
        if (this.showConfigDrawer) {
          this.showConfigDrawer = false;
        }
        return;
      }
      
      // 对于普通节点，正常更新选中状态和显示编辑框
      this.selectedNode = node;
      this.activeNodeId = node.nodeId;
      this.showConfigDrawer = true;
    },
    closeConfigDrawer() {
      this.showConfigDrawer = false;
      this.activeNodeId = null;
      this.selectedNode = null;
    },
    handleTabChange(activeTab) {
      this.select = activeTab;
    },
    clearCanvas() {
      if (this.data.nodeList.length === 0) {
        this.$message.info('画布已经是空的了');
        return;
      }
      this.$refs.clearCanvasDialog.show();
    },
    confirmClearCanvas() {
      // 保留开始和结束节点，只清空其他节点
      this.data.nodeList = this.data.nodeList.filter(node => {
        return node.nodeType === "-1" || node.nodeType === "-2";
      });
      
      // 清空jsplumb实例中的所有元素
      if (this.jsPlumb) {
        this.jsPlumb.deleteEveryConnection();
        this.jsPlumb.deleteEveryEndpoint();
      }
      
      // 清除激活状态
      this.activeNodeId = null;
      this.selectedNode = null;
      this.showConfigDrawer = false;
      
      // 重新初始化
      this.$nextTick(() => {
        this.init();
      });
      this.$refs.clearCanvasDialog.hide();
      this.$message.success('画布已清空，保留开始和结束节点');
    },
    autoLayout() {
      // 自动布局逻辑
      if (this.data.nodeList.length === 0) {
        this.$message.info('暂无节点需要布局');
        return;
      }
      
      // 简单的自动布局：按行排列
      const startX = 100;
      const startY = 100;
      const stepX = 200;
      const stepY = 120;
      let currentX = startX;
      let currentY = startY;
      let nodesInRow = 0;
      const maxNodesInRow = 4;
      
      this.data.nodeList.forEach((node, index) => {
        node.positionLeft = currentX + 'px';
        node.positionTop = currentY + 'px';
        
        nodesInRow++;
        if (nodesInRow >= maxNodesInRow) {
          currentX = startX;
          currentY += stepY;
          nodesInRow = 0;
        } else {
          currentX += stepX;
        }
      });
      
      // 重新渲染连线
      this.$nextTick(() => {
        this.init();
      });
      
      this.$message.success('自动布局完成');
    },
    handleDragEnd() {
      // 拖拽结束处理
    },
    handleItemHover(item) {
      // 项目悬停处理
    },
    handleSectionToggle(section) {
      // 面板切换处理
    },

    // ConfigDrawer相关的事件处理方法
    outPutChange(data) {
      // 输出字段变更处理
      console.log('outPutChange:', data);
    },
    outPutAdd(data) {
      // 添加输出字段处理
      console.log('outPutAdd:', data);
    },
    outPutDel(data) {
      // 删除输出字段处理
      console.log('outPutDel:', data);
    },
    outGroupChange(data) {
      // 分组变更处理
      console.log('outGroupChange:', data);
    },
    outGroupAdd(data) {
      // 添加分组处理
      console.log('outGroupAdd:', data);
    },
    outGroupDel(data) {
      // 删除分组处理
      console.log('outGroupDel:', data);
    },
    outConnectChange(data) {
      // 连接变更处理
      console.log('outConnectChange:', data);
    },
    outConnectAdd(data) {
      // 添加连接处理
      console.log('outConnectAdd:', data);
    },
    outConnectDel(data) {
      // 删除连接处理
      console.log('outConnectDel:', data);
    },
    outCoFnChange(data) {
      // 条件函数变更处理
      console.log('outCoFnChange:', data);
    },
    outCoFnAdd(data) {
      // 添加条件函数处理
      console.log('outCoFnAdd:', data);
    },
    outCoFnDel(data) {
      // 删除条件函数处理
      console.log('outCoFnDel:', data);
    },
    outFilterChange(data) {
      // 过滤器变更处理
      console.log('outFilterChange:', data);
    },
    outFilterAdd(data) {
      // 添加过滤器处理
      console.log('outFilterAdd:', data);
    },
    outFilterDel(data) {
      // 删除过滤器处理
      console.log('outFilterDel:', data);
    }
  }
};
</script>
<style lang="less" scoped>
.strategy-config-container {
  min-height: 100vh;
  background: #fcfaf7;
  
  .main-content {
    
    .canvas-container {
      display: flex;
      flex-direction: column;
      height: calc(100vh - 160px);
      min-height: 600px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 16px rgba(215, 162, 86, 0.08);
      overflow: hidden;
      position: relative;
      
      .flow-canvas {
        flex: 1;
        position: relative;
        background: #fefefe;
        overflow: hidden;
        cursor: grab;
        
        &:active {
          cursor: grabbing;
        }
        
        // 添加深度和质感的多层覆盖
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          // 创建微妙的径向渐变，增强中心聚焦感，使用更轻的透明度
          background: 
            radial-gradient(ellipse at center, transparent 0%, rgba(215, 162, 86, 0.01) 50%, rgba(215, 162, 86, 0.02) 100%),
            // 添加纸张质感
            radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
          pointer-events: none;
          z-index: 1;
        }
        
        // 现代化边框设计和中心线
        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          border: 1px solid rgba(215, 162, 86, 0.04);
          border-radius: 12px;
          // 添加内阴影效果，增强深度感
          box-shadow: 
            inset 0 1px 0 rgba(255, 255, 255, 0.3),
            inset 0 -1px 0 rgba(215, 162, 86, 0.02);
          pointer-events: none;
          z-index: 1;
        }
        
        .floating-title {
          position: absolute;
          top: 20px;
          left: 20px;
          z-index: 100;
          pointer-events: none;
          
          .title-content {
            color: #7f8c8d;
            padding: 0;
            font-size: 13px;
            
            .subtitle-text {
              display: flex;
              align-items: center;
              gap: 8px;
            }
          }
        }
        
        .flow-content {
          width: 2001px;  // 设置合理的画布宽度
          height: 1501px; // 设置合理的画布高度
          position: relative;
          z-index: 2; // 确保内容在背景网格之上
          // 现代化渐变背景 - 与整体UI风格保持一致，使用更浅的色调
          background: linear-gradient(135deg, 
            #fefdfb 0%, 
            #ffffff 25%, 
            #ffffff 50%, 
            #ffffff 75%, 
            #fefdfb 100%);
          
          // 专业级网格系统 - 参考Figma/Sketch等设计工具，使用更微妙的透明度
          background-image: 
            // 主要网格线 - 100px间距（包含加强的中心线）
            linear-gradient(to right, rgba(128, 128, 128, 0.06) 1px, transparent 1px),
            linear-gradient(to bottom, rgba(128, 128, 128, 0.06) 1px, transparent 1px),
            // 次要网格线 - 20px间距
            linear-gradient(to right, rgba(215, 162, 86, 0.03) 1px, transparent 1px),
            linear-gradient(to bottom, rgba(215, 162, 86, 0.03) 1px, transparent 1px),
            // 微细网格点 - 增强对齐感
            radial-gradient(circle, rgba(215, 162, 86, 0.04) 0.5px, transparent 0.5px);
          
          background-size: 
            100px 100%,    // 主要垂直网格线
            100% 100px,    // 主要水平网格线
            20px 100%,     // 次要垂直网格线
            100% 20px,     // 次要水平网格线
            20px 20px;     // 网格点
          
          background-position: 
            0 0,           // 主要垂直网格线
            0 0,           // 主要水平网格线
            0 0,           // 次要垂直网格线
            0 0,           // 次要水平网格线
            0 0;           // 网格点
          
          // 现代化辅助线样式
          .auxiliary-line-x {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, 
              transparent 0%, 
              rgba(215, 162, 86, 0.8) 10%, 
              #D7A256 50%, 
              rgba(215, 162, 86, 0.8) 90%, 
              transparent 100%);
            border-radius: 1px;
            z-index: 100;
            pointer-events: none;
            box-shadow: 
              0 0 8px rgba(215, 162, 86, 0.4),
              0 1px 0 rgba(255, 255, 255, 0.8);
            
            // 添加动态指示器
            &::before {
              content: '';
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
              width: 8px;
              height: 8px;
              background: #D7A256;
              border-radius: 50%;
              border: 2px solid white;
              box-shadow: 0 0 6px rgba(215, 162, 86, 0.6);
            }
          }

          .auxiliary-line-y {
            position: absolute;
            width: 2px;
            background: linear-gradient(180deg, 
              transparent 0%, 
              rgba(215, 162, 86, 0.8) 10%, 
              #D7A256 50%, 
              rgba(215, 162, 86, 0.8) 90%, 
              transparent 100%);
            border-radius: 1px;
            z-index: 100;
            pointer-events: none;
            box-shadow: 
              0 0 8px rgba(215, 162, 86, 0.4),
              1px 0 0 rgba(255, 255, 255, 0.8);
            
            // 添加动态指示器
            &::before {
              content: '';
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
              width: 8px;
              height: 8px;
              background: #D7A256;
              border-radius: 50%;
              border: 2px solid white;
              box-shadow: 0 0 6px rgba(215, 162, 86, 0.6);
            }
          }
          
          .empty-canvas {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #909399;
            
            .empty-content {
              .empty-icon {
                font-size: 48px;
                color: #D7A256;
                margin-bottom: 16px;
                opacity: 0.6;
              }
              
              h3 {
                font-size: 18px;
                color: #606266;
                margin: 0 0 8px 0;
                font-weight: 500;
              }
              
              p {
                font-size: 14px;
                color: #909399;
                margin: 0;
              }
            }
          }
        }
        
        .floating-toolbar {
          position: absolute;
          bottom: 20px;
          left: 50%;
          transform: translateX(-50%);
          z-index: 1000;
          
          .toolbar-content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 8px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            display: flex;
            gap: 4px;
            
            .toolbar-icon {
              width: 40px;
              height: 40px;
              border-radius: 8px;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #606266;
              border: none;
              background: transparent;
              transition: all 0.3s ease;
              position: relative;
              
              &:hover {
                background: rgba(215, 162, 86, 0.1);
                color: #D7A256;
                transform: translateY(-2px);
              }
              
              &.active {
                background: rgba(215, 162, 86, 0.2);
                color: #D7A256;
              }
              
              &.primary {
                background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
                color: white;
                box-shadow: 0 4px 12px rgba(215, 162, 86, 0.3);
                
                &:hover {
                  transform: translateY(-2px);
                  box-shadow: 0 6px 16px rgba(215, 162, 86, 0.4);
                }
              }
              
              i {
                font-size: 16px;
              }
            }
          }
        }
      }
    }
  }
}

// 全局样式
/deep/ .jtk-connector {
  z-index: 4;
}

/deep/ .jtk-endpoint {
  z-index: 5;
}

/deep/ .jtk-overlay {
  z-index: 6;
}

// 连线标签样式
/deep/ .connection-label {
  background: rgba(215, 162, 86, 0.9) !important;
  color: white !important;
  padding: 4px 8px !important;
  border-radius: 4px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  user-select: none !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  backdrop-filter: blur(4px) !important;
  
  &:hover {
    background: rgba(215, 162, 86, 1) !important;
    transform: scale(1.05) !important;
  }
}

// 连线箭头样式
/deep/ .connection-arrow {
  fill: #D7A256 !important;
  stroke: #D7A256 !important;
}


</style>
