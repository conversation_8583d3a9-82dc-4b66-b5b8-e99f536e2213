<template>
  <div class="strateg-match-list">
    <!-- <el-breadcrumb separator-class="el-icon-arrow-right" class="dt-bread">
      <el-breadcrumb-item
        :to="{
          name: 'strategyBach'
        }"
        >执行批次列表</el-breadcrumb-item
      >
      <el-breadcrumb-item
        :to="{
          name: 'bachDetail',
          query: {
            batchId: $route.query.batchId
          }
        }"
        >执行记录-批次明细</el-breadcrumb-item
      >
      <el-breadcrumb-item
        :style="{ color: $store.state.layoutStore.themeObj.color }"
        >执行记录-数据明细</el-breadcrumb-item
      >
    </el-breadcrumb> -->
    <SecondaryPageHeader
          title="执行记录-数据明细"
          subtitle="执行记录-数据明细"
          icon="el-icon-files"
          back-button-text="执行记录-数据明细"
          back-route="strategyBach"
          :breadcrumb-items="breadcrumbItems"
          @back="goBack"
        />
    <!-- <TableToolTemp
      :toolListProps="toolListProps"
      @handleTool="handleTool"
    ></TableToolTemp> -->
    <Search
      :searchForm="initParam"
       style="padding:24px;border-top: 1px solid #f7ecdd;
       border-bottom: 1px solid #f7ecdd;"
      :searchFormTemp="searchFormTemp"
      @normalSearch="normalSearch"
      @addTemp="addTemp"
      @del="del"
      @handleChange="handleChange"
      @normalResetQuery="normalResetQuery"
      @export="exportUrl"
    ></Search>
    <div class="table-section">
      <div class="table-wrapper">
    <el-table
      :data="tableData"
      stripe
        v-hover
      class="modern-table"
      style="width: 100%; font-size: 14px;"
    >
      <el-table-column
        v-for="(item, index) in FiledList"
        :key="index"
        align="center"
        :prop="item.fieldName"
        :label="item.fieldDesc"
      >
        <template slot-scope="scope">
          <span v-if="item.fieldName === 'agentcode'">
            <span class="consultant-icon">
              <i class="el-icon-document"></i>
            </span>
            {{ scope.row[item.fieldName] }}
          </span>
          <span v-else>
            {{ scope.row[item.fieldName] }}
          </span>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      class="pagination-section"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :pageData="initParam"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
    ></Pagination>
      </div>
    </div>
    <DtPopup
      :isShow.sync="showPopup"
      @close="showPopup = false"
      @confirm="confirmUpdate"
      :isSmall="true"
    >
      <div class="popup-text">
        删除后无法恢复，请确认是否删除？
      </div>
    </DtPopup>
  </div>
</template>
<script>
import {
  algoSourceExport,
  algostrategybatchnodeDetailPage,
  algoSourceFieldData
} from "@/api/template/index.js";
import { getDicItemList } from "@/config/tool";
import { baseComponent } from "@/utils/common";
import Search from "./components/Search";
import { validate, validateAlls } from "@/config/validation";
import { Loading } from "element-ui";
import fileDownload from "js-file-download";
import Axios from "axios";
import domainObj from "@/utils/globalParam";
import store from "../../store/index";
import SecondaryPageHeader from "@/components/layouts/SecondaryPageHeader";
export default {
  name: "sourceDetail",
  mixins: [baseComponent],
  components: {
    Search,
    SecondaryPageHeader
  },
  data() {
    return {
      toolListProps: {
        toolTitle: "数据明细列表",
        toolList: [
          {
            name: "导出",
            btnCode: "",
            downloadURL: "",
            type: "download"
          }
        ]
      },
      downloadURL: "/web/algoSource/data/export",
      searchFormTemp: [
          {
          strategyId: {
            label: "字段",
            name: "strategyId",
            type: "select",
            list: [],
            value: ""
          },
          match: {
            label: "匹配符",
            name: "match",
            type: "select",
            value: "=",
            list: [
              {
                dicItemCode: "=",
                dicItemName: "="
              },
              {
                dicItemCode: ">",
                dicItemName: ">"
              },
              {
                dicItemCode: ">=",
                dicItemName: ">="
              },
              {
                dicItemCode: "<",
                dicItemName: "<"
              },
              {
                dicItemCode: "<=",
                dicItemName: "<="
              }
            ]
          },
          filed: {
            label: "输入字段",
            name: "filed",
            type: "input",
            value: ""
          },
          btn: {
            label: "删除",
            name: "btn"
          }
        }
      ],
      rules: {
        name: [
          {
            required: true,
            validator: validate,
            trigger: "blur",
            min: 1,
            max: 30,
            regax: [
              {
                message: "请输入30个字符以内不能包含特殊字符",
                ruleFormat: /^[a-zA-Z0-9\u4e00-\u9fa5]+$/i
              }
            ]
          }
        ]
      },
      tableData: [],
      initParam: {
        param: {
          strategyBatchId: "",
          bizCode: "",
          nodeType: "",
          searchParamList: [
            {
              fieldName: "",
              operator: "",
              value: ""
            }
          ]
        },
        pageSize: 10,
        pageNum: 1
      },
      total: 0,
      showPopup: false,
      showCopyPopup: false,
      strategyBatchId: "",
      FiledList: [],
      optList: [],
    };
  },
  computed: {
    breadcrumbItems() {
      if (this.$route.query.fromDataSpace) {
        return [
          { text: '数据空间', to: { name: 'dataSpace' } },
          { text: '策略执行情况', to: { name: 'strategyBach', query: { fromDataSpace: 1 } } },
          { text: '执行记录-批次明细', icon: 'el-icon-document', to: { name: 'bachDetail', query: { batchId: this.$route.query.batchId, fromDataSpace: 1 } } },
          { text: '执行记录-数据明细', icon: 'el-icon-files' }
        ];
      }
      return [
        { text: '执行批次列表', icon: 'el-icon-back', to: { name: 'strategyBach' } },
        { text: '执行记录-批次明细', icon: 'el-icon-document', to: { name: 'bachDetail', query: { batchId: this.$route.query.batchId } } },
        { text: '执行记录-数据明细', icon: 'el-icon-files' }
      ];
    }
  },
  created() {
    this.initParam.param = {
      strategyBatchId: this.$route.query.batchId,
      bizCode: this.$route.query.nodeValue,
      nodeType: this.$route.query.nodeType,
      searchParamList: [
        {
          fieldName: "",
          operator: "=",
          value: ""
        }
      ]
    };
    this.initData();
  },
  methods: {
      // 返回指标管理
    goBack() {
      // this.$router.push({ name: 'strategyBach' });
    },
    async exportUrl() {
      let fileurl = domainObj.baseUrl + this.downloadURL;
      console.log(fileurl);
      const loadingInstance = Loading.service({
        fullscreen: true,
        lock: true,
        text: "加载中...",
        target: document.getElementsByTagName("body")[0]
      });
      Axios({
        method: "post",
        url: fileurl,
        headers: {
          access_token: sessionStorage.getItem("LoginAccessToken"),
          tenantId:
            store.state.layoutStore.currentLoginUser.tenantId ||
            sessionStorage.getItem("tenantId"),
          funcId:
            store.state.layoutStore.currentLoginUser.funcId ||
            sessionStorage.getItem("funcId")
        },
        data: this.initParam.param,
        responseType: "blob"
      }).then(res => {
        console.log(res);
        if (res.status === 200) {
          fileDownload(res.data, decodeURI(res.headers["file-name"]));
          setTimeout(function() {
            loadingInstance.close();
          }, 1000);
        }
      });
    },
    handleTool(val) {
      this.exportUrl();
    },
    //初始化数据
    async initData() {
      // await this.getDictList();
      this.initFiledList();
      this.initList();
    },
    async initFiledList() {
      let res = await algoSourceFieldData({
        nodeType: this.initParam.param.nodeType,
        bizCode: this.initParam.param.bizCode,
        strategyBatchId: this.$route.query.batchId
      });
      if (!res) {
        return;
      }
      console.log(res);
      if (res && res.length > 0) {
        this.FiledList = res;
        for (let i = 0; i < this.searchFormTemp.length; i++) {
          if (this.searchFormTemp[i].strategyId.name == "strategyId") {
            this.searchFormTemp[i].strategyId.list = res;
            this.optList = res
          }
        }
      }
    },
    handleChange(val, num, type, index) {
      console.log(val, num, type, index);
    },
    async initList() {
      //todo
      let res = await algostrategybatchnodeDetailPage(this.initParam);
      if (!res) {
        return;
      }
      if (res.list && res.list.length > 0) {
        this.tableData = res.list;
        this.total = res.total ? Number(res.total) : 0;
      } else {
        this.tableData = [];
      }
    },

    normalSearch(data) {
      let arr = [];
      for (let i = 0; i < data.length; i++) {
        arr.push({
          fieldName: data[i].strategyId.value,
          operator: data[i].match.value,
          value: data[i].filed.value
        });
      }
      this.initParam.param.searchParamList = arr;
      console.log(data);
      this.initList();
    },
    addTemp() {
      this.searchFormTemp.push(
        JSON.parse(
          JSON.stringify({
            strategyId: {
              label: "字段",
              name: "strategyId",
              type: "select",
              list: this.optList,
              value: ""
            },
            match: {
              label: "匹配符",
              name: "match",
              type: "select",
              list: [
                {
                  dicItemCode: "=",
                  dicItemName: "="
                },
                {
                  dicItemCode: ">",
                  dicItemName: ">"
                },
                {
                  dicItemCode: ">=",
                  dicItemName: ">="
                },
                {
                  dicItemCode: "<",
                  dicItemName: "<"
                },
                {
                  dicItemCode: "<=",
                  dicItemName: "<="
                }
              ],
              value: "="
            },
            filed: {
              label: "输入字段",
              name: "filed",
              type: "input",
              value: ""
            },
            btn: {
              label: "删除",
              name: "btn"
            }
          })
        )
      );
    },
    del(i) {
      this.searchFormTemp.splice(i, 1);
    },
    normalResetQuery() {
      this.initParam.pageNum = 1;
      this.initParam.pageSize = 10;
      this.initParam.param = {
        strategyBatchId: this.$route.query.batchId,
        bizCode: this.$route.query.nodeValue,
        nodeType: this.$route.query.nodeType,
        searchParamList: [
          {
            fieldName: "",
            operator: "",
            value: ""
          }
        ]
      };
      this.initList();
    },
    handleUpdate(row) {
      this.$store.commit("strategyMatchManage/setStrategyMatchObj", row);
      this.$router.push({
        name: "strategyMatchUpdate",
        query: {
          type: "edit",
          strategyId: row.strategyId
        }
      });
    },
    confirmUpdate() {},
    handleDel(row) {
      this.strategyBatchId = row.strategyId;
      this.showPopup = true;
    },
    handleSourceConfig(row) {
      this.handleRoute(row, "matchSource");
      this.$router.push({
        name: "matchSource",
        query: {
          fbId: row.fbId
        }
      });
    },
    handleIndicatorConfig(row) {
      this.handleRoute(row, "strategyMatchIndicatorList");
    },
    handleRoute(row, name) {
      this.setQueryParam(this.initParam, "strategyMatchParam");
      this.$store.commit("strategyMatchManage/setStrategyMatchObj", row);
      this.$router.push({
        name: name,
        query: {
          strategyId: row.strategyId
        }
      });
    },
    setCacheArr(type) {
      let arr = [
        "baseInfoConfig",
        "strategyExecutedRecordList",
        "strategyMatchIndicatorListView",
        "sourceAssignFieldList",
        "sourceMatchFieldList",
        "priorityConfigList"
      ];
      this._.each(arr, item => {
        this.$store.commit("layoutStore/setCacheArr", {
          status: type,
          routeName: item
        });
      });
    },
    // 获取字典/标签/分组
    async getDictList() {
      let res = await getDicItemList("sct.match.rule");
      this.setSearchFormTemp("matchRule", res);
      res = await getDicItemList("sct.source.config.status");
      this.setSearchFormTemp("sourceConfigStatus", res);
      this.setSearchFormTemp("indicatorConfigStatus", res);
      res = await getDicItemList("gen.yesorno.num");
      this.setSearchFormTemp("status", res);
    }
  }
};
</script>

<style lang="less" scoped>
// 表格区域
.table-section {
        .table-wrapper {
          .modern-table {
            /deep/ .el-table__header-wrapper {
              .el-table__header {
                th {
                  color: #2c3e50;
                  font-weight: 600;
                  font-size: 14px;
                }
              }
            }

            /deep/ .el-table__body-wrapper {
              .el-table__row {
                transition: all 0.3s ease;

                &:hover {
                  background: rgba(215, 162, 86, 0.05) !important;
                  transform: translateY(-1px);
                  box-shadow: 0 2px 8px rgba(215, 162, 86, 0.1);
                }

                td {
                  border-bottom: 1px solid #f7ecdd;
                }
              }
            }

            /deep/ .el-table--striped .el-table__body tr.el-table__row--striped td {
              background: #fefdfb;
            }
          }

          // 表格单元格样式
          .version-cell {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;

            .version-icon {
              width: 24px;
              height: 24px;
              background: rgba(215, 162, 86, 0.1);
              border-radius: 6px;
              display: flex;
              align-items: center;
              justify-content: center;

              i {
                color: #D7A256;
                font-size: 12px;
              }
            }

            .version-text {
              font-family: 'Courier New', monospace;
              font-weight: 500;
              color: #2c3e50;
              font-size: 13px;
            }
          }

          .time-cell {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            color: #7f8c8d;
            font-size: 13px;

            i {
              color: #c0c4cc;
            }
          }

          .user-cell {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;

            .user-avatar {
              width: 20px;
  height: 20px;
              background: rgba(215, 162, 86, 0.1);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;

              i {
                color: #D7A256;
                font-size: 10px;
              }
            }
          }

          .remark-cell {
            .remark-text {
  display: block;
              max-width: 180px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
              color: #2c3e50;
            }

            .empty-remark {
              color: #c0c4cc;
              font-style: italic;
              font-size: 12px;
            }
          }

          // 类型标签样式
          .current-tag {
            background: rgba(103, 194, 58, 0.1);
            color: #67c23a;
            border: 1px solid rgba(103, 194, 58, 0.3);

            i {
              margin-right: 4px;
            }
          }

          .draft-tag {
            background: rgba(64, 158, 255, 0.1);
            color: #409eff;
            border: 1px solid rgba(64, 158, 255, 0.3);

            i {
              margin-right: 4px;
            }
          }

          .history-tag {
            background: rgba(144, 147, 153, 0.1);
            color: #909399;
            border: 1px solid rgba(144, 147, 153, 0.3);

            i {
              margin-right: 4px;
            }
          }

          // 操作按钮样式
          .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;

            .action-btn {
              font-size: 11px;
              padding: 4px 8px;
              border-radius: 4px;
              font-weight: 500;
              min-width: 56px;
              height: 28px;
              transition: all 0.3s ease;

              i {
                margin-right: 3px;
                font-size: 10px;
              }

              &.config-btn, &.edit-btn {
                background: rgba(215, 162, 86, 0.1);
                border: 1px solid rgba(215, 162, 86, 0.3) !important;
                color: #D7A256;

                &:hover {
                  background: #D7A256 !important;
                  color: white !important;
                  border-color: #D7A256 !important;
                  transform: translateY(-1px);
                  box-shadow: 0 2px 8px rgba(215, 162, 86, 0.3);
                }
              }

              &.delete-btn {
                background: rgba(245, 108, 108, 0.1);
                border: 1px solid rgba(245, 108, 108, 0.3) !important;
                color: #f56c6c;

                &:hover {
                  background: #f56c6c !important;
                  color: white !important;
                  border-color: #f56c6c !important;
                  transform: translateY(-1px);
                  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
                }
              }
            }
          }
        }

        // 空状态样式
        .empty-state {
          padding: 80px 20px;
          text-align: center;

          .empty-content {
            .empty-icon {
              width: 80px;
              height: 80px;
              margin: 0 auto 24px;
              background: rgba(215, 162, 86, 0.1);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;

              i {
                font-size: 36px;
                color: rgba(215, 162, 86, 0.6);
              }
            }

            h3 {
              margin: 0 0 12px 0;
              font-size: 18px;
              font-weight: 600;
              color: #2c3e50;
            }

            p {
              margin: 0;
              color: #7f8c8d;
              font-size: 14px;
              line-height: 1.5;
            }
          }
        }
  }
.consultant-icon {
  width: 24px;
  height: 24px;
  background: rgba(215, 162, 86, 0.1);
  border-radius: 6px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
  i {
    color: #D7A256;
    font-size: 12px;
  }
}
 // 分页区域
 .pagination-section {
    padding: 20px 28px;
    border-top: 1px solid #f7ecdd;
  }
</style>
