<template>
  <div class="strateg-match-indicator-list">
    <el-breadcrumb separator-class="el-icon-arrow-right" class="dt-bread">
      <el-breadcrumb-item :to="{ name: 'strategyMatchList' }"
        >匹配策略管理</el-breadcrumb-item
      >
      <el-breadcrumb-item
        :style="{ color: $store.state.layoutStore.themeObj.color }"
        >{{
          tabsIndex == 0 ? "配置指标管理" : "定制指标管理"
        }}</el-breadcrumb-item
      >
    </el-breadcrumb>
    <el-tabs type="border-card" class="dt-tabs tabs-wrap" v-model="tabsIndex">
      <template v-for="(obj, idx) in paneList">
        <el-tab-pane
          :name="obj.index"
          :label="obj.label"
          :key="'paneList-' + idx"
        >
          <div
            slot="label"
            class="tab-label"
            :style="{
              'border-top':
                tabsIndex == obj.index
                  ? '4px solid' + themeObj.color
                  : '4px solid transparent',
              color: tabsIndex == obj.index ? themeObj.color : '',
            }"
          >
            {{ obj.label }}
          </div>
        </el-tab-pane>
      </template>
    </el-tabs>
    <TableToolTemp
      :toolListProps="toolListProps"
      @handleTool="handleTool"
    ></TableToolTemp>
    <el-table
      :data="tableData"
      stripe
      v-hover
      class="dt-table"
      style="width: 100%"
    >
      <el-table-column
        align="center"
        prop="priority"
        width="150px"
        label="指标优先级"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="name"
        width="250px"
        label="指标名称"
      ></el-table-column>
      <el-table-column
        align="center"
        v-if="initParam.param.indicatorType == '1'"
        prop="isSkipable"
        label="指标是否可跳过"
      >
        <template slot-scope="scope">
          <span>{{
            scope.row.isSkipable | getDicItemName("gen.yesorno.num")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="remark"
        width="250px"
        label="备注"
      ></el-table-column>
      <el-table-column align="center" prop="updateId" label="最后一次修改人">
        <template slot-scope="scope">
          <span>{{
            scope.row.updateId | getNickName("scope.row.updateId")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="updateTime"
        label="最后一次修改时间"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.updateTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        header-align="center"
        label="操作"
        fixed="right"
        width="100"
      >
        <template slot-scope="scope">
          <div>
            <el-button
              type="text"
              v-if="initParam.param.indicatorType == '1'"
              size="mini"
              @click="handleConfig(scope.row)"
              >配置</el-button
            >
            <el-button type="text" size="mini" @click="handleUpdate(scope.row)"
              >编辑</el-button
            >
          </div>
          <div>
            <el-button
              type="text"
              size="mini"
              v-if="tableData.length > 1 && scope.$index != 0"
              @click="handleSort(scope.row, 'up')"
              >上移</el-button
            >
            <el-button
              type="text"
              size="mini"
              v-if="
                tableData.length > 1 && scope.$index != tableData.length - 1
              "
              @click="handleSort(scope.row, 'down')"
              >下移</el-button
            >
            <el-button type="text" size="mini" @click="handleDel(scope.row)"
              >删除</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :pageData="initParam"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
    ></Pagination>
    <DtPopup
      :isShow.sync="showPopup"
      @close="showPopup = false"
      @confirm="confirmUpdate"
      :isSmall="true"
    >
      <div class="popup-text">
        删除后无法恢复，请确认是否删除？
      </div>
    </DtPopup>
  </div>
</template>
<script>
import {
  getStrategyMatchIndicator,
  deleteStrategyMatchIndicator,
  strategyMatchIndicatorUp,
  strategyMatchIndicatorDown,
} from "@/api/strategyMatchManage/strategymatchindicator/index.js";
import { hasRights } from "@/config/tool";
import { syncToBr } from "@/api/allocation/index.js";
import { getDicItemList } from "@/config/tool";
import { baseComponent } from "@/utils/common";

export default {
  name: "strategyMatchIndicatorList",
  mixins: [baseComponent],
  data() {
    return {
      toolListProps: {},
      tableData: [],
      initParam: {
        param: {
          strategyId: "",
        },
        pageSize: 10,
        pageNum: 1,
        sort: "priority",
      },
      total: 0,
      showPopup: false,
      delIndicatorId: "",
      tabsIndex: "0",
      paneList: [
        {
          index: "0",
          label: "配置指标",
        },
      ],
    };
  },
  watch: {
    tabsIndex: {
      immediate: true,
      handler(newValue, oldValue) {
        let idx = Number(newValue);
        if (idx == "0") {
          this.toolListProps = {
            toolTitle: "配置指标管理",
            toolList: [
              {
                name: "新增策略指标",
                btnCode: "",
                type: "add",
              },
            ],
          };
        } else {
          this.toolListProps = {
            toolTitle: "定制指标管理",
            toolList: [
              {
                name: "新增策略指标",
                btnCode: "",
                type: "add",
              },
              {
                name: "同步",
                btnCode: "",
                type: "async",
              },
            ],
          };
        }
        this.initData();
      },
    },
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
  },
  async created() {
    if (hasRights("sct:strategymatch:configuration")) {
      this.paneList.push({
        index: "1",
        label: "定制指标",
      });
    }
    this.strategyId = this.$route.query.strategyId;
    await this.getDictList();
  },
  methods: {
    //初始化数据
    async initData() {
      this.initList();
    },
    async initList() {
      this.initParam.param.indicatorType = this.tabsIndex == 0 ? "1" : "2";
      this.getQueryParam(this.initParam, "strategyMatchIndicatorParam");
      this.initParam.param.strategyId = this.$route.query.strategyId;
      let res = await getStrategyMatchIndicator(this.initParam);
      if (!res) {
        return;
      }
      this.tableData = res.list;
      this.initParam.pageNum = res.pageNum;
      this.initParam.pageSize = res.pageSize;
      this.total = Number(res.total);
    },
    handleTool(item) {
      if (item.type == "add") {
        this.$router.push({
          name: "strategyMatchIndicatorUpdate",
          query: {
            type: item.type,
            strategyId: this.strategyId,
            indicatorType: this.initParam.param.indicatorType,
          },
        });
      } else if (item.type == "async") {
        this.handleSyncToBr();
      }
    },
    handleUpdate(row) {
      this.$store.commit(
        "strategyMatchManage/setStrategyMatchIndicatorObj",
        row
      );
      this.$router.push({
        name: "strategyMatchIndicatorUpdate",
        query: {
          type: "edit",
          strategyId: row.strategyId,
          indicatorType: this.initParam.param.indicatorType,
        },
      });
    },
    handleDel(row) {
      this.delIndicatorId = row.indicatorId;
      this.showPopup = true;
    },
    async confirmUpdate() {
      let res = await deleteStrategyMatchIndicator({
        indicatorId: this.delIndicatorId,
      });
      if (!res) {
        return;
      }
      this.initList();
      this.showPopup = false;
    },
    handleConfig(row) {
      this.setQueryParam(this.initParam, "strategyMatchIndicatorParam");
      this.$store.commit(
        "strategyMatchManage/setStrategyMatchIndicatorObj",
        row
      );
      this.$router.push({
        name: "strategyMatchIndicatorConfig",
        query: {
          indicatorId: row.indicatorId,
          strategyId: row.strategyId,
        },
      });
    },
    async handleSort(row, type) {
      let res;
      if (type == "up") {
        res = await strategyMatchIndicatorUp({ indicatorId: row.indicatorId });
      } else {
        res = await strategyMatchIndicatorDown({
          indicatorId: row.indicatorId,
        });
      }
      if (!res) {
        return;
      }
      this.initList();
    },
    // 获取字典/标签/分组
    async getDictList() {
      await getDicItemList("gen.yesorno.num");
    },
    async handleSyncToBr() {
      let res = await syncToBr({ strategyId: this.strategyId });
      if (!res) {
        return;
      }
      this.$message.success("同步成功");
    },
  },
};
</script>
