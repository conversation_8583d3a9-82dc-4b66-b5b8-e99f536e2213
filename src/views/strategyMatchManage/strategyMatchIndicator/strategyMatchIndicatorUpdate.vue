<template>
  <div class="strategy-match-indicator-update">
    <el-breadcrumb separator-class="el-icon-arrow-right" class="dt-bread">
      <el-breadcrumb-item :to="{ name: 'strategyMatchList' }"
        >匹配策略管理</el-breadcrumb-item
      >
      <el-breadcrumb-item
        :to="{
          name: 'strategyMatchIndicatorList',
          query: { strategyId: $route.query.strategyId },
        }"
        >配置指标管理</el-breadcrumb-item
      >
      <el-breadcrumb-item
        :style="{ color: $store.state.layoutStore.themeObj.color }"
        >{{ getText }}</el-breadcrumb-item
      >
    </el-breadcrumb>
    <TableToolTemp :toolListProps="titleListPros"></TableToolTemp>
    <el-form
      :model="updateForm"
      label-width="160px"
      :rules="rules"
      ref="updateForm"
      label-position="left"
      class="pdl-20"
    >
      <el-form-item label="指标名称" prop="name">
        <el-input
          v-model="updateForm.name"
          class="dt-input-width"
          placeholder="请输入30个字符以内不能包含特殊字符"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="指标编码"
        v-if="updateForm.indicatorType == '2'"
        prop="name"
      >
        <el-input
          :disabled="baseDisabled"
          v-model="updateForm.indicatorId"
          class="dt-input-width"
          placeholder="请输入30个字符以内不能包含特殊字符"
        ></el-input>
      </el-form-item>

      <el-form-item
        label="是否可跳过"
        v-if="updateForm.indicatorType == '1'"
        prop="isSkipable"
      >
        <el-radio
          v-model="updateForm.isSkipable"
          v-for="(item, index) in dicGather.status"
          :key="index"
          :label="item.dicItemCode"
          >{{ item.dicItemName }}</el-radio
        >
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          type="textarea"
          v-model="updateForm.remark"
          class="dt-input-width"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submit">保存</el-button>
        <el-button
          @click="goBack"
          type="primary"
          plain
          :style="{ color: $store.state.layoutStore.themeObj.color }"
          >取消</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import { validate, validateAlls } from "@/config/validation";
import { getDicItemList } from "@/config/tool.js";
import {
  addStrategyMatchIndicator,
  updateStrategyMatchIndicator,
} from "@/api/strategyMatchManage/strategymatchindicator/index.js";

export default {
  name: "strategyMatchIndicatorUpdate",
  data() {
    return {
      titleListPros: {
        toolTitle: "",
      },
      rules: {
        name: [
          {
            required: true,
            validator: validate,
            trigger: "blur",
            min: 1,
            max: 30,
            regax: [
              {
                message: "请输入30个字符以内不能包含特殊字符",
                ruleFormat: /^[a-zA-Z0-9\u4e00-\u9fa5]+$/i,
              },
            ],
          },
        ],
      },
      updateForm: {
        strategyId: "",
        indicatorId: "",
        name: "",
        isSkipable: "0",
        remark: "",
        indicatorType: "",
      },
      dicGather: {
        status: [],
      },
      baseDisabled: false,
    };
  },
  components: {
    TableToolTemp,
  },
  computed: {
    getText() {
      if (this.type == "add") {
        return "新增指标";
      }
      if (this.type == "edit") {
        return "编辑指标";
      }
    },
  },
  created() {
    this.updateForm.indicatorType = this.$route.query.indicatorType || "";
    this.init();
  },
  methods: {
    async init() {
      this.type = this.$route.query.type;
      this.updateForm.strategyId = this.$route.query.strategyId || "";
      this.titleListPros.toolTitle = this.getText;
      await this.getDictList();
      if (this.type == "edit") {
        this.baseDisabled = true;
        await this.initData();
      }
    },
    // 获取字典/标签/分组
    async getDictList() {
      this.dicGather.status = await getDicItemList("gen.yesorno.num");
    },
    initData() {
      let o = this._.cloneDeep(
        this.$store.state.strategyMatchManage.strategyMatchIndicatorObj
      );
      this.updateForm = {
        name: o.name,
        indicatorId: o.indicatorId,
        isSkipable: o.isSkipable,
        remark: o.remark,
        indicatorType: o.indicatorType,
      };
    },
    goBack() {
      this.$router.go(-1);
    },
    async submit() {
      if (!validateAlls(this.$refs.updateForm)) {
        return;
      }
      let param = this._.cloneDeep(this.updateForm);
      let res;
      if (this.type === "add") {
        if (this.updateForm.indicatorType == "1") {
          delete param.indicatorId;
        } else {
          if (this.updateForm.indicatorId == "") {
            this.$message.error("指标编码不能为空");
            return;
          }
        }
        res = await addStrategyMatchIndicator(param);
      } else {
        res = await updateStrategyMatchIndicator(param);
      }
      if (!res) {
        return;
      }
      this.goBack();
    },
  },
};
</script>

<style lang="less">
.strategy-match-indicator-update {
  .table-tool {
    padding-bottom: 10px;
  }
}
</style>
