<template>
  <div class="strategy-match-indicator-config">
    <el-breadcrumb separator-class="el-icon-arrow-right" class="dt-bread">
      <el-breadcrumb-item :to="{ name: 'strategyMatchList' }"
        >匹配策略管理</el-breadcrumb-item
      >
      <el-breadcrumb-item
        :to="{
          name: 'strategyMatchIndicatorList',
          query: { strategyId: $route.query.strategyId },
        }"
        >配置指标管理</el-breadcrumb-item
      >
      <el-breadcrumb-item
        :style="{ color: $store.state.layoutStore.themeObj.color }"
        >配置指标</el-breadcrumb-item
      >
    </el-breadcrumb>
    <TableToolTemp :toolListProps="titleListProsBase"></TableToolTemp>
    <el-form label-width="160px" label-position="left" class="pdl-20">
      <el-form-item label="指标名称" prop="name">
        <el-input
          v-model="strategyMatchIndicatorObj.name"
          class="dt-input-width"
          :disabled="true"
        ></el-input>
      </el-form-item>
      <el-form-item label="指标优先级" prop="priority">
        <el-input
          v-model="strategyMatchIndicatorObj.priority"
          class="dt-input-width"
          :disabled="true"
        ></el-input>
      </el-form-item>
      <el-form-item label="是否可跳过" prop="isSkipable">
        <el-select
          v-model="strategyMatchIndicatorObj.isSkipable"
          :disabled="true"
          placeholder="请选择"
          class="dt-select"
        >
          <el-option
            v-for="(item, index) in dicGather.status"
            :key="index"
            :label="item.dicItemName"
            :value="item.dicItemCode"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="优先权设置">
        <vuedraggable
          :list="updateForm.priorityFieldsDisplay"
          class="draggable-wrap"
          :animation="300"
        >
          <span
            class="priorityField-tags"
            v-for="(item, index) in updateForm.priorityFieldsDisplay"
            :key="index"
            >{{ item.name }}
            <i @click="handleClose(item, index)" class="el-icon-close"></i>
          </span>
        </vuedraggable>
        <span
          class="priorityField-tags"
          @click="handleShowComGroup('1')"
          :style="{
            color: $store.state.layoutStore.themeObj.color,
            background: $store.state.layoutStore.themeObj.navTagUnselectedColor,
          }"
        >
          <i class="el-icon-plus"></i>选择优先权</span
        >
      </el-form-item>
    </el-form>
    <TableToolTemp
      :toolListProps="titleListPros"
      @handleTool="handleTool"
    ></TableToolTemp>
    <div class="rule-group-wrap">
      <div
        class="rule-group-item"
        v-for="(item, index) in updateForm.ruleGroupList"
        :key="index"
      >
        <div class="rule-group-title">
          <div class="title" @click="item.showRule = !item.showRule">
            <i
              :class="
                item.showRule ? 'el-icon-caret-top' : 'el-icon-caret-bottom'
              "
            ></i
            >规则组{{ index + 1 }}
          </div>
          <div>
            <span
              class="marr"
              v-for="(rule, ruleIdx) in dicGather.ruleTypeList"
              :key="ruleIdx"
            >
              <el-button
                type="text"
                v-if="showAddRule(item, rule.dicItemCode)"
                @click="addRule(item, rule.dicItemCode)"
                >新增{{ rule.dicItemName }}</el-button
              >
            </span>
            <el-button
              type="text"
              @click="delRule(updateForm.ruleGroupList, index)"
              >删除</el-button
            >
          </div>
        </div>
        <div v-show="item.showRule">
          <!-- 条件型规则 -->
          <div
            class="sub-title"
            v-if="item.conditionRuleList && item.conditionRuleList.length"
          >
            <i
              :style="{ background: $store.state.layoutStore.themeObj.color }"
            ></i>
            <span class="sub-font marr">条件型规则</span>
            <el-button
              type="text"
              v-if="item.conditionRuleList.length < 2"
              @click="addRule(item, 'condition')"
              >新增条件型规则</el-button
            >
            <el-button
              type="text"
              v-if="item.conditionRuleList.length > 0"
              @click="delAll(item.conditionRuleList)"
              >清空</el-button
            >
          </div>
          <div class="bdr">
            <div
              class="rule-content"
              v-for="(el, idx) in item.conditionRuleList"
              :key="idx + 'a'"
            >
              <div class="rule-title marr">
                条件型规则{{ idx + 1 }}
                <el-button
                  type="text"
                  v-if="item.conditionRuleList.length > 0"
                  @click="delItemRule(item.conditionRuleList, idx)"
                  >删除</el-button
                >
              </div>
              <div class="rule-form">
                <el-form label-width="160px" label-position="left">
                  <el-form-item v-if="idx > 0" label="与条件型规则1的关系">
                    <el-radio-group
                      v-model="el.ruleRelation"
                      @change="changeValue"
                    >
                      <el-radio
                        :label="p.dicItemCode"
                        v-for="(p, a) in dicGather.ruleRelationList"
                        :key="a"
                        >{{ p.dicItemName }}</el-radio
                      >
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="分配数据源字段">
                    <el-select
                      v-model="el.assignFId"
                      placeholder="请选择"
                      @change="changeFId(el, 'assignFieldList')"
                      class="dt-select"
                    >
                      <el-option
                        v-for="(o, i) in dicGather.assignFieldList"
                        :key="i"
                        :label="o.name"
                        :value="o.fid"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="匹配操作符">
                    <el-select
                      v-model="el.oper"
                      placeholder="请选择"
                      @change="changeOper(el, 'assignFieldList', ...arguments)"
                      class="dt-select"
                    >
                      <el-option
                        v-for="(o, i) in el.indicatorOperList"
                        :key="i"
                        :label="o.dicItemName"
                        :value="o.dicItemCode"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    :label="el.oper == 'range' ? '匹配范围' : '匹配值'"
                    v-if="el.assignFId && el.oper"
                  >
                    <div v-if="el.oper == 'range'">
                      <el-select
                        v-model="el.matchValueRange.startDic"
                        @change="changeValue"
                        placeholder="请选择"
                        class="dt-select-range"
                      >
                        <el-option
                          v-for="(o, i) in dicGather.rangeDicList"
                          :key="i"
                          :label="o.dicItemName"
                          :value="o.dicItemCode"
                        >
                        </el-option>
                      </el-select>
                      <el-input
                        v-model="el.matchValueRange.startValue"
                        @input="changeValue"
                        class="dt-input-range-width"
                        placeholder="请输入"
                      >
                        <template
                          v-if="
                            getType(el.assignFId, el.oper, 'assignFieldList')
                              .showUnit
                          "
                          slot="append"
                          >{{
                            getType(el.assignFId, el.oper, "assignFieldList")
                              .showUnit
                          }}</template
                        >
                      </el-input>
                      <el-select
                        v-model="el.matchValueRange.endDic"
                        @change="changeValue"
                        placeholder="请选择"
                        class="dt-select-range marl"
                      >
                        <el-option
                          v-for="(o, i) in dicGather.rangeDicList"
                          :key="i"
                          :label="o.dicItemName"
                          :value="o.dicItemCode"
                        >
                        </el-option>
                      </el-select>
                      <el-input
                        v-model="el.matchValueRange.endValue"
                        @input="changeValue"
                        class="dt-input-range-width"
                        placeholder="请输入"
                      >
                        <template
                          v-if="
                            getType(el.assignFId, el.oper, 'assignFieldList')
                              .showUnit
                          "
                          slot="append"
                          >{{
                            getType(el.assignFId, el.oper, "assignFieldList")
                              .showUnit
                          }}</template
                        >
                      </el-input>
                    </div>
                    <div v-else>
                      <el-select
                        v-if="
                          getType(el.assignFId, el.oper, 'assignFieldList')
                            .type == 'select'
                        "
                        v-model="el.matchValue"
                        @change="changeValue"
                        placeholder="请选择"
                        class="dt-select"
                      >
                        <el-option
                          v-for="(o, i) in el.matchListForSelect"
                          :key="i"
                          :label="o.dicItemName"
                          :value="o.dicItemCode"
                        >
                        </el-option>
                      </el-select>
                      <el-input
                        v-if="
                          getType(el.assignFId, el.oper, 'assignFieldList')
                            .type == 'input'
                        "
                        :disabled="
                          getType(el.assignFId, el.oper, 'assignFieldList')
                            .isFindBack
                        "
                        v-model="el.matchValue"
                        @input="changeValue"
                        class="dt-input-width"
                        placeholder="请输入"
                      >
                        <template
                          v-if="
                            getType(el.assignFId, el.oper, 'assignFieldList')
                              .showUnit
                          "
                          slot="append"
                          >{{
                            getType(el.assignFId, el.oper, "assignFieldList")
                              .showUnit
                          }}</template
                        >
                      </el-input>
                      <el-button
                        type="text"
                        v-if="
                          getType(el.assignFId, el.oper, 'assignFieldList')
                            .isFindBack
                        "
                        @click="handleShowComGroup('3', el, 'assignFieldList')"
                        >查找带回</el-button
                      >
                      <el-button
                        type="text"
                        v-if="
                          getType(el.assignFId, el.oper, 'assignFieldList')
                            .isFindBack
                        "
                        @click="clearMatchValue(el)"
                        >清空</el-button
                      >
                    </div>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </div>
          <!-- 匹配型规则 -->
          <div
            class="sub-title"
            v-if="item.matchRuleList && item.matchRuleList.length"
          >
            <i
              :style="{ background: $store.state.layoutStore.themeObj.color }"
            ></i>
            <span class="sub-font marr">匹配型规则</span>
            <el-button
              type="text"
              v-if="item.matchRuleList.length < 2"
              @click="addRule(item, 'match')"
              >新增匹配型规则</el-button
            >
            <el-button
              type="text"
              v-if="item.matchRuleList.length > 0"
              @click="delAll(item.matchRuleList)"
              >清空</el-button
            >
          </div>
          <div class="bdr">
            <div
              class="rule-content"
              v-for="(el, idx) in item.matchRuleList"
              :key="idx + 'b'"
            >
              <div class="rule-title marr">
                匹配型规则{{ idx + 1 }}
                <el-button
                  type="text"
                  v-if="item.matchRuleList.length > 0"
                  @click="delItemRule(item.matchRuleList, idx)"
                  >删除</el-button
                >
              </div>
              <div class="rule-form">
                <el-form label-width="160px" label-position="left">
                  <el-form-item v-if="idx > 0" label="与匹配型规则1的关系">
                    <el-radio-group
                      v-model="el.ruleRelation"
                      @change="changeValue"
                    >
                      <el-radio
                        :label="p.dicItemCode"
                        v-for="(p, a) in dicGather.ruleRelationList"
                        :key="a"
                        >{{ p.dicItemName }}</el-radio
                      >
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="匹配源选择">
                    <el-select
                      v-model="el.sourceType"
                      placeholder="请选择"
                      @change="changeSourceType(el)"
                      class="dt-select"
                    >
                      <el-option
                        v-for="(p, a) in dicGather.sourceTypeList"
                        :key="a"
                        :label="p.dicItemName"
                        :value="p.dicItemCode"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    label="分配数据源字段"
                    v-if="el.sourceType == '1'"
                  >
                    <el-select
                      v-model="el.assignFId"
                      placeholder="请选择"
                      @change="changeFId(el, 'assignFieldList')"
                      class="dt-select"
                    >
                      <el-option
                        v-for="(p, a) in dicGather.assignFieldList"
                        :key="a"
                        :label="p.name"
                        :value="p.fid"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="匹配操作符" v-if="el.sourceType == '1'">
                    <el-select
                      v-model="el.oper"
                      placeholder="请选择"
                      @change="changeOper(el, 'assignFieldList', ...arguments)"
                      class="dt-select"
                    >
                      <el-option
                        v-for="(p, a) in el.indicatorOperList"
                        :key="a"
                        :label="p.dicItemName"
                        :value="p.dicItemCode"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="匹配数据源字段">
                    <el-select
                      v-model="el.matchFId"
                      placeholder="请选择"
                      @change="changeFId(el, 'matchFieldList')"
                      class="dt-select"
                    >
                      <el-option
                        v-for="(p, a) in dicGather.matchFieldList"
                        :key="a"
                        :label="p.name"
                        :value="p.fid"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="匹配操作符" v-if="el.sourceType == '2'">
                    <el-select
                      v-model="el.oper"
                      placeholder="请选择"
                      @change="changeOper(el, 'matchFieldList', ...arguments)"
                      class="dt-select"
                    >
                      <el-option
                        v-for="(p, a) in el.indicatorOperList"
                        :key="a"
                        :label="p.dicItemName"
                        :value="p.dicItemCode"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    :label="el.oper == 'range' ? '匹配范围' : '匹配值'"
                    v-if="el.sourceType == '2' && el.matchFId && el.oper"
                  >
                    <div v-if="el.oper == 'range'">
                      <el-select
                        v-model="el.matchValueRange.startDic"
                        placeholder="请选择"
                        @change="changeValue"
                        class="dt-select-range"
                      >
                        <el-option
                          v-for="(p, a) in dicGather.rangeDicList"
                          :key="a"
                          :label="p.dicItemName"
                          :value="p.dicItemCode"
                        >
                        </el-option>
                      </el-select>
                      <el-input
                        v-model="el.matchValueRange.startValue"
                        @input="changeValue"
                        class="dt-input-range-width"
                        placeholder="请输入"
                      >
                        <template
                          v-if="
                            getType(el.matchFId, el.oper, 'matchFieldList')
                              .showUnit
                          "
                          slot="append"
                          >{{
                            getType(el.matchFId, el.oper, "matchFieldList")
                              .showUnit
                          }}</template
                        >
                      </el-input>
                      <el-select
                        v-model="el.matchValueRange.endDic"
                        placeholder="请选择"
                        @change="changeValue"
                        class="dt-select-range marl"
                      >
                        <el-option
                          v-for="(p, a) in dicGather.rangeDicList"
                          :key="a"
                          :label="p.dicItemName"
                          :value="p.dicItemCode"
                        >
                        </el-option>
                      </el-select>
                      <el-input
                        v-model="el.matchValueRange.endValue"
                        @input="changeValue"
                        class="dt-input-range-width"
                        placeholder="请输入"
                      >
                        <template
                          v-if="
                            getType(el.matchFId, el.oper, 'matchFieldList')
                              .showUnit
                          "
                          slot="append"
                          >{{
                            getType(el.matchFId, el.oper, "matchFieldList")
                              .showUnit
                          }}</template
                        >
                      </el-input>
                    </div>
                    <div v-else>
                      <el-select
                        v-if="
                          getType(el.matchFId, el.oper, 'matchFieldList')
                            .type == 'select'
                        "
                        v-model="el.matchValue"
                        @change="changeValue"
                        placeholder="请选择"
                        class="dt-select"
                      >
                        <el-option
                          v-for="(p, a) in el.matchListForSelect"
                          :key="a"
                          :label="p.dicItemName"
                          :value="p.dicItemCode"
                        >
                        </el-option>
                      </el-select>
                      <el-input
                        v-if="
                          getType(el.matchFId, el.oper, 'matchFieldList')
                            .type == 'input'
                        "
                        :disabled="
                          getType(el.matchFId, el.oper, 'matchFieldList')
                            .isFindBack
                        "
                        v-model="el.matchValue"
                        @input="changeValue"
                        class="dt-input-width"
                        placeholder="请输入"
                      >
                        <template
                          v-if="
                            getType(el.matchFId, el.oper, 'matchFieldList')
                              .showUnit
                          "
                          slot="append"
                          >{{
                            getType(el.matchFId, el.oper, "matchFieldList")
                              .showUnit
                          }}</template
                        >
                      </el-input>
                      <el-button
                        type="text"
                        v-if="
                          getType(el.matchFId, el.oper, 'matchFieldList')
                            .isFindBack
                        "
                        @click="handleShowComGroup('3', el, 'matchFieldList')"
                        >查找带回</el-button
                      >
                      <el-button
                        type="text"
                        v-if="
                          getType(el.matchFId, el.oper, 'matchFieldList')
                            .isFindBack
                        "
                        @click="clearMatchValue(el)"
                        >清空</el-button
                      >
                    </div>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </div>
          <!--公式型规则-->
          <div class="sub-title" v-if="item.indicatorFormulaRule">
            <i
              :style="{ background: $store.state.layoutStore.themeObj.color }"
            ></i>
            <span class="sub-font marr"
              >公式型规则
              <el-button
                type="text"
                @click="delAll(item, 'indicatorFormulaRule')"
                >清空</el-button
              >
              <el-button
                type="text"
                @click="delAll(item, 'indicatorFormulaRule', 'del')"
                >删除</el-button
              >
            </span>
          </div>
          <div class="bdr" v-if="item.indicatorFormulaRule">
            <div class="rule-title"></div>
            <div class="rule-form pd20">
              <el-form label-width="160px" label-position="left">
                <el-form-item label="分配数据源字段">
                  <el-select
                    v-model="item.indicatorFormulaRule.assignFId"
                    placeholder="请选择"
                    @change="changeIndicator"
                    class="dt-select"
                  >
                    <el-option
                      v-for="(p, a) in dicGather.assignFieldList"
                      :key="a"
                      :label="p.name"
                      :value="p.fid"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="计算公式">
                  <div>
                    <el-input
                      type="textarea"
                      v-model="item.indicatorFormulaRule.computeFormula"
                      @input="changeValue"
                      class="dt-input-width"
                      placeholder="请输入"
                    >
                    </el-input>
                  </div>
                </el-form-item>
                <el-form-item label="备注">
                  <el-input
                    type="textarea"
                    v-model="item.indicatorFormulaRule.remark"
                    @input="changeValue"
                    class="dt-input-width"
                    placeholder="请输入"
                  >
                  </el-input>
                </el-form-item>
              </el-form>
            </div>
          </div>
          <!-- 匹配结果决策 -->
          <div
            class="sub-title"
            v-if="item.resultDecisionList && item.resultDecisionList.length"
          >
            <i
              :style="{ background: $store.state.layoutStore.themeObj.color }"
            ></i>
            <span class="sub-font marr">匹配结果决策</span>
            <el-button
              type="text"
              v-if="
                showAddInterveneRule &&
                  (item.interveneRuleList == undefined ||
                    item.interveneRuleList.length == 0)
              "
              @click="addInterveneRule(item)"
              >新增优先权比例分配</el-button
            >
          </div>
          <div v-for="(el, idx) in item.resultDecisionList" :key="idx + 'c'">
            <el-form class="marl20" label-width="160px" label-position="left">
              <el-form-item :label="getValue(el.decisionType)">
                <el-select
                  v-model="el.decisionValue"
                  placeholder="请选择"
                  @change="changeDecisionValue(el)"
                  class="dt-select marr"
                >
                  <el-option
                    v-for="(o, i) in dicGather.resultDecisionValueList"
                    :key="i"
                    :label="o.dicItemName"
                    :value="o.dicItemCode"
                  >
                  </el-option>
                </el-select>
                <el-button
                  type="text"
                  v-if="
                    (el.decisionValue == '2' || el.decisionValue == '4') &&
                      el.decisionRuleList &&
                      el.decisionRuleList.length < 2
                  "
                  @click="addDecisionRule(el.decisionRuleList)"
                  >新增决策型规则</el-button
                >
                <el-button
                  type="text"
                  v-if="el.decisionRuleList && el.decisionRuleList.length > 0"
                  @click="delAll(el.decisionRuleList)"
                  >清空</el-button
                >
              </el-form-item>
            </el-form>
            <div class="bdr">
              <div
                v-if="
                  el.decisionType == '2' &&
                    (el.decisionValue == '3' || el.decisionValue == '4') &&
                    el.skipRule
                "
                class="rule-content mar"
              >
                <div class="rule-title">匹配多条数据向下规则</div>
                <div class="rule-form">
                  <el-form label-width="160px" label-position="left">
                    <el-form-item label="是否跳过后续指标">
                      <el-radio-group
                        v-model="el.skipRule.isSkig"
                        @change="changeSkip(el)"
                      >
                        <el-radio
                          :label="p.dicItemCode"
                          v-for="(p, a) in dicGather.status"
                          :key="a"
                          >{{ p.dicItemName }}</el-radio
                        >
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item
                      v-if="el.skipRule.isSkig == '1'"
                      label="跳过后续指标选择"
                    >
                      <el-input
                        disabled
                        v-model="el.skipRule.skipIndicatorIdDisplay"
                        @input="changeValue"
                        class="dt-input-width marr"
                        placeholder="请输入"
                      ></el-input>
                      <el-button
                        type="text"
                        @click="handleShowComGroup('4', el)"
                        >查找带回</el-button
                      >
                      <el-button type="text" @click="delSkipRuleIndicator(el)"
                        >清空</el-button
                      >
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </div>
            <div
              v-if="el.decisionRuleList && el.decisionRuleList.length > 0"
              class="mar bdr"
            >
              <div
                class="rule-content"
                v-for="(o, i) in el.decisionRuleList"
                :key="i"
              >
                <div class="rule-title marr">
                  决策型规则{{ i + 1 }}
                  <el-button
                    type="text"
                    v-if="el.decisionRuleList.length > 0"
                    @click="delItemRule(el.decisionRuleList, i)"
                    >删除</el-button
                  >
                </div>
                <div class="rule-form">
                  <el-form label-width="160px" label-position="left">
                    <el-form-item v-if="i > 0" label="与决策型规则1的关系">
                      <el-radio-group
                        v-model="o.ruleRelation"
                        @change="changeValue"
                      >
                        <el-radio
                          :label="p.dicItemCode"
                          v-for="(p, a) in dicGather.ruleRelationList"
                          :key="a"
                          >{{ p.dicItemName }}</el-radio
                        >
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item label="匹配源选择">
                      <el-select
                        v-model="o.sourceType"
                        placeholder="请选择"
                        @change="changeSourceType(o)"
                        class="dt-select"
                      >
                        <el-option
                          v-for="(p, a) in dicGather.sourceTypeList"
                          :key="a"
                          :label="p.dicItemName"
                          :value="p.dicItemCode"
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item
                      label="分配数据源字段"
                      v-if="o.sourceType == '1'"
                    >
                      <el-select
                        v-model="o.assignFId"
                        placeholder="请选择"
                        @change="changeFId(o, 'assignFieldList')"
                        class="dt-select"
                      >
                        <el-option
                          v-for="(p, a) in dicGather.assignFieldList"
                          :key="a"
                          :label="p.name"
                          :value="p.fid"
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="匹配操作符" v-if="o.sourceType == '1'">
                      <el-select
                        v-model="o.oper"
                        placeholder="请选择"
                        @change="changeOper(o, 'assignFieldList', ...arguments)"
                        class="dt-select"
                      >
                        <el-option
                          v-for="(p, a) in o.indicatorOperList"
                          :key="a"
                          :label="p.dicItemName"
                          :value="p.dicItemCode"
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="匹配数据源字段">
                      <el-select
                        v-model="o.matchFId"
                        placeholder="请选择"
                        @change="changeFId(o, 'matchFieldList')"
                        class="dt-select"
                      >
                        <el-option
                          v-for="(p, a) in dicGather.matchFieldList"
                          :key="a"
                          :label="p.name"
                          :value="p.fid"
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="匹配操作符" v-if="o.sourceType == '2'">
                      <el-select
                        v-model="o.oper"
                        placeholder="请选择"
                        @change="changeOper(o, 'matchFieldList', ...arguments)"
                        class="dt-select"
                      >
                        <el-option
                          v-for="(p, a) in o.indicatorOperList"
                          :key="a"
                          :label="p.dicItemName"
                          :value="p.dicItemCode"
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item
                      :label="o.oper == 'range' ? '匹配范围' : '匹配值'"
                      v-if="o.sourceType == '2' && o.matchFId && o.oper"
                    >
                      <div v-if="o.oper == 'range'">
                        <el-select
                          v-model="o.matchValueRange.startDic"
                          placeholder="请选择"
                          @change="changeValue"
                          class="dt-select-range"
                        >
                          <el-option
                            v-for="(p, a) in dicGather.rangeDicList"
                            :key="a"
                            :label="p.dicItemName"
                            :value="p.dicItemCode"
                          >
                          </el-option>
                        </el-select>
                        <el-input
                          v-model="o.matchValueRange.startValue"
                          @input="changeValue"
                          class="dt-input-range-width"
                          placeholder="请输入"
                        >
                          <template
                            v-if="
                              getType(o.matchFId, o.oper, 'matchFieldList')
                                .showUnit
                            "
                            slot="append"
                            >{{
                              getType(o.matchFId, o.oper, "matchFieldList")
                                .showUnit
                            }}</template
                          >
                        </el-input>
                        <el-select
                          v-model="o.matchValueRange.endDic"
                          placeholder="请选择"
                          @change="changeValue"
                          class="dt-select-range marl"
                        >
                          <el-option
                            v-for="(p, a) in dicGather.rangeDicList"
                            :key="a"
                            :label="p.dicItemName"
                            :value="p.dicItemCode"
                          >
                          </el-option>
                        </el-select>
                        <el-input
                          v-model="o.matchValueRange.endValue"
                          @input="changeValue"
                          class="dt-input-range-width"
                          placeholder="请输入"
                        >
                          <template
                            v-if="
                              getType(o.matchFId, o.oper, 'matchFieldList')
                                .showUnit
                            "
                            slot="append"
                            >{{
                              getType(o.matchFId, o.oper, "matchFieldList")
                                .showUnit
                            }}</template
                          >
                        </el-input>
                      </div>
                      <div v-else>
                        <el-select
                          v-if="
                            getType(o.matchFId, o.oper, 'matchFieldList')
                              .type == 'select'
                          "
                          v-model="o.matchValue"
                          @change="changeValue"
                          placeholder="请选择"
                          class="dt-select"
                        >
                          <el-option
                            v-for="(p, a) in o.matchListForSelect"
                            :key="a"
                            :label="p.dicItemName"
                            :value="p.dicItemCode"
                          >
                          </el-option>
                        </el-select>
                        <el-input
                          v-if="
                            getType(o.matchFId, o.oper, 'matchFieldList')
                              .type == 'input'
                          "
                          :disabled="
                            getType(o.matchFId, o.oper, 'matchFieldList')
                              .isFindBack
                          "
                          v-model="o.matchValue"
                          @input="changeValue"
                          class="dt-input-width"
                          placeholder="请输入"
                        >
                          <template
                            v-if="
                              getType(o.matchFId, o.oper, 'matchFieldList')
                                .showUnit
                            "
                            slot="append"
                            >{{
                              getType(o.matchFId, o.oper, "matchFieldList")
                                .showUnit
                            }}</template
                          >
                        </el-input>
                        <el-button
                          type="text"
                          v-if="
                            getType(o.matchFId, o.oper, 'matchFieldList')
                              .isFindBack
                          "
                          @click="handleShowComGroup('3', o, 'matchFieldList')"
                          >查找带回</el-button
                        >
                        <el-button
                          type="text"
                          v-if="
                            getType(o.matchFId, o.oper, 'matchFieldList')
                              .isFindBack
                          "
                          @click="clearMatchValue(o)"
                          >清空</el-button
                        >
                      </div>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </div>
          </div>

          <!-- 优先权比例分配设置 -->
          <div class="sub-title" v-if="item.interveneRuleList != undefined">
            <i
              :style="{ background: $store.state.layoutStore.themeObj.color }"
            ></i>
            <span class="sub-font marr">优先权比例分配设置</span>
            <el-button
              type="text"
              @click="
                handleShowComGroup(
                  '2',
                  item.interveneRuleList,
                  'interveneRuleList',
                  index
                )
              "
              >优先权选择</el-button
            >
            <el-button
              type="text"
              v-if="item.interveneRuleList.length > 0"
              @click="delAll(item, 'interveneRuleList')"
              >清空</el-button
            >
          </div>
          <div
            v-if="
              item.interveneRuleList != undefined &&
                item.interveneRuleList.length
            "
            class="marl20"
          >
            <div class="rule-form">
              <el-form label-width="160px" label-position="left">
                <el-form-item
                  v-for="(el, idx) in item.interveneRuleList"
                  :key="idx"
                  :label="el.priorityName"
                >
                  <el-input
                    v-model="el.assignCount"
                    class="dt-input-width marr"
                    placeholder="请输入"
                  ></el-input>
                  <el-button
                    type="text"
                    @click="delItemRule(item.interveneRuleList, idx)"
                    >删除</el-button
                  >
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>
      </div>
      <div class="marl13">
        <el-button type="primary" @click="submit">保存</el-button>
        <el-button
          @click="goBack"
          type="primary"
          plain
          :style="{ color: $store.state.layoutStore.themeObj.color }"
          >取消</el-button
        >
      </div>
    </div>

    <ComPopup
      :showComGroup="showComGroup"
      :title="comPopupTitile"
      :comGroupSearchVal="comGroupSearchVal"
      :tableHead="popupTableHead"
      :translateArr="translateArr"
      :searchTemp="popupSearchTemp"
      :selectList="selectList"
      :tableData="popupTableData"
      @closeDialog="closeDialog"
      @select="select"
    ></ComPopup>
  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import ComPopup from "@/components/layouts/ComPopup";
import vuedraggable from "vuedraggable";
import { getDicItemList } from "@/config/tool.js";
import {
  getConfig,
  getSourceTemplate,
  saveConfig,
} from "@/api/strategyMatchManage/strategymatchindicator/index.js";
import { Loading } from "element-ui";
export default {
  name: "strategyMatchIndicatorConfig",
  data() {
    return {
      titleListProsBase: {
        toolTitle: "指标基础信息",
      },
      titleListPros: {
        toolTitle: "指标规则设置",
        toolList: [
          {
            name: "新增规则组",
            btnCode: "",
            type: "add",
          },
        ],
      },
      updateForm: {
        indicatorId: "",
        priorityFields: "",
        priorityFieldsDisplay: [],
        ruleGroupList: [],
      },
      sourceTemplate: {},
      ruleType: "condition",
      dicGather: {
        status: [],
        assignFieldList: [],
        matchFieldList: [],
        operList: [],
        rangeDicList: [],
        ruleTypeList: [],
        skipRuleIndicatorList: [],
        resultDecisionTypeList: [],
        resultDecisionValueList: [],
        ruleRelationList: [],
        sourceTypeList: [],
      },
      showComGroup: false,
      popupTableHead: [],
      popupTableData: [],
      popupSearchTemp: [
        {
          label: "",
          name: "name",
          type: "input",
        },
      ],
      selectList: [],
      translateArr: [],
      comGroupSearchVal: "priorityName",
      comPopupTitile: "",
      selectType: "",
      selectEle: {},
      selectListType: "",
      selectRuleGroupIndex: "",
    };
  },
  components: {
    TableToolTemp,
    ComPopup,
    vuedraggable,
  },
  computed: {
    getText() {
      if (this.type == "add") {
        return "新增指标";
      }
      if (this.type == "edit") {
        return "编辑指标";
      }
    },
    strategyMatchIndicatorObj() {
      return this.$store.state.strategyMatchManage.strategyMatchIndicatorObj;
    },
    showAddInterveneRule() {
      let o = this._.find(this.dicGather.matchFieldList, ["propertyType", "3"]);
      if (o) {
        return true;
      }
      return false;
    },
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      this.type = this.$route.query.type;
      this.updateForm.indicatorId = this.$route.query.indicatorId || "";
      let loadingInstance = Loading.service({
        fullscreen: true,
        lock: true,
        text: "加载中...",
        target: document.getElementsByTagName("body")[0],
      });
      await this.getSourceTemplate();
      await this.getDictList();
      await this.getConfig();
      loadingInstance.close();
    },
    async getSourceTemplate() {
      let param = {
        strategyId: this.$route.query.strategyId,
        indicatorId: this.$route.query.indicatorId,
      };
      let res = await getSourceTemplate(param);
      if (!res) {
        return;
      }
      this.sourceTemplate = res;
    },
    async getDictList() {
      this.dicGather.assignFieldList = this.sourceTemplate.assignFieldList;
      this.dicGather.matchFieldList = this.sourceTemplate.matchFieldList;
      this.dicGather.skipRuleIndicatorList = this.sourceTemplate.skipRuleIndicatorList;
      this.dicGather.ruleTypeList = await getDicItemList(
        "sct.indicator.ruleType"
      );
      this.dicGather.status = await getDicItemList("gen.yesorno.num");
      this.dicGather.operList = await getDicItemList("sct.indicator.oper");
      this.dicGather.resultDecisionTypeList = await getDicItemList(
        "sct.indicator.resultDecisionType"
      );
      this.dicGather.resultDecisionValueList = await getDicItemList(
        "sct.indicator.resultDecisionValue"
      );
      this.dicGather.ruleRelationList = await getDicItemList(
        "sct.indicator.ruleRelation"
      );
      this.dicGather.sourceTypeList = await getDicItemList(
        "sct.indicator.sourceType"
      );
      let arr = ["gt", "gte", "lt", "lte"];
      this.dicGather.rangeDicList = this._.filter(
        this.dicGather.operList,
        (el) => {
          return this._.includes(arr, el.dicItemCode);
        }
      );
      await getDicItemList("sct.source.priorityRule");
    },
    async getConfig() {
      let res = await getConfig({ indicatorId: this.$route.query.indicatorId });
      if (res) {
        let getList = (list, listType) => {
          if (list != undefined) {
            this._.each(list, (item) => {
              item.matchValueRange = {
                startDic: "",
                startValue: "",
                endDic: "",
                endValue: "",
              };

              if (item.sourceType == "2") {
                item.indicatorOperList = this.getIndicatorOperList(
                  "matchFieldList",
                  item.matchFId
                );
                item.matchListForSelect = this.getMatchListForSelect(
                  "matchFieldList",
                  item.matchFId
                );
              } else {
                item.indicatorOperList = this.getIndicatorOperList(
                  "assignFieldList",
                  item.assignFId
                );
                item.matchListForSelect = this.getMatchListForSelect(
                  "assignFieldList",
                  item.assignFId
                );
              }

              item.ruleRelation = item.ruleRelation || "or";

              let fid = item.assignFId;

              if (listType == "matchFieldList") {
                fid = item.matchFId;
              }

              // 字典转换
              if (
                this.getType(fid, item.oper, listType).isFindBack &&
                item.matchValue
              ) {
                item.matchValueDic = item.matchValue;
                let arr = item.matchValue.split("/");

                item.matchValue = this._.map(arr, (o) => {
                  let dic = this._.find(item.matchListForSelect, [
                    "dicItemCode",
                    o,
                  ]);
                  if (dic) {
                    return dic.dicItemName;
                  } else {
                    return o;
                  }
                }).join("/");
              } else {
                item.matchValueDic = "";
              }

              if (item.oper == "range") {
                let o = JSON.parse(item.matchValue);
                let keys = this._.keys(o);
                item.matchValueRange.startDic = keys[0];
                item.matchValueRange.startValue = o[keys[0]];
                item.matchValueRange.endDic = keys[1];
                item.matchValueRange.endValue = o[keys[1]];
              }
            });
          }
          return list || [];
        };

        this.updateForm.priorityFields = res.priorityFields;
        if (this.updateForm.priorityFields) {
          let arr = this.updateForm.priorityFields.split("/");
          if (arr) {
            let priorityRuleList = this._.filter(
              this.dicGather.matchFieldList,
              (el) => {
                return el.propertyType == "2";
              }
            );
            this.updateForm.priorityFieldsDisplay = this._.map(arr, (item) => {
              let o = this._.find(priorityRuleList, ["fid", item]);
              if (o) {
                return {
                  fid: o.fid,
                  name: o.name,
                };
              } else {
                return {
                  fid: item,
                  name: "",
                };
              }
            });
          }
        }

        this._.each(res.ruleGroupList, (obj) => {
          obj.showRule = true;
          obj.conditionRuleList = getList(
            obj.conditionRuleList,
            "assignFieldList"
          );
          obj.matchRuleList = getList(obj.matchRuleList, "matchFieldList");
          if (obj.resultDecisionList != undefined) {
            this._.each(obj.resultDecisionList, (item, index) => {
              item.decisionRuleList = getList(
                item.decisionRuleList,
                "matchFieldList"
              );
              if (item.skipRule) {
                if (item.skipRule.skipIndicatorId) {
                  item.skipRule.isSkig = "1";
                  let arr1 = item.skipRule.skipIndicatorId.split("/");
                  let str2 = this._.map(arr1, (el) => {
                    let o = this._.find(this.dicGather.skipRuleIndicatorList, [
                      "indicatorId",
                      el,
                    ]);
                    return o.name;
                  }).join("/");

                  item.skipRule.skipIndicatorIdDisplay = str2;
                }
              } else {
                item.skipRule = {
                  isSkig: "0",
                  skipIndicatorId: "",
                  skipIndicatorIdDisplay: "",
                };
              }
            });
          }

          if (obj.interveneRuleList != undefined) {
            let arr = this._.filter(this.dicGather.matchFieldList, (el) => {
              return el.propertyType == "2" || el.propertyType == "3";
            });
            obj.interveneRuleList = this._.map(
              obj.interveneRuleList,
              (item) => {
                let o = this._.find(arr, (el) => {
                  return el.priorityConfig.fid == item.matchFId;
                });
                return {
                  matchFId: item.matchFId,
                  assignCount: item.assignCount,
                  priorityName: o.priorityConfig.priorityName,
                };
              }
            );
          }
        });

        this.updateForm.ruleGroupList = res.ruleGroupList || [];
      }
    },

    handleTool(item) {
      if (item.type == "add") {
        if (this.updateForm.ruleGroupList.length == 3) {
          this.$message.error("规则组最多新增三个");
          return;
        }
        let len = this.updateForm.ruleGroupList.length;
        this.updateForm.ruleGroupList.push({
          showRule: true,
          resultDecisionList: [
            {
              sort: 1,
              decisionType: "1",
              decisionValue: "1",
              decisionRuleList: [],
              skipRule: {
                isSkig: "0",
                skipIndicatorId: "",
                skipIndicatorIdDisplay: "",
              },
            },
            {
              sort: 2,
              decisionType: "2",
              decisionValue: "3",
              decisionRuleList: [],
              skipRule: {
                isSkig: "0",
                skipIndicatorId: "",
                skipIndicatorIdDisplay: "",
              },
            },
          ],
        });
      }
    },
    getValue(val) {
      let o = this._.find(this.dicGather.resultDecisionTypeList, [
        "dicItemCode",
        val,
      ]);
      if (o) {
        return o.dicItemName;
      }
      return val;
    },
    getType(fid, oper, listType) {
      let o = this._.find(this.dicGather[listType], ["fid", fid]);
      let obj = {
        type: "input",
        isFindBack: false,
        showUnit: false,
      };
      if (o) {
        obj.showUnit = o.unit || false;
        if (o.isDic == "1") {
          // 等于、不等于，select单选
          if (this._.includes(["eq", "neq"], oper)) {
            obj.type = "select";
          } else {
            obj.isFindBack = true;
          }
        }
      }
      return obj;
    },
    changeSourceType(el) {
      el.assignFId = "";
      el.oper = "";
      el.matchFId = "";
      el.matchValue = "";
      el.matchValueDic = "";
      el.matchValueRange = {
        startDic: "",
        startValue: "",
        endDic: "",
        endValue: "",
      };
      el.matchListForSelect = [];
      el.indicatorOperList = [];
      this.changeValue();
    },
    changeFId(el, listType) {
      if (el.sourceType == "2" || listType == "assignFieldList") {
        el.oper = "";
      }
      el.matchValue = "";
      el.matchValueDic = "";
      el.matchValueRange = {
        startDic: "",
        startValue: "",
        endDic: "",
        endValue: "",
      };
      el.matchListForSelect = [];
      let fid = el.assignFId;
      if (listType == "matchFieldList") {
        fid = el.matchFId;
      }

      el.indicatorOperList = this.getIndicatorOperList(listType, fid);

      this.changeValue();
    },
    changeIndicator() {
      this.changeValue();
    },
    getIndicatorOperList(listType, fid) {
      let list = [];
      let o = this._.find(this.dicGather[listType], ["fid", fid]);
      if (o) {
        let arr = [];
        if (o.isDic == "1") {
          // 字段类型是字典  匹配符 :包含 不包含 等于 不等于
          arr = ["eq", "neq", "contain", "notcontain"];
        } else if (o.dataType == "int" || o.dataType == "float") {
          // 字段类型是整数或浮点 匹配符：等于 不等于 大于 小于 大于等于 小于等于 范围
          arr = ["eq", "neq", "gte", "gt", "lt", "lte", "range"];
        } else {
          // 字段类型是字符串  匹配符：等于 不等于 包含 不包含
          arr = ["eq", "neq", "contain", "notcontain"];
        }
        list = this._.filter(this.dicGather.operList, (el) => {
          return this._.includes(arr, el.dicItemCode);
        });
      }

      return list;
    },
    changeOper(el, listType, val) {
      el.matchValue = "";
      el.matchValueDic = "";
      el.matchValueRange = {
        startDic: "",
        startValue: "",
        endDic: "",
        endValue: "",
      };
      el.matchListForSelect = [];
      if (listType) {
        let fid = el.assignFId;
        if (listType == "matchFieldList") {
          fid = el.matchFId;
        }
        el.matchListForSelect = this.getMatchListForSelect(listType, fid);
      }

      this.changeValue();
    },

    getMatchListForSelect(listType, fid) {
      let list = [];
      let o = this._.find(this.dicGather[listType], ["fid", fid]);
      if (o) {
        list = this._.map(o.dicList, (item) => {
          return {
            dicItemCode: item.dicKey,
            dicItemName: item.dicValue,
          };
        });
      }
      return list;
    },
    changeValue() {
      this.$forceUpdate();
    },

    changeSkip(item) {
      item.skipRule.skipIndicatorId = "";
      item.skipRule.skipIndicatorIdDisplay = "";
      this.changeValue();
    },
    changeDecisionValue(item) {
      if (item.decisionType == "2") {
        item.skipRule.isSkig = "1";
        item.skipRule.skipIndicatorId = "";
        item.skipRule.skipIndicatorIdDisplay = "";

        if (item.decisionValue == "1" || item.decisionValue == "3") {
          item.decisionRuleList = [];
        }
      }
      this.changeValue();
    },
    delSkipRuleIndicator(item) {
      item.skipRule.skipIndicatorId = "";
      item.skipRule.skipIndicatorIdDisplay = "";
      this.changeValue();
    },
    // type  1 基础信息优先权  2优先权比例分配  3 字典 4指标
    handleShowComGroup(type, el, listType, index) {
      this.selectType = type;
      this.selectEle = el;
      this.selectListType = listType;
      this.selectRuleGroupIndex = index;
      if (type == "1" || type == "2") {
        this.popupTableHead = [
          {
            column_name: "priorityName",
            column_comment: "优先权名称",
          },
          {
            column_name: "priorityRule",
            column_comment: "优先权规则",
          },
          {
            column_name: "description",
            column_comment: "说明",
          },
        ];
        this.popupSearchTemp[0].label = "优先权名称";
        this.translateArr = [
          {
            key: "priorityRule",
            dic: "sct.source.priorityRule",
          },
        ];
        let list = ["2"];
        if (type == "2") {
          list = ["2", "3"];
        }
        let arr = this._.filter(this.dicGather.matchFieldList, (el) => {
          return this._.includes(list, el.propertyType);
        });
        this.popupTableData = this._.map(arr, (el) => {
          return {
            fid: el.priorityConfig.fid,
            priorityName: el.priorityConfig.priorityName,
            priorityRule: el.priorityConfig.priorityRule,
            description: el.priorityConfig.description,
          };
        });
        if (type == "2") {
          this.selectList = this._.map(el, (o) => {
            return {
              fid: o.fid,
            };
          });
        } else {
          this.selectList = this.updateForm.priorityFieldsDisplay;
        }

        this.comGroupSearchVal = "priorityName";
        this.comPopupTitile = "优先权选择";
      } else if (type == "4") {
        this.popupTableHead = [
          {
            column_name: "name",
            column_comment: "指标名称",
          },
          {
            column_name: "fid",
            column_comment: "指标ID",
          },
        ];
        this.popupSearchTemp[0].label = "指标名称";
        if (el.skipRule.skipIndicatorId != "") {
          let arr = el.skipRule.skipIndicatorId.split("/");
          this.selectList = this._.map(arr, (item) => {
            let o = this._.find(this.dicGather.skipRuleIndicatorList, [
              "indicatorId",
              item,
            ]);
            if (o) {
              return {
                fid: o.indicatorId,
                name: o.name,
              };
            } else {
              return {
                fid: item,
                name: "",
              };
            }
          });
        } else {
          this.selectList = [];
        }

        this.popupTableData = this._.map(
          this.dicGather.skipRuleIndicatorList,
          (item) => {
            return {
              fid: item.indicatorId,
              name: item.name,
            };
          }
        );
        this.comGroupSearchVal = "name";
        this.comPopupTitile = "指标名称";
      } else {
        let fid = el.assignFId;
        if (listType == "matchFieldList") {
          fid = el.matchFId;
        }
        let o = this._.find(this.dicGather[listType], ["fid", fid]);
        if (!o) {
          return;
        }

        this.popupTableHead = [
          {
            column_name: "name",
            column_comment: "字典名称",
          },
          {
            column_name: "fid",
            column_comment: "字典值",
          },
        ];
        this.popupSearchTemp[0].label = "字典名称";
        if (el.matchValue != "") {
          let arr = el.matchValue.split("/");
          this.selectList = this._.map(arr, (item) => {
            let dic = this._.find(o.dicList, ["dicValue", item]);
            if (dic) {
              return {
                fid: dic.dicKey,
                name: dic.dicValue,
              };
            } else {
              return {
                fid: "",
                name: item,
              };
            }
          });
        } else {
          this.selectList = [];
        }

        this.popupTableData = this._.map(o.dicList, (item) => {
          return {
            fid: item.dicKey,
            name: item.dicValue,
          };
        });
        this.comGroupSearchVal = "name";
        this.comPopupTitile = o.name;
      }

      this.showComGroup = true;
    },
    handleClose(item, index) {
      this.updateForm.priorityFieldsDisplay.splice(index, 1);
    },
    showAddRule(item, code) {
      let key = code + "RuleList";
      if (item[key] && item[key].length > 0) {
        return false;
      }
      if (code == "formula" && item["indicatorFormulaRule"]) {
        return false;
      }
      return true;
    },
    addRule(item, code) {
      console.log(code);
      let key = code + "RuleList";
      let obj = {
        ruleRelation: "or",
        assignFId: "",
        oper: "",
        matchValueRange: {
          startDic: "",
          startValue: "",
          endDic: "",
          endValue: "",
        },
        matchValue: "",
        matchValueDic: "",
        indicatorOperList: [],
        matchListForSelect: [],
      };
      if (code == "match") {
        obj.sourceType = "";
        obj.matchFId = "";
      }
      if (this._.has(item, key)) {
        item[key].push(obj);
      } else if (code == "formula") {
        item["indicatorFormulaRule"] = {
          computeFormula: "",
          assignFId: "",
          remark: "",
        };
      } else {
        item[key] = [obj];
      }
      this.$forceUpdate();
    },
    delRule(item, index) {
      item.splice(index, 1);
    },
    clearMatchValue(item) {
      item.matchValue = "";
      item.matchValueDic = "";
    },
    addDecisionRule(item) {
      item.push({
        ruleRelation: "or",
        sourceType: "",
        assignFId: "",
        oper: "",
        matchFId: "",
        matchValueRange: {
          startDic: "",
          startValue: "",
          endDic: "",
          endValue: "",
        },
        matchValue: "",
        matchValueDic: "",
        indicatorOperList: [],
        matchListForSelect: [],
      });
    },
    addInterveneRule(item) {
      item.interveneRuleList = [];
      this.changeValue();
    },
    delItemRule(item, index) {
      item.splice(index, 1);
      this.changeValue();
    },
    delAll(item, type, name) {
      if (type) {
        if (type == "indicatorFormulaRule") {
          if (name == "del") {
            delete item[type];
          } else {
            item[type] = { computeFormula: "", assignFId: "", remark: "" };
          }
        } else {
          delete item[type];
          return;
        }
      }
      item.length = 0;
      this.changeValue();
    },

    closeDialog() {
      this.showComGroup = false;
    },

    select(data) {
      if (this.selectType == "1") {
        this.updateForm.priorityFieldsDisplay = data;
        this.updateForm.priorityFields = this._.map(data, (o) => {
          return o.fid;
        }).join("/");
      } else if (this.selectType == "2") {
        this.selectEle = this._.map(data, (o) => {
          return {
            fid: o.fid,
            matchFId: o.fid,
            priorityName: o.name,
            assignCount: "",
          };
        });

        if (this.selectListType == "interveneRuleList") {
          this.updateForm.ruleGroupList[
            this.selectRuleGroupIndex
          ].interveneRuleList = this.selectEle;
        }
      } else if (this.selectType == "3") {
        let str1 = this._.map(data, (o) => {
          return o.name;
        }).join("/");
        let str2 = this._.map(data, (o) => {
          return o.fid;
        }).join("/");
        this.selectEle.matchValue = str1;
        this.selectEle.matchValueDic = str2;
      } else if (this.selectType == "4") {
        let strFid = this._.map(data, (o) => {
          return o.fid;
        }).join("/");
        let strName = this._.map(data, (o) => {
          return o.name;
        }).join("/");
        this.selectEle.skipRule.skipIndicatorIdDisplay = strName;
        this.selectEle.skipRule.skipIndicatorId = strFid;
      }

      this.showComGroup = false;
    },
    validateCom(list, index, type) {
      let mapText = {
        conditionRuleList: "条件型规则",
        matchRuleList: "匹配型规则",
        indicatorFormulaRule: "公式型规则",
        decisionRuleList: "决策型规则",
      };

      let obj = {
        pass: true,
        data: null,
      };
      if (list && type == "indicatorFormulaRule") {
        if (!list.assignFId) {
          obj.pass = false;
          obj.data = `请选择规则组${index + 1}下的${
            mapText[type]
          }的分配数据源字段`;
        } else if (!list.computeFormula) {
          obj.pass = false;
          obj.data = `请选择规则组${index + 1}下的${
            mapText[type]
          }的计算公式字段`;
        }
        return obj;
      }
      if (list && list.length > 0) {
        this._.each(list, (el, idx) => {
          if (
            (type == "matchRuleList" || type == "decisionRuleList") &&
            !el.sourceType
          ) {
            obj.pass = false;
            obj.data = `请选择规则组${index + 1}下的${mapText[type]}${idx +
              1}的匹配源`;
            return false;
          }

          if (
            (type == "conditionRuleList" ||
              type == "indicatorFormulaRule" ||
              ((type == "matchRuleList" || type == "decisionRuleList") &&
                el.sourceType == "1")) &&
            !el.assignFId
          ) {
            obj.pass = false;
            obj.data = `请选择规则组${index + 1}下的${mapText[type]}${idx +
              1}的分配数据源字段`;
            return false;
          }

          if (
            (type == "matchRuleList" || type == "decisionRuleList") &&
            !el.matchFId
          ) {
            obj.pass = false;
            obj.data = `请选择规则组${index + 1}下的${mapText[type]}${idx +
              1}的匹配数据源字段`;
            return false;
          }

          if (!el.oper) {
            obj.pass = false;
            obj.data = `请选择规则组${index + 1}下的${mapText[type]}${idx +
              1}的匹配操作符`;
            return false;
          }
          if (
            (((type == "matchRuleList" || type == "decisionRuleList") &&
              el.sourceType == "2") ||
              type == "conditionRuleList") &&
            el.oper != "range" &&
            !el.matchValue
          ) {
            obj.pass = false;
            obj.data = `请选择规则组${index + 1}下的${mapText[type]}${idx +
              1}的匹配值`;
            return false;
          }

          if (
            (((type == "matchRuleList" || type == "decisionRuleList") &&
              el.sourceType == "2") ||
              type == "conditionRuleList") &&
            el.oper == "range" &&
            (!el.matchValueRange.startDic ||
              !el.matchValueRange.startValue ||
              !el.matchValueRange.endDic ||
              !el.matchValueRange.endValue)
          ) {
            obj.pass = false;
            obj.data = `请完成规则组${index + 1}下的${mapText[type]}${idx +
              1}的所有匹配值录入项`;
            return false;
          }

          if (
            (((type == "matchRuleList" || type == "decisionRuleList") &&
              el.sourceType == "2") ||
              type == "conditionRuleList") &&
            el.oper == "range" &&
            (!/^\d+$/.test(el.matchValueRange.startValue) ||
              !/^\d+$/.test(el.matchValueRange.endValue))
          ) {
            obj.pass = false;
            obj.data = `请完成规则组${index + 1}下的${mapText[type]}${idx +
              1}的所有匹配值必须为数字`;
            return false;
          }
        });
      }

      return obj;
    },
    validateInput() {
      let obj = {
        pass: true,
        data: null,
      };

      // if (!this.updateForm.priorityFields) {
      //   obj.pass = false
      //   obj.data = `请选择优先权`
      //   return obj
      // }

      this._.each(this.updateForm.ruleGroupList, (item, index) => {
        obj = this.validateCom(
          item.conditionRuleList,
          index,
          "conditionRuleList"
        );
        if (!obj.pass) {
          return false;
        }
        obj = this.validateCom(item.matchRuleList, index, "matchRuleList");
        if (!obj.pass) {
          return false;
        }

        obj = this.validateCom(
          item.indicatorFormulaRule,
          index,
          "indicatorFormulaRule"
        );
        if (!obj.pass) {
          return false;
        }

        this._.each(item.resultDecisionList, (el, idx) => {
          if (
            el.decisionType == "2" &&
            (el.decisionValue == "3" || el.decisionValue == "4")
          ) {
            if (
              el.skipRule &&
              el.skipRule.isSkig == "1" &&
              !el.skipRule.skipIndicatorId
            ) {
              obj.pass = false;
              obj.data = `请选择规则组${index +
                1}下的匹配多条数据向下规则的跳过后续指标`;
              return false;
            }
          }
          obj = this.validateCom(
            el.decisionRuleList,
            index,
            "decisionRuleList"
          );
          if (!obj.pass) {
            return false;
          }
        });
        if (!obj.pass) {
          return false;
        }

        if (item.interveneRuleList && item.interveneRuleList.length > 0) {
          this._.each(item.interveneRuleList, (el, idx) => {
            if (!el.assignCount) {
              obj.pass = false;
              obj.data = `请填写规则组${index + 1}下的${
                el.priorityName
              }分配比例`;
              return false;
            } else if (!/^\d+$/.test(el.assignCount)) {
              obj.pass = false;
              obj.data = `规则组${index + 1}下的${
                el.priorityName
              }分配比例必须为数字`;
              return false;
            }
          });
        }
        if (!obj.pass) {
          return false;
        }
      });

      return obj;
    },

    goBack() {
      this.$router.go(-1);
    },

    structureParam(updateForm) {
      let obj = this._.cloneDeep(updateForm);
      let arr = this._.map(obj.priorityFieldsDisplay, (item) => {
        return item.fid;
      });
      obj.priorityFields = arr.join("/");
      delete obj.priorityFieldsDisplay;

      let mapList = (item, list, type) => {
        if (list && list.length > 0) {
          list = this._.map(list, (el, idx) => {
            let o = {
              ruleGroup: item.ruleGroup,
              sort: idx + 1,
              assignFId: el.assignFId,
              oper: el.oper,
            };
            if (idx > 0) {
              o.ruleRelation = el.ruleRelation;
            }
            if (type == "matchRuleList" || type == "decisionRuleList") {
              o.sourceType = el.sourceType;
              o.matchFId = el.matchFId;
            }
            if (o.oper == "range") {
              let obj = {};
              obj[el.matchValueRange.startDic] = el.matchValueRange.startValue;
              obj[el.matchValueRange.endDic] = el.matchValueRange.endValue;
              o.matchValue = JSON.stringify(obj);
              delete el.matchValueRange;
            } else {
              // o.matchValue = el.matchValue

              o.matchValue = el.matchValueDic || el.matchValue;
            }

            return o;
          });
        }
        return list;
      };

      this._.each(obj.ruleGroupList, (item, index) => {
        delete item.showRule;
        item.ruleGroup = index + 1;
        item.conditionRuleList = mapList(
          item,
          item.conditionRuleList,
          "conditionRuleList"
        );
        item.matchRuleList = mapList(item, item.matchRuleList, "matchRuleList");
        this._.each(item.resultDecisionList, (el, idx) => {
          el.ruleGroup = item.ruleGroup;
          el.sort = idx + 1;
          if (idx == 0) {
            delete el.skipRule;
          } else {
            if (el.skipRule.isSkig == "0") {
              // el.skipRule.ruleGroup = item.ruleGroup
              // delete el.skipRule.isSkig
              // delete el.skipRule.skipIndicatorIdDisplay
              delete el.skipRule;
            }
          }
          el.decisionRuleList = mapList(
            item,
            el.decisionRuleList,
            "decisionRuleList"
          );
        });

        this._.each(item.interveneRuleList, (el, idx) => {
          el.ruleGroup = item.ruleGroup;
          el.sort = idx + 1;
          delete el.priorityName;
          delete el.fid;
        });
      });

      return obj;
    },
    async submit() {
      let obj = this.validateInput();
      console.log(obj);
      if (!obj.pass) {
        this.$message.error(obj.data);
        return;
      }
      let param = this.structureParam(this.updateForm);
      let res = await saveConfig(param);
      if (!res) {
        return;
      }

      this.goBack();
    },
  },
};
</script>

<style lang="less">
.strategy-match-indicator-config {
  .draggable-wrap {
    display: inline-block;
  }
  .mar {
    margin-bottom: 20px;
  }
  .marl {
    margin-left: 6px;
  }
  .marr {
    margin-right: 6px;
  }
  .marl20 {
    margin-left: 20px;
  }
  .marl13 {
    margin-left: 13px;
  }
  .pd20 {
    padding: 20px;
  }
  .bdr {
    border-radius: 7px;
    background: #f5f5f5;
    margin-left: 30px;
    position: relative;
    &:before {
      width: 0;
      height: 0;
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      border-bottom: 8px solid #f5f5f5;
      position: absolute;
      left: 20px;
      top: -6px;
    }
  } // .bdr ::before {
  //   width: 0;
  //   height: 0;
  //   border-left: 6px solid transparent;
  //   border-right: 6px solid transparent;
  //   border-bottom: 8px solid #F5F5F5;
  //   position: absolute;
  //   left: 20px;
  //   top: -6px;
  // }
  .rule-group-wrap {
    padding-left: 20px;
    width: 70%;
    .rule-group-item {
      padding: 0 12px;
      margin-bottom: 10px;
      .rule-group-title {
        background: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 42px;
        line-height: 42px;
        padding-left: 10px;
        padding-right: 10px;
        border-radius: 5px;
        .title {
          font-weight: bold;
          cursor: pointer;
          font-size: 15px;
        }
      }
      .sub-title {
        margin-top: 10px;
        margin-bottom: 10px;
        .sub-font {
          font-size: 15px;
          font-weight: bold;
        }
        i {
          display: inline-block;
          width: 8px;
          height: 8px;
          margin-left: 10px;
          margin-right: 10px;
          border-radius: 50%;
        }
      }
      .skip-rule {
        background: #f5f5f5;
      }
      .rule-content {
        // margin-left: 30px;
        padding-bottom: 4px;
        .rule-title {
          height: 42px;
          line-height: 42px;
          font-size: 14px;
          padding-left: 20px;
          font-weight: bold;
        }
        .rule-form {
          padding-left: 10px;
        }
      }
    }

    .dt-select-range {
      width: 120px;
      margin-right: 6px;
    }
    .dt-input-range-width {
      width: 130px;
    }
  }
}
</style>
