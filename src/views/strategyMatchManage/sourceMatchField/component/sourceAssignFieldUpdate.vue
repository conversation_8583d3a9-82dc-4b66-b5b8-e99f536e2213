<template>
  <div class="source-assign-field-update">
    <div class="table-tool">
      <TableToolTemp :toolListProps="toolListProps" class="second-tab-tool" @handleTool="handleTool"></TableToolTemp>
    </div>
    <el-form :model="updateForm" label-width="160px" ref="updateForm" :rules="rules" label-position="left" class="pdl-20">
      <el-form-item label="字段ID" prop="fieldId" v-if="type!='add'">
        <el-input v-model="updateForm.fieldId" :disabled="type=='view'" class="dt-input-width"></el-input>
      </el-form-item>
      <el-form-item label="字段名称" prop="name">
        <el-input v-model="updateForm.name" class="dt-input-width" :disabled="type=='view'"></el-input>
      </el-form-item>
      <el-form-item label="数据类型" prop="dataType">
        <el-select v-model="updateForm.dataType" :disabled="type=='view'" placeholder="请选择" class="dt-select">
          <el-option v-for="(item, index) in dicGather.dataTypeList" :key="index" :label="item.dicItemName" :value="item.dicItemCode">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否为字典" prop="isDic">
        <el-select v-model="updateForm.isDic" :disabled="type=='view'" placeholder="请选择" class="dt-select">
          <el-option v-for="(item, index) in dicGather.yesorno" :key="index" :label="item.dicItemName" :value="item.dicItemCode">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="字典值" v-if="updateForm.tableData&&updateForm.tableData.length">
        <el-table :data="updateForm.tableData" v-hover class="dt-dic-table" :span-method="objectSpanMethod" style="width: 100%" ref="myTable">
          <el-table-column align='center' prop="dicKey" label="字典值">
            <template slot-scope="scope">
              <span>{{scope.row.dicKey}}</span>
            </template>
          </el-table-column>
          <el-table-column align='center' prop="dicValue" label="字典名称">
            <template slot-scope="scope">
              <span>{{scope.row.dicValue}}</span>
            </template>
          </el-table-column>
          <el-table-column align='center' v-if="showTargetField" prop="targetFieldName" label="映射字段">
            <template slot-scope="scope">
              <span>{{scope.row.targetFieldName}}&nbsp</span>
            </template>
          </el-table-column>
          <el-table-column align='center' v-if="showTargetField" prop="targetDicDicKey" label="映射字典值">
            <template slot-scope="scope">
              <span>{{scope.row.targetDicDicKey}}</span>
            </template>
          </el-table-column>
          <el-table-column align='center' v-if="showTargetField" prop="targetDicDicValue" label="映射字典名称">
            <template slot-scope="scope">
              <span>{{scope.row.targetDicDicValue}}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column align='center' v-if="showTargetField" prop="targetDicDicValue" label="操作" width="120px">
            <template slot-scope="scope" >
              <el-button size="mini" @click="handleAdd(scope.row)" type="text">新增</el-button>
              <el-button size="mini" @click="handleEdit(scope.row)" type="text">编辑</el-button>
              <el-button size="mini" @click="handleDel(scope.row)" type="text">删除</el-button>
            </template>
          </el-table-column> -->

        </el-table>
      </el-form-item>
      <el-form-item label="是否参与计算" prop="isCalc">
        <el-select v-model="updateForm.isCalc" :disabled="type=='view'" placeholder="请选择" class="dt-select">
          <el-option v-for="(item, index) in dicGather.yesorno" :key="index" :label="item.dicItemName" :value="item.dicItemCode">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="属性类型" prop="propertyType" v-if="updateForm.propertyType">
        <el-select v-model="updateForm.propertyType" :disabled="type=='view'" placeholder="请选择" class="dt-select">
          <el-option v-for="(item, index) in dicGather.propertyTypeList" :key="index" :label="item.dicItemName" :value="item.dicItemCode">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submit">确定</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import { validate, validateAlls } from "@/config/validation";
import { getDicItemList } from "@/config/tool.js";
import { getSourceAssignFieldDetail } from "@/api/strategyMatchManage/sourceMatchField/index.js";
export default {
  name: "sourceAssignFieldUpdate",
  inject: ["sourceMatchFieldConfig"],
  data() {
    return {
      toolListProps: {
        toolTitle: "字段详情页",
        toolList: [
          {
            name: "返回",
            btnCode: ""
          }
        ],
      },
      rules: {
        name: [
          {
            required: true,
            validator: validate,
            trigger: "blur",
            // regax: [
            //   {
            //     message: "请输入英文、数字、下划线、$符号",
            //     ruleFormat: /^[a-zA-Z0-9\_$]*$/
            //   }
            // ]
          },
        ],
        dataType: [
          {
            required: true,
            validator: validate,
            trigger: "blur",
          },
        ],
        isDic: [
          {
            required: true,
            validator: validate,
            trigger: "change",
          }
        ],
        isCalc: [
          {
            required: true,
            validator: validate,
            trigger: "change",
          }
        ],
      },
      updateForm: {
        fieldId:"",
        name:"",
        dataType:"",
        isDic:"",
        isCalc:"",
        propertyType:"",
        tableData:[],
      },
      dicGather: {
        dataTypeList: [],
        yesorno: [],
        propertyTypeList: []
      },
      showTargetField: true
    };
  },
  computed: {
    type() {
      return this.sourceMatchFieldConfig.currentTab.type
    }
  },
  components: {
    TableToolTemp
  },
  created() {
    this.init()
  },
  methods: {
    async init() {
      await this.getDictList()
      if (this.type != "add") {
        this.initData()
      }
    },
    async getDictList() {
      this.dicGather.dataTypeList = await getDicItemList("sct.source.dataType")
      this.dicGather.yesorno = await getDicItemList("gen.yesorno.num")
      this.dicGather.propertyTypeList = await getDicItemList("sct.source.propertyType")
    },
    async initData() {
      const ctx = this.sourceMatchFieldConfig;
      let res = await getSourceAssignFieldDetail({ fid: ctx.currentTab.fid })
      if (!res) { return }
      res.tableData = []

      this.showTargetField = this._.some(res.dicList, item => { return item.dicConvertList != undefined });

      this._.each(res.dicList, item => {
        if (item.dicConvertList == undefined) {
          item.dicConvertList = [
            {
              dicKey: item.dicKey,
              dicValue: item.dicValue,
              targetFieldName: "",
              targetDicDicKey: "",
              targetDicDicValue: "",
              rowspan: 1
            }
          ]
        } else {
          this._.each(item.dicConvertList, el => {
            el.dicKey = item.dicKey || ""
            el.dicValue = item.dicValue || ""
            el.targetFieldName = el.targetField ? el.targetField.name : ""
            el.targetDicDicKey = el.targetDic ? el.targetDic.dicKey : ""
            el.targetDicDicValue = el.targetDic ? el.targetDic.dicValue : ""
            el.rowspan = 1
          })
        }
      })

      this._.each(res.dicList, item => {
        res.tableData = this._.concat(res.tableData, item.dicConvertList || [])
      })

      for (let i = 0; i < res.tableData.length; i++) {
        // 如果当前行的key和下一行的key相等
        // 就把当前v.rowspan + 1
        // 下一行的v.rowspan - 1
        for (let j = i + 1; j < res.tableData.length; j++) {
          //此处可根据相同字段进行合并


          if (res.tableData[i]["dicKey"] === res.tableData[j]["dicKey"] || res.tableData[i]["dicValue"] === res.tableData[j]["dicValue"]) {
            res.tableData[i].rowspan++;
            res.tableData[j].rowspan--;
          }
        }
        // 跳过已经重复的数据
        i = i + res.tableData[i].rowspan - 1;
      }

      this.updateForm = res
    },
    handleTool(item) {
      if (item.name.indexOf('返回') > -1) {
        this.goBack()
      }
    },
    goBack() {
      const ctx = this.sourceMatchFieldConfig;
      ctx.componentName = ctx.currentTab.component;
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex == 0 || columnIndex == 1) {
        return {
          rowspan: row.rowspan,
          colspan: 1
        };
      }
    },
    submit(){
      if(this.type == "view"){
        this.goBack()
        return
      }
      if (!validateAlls(this.$refs.updateForm)) { return }
      if (this.type == "add") {
        
      } else if (this.type == "edit") {
        
      }

      // let res = await saveMetadataEvents(this.editForm)
      // if (!res) { return }
      // this.goBack();
    },
    handleAdd(row) { },
    handleEdit(row) { },
    handleDel(row) { },
  }
};
</script>

<style lang="less">
.source-assign-field-update {
  .table-tool {
    padding-bottom: 10px;
  }
  .el-input.is-disabled .el-input__inner {
    background: #FAFAFA;
  }

  .el-select .el-input.is-disabled .el-input__inner {
    background: #FAFAFA;
  }
  .el-input.is-disabled .el-input__inner {
    background: #FAFAFA;
  }


  .el-textarea.is-disabled .el-textarea__inner {
    background: #FAFAFA;
  }
}
</style>
