<template>
  <div class="source-match-field-list">
    <TableToolTemp
      :toolListProps="toolListProps"
      class="second-tab-tool"
      @handleTool="handleTool"
    ></TableToolTemp>
    <el-table
      :data="tableData"
      stripe
      v-hover
      class="dt-table"
      style="width: 100%"
      ref="myTable"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column align="left"  type="selection"></el-table-column> -->
      <el-table-column
        align="center"
        property="strategyId"
        label="策略ID"
      ></el-table-column>
      <el-table-column
        align="center"
        property="name"
        label="字段名称"
      ></el-table-column>
      <el-table-column align="center" property="dataType" label="数据类型">
        <template slot-scope="scope">
          <p>
            {{ scope.row.dataType | getDicItemName("sct.source.dataType") }}
          </p>
        </template>
      </el-table-column>
      <el-table-column align="center" property="isDic" label="是否为字典">
        <template slot-scope="scope">
          <p>{{ scope.row.isDic | getDicItemName("gen.yesorno.num") }}</p>
        </template>
      </el-table-column>
      <el-table-column align="center" property="isCalc" label="是否参与计算">
        <template slot-scope="scope">
          <p>{{ scope.row.isCalc | getDicItemName("gen.yesorno.num") }}</p>
        </template>
      </el-table-column>
      <el-table-column align="center" property="propertyType" label="属性类型">
        <template slot-scope="scope">
          <p>
            {{
              scope.row.propertyType | getDicItemName("sct.source.propertyType")
            }}
          </p>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" fixed="right" width="160px">
        <template slot-scope="scope">
          <el-button size="mini" @click="handleView(scope.row)" type="text"
            >查看详情</el-button
          >
          <!-- <el-button size="mini" @click="handleEdit(scope.row)" type="text">编辑</el-button>
          <el-button size="mini" @click="handleDelete(scope.row)" type="text">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :pageData="initParam"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
    ></Pagination>
    <DtPopup
      title="选择匹配数据源"
      width="70%"
      confirmBeforeClose
      :isShow.sync="addPopupStatus"
      @close="addPopupStatus = false"
      @confirm="confirmUpdateByFbid"
      :isSmall="true"
    >
      <sourceTemp
        popStatus="4"
        ref="sourceTemp"
        v-if="addPopupStatus"
        @confirm="closedPop"
        :strategyId="initParam.param.strategyId"
      />
    </DtPopup>
  </div>
</template>

<script>
import domainObj from "@/utils/globalParam";
import { getDicItemList } from "@/config/tool.js";
import { baseComponent } from "@/utils/common";
import sourceTemp from "@/components/sourceTemp";
import { getSourceMatchField } from "@/api/strategyMatchManage/sourceMatchField/index.js";
import { validate, validateAlls } from "@/config/validation";

export default {
  name: "sourceMatchFieldList",
  mixins: [baseComponent],
  inject: ["sourceMatchFieldConfig"],
  components: {
    sourceTemp,
  },
  computed: {
    currentLoginUser() {
      return this.$store.getters["layoutStore/getCurrentLoginUser"];
    },
  },
  data() {
    return {
      toolListProps: {
        toolTitle: "数据源结构列表",
        toolList: [
          {
            name: "选择数据源",
            btnCode: "",
            type: "add",
          },
        ],
      },
      tableData: [{}],
      downloadURL: "/web/sourcematchfield/download",
      loginParam: "",
      initParam: {
        pageNum: 1,
        pageSize: 20,
        sort: "sort",
        param: {
          strategyId: "",
        },
      },
      total: 0,
      multipleSelection: [],
      delFid: "",
      showPopup: false,
      addPopupStatus: false,
    };
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      this.initParam.param.strategyId = this.$route.query.strategyId;
      this.loginParam = `access_token=${sessionStorage.getItem(
        "LoginAccessToken"
      )}&tenantId=${this.currentLoginUser.tenantId}&funcId=${
        this.currentLoginUser.funcId
      }`;
      this.getQueryParam(this.initParam);
      await this.getDictList();
      this.initList();
    },

    async getDictList() {
      await getDicItemList("sct.source.dataType");
      await getDicItemList("gen.yesorno.num");
      await getDicItemList("sct.source.propertyType");
    },
    async initList() {
      if (!this.initParam.param.strategyId) {
        return;
      }
      let res = await getSourceMatchField(this.initParam);
      if (!res) {
        return;
      }
      this.tableData = res.list;
      this.initParam.pageNum = res.pageNum;
      this.initParam.pageSize = res.pageSize;
      this.total = Number(res.total);
    },
    changeComponent(type, row) {
      const ctx = this.sourceMatchFieldConfig;
      if (type != "add") {
        ctx.currentTab.fid = row.fid;
      }
      ctx.currentTab.type = type;
      ctx.componentName = ctx.currentTab.updateComponent;
    },
    handleView(row) {
      this.changeComponent("view", row);
    },
    handleEdit(row) {
      this.changeComponent("edit", row);
    },
    handleTool(item) {
      this.addPopupStatus = true;
    },
    confirmUpdateByFbid() {
      this.$refs.sourceTemp.saveFbId();
    },
    closedPop() {
      this.$message({
        message: "添加成功",
        type: "success",
      });
      this.addPopupStatus = false;
      this.initList();
    },
  },
};
</script>

<style lang="less">
.source-match-field-list {
}
</style>
