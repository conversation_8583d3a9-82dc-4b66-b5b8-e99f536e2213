<template>
  <div class="source-match-field-config">
    <el-breadcrumb separator-class="el-icon-arrow-right" class="dt-bread">
      <el-breadcrumb-item :to="{ name: 'strategyMatchList' }"
        >分配配策略管理</el-breadcrumb-item
      >
      <el-breadcrumb-item
        :style="{ color: $store.state.layoutStore.themeObj.color }"
        >配置数据源</el-breadcrumb-item
      >
    </el-breadcrumb>
    <TableToolTemp :toolListProps="titleListPros"></TableToolTemp>
    <el-tabs
      type="border-card"
      class="dt-tabs tabs-wrap"
      v-model="tabsIndex"
      v-if="paneList.length"
    >
      <template v-for="(obj, idx) in paneList">
        <el-tab-pane
          :name="obj.index"
          :label="obj.label"
          :key="'paneList-' + idx"
        >
          <div
            slot="label"
            class="tab-label"
            :style="{
              'border-top':
                tabsIndex == obj.index
                  ? '4px solid' + themeObj.color
                  : '4px solid transparent',
              color: tabsIndex == obj.index ? themeObj.color : '',
            }"
          >
            {{ obj.label }}
          </div>
        </el-tab-pane>
      </template>
    </el-tabs>
    <!-- 使用component 占位符来展示组件 -->
    <!-- 注意 :is 是绑定的属性，需要在实例的data中绑定的 组件的名称是字符串 -->
    <component :is="componentName" :key="tabsIndex"></component>
  </div>
</template>
<script>
import sourceAssignFieldList from "@/views/strategyMatchManage/sourceMatchField/component/sourceAssignFieldList.vue"; // 分配数据源管理
import sourceAssignFieldUpdate from "@/views/strategyMatchManage/sourceMatchField/component/sourceAssignFieldUpdate.vue"; // 分配数据源管理-详情
import sourceMatchFieldList from "@/views/strategyMatchManage/sourceMatchField/component/sourceMatchFieldList.vue"; // 分配数据源管理
import sourceMatchFieldUpdate from "@/views/strategyMatchManage/sourceMatchField/component/sourceMatchFieldUpdate.vue"; // 分配数据源管理-详情
import priorityConfigList from "@/views/strategyMatchManage/sourceMatchField/component/priorityConfigList.vue"; // 分配数据源管理-详情
import { paneList } from "@/views/strategyMatchManage/sourceMatchField/index";
import TableToolTemp from "@/components/layouts/TableToolTemp";
export default {
  name: "sourceMatchFieldConfig",
  components: {
    TableToolTemp,
    sourceAssignFieldList,
    sourceAssignFieldUpdate,
    sourceMatchFieldList,
    sourceMatchFieldUpdate,
    priorityConfigList,
  },
  provide() {
    return {
      sourceMatchFieldConfig: this,
    };
  },
  data() {
    return {
      paneList,
      titleListPros: {
        toolTitle: "配置数据源",
        toolList: [],
      },
      tabsIndex: "0",
      currentTab: {},
      componentName: "",
    };
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    currentLoginUser() {
      return this.$store.getters["layoutStore/getCurrentLoginUser"];
    },
  },

  watch: {
    tabsIndex: {
      immediate: true,
      handler(newValue, oldValue) {
        let idx = Number(newValue);
        this.currentTab = this.paneList[idx];
        this.componentName = this.currentTab.component;
      },
    },
  },
  methods: {},
};
</script>
<style lang="less">
.source-match-field-config {
  .tabs-wrap {
    padding-top: 10px;
    background: #f5f5f5;
  }
  .el-tabs__item {
    border-radius: 8px 8px 0 0;
  }
}
</style>
