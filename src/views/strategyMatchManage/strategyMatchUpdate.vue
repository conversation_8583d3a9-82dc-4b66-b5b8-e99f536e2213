<template>
  <div class="strategy-match-update">
    <el-breadcrumb separator-class="el-icon-arrow-right" class="dt-bread">
      <el-breadcrumb-item :to="{ name: 'strategyMatchList' }">匹配策略管理</el-breadcrumb-item>
      <el-breadcrumb-item :style="{ color: $store.state.layoutStore.themeObj.color }">{{getText}}</el-breadcrumb-item>
    </el-breadcrumb>
    <TableToolTemp :toolListProps="titleListPros"></TableToolTemp>
    <el-form :model="updateForm" label-width="160px" :rules="rules" ref="updateForm" label-position="left" class="pdl-20">
      <el-form-item label="策略名称" prop="name" :disabled="baseDisabled">
        <el-input v-model="updateForm.name" class="dt-input-width" placeholder="请输入30个字符以内不能包含特殊字符"></el-input>
      </el-form-item>
      <el-form-item label="匹配规则" prop="matchRule">
        <el-radio v-model="updateForm.matchRule" :disabled="baseDisabled" v-for="(item,index) in dicGather.matchRuleList" :key="index" :label="item.dicItemCode">{{item.dicItemName}}</el-radio>
      </el-form-item>
      <el-form-item v-if="type=='edit'" label="数据源配置状态" prop="sourceConfigStatus">
        <el-select v-model="updateForm.sourceConfigStatus" :disabled="true" placeholder="请选择" class="dt-select">
          <el-option v-for="(item, index) in dicGather.sourceConfigStatus" :key="index" :label="item.dicItemName" :value="item.dicItemCode">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="type=='edit'" label="指标配置状态" prop="indicatorConfigStatus">
        <el-select v-model="updateForm.indicatorConfigStatus" :disabled="true" placeholder="请选择" class="dt-select">
          <el-option v-for="(item, index) in dicGather.sourceConfigStatus" :key="index" :label="item.dicItemName" :value="item.dicItemCode">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否有效" v-if="type=='edit'" prop="status">
        <el-radio v-model="updateForm.status" :disabled="statusDisabled" v-for="(item,index) in dicGather.status" :key="index" :label="item.dicItemCode">{{item.dicItemName}}</el-radio>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input type="textarea" v-model="updateForm.remark"  class="dt-input-width" placeholder="请输入10-100个字符"></el-input>
      </el-form-item>
      <el-form-item label="优先权设置" v-if="type=='edit'" prop="priorityFieldsDisplay">
        <vuedraggable :list="updateForm.priorityFieldsDisplay" class="draggable-wrap" :animation="300">
           <span class="priorityField-tags" v-for="(item,index) in updateForm.priorityFieldsDisplay"  :key="index">{{item.name}} <i @click="handleClose(item,index)"  class="el-icon-close"></i></span> 
        </vuedraggable>
        <span class="priorityField-tags"  @click="showComGroup=true" :style="{color:$store.state.layoutStore.themeObj.color,background:$store.state.layoutStore.themeObj.navTagUnselectedColor}"><i class="el-icon-plus"></i>选择优先权</span>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submit">保存</el-button>
        <el-button @click="goBack" type="primary" plain :style="{color:$store.state.layoutStore.themeObj.color}">取消</el-button>
      </el-form-item>
    </el-form>

    <ComPopup :showComGroup="showComGroup" :title="'优先权选择'" :comGroupSearchVal="comGroupSearchVal" :tableHead="popupTableHead" :translateArr="translateArr" :searchTemp="popupSearchTemp" :selectList="selectList" :tableData="popupTableData" @closeDialog="closeDialog" @select="select"></ComPopup>
  </div>
</template>
<script>
import vuedraggable from 'vuedraggable'
import TableToolTemp from "@/components/layouts/TableToolTemp";
import ComPopup from "@/components/layouts/ComPopup";
import { validate, validateAlls } from "@/config/validation";
import { getDicItemList } from "@/config/tool.js";
import { addStrategymatch, updateStrategymatch } from "@/api/strategyMatchManage/index.js";
import { getPriorityConfigList } from "@/api/strategyMatchManage/sourceMatchField/index.js";

export default {
  name: "strategyMatchUpdate",
  data() {
    return {
      titleListPros: {
        toolTitle: "",
      },
      rules: {
        name: [
          {
            required: true,
            validator: validate,
            trigger: "blur",
            min: 1,
            max: 30,
            regax: [
              {
                message: "策略名称请输入30个字符以内不能包含特殊字符",
                ruleFormat: /^[a-zA-Z0-9\u4e00-\u9fa5]+$/i
              }
            ]
          }
        ],
        remark: [{ required: true, min: 1, max: 100, validator: validate, trigger: "blur" }],
        matchRule: [{ required: true, validator: validate, trigger: "blur" }],
        // priorityFieldsDisplay:[{ required: true, validator: validate, trigger: "blur" }]
      },
      updateForm: {
        strategyId: "",
        name: "",
        matchRule: "1",
        remark: "",
        status: "0",
        sourceConfigStatus: "",
        indicatorConfigStatus: "",
        priorityFieldsDisplay: [],
        priorityFields: ""
      },
      dicGather: {
        matchRuleList: [],
        sourceConfigStatus: [],
        status: []
      },

      statusDisabled: false,
      baseDisabled: false,
      showComGroup: false,
      popupTableHead: [],
      popupTableData: [],
      popupSearchTemp: [
        {
          label: "优先权名称",
          name: "name",
          type: "input"
        },
      ],
      selectList: [],
      translateArr: [],
      comGroupSearchVal: "priorityName"
    };
  },
  components: {
    TableToolTemp,
    ComPopup,
    vuedraggable
  },
  computed: {
    getText() {
      if (this.type == "add") {
        return "新增策略"
      }
      if (this.type == "edit") {
        return "编辑策略"
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    async init() {
      this.type = this.$route.query.type
      this.titleListPros.toolTitle = this.getText
      
      await this.getDictList()
      await this.initPriorityConfigLis()
      if (this.type == "edit") {

        this.initData()
        
      }
    },

    async initPriorityConfigLis() {
      let res = await getPriorityConfigList({ strategyId: this.$route.query.strategyId,propertyType:"2" });

      if (this._.isArray(res)) {
        this.popupTableHead = [
          {
            column_name: "priorityName",
            column_comment: "优先权名称"
          },
          {
            column_name: "priorityRule",
            column_comment: "优先权规则"
          },
          {
            column_name: "description",
            column_comment: "说明"
          }
        ]

        this.translateArr = [{
          key: "priorityRule",
          dic: "sct.source.priorityRule"
        }]
        this.popupTableData = res
      }
    },

    // 获取字典/标签/分组
    async getDictList() {
      this.dicGather.matchRuleList = await getDicItemList("sct.match.rule");
      this.dicGather.sourceConfigStatus = await getDicItemList("sct.source.config.status");
      this.dicGather.status = await getDicItemList("gen.yesorno.num");
      await getDicItemList("sct.source.priorityRule");
    },
    initData() {
      let o = this._.cloneDeep(this.$store.state.strategyMatchManage.strategyMatchObj)
      this.updateForm = {
        strategyId: o.strategyId,
        name: o.name,
        matchRule: o.matchRule,
        remark: o.remark,
        status: o.status,
        sourceConfigStatus: o.sourceConfigStatus,
        indicatorConfigStatus: o.indicatorConfigStatus,
        priorityFields: o.priorityFields || ""
      }

      let arr = o.priorityFields && o.priorityFields.split("/") || []
      
      this.updateForm.priorityFieldsDisplay = this._.map(arr, item => {
        
        let o = this._.find(this.popupTableData, ["fid", item])
        if (o) {
          return {
            fid: o.fid,
            name: o[this.comGroupSearchVal]
          }
        }else{
          return {
            fid: "",
            name: ""
          }
        }
      })


      this.selectList = this.updateForm.priorityFieldsDisplay

      if (this.updateForm.sourceConfigStatus == "1" && this.updateForm.indicatorConfigStatus == "1") {
        this.baseDisabled = true
      }

      if (this.updateForm.indicatorConfigStatus == "0") {
        this.statusDisabled = true
      }


    },

    goBack() {
      this.$router.go(-1)
    },
    async submit() {
      if (!validateAlls(this.$refs.updateForm)) {
        return
      }
      let param = {}
      let res
      if (this.type === "add") {
        param = {
          name: this.updateForm.name,
          matchRule: this.updateForm.matchRule,
          remark: this.updateForm.remark
        }
        res = await addStrategymatch(param)
      } else {
        param = {
          name: this.updateForm.name,
          matchRule: this.updateForm.matchRule,
          remark: this.updateForm.remark,
          strategyId: this.updateForm.strategyId,
          status: this.updateForm.status
        }

        if (this.updateForm.priorityFieldsDisplay.length <= 0) {
          param.priorityFields = ""
        } else {
          param.priorityFields = this._.map(this.updateForm.priorityFieldsDisplay,o=>{ return o.fid}).join("/")
        }
        res = await updateStrategymatch(param)
      }
      if (!res) {
        return
      }
      this.goBack()
    },
    closeDialog() {
      this.showComGroup = false
    },
    select(data) {
      this.updateForm.priorityFieldsDisplay = this.selectList = data
      this.updateForm.priorityFields = this._.map(data,o=>{ return o.fid}).join("/")
      this.showComGroup = false
    },
    handleClose(item, index) {
      this.updateForm.priorityFieldsDisplay.splice(index, 1)
      this.selectList = this.updateForm.priorityFieldsDisplay
    }
  }
};
</script>

<style lang="less">
.strategy-match-update {
  .table-tool {
    padding-bottom: 10px;
  }
  .draggable-wrap{
    display: inline-block;
  }
  .tags {
    margin-right: 6px;
  }
}
</style>
