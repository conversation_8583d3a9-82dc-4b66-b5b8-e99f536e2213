<template>
  <div class="source-match-field-config-view">
    <el-breadcrumb separator-class="el-icon-arrow-right" class="dt-bread">
      <el-breadcrumb-item :to="{ name: 'strategyMatchList' }">匹配策略管理</el-breadcrumb-item>
      <el-breadcrumb-item :style="{ color: $store.state.layoutStore.themeObj.color }">查看</el-breadcrumb-item>
    </el-breadcrumb>
    <TableToolTemp :toolListProps="titleListPros"></TableToolTemp>
    <el-tabs type="border-card" class="dt-tabs tabs-wrap" v-model="tabsIndex" v-if="paneList.length">
      <template v-for="(obj, idx) in paneList">
        <el-tab-pane :name="obj.index" :label="obj.label" :key="'paneList-' + idx">
          <div slot="label" class="tab-label" :style="{'border-top':tabsIndex == obj.index? '4px solid' + themeObj.color: '4px solid transparent',color: tabsIndex == obj.index ? themeObj.color : '',}">{{ obj.label }}</div>
        </el-tab-pane>
      </template>
    </el-tabs>
    <!-- 使用component 占位符来展示组件 -->
    <!-- 注意 :is 是绑定的属性，需要在实例的data中绑定的 组件的名称是字符串 -->

    <keep-alive :include="cacheArr" :exclude="notCacheArr">
      <component :is="componentName"  :key="tabsIndex"></component>
    </keep-alive>
    <!-- <component :is="componentName"  :key="tabsIndex"></component> -->

  </div>
</template>
<script>
import baseInfoConfig from "@/views/strategyMatchManage/sourceMatchFieldView/component/baseInfoConfig.vue";
import strategyExecutedRecordList from "@/views/strategyMatchManage/sourceMatchFieldView/component/strategyExecutedRecordList.vue";
import strategyMatchIndicatorListView from "@/views/strategyMatchManage/sourceMatchFieldView/component/strategyMatchIndicatorListView.vue";
import strategyMatchIndicatorConfigView from "@/views/strategyMatchManage/sourceMatchFieldView/component/strategyMatchIndicatorConfigView.vue";
import executedRecordInfoView from "@/views/strategyMatchManage/sourceMatchFieldView/component/executedRecordInfoView.vue";



import { paneList } from "@/views/strategyMatchManage/sourceMatchFieldView/index";
import TableToolTemp from "@/components/layouts/TableToolTemp";
export default {
  name: "sourceMatchFieldConfigView",
  components: {
    TableToolTemp,
    baseInfoConfig,
    strategyExecutedRecordList,
    strategyMatchIndicatorListView,
    strategyMatchIndicatorConfigView,
    executedRecordInfoView
  },
  provide() {
    return {
      sourceMatchFieldConfigView: this,
    };
  },
  data() {
    return {
      paneList,
      titleListPros: {
        toolTitle: "策略ID"
      },
      tabsIndex: "0",
      currentTab: {},
      componentName: "",
      subComponentName: "",
      subTable: {
        fromProcessAnalysis: false,
        list: []
      },
      param:{},
    };
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    currentLoginUser() {
      return this.$store.getters["layoutStore/getCurrentLoginUser"];
    },
    cacheArr() {
      return this.$store.state["layoutStore"].cacheArrName;
    },
    notCacheArr() {
      return this.$store.state["layoutStore"].notCacheArrName;
    }
  },

  created() {
    this.titleListPros.toolTitle = "策略ID：" + this.$route.query.strategyId
    this.setCacheArr("add")
  },

  watch: {
    tabsIndex: {
      immediate: true,
      handler(newValue, oldValue) {
        let idx = Number(newValue);
        this.currentTab = this.paneList[idx];
        this.componentName = this.currentTab.component;
        
      },
    },

  },
  methods: {
    setCacheArr(type) {
      let arr = ["baseInfoConfig", "strategyExecutedRecordList", "strategyMatchIndicatorListView"]
      this._.each(arr, item => {
        this.$store.commit("layoutStore/setCacheArr", {
          status: type,
          routeName: item
        });
      })


    },


  },
};
</script>
<style lang="less">
.source-match-field-config-view {
  // overflow-x: hidden;
  .tabs-wrap {
    padding-top: 10px;
    background: #f5f5f5;
  }
  .el-tabs__item {
    border-radius: 8px 8px 0 0;
  }
}
</style>
