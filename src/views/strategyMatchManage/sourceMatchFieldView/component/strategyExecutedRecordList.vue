<template>
  <div class="executed-record-list">
    <TableToolTemp :toolListProps="{ toolTitle: '执行记录' }"></TableToolTemp>
    <SearchForm
      :searchForm="initParam"
      :labelWidth="'120px'"
      :searchFormTemp="searchFormTemp"
      @normalSearch="normalSearch"
      @normalResetQuery="normalResetQuery"
    ></SearchForm>
    <el-table
      :data="tableData"
      stripe
      v-hover
      class="dt-table"
      style="width: 100%"
      ref="myTable"
    >
      <el-table-column
        align="center"
        property="executeTime"
        width="160px"
        label="执行时间"
      ></el-table-column>
      <el-table-column align="center" property="executeId" label="执行人">
        <template slot-scope="scope">
          <span>{{
            scope.row.executeId | getNickName("scope.row.executeId")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        property="recordId"
        label="记录ID"
      ></el-table-column>
      <el-table-column align="center" property="executeType" label="执行方式">
        <template slot-scope="scope">
          <p>
            {{ scope.row.executeType | getDicItemName("sct.execute.type") }}
          </p>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        property="bizId"
        label="业务编码"
      ></el-table-column>
      <el-table-column
        align="center"
        property="hasMatchRecord"
        width="126px"
        label="是否匹配到数据"
      >
        <template slot-scope="scope">
          <p>
            {{ scope.row.hasMatchRecord | getDicItemName("gen.yesorno.num") }}
          </p>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        property="matchRecordCount"
        label="匹配数量"
        width="80px"
      ></el-table-column>
      <el-table-column align="center" label="分配源数据" width="110px">
        <template slot-scope="scope">
          <el-button
            size="mini"
            @click="handleView(scope.row, 'sourceAssignFieldInfoList')"
            type="text"
            >查看</el-button
          >
        </template>
      </el-table-column>
      <el-table-column align="center" label="匹配源数据" width="110px">
        <template slot-scope="scope">
          <el-button
            size="mini"
            @click="handleView(scope.row, 'sourceMatchFieldInfoList')"
            type="text"
            >查看</el-button
          >
        </template>
      </el-table-column>
      <el-table-column align="center" label="最终匹配数据" width="110px">
        <template slot-scope="scope">
          <span
            class="num"
            @click="handleView(scope.row, 'matchResultInfoList')"
            :style="{ color: $store.state.layoutStore.themeObj.color }"
            >{{ scope.row.executeDataCount }}</span
          >
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :pageData="initParam"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
    ></Pagination>
  </div>
</template>

<script>
import { getDicItemList } from "@/config/tool.js";
import { baseComponent } from "@/utils/common";
import { dateFormat } from "@/utils/utils.js";
import { getStrategyExecutedRecordList } from "@/api/strategyMatchManage/strategyExecutedRecord/index.js";

export default {
  name: "strategyExecutedRecordList",
  mixins: [baseComponent],
  inject: ["sourceMatchFieldConfigView"],
  computed: {
    executedRecordCount() {
      return this.$store.state.strategyMatchManage.executedRecordCount;
    },
  },
  watch: {
    executedRecordCount() {
      this.initList();
    },
  },
  data() {
    return {
      tableData: [],
      loginParam: "",
      searchFormTemp: [
        {
          label: "记录ID",
          name: "recordId",
          type: "input",
        },
        {
          label: "执行方式",
          name: "executeType",
          type: "select",
          list: [],
        },
        {
          label: "业务编码",
          name: "bizId",
          type: "input",
        },
        {
          label: "是否匹配到数据",
          name: "hasMatchRecord",
          type: "select",
          list: [],
        },
        {
          name: "executeTime",
          type: "doubleDate",
          elType: "DateTimePicker",
          label: "执行时间",
          placeholder: "请选择执行时间",
          pickerOptions: {
            disabledDate(time) {
              let curDate = new Date().getTime();
              let three = 60 * 24 * 3600 * 1000;
              let threeMonths = curDate - three;
              return (
                time.getTime() > threeMonths && time.getTime() < Date.now()
              );
            },
          },
          options: [
            {
              name: "executeTimeS",
              placeholder: "请输入开始时间",
              value: "",
            },
            {
              name: "executeTimeE",
              placeholder: "请输入结束时间",
              value: "",
            },
          ],
        },
      ],
      initParam: {
        pageNum: 1,
        pageSize: 20,
        sort: "executeTime desc",
        param: {
          strategyId: "",
          recordId: "",
          executeType: "",
          bizId: "",
          hasMatchRecord: "",
          executeTimeS: "",
          executeTimeE: "",
        },
      },
      total: 0,
      today: "",
    };
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      this.initParam.param.strategyId = this.$route.query.strategyId;
      await this.initDefaultTime();
      await this.getDictList();
      this.initList();
    },
    async normalSearch() {
      this.init();
    },
    async getDictList() {
      let res = await getDicItemList("sct.execute.type");
      this.setSearchFormTemp("executeType", res);
      res = await getDicItemList("gen.yesorno.num");
      this.setSearchFormTemp("hasMatchRecord", res);
    },
    async initList() {
      if (!this.initParam.param.strategyId) {
        return;
      }
      let res = await getStrategyExecutedRecordList(this.initParam);
      if (!res) {
        return;
      }
      this.tableData = res.list;
      this.initParam.pageNum = res.pageNum;
      this.initParam.pageSize = res.pageSize;
      this.total = Number(res.total);
    },
    initDefaultTime() {
      this._.each(this.searchFormTemp, (el) => {
        if (el.name == "executeTime") {
          el.options[0].value = this.initParam.param.executeTimeE;
          el.options[1].value = this.initParam.param.executeTimeS;
        }
      });
    },
    changeComponent(row, subComponentName) {
      const ctx = this.sourceMatchFieldConfigView;
      ctx.param = {};
      ctx.subComponentName = subComponentName;
      ctx.param.recordId = row.recordId;
      ctx.componentName = ctx.currentTab.updateComponent;
    },
    handleView(row, subComponentName) {
      if (
        subComponentName == "matchResultInfoList" &&
        row.executeDataCount == 0
      ) {
        return;
      }
      this.changeComponent(row, subComponentName);
    },
  },
};
</script>

<style lang="less">
.executed-record-list {
  .num {
    cursor: pointer;
  }
}
</style>
