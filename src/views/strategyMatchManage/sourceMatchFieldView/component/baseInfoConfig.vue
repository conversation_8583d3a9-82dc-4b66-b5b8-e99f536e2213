<template>
  <!-- 产品销售配置 -->
  <div class="base-info-config">
    <TableToolTemp :toolListProps="titleListPros" class="second-tab-tool"></TableToolTemp>
    <div class="base-info clearfix">
      <div class="base-item">策略名称：{{strategyMatchObj.name}}</div>
      <div class="base-item">数据源配置状态：{{strategyMatchObj.sourceConfigStatus | getDicItemName("sct.source.config.status")}}</div>
      <div class="base-item">指标配置状态：{{strategyMatchObj.indicatorConfigStatus | getDicItemName("sct.source.config.status")}}</div>
      <div class="base-item">是否有效：{{strategyMatchObj.status | getDicItemName("gen.yesorno.num")}}</div>
      <div class="base-item">备注：{{strategyMatchObj.remark}}</div>
    </div>

    <div class="clearfix">
      <TableToolTemp :toolListProps="titleListPros2" class="second-tab-tool"></TableToolTemp>
      <ul class="menu-list fl">
        <li class="sin-menu" v-for="(v,index) in paneList" @click="menuClick(index)" :class="tabsIndex == index ? 'current' : ''" :style="tabsIndex == index ? 'background:' + themeObj.color : ''" :key="index">
          {{v.label}}
        </li>
      </ul>
      <div class="component-container fl">

        <keep-alive :include="cacheArr" :exclude="notCacheArr">
          <component :is="componentName"></component>
        </keep-alive>
        <!-- <component :is="componentName"></component> -->
      </div>
    </div>
  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import sourceAssignFieldList from "@/views/strategyMatchManage/sourceMatchFieldView/component/sourceAssignFieldList.vue"; // 分配数据源管理
import sourceAssignFieldUpdate from "@/views/strategyMatchManage/sourceMatchFieldView/component/sourceAssignFieldUpdate.vue"; // 分配数据源管理-详情
import sourceMatchFieldList from "@/views/strategyMatchManage/sourceMatchFieldView/component/sourceMatchFieldList.vue"; // 分配数据源管理
import sourceMatchFieldUpdate from "@/views/strategyMatchManage/sourceMatchFieldView/component/sourceMatchFieldUpdate.vue"; // 分配数据源管理-详情
import priorityConfigList from "@/views/strategyMatchManage/sourceMatchFieldView/component/priorityConfigList.vue"; // 分配数据源管理-详情
import { getDicItemList } from "@/config/tool.js";
export default {
  name: "baseInfoConfig",
  components: {
    sourceAssignFieldList,
    sourceAssignFieldUpdate,
    sourceMatchFieldList,
    sourceMatchFieldUpdate,
    priorityConfigList,
    TableToolTemp
  },
  created() {
    this.getDictList()
    this.setCacheArr("add")
  },
  provide() {
    return {
      baseInfoConfig: this,
    };
  },
  data() {
    return {
      titleListPros: {
        toolTitle: "基础信息",
      },
      titleListPros2: {
        toolTitle: "数据源信息",
      },
      paneList: [
        {
          index: "0",
          label: "分配数据源",
          component: "sourceAssignFieldList",
          updateComponent: "sourceAssignFieldUpdate",
          children: [
            {
              index: "0",
              label: "分配数据源详情",
              component: "sourceAssignFieldUpdate",
            }
          ]
        },
        {
          index: "1",
          label: "匹配数据源",
          component: "sourceMatchFieldList",
          updateComponent: "sourceMatchFieldUpdate"
        },
        {
          index: "2",
          label: "优先权说明",
          component: "priorityConfigList",
        },
      ],
      tabsIndex: "0",
      componentName: "",
      currentTab: {},
      lastPaneList: []
    };
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    strategyMatchObj() {
      return this.$store.state.strategyMatchManage.strategyMatchObj
    },
    cacheArr() {
      return this.$store.state["layoutStore"].cacheArrName;
    },
    notCacheArr() {
      return this.$store.state["layoutStore"].notCacheArrName;
    }
  },
  watch: {
    tabsIndex: {
      immediate: true,
      handler(newValue, oldValue) {
        let idx = parseInt(newValue)
        this.currentTab = this.paneList[idx]
        this.componentName = this.currentTab.component;
      },
    },
  },
  methods: {
    menuClick(index) {
      this.tabsIndex = index
    },
    async getDictList() {
      await getDicItemList("sct.source.config.status");
      await getDicItemList("gen.yesorno.num");
    },
    setCacheArr(type) {
      let arr = ["sourceAssignFieldList",  "sourceMatchFieldList", "priorityConfigList"]
      this._.each(arr, item => {
        this.$store.commit("layoutStore/setCacheArr", {
          status: type,
          routeName: item
        });
      })

    }
  },
};
</script>
<style lang="less">
.base-info-config {
  width: 100%;
  .menu-list {
    width: 180px;
    margin: 0 30px 0 38px; // border-right: 1px solid #EBEBEB;
    .sin-menu {
      height: 50px;
      line-height: 50px;
      text-align: center;
      background: #F9F9F9;
      border-bottom: 1px solid #fff;
      cursor: pointer;
      position: relative;
      &.current {
        color: #fff;
      }
    }
    border-bottom-color: transparent;
  }

  .component-container {
    width: calc(~"(100% - 268px)");
  }

  .base-info {
    width: 100%;
    padding-left: 38px;
    .base-item {
      float: left;
      width: 25%;
      margin-bottom: 20px;
    }
  }
}
</style>
