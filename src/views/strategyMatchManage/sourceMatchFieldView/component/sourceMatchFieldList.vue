<template>
  <div class="source-match-field-list">
    <el-table :data="tableData" stripe v-hover class="dt-table" style="width: 100%" ref="myTable">
      <el-table-column align='center' property="strategyId" label="策略ID"></el-table-column>
      <el-table-column align='center' property="name" label="字段名称"></el-table-column>
      <el-table-column align='center' property="dataType" label="数据类型">
        <template slot-scope="scope">
          <p>{{scope.row.dataType |getDicItemName("sct.source.dataType")}}</p>
        </template>
      </el-table-column>
      <el-table-column align='center' property="isDic" label="是否为字典">
        <template slot-scope="scope">
          <p>{{scope.row.isDic |getDicItemName("gen.yesorno.num")}}</p>
        </template>
      </el-table-column>
      <el-table-column align='center' property="isCalc" label="是否参与计算">
        <template slot-scope="scope">
          <p>{{scope.row.isCalc |getDicItemName("gen.yesorno.num")}}</p>
        </template>
      </el-table-column>
      <el-table-column align='center' property="propertyType" label="属性类型">
        <template slot-scope="scope">
          <p>{{scope.row.propertyType |getDicItemName("sct.source.propertyType")}}</p>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" fixed="right" width="100px">
        <template slot-scope="scope">
          <el-button size="mini" @click="handleView(scope.row)" type="text">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :pageData="initParam" :total="total" layout="total, sizes, prev, pager, next, jumper"></Pagination>

  </div>
</template>

<script>
  import {getDicItemList} from "@/config/tool.js";
  import {baseComponent} from "@/utils/common"
  import {getSourceMatchField} from "@/api/strategyMatchManage/sourceMatchField/index.js";
  import {validate, validateAlls} from "@/config/validation";

  export default {
    name: "sourceMatchFieldList",
    mixins: [baseComponent],
    inject: ["baseInfoConfig"],
    computed: {
      currentLoginUser() {
        return this.$store.getters["layoutStore/getCurrentLoginUser"];
      }
    },
    data() {
      return {
        tableData: [],
        initParam: {
          pageNum: 1,
          pageSize: 20,
          sort:"sort",
          param: {
            strategyId:""
          }
        },
        total: 0
      };
    },
    created() {
      this.init()
    },
    methods: {
      async init() {
        this.initParam.param.strategyId = this.$route.query.strategyId
        await this.getDictList()
        this.initList()
      },

      async getDictList() {
        await getDicItemList("sct.source.dataType");
        await getDicItemList("gen.yesorno.num");
        await getDicItemList("sct.source.propertyType");
      },
      async initList() {
        if (!this.initParam.param.strategyId) {
          return
        }
        let res = await getSourceMatchField(this.initParam);
        if (!res) {
          return
        }
        this.tableData = res.list;
        this.initParam.pageNum = res.pageNum;
        this.initParam.pageSize = res.pageSize;
        this.total = Number(res.total);
      },
      changeComponent(row) {
        const ctx = this.baseInfoConfig;
        ctx.currentTab.fid = row.fid;
        ctx.componentName = ctx.currentTab.updateComponent;
      },
      handleView(row) {
        this.changeComponent(row)
      },
    }
  };
</script>

<style lang="less">
  .source-match-field-list {
   
  }
</style>
