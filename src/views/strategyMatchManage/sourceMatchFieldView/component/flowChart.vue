<template>
  <div class="flow-chart">
    <TableToolTemp
      :toolListProps="toolListProps"
      class="second-tab-tool"
    ></TableToolTemp>
    <div class="chart-content">
      <div id="myChart" style="height:690px;"></div>
    </div>

    <DtPopup
      :isShow.sync="showMatchDataPopup"
      @close="closeMatchDataPopup"
      title="匹配数据"
      class="no-padding"
      width="99%"
      :footer="false"
    >
      <el-button
        class="export-btn"
        @click="exportMatchdata"
        type="primary"
        plain
        v-if="exportStatus"
        :style="{ color: $store.state.layoutStore.themeObj.color }"
        >导出</el-button
      >
      <el-table
        :data="matchDataList"
        stripe
        class="dt-table"
        style="width: 100%"
        height="60vh"
        v-hover
      >
        <template v-for="(item, index) in tableHead">
          <el-table-column
            :prop="item.column_name"
            :label="item.column_comment"
            :key="index"
          >
            <template slot-scope="scope">
              <div>
                {{ scope.row[item.column_name] }}
              </div>
            </template>
          </el-table-column>
        </template>
      </el-table>
    </DtPopup>
  </div>
</template>

<script>
const echarts = require("echarts/lib/echarts");
require("echarts/lib/component/tooltip");
require("echarts/lib/chart/tree");

import TableToolTemp from "@/components/layouts/TableToolTemp";
import DtPopup from "@/components/layouts/DtPopup";
import { getProcessMind } from "@/api/strategyMatchManage/index.js";
import {
  getSourceMatchFieldList,
  getSourceAssignFieldList,
} from "@/api/strategyMatchManage/strategyExecutedRecord/index.js";
import domainObj from "@/utils/globalParam";
import { hasRights } from "@/config/tool";
export default {
  name: "flowChart",
  inject: ["sourceMatchFieldConfigView"],
  components: {
    TableToolTemp,
    DtPopup,
  },
  data() {
    return {
      toolListProps: {
        toolTitle: "脑图分析",
      },
      res: null,
      myChart: null,
      options: {},
      height: 0,
      timer: null,
      matchDataTableHead: [],
      assignDataTableHead: [],
      tableHead: [],
      matchDataList: [],
      showMatchDataPopup: false,
      downloadURL: "/web/strategyexecutedrecord/matchdata/export",
      loginParam: "",
      resultId: "",
      indicatorId: "",
      exportStatus: false,
      downloadURL2: "/web/strategyexecutedrecord/matchdata/priority/export",
      matchDataType: 1,
      logId: "",
    };
  },
  created() {},
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    currentLoginUser() {
      return this.$store.getters["layoutStore/getCurrentLoginUser"];
    },
  },
  watch: {
    themeObj() {
      this.init();
    },
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        this.initWH();
      })();
    };
    this.init();
  },
  methods: {
    async getTableHead(type) {
      let res;
      if (type == "2") {
        if (this.assignDataTableHead.length) {
          return;
        }
        res = await getSourceAssignFieldList({
          strategyId: this.$route.query.strategyId,
        });
        if (res) {
          this.assignDataTableHead = this._.map(res, (item) => {
            return {
              column_name: item.fieldId,
              column_comment: item.name,
            };
          });
        }
      } else {
        if (this.matchDataTableHead.length) {
          return;
        }
        res = await getSourceMatchFieldList({
          strategyId: this.$route.query.strategyId,
        });
        if (res) {
          this.matchDataTableHead = this._.map(res, (item) => {
            return {
              column_name: item.fieldId,
              column_comment: item.name,
            };
          });
        }
      }
    },
    hexToRgba(hexColor, alpha = 1) {
      // 移除 # 号并提取颜色值
      var hex = hexColor.replace("#", "");
      // 将颜色值拆分成 R、‌G、‌B 三个部分
      var r = parseInt(hex.substring(0, 2), 16);
      var g = parseInt(hex.substring(2, 4), 16);
      var b = parseInt(hex.substring(4, 6), 16);
      // 转换为 RGBA 格式并添加透明度
      var rgba = `rgba(${r}, ${g}, ${b}, ${alpha})`;
      return rgba;
    },
    async init() {
      if (hasRights("sct:strategymatch:export")) {
        this.exportStatus = true;
        console.log("有权限");
      }
      let param = {
        strategyId: this.$route.query.strategyId,
        resultId: this.sourceMatchFieldConfigView.param.resultId,
      };
      if (!this.res) {
        this.res = await getProcessMind(param);
      }
      let res = this.res;
      if (!res) {
        return;
      }
      res.label = {
        backgroundColor: this.themeObj.color,
        color: "#fff",
        // borderColor: "#000",
        // borderWidth: 2,
        position: "left",
        verticalAlign: "middle",
        align: "left",
        padding: [8, 8],
        borderRadius: 8,
        isRoot: true,
      };
      res.lineStyle = {};
      res.itemStyle = {};

      let setData = (res, type) => {
        this._.each(res, (item) => {
          item.label = {
            backgroundColor:
              item.type == 3 || type == 3
                ? "#d98103"
                : item.type == 2 || type == 2
                ? "#f3bb6a"
                : this.themeObj.color,
            color: "#fff",
            // borderColor: "#999",
            // borderWidth: 1,
            position: "left",
            verticalAlign: "middle",
            align: "left",
            padding: [8, 8],
            borderRadius: 8,
            ndataType: item.type || type,
            width: 200,
            overflow: "truncate",
            ellipsis: "...",
          };

          item.lineStyle = {};
          item.itemStyle = {};

          if (
            item.matchProcess &&
            ((item.matchProcess.nodeType == "condition" &&
              !item.matchProcess.match) ||
              item.matchProcess.nodeType == "skip")
          ) {
            item.label.backgroundColor = this.themeObj.navTagUnselectedColor;
            item.label.color = this.themeObj.color;
          }

          if (item.children) {
            if (
              this._.find(item.children, (el) => {
                return el.matchProcess && el.matchProcess.nodeType == "skip";
              })
            ) {
              item.label.backgroundColor = this.themeObj.navTagUnselectedColor;
              item.label.color = this.themeObj.color;
            }
          }

          if (
            item.matchProcess &&
            item.matchProcess.matchSourceDataList &&
            item.matchProcess.matchSourceDataList.length
          ) {
            // item.label.borderColor = "#000"
            // item.label.borderWidth = 2
          }
          if (item.children) {
            setData(item.children, item.type);
          }
        });
      };
      setData(res.children);

      this.myChart = echarts.init(document.getElementById("myChart"));
      this.myChart.on("click", (params) => {
        this.handleMyChartClick(params);
      });
      this.options = {
        tooltip: {
          trigger: "item",
          triggerOn: "mousemove",
          formatter: function(params) {
            return (
              '<div style="width: 300px; white-space: break-spaces; ">' +
              params.name +
              "</div>"
            );
          },
        },
        series: [
          {
            type: "tree",
            id: 0,
            name: "tree1",
            data: [res],
            top: "0",
            left: "0.5%",
            bottom: "0",
            right: "14%",
            symbol: "none",
            edgeShape: "polyline",
            edgeForkPosition: "50%",
            initialTreeDepth: null,
            expandAndCollapse: false,
          },
        ],
      };
      this.myChart.setOption(this.options);
      this.initWH();
      // this.timer = setTimeout(() => {
      //   this.toolListProps.toolList[0].downloadURL = this.myChart.getDataURL();
      // }, 600)
    },

    initWH() {
      const tree = echarts.init(document.getElementById("myChart"));
      const nodes = tree._chartsViews[0]._data._graphicEls;
      let allNode = 0;
      for (let index = 0; index < nodes.length; index++) {
        const node = nodes[index];
        if (node === undefined) {
          continue;
        }
        allNode++;
      }
      const height =
        document.documentElement.clientHeight - 43 - 50 - 52 - 43 - 60;
      const width = window.innerWidth;
      const currentHeight = 60 * allNode;
      const currentWidth = 50 * allNode;
      const newHeight = Math.max(currentHeight, height);
      const newWidth = Math.max(currentWidth, width);
      const tree_ele = document.getElementById("myChart");
      tree_ele.style.height = newHeight + "px";
      tree_ele.style.width = newWidth + "px";
      tree.resize();
    },
    async handleMyChartClick(params) {
      console.log(params);
      if (params.data.label.isRoot) {
        await this.getTableHead("2");
        this.matchDataList = [];
        this.matchDataList.push(
          this.sourceMatchFieldConfigView.param.flowChartParam.assignData.data
        );
        this.tableHead = this._.cloneDeep(this.assignDataTableHead);
        this.showMatchDataPopup = true;
      } else if (
        params.data.matchProcess &&
        params.data.matchProcess.matchSourceDataList &&
        params.data.matchProcess.matchSourceDataList.length
      ) {
        await this.getTableHead("1");
        this.matchDataList = this._.map(
          params.data.matchProcess.matchSourceDataList,
          (item) => {
            return item.data;
          }
        );
        this.tableHead = this._.cloneDeep(this.matchDataTableHead);
        this.showMatchDataPopup = true;
      }
      if (params.data.matchProcess) {
        this.resultId = params.data.matchProcess.resultId;
        this.indicatorId = params.data.matchProcess.indicatorId;
        this.logId = params.data.matchProcess.logId;
      }
      this.matchDataType = params.data.label.ndataType;
      return false;
    },
    closeMatchDataPopup() {
      this.showMatchDataPopup = false;
    },
    exportMatchdata() {
      console.log(this.matchDataType);
      if (this.matchDataType == 3) {
        this.loginParam = `access_token=${sessionStorage.getItem(
          "LoginAccessToken"
        )}&tenantId=${this.currentLoginUser.tenantId}&funcId=${
          this.currentLoginUser.funcId
        }&strategyId=${this.$route.query.strategyId}&logId=${this.logId}`;
        window.location.href = `${domainObj.baseUrl}${this.downloadURL2}?${this.loginParam}`;
      } else {
        this.loginParam = `access_token=${sessionStorage.getItem(
          "LoginAccessToken"
        )}&tenantId=${this.currentLoginUser.tenantId}&funcId=${
          this.currentLoginUser.funcId
        }&strategyId=${this.$route.query.strategyId}&resultId=${
          this.resultId
        }&indicatorId=${this.indicatorId}`;
        window.location.href = `${domainObj.baseUrl}${this.downloadURL}?${this.loginParam}`;
      }
    },
  },
};
</script>

<style lang="less">
.flow-chart {
  .chart-content {
    width: 100%;
    overflow-x: scroll;
    #myChart {
      width: 100%;
    }
  }

  .tree-label {
    padding: 20px;
    background: #000;
    color: #fff;
  }
  .match-wrap {
    .mt10 {
      margin-bottom: 10px;
    }
  }
  .no-padding {
    .dt-popup {
      .el-dialog {
        padding: 0;
      }
    }
  }
  .export-btn {
    position: absolute;
    right: 50px;
    top: 15px;
  }
}
</style>
