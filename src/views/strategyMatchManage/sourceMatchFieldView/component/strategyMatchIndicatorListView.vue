<template>
  <div class="strateg-match-indicator-list">
    <TableToolTemp :toolListProps="toolListProps"></TableToolTemp>
    <el-table :data="tableData" stripe v-hover class="dt-table" style="width: 100%">
      <el-table-column align='center' prop="priority" width="150px" label="指标优先级"></el-table-column>
      <el-table-column align='center' prop="name" width="250px" label="指标名称"></el-table-column>
      <el-table-column align='center' prop="isSkipable" label="指标是否可跳过">
        <template slot-scope="scope">
          <span>{{scope.row.isSkipable |getDicItemName("gen.yesorno.num")}}</span>
        </template>
      </el-table-column>
      <el-table-column align='center' prop="updateId" label="最后一次修改人">
        <template slot-scope="scope">
          <span>{{scope.row.updateId |getNickName("scope.row.updateId")}}</span>
        </template>
      </el-table-column>
      <el-table-column align='center' prop="updateTime" label="最后一次修改时间">
        <template slot-scope="scope">
          <span>{{scope.row.updateTime}}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" header-align="center" label="操作" fixed="right" width="100">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="handleConfig(scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :pageData="initParam" :total="total" layout="total, sizes, prev, pager, next, jumper"></Pagination>


  </div>
</template>
<script>

import { getStrategyMatchIndicator, deleteStrategyMatchIndicator,strategyMatchIndicatorUp,strategyMatchIndicatorDown } from "@/api/strategyMatchManage/strategymatchindicator/index.js";
import { getDicItemList } from "@/config/tool";
import { baseComponent } from "@/utils/common"

export default {
  name: "strategyMatchIndicatorListView",
  mixins: [baseComponent],
  inject: ["sourceMatchFieldConfigView"],
  data() {
    return {
      toolListProps: {
        toolTitle: "策略指标查看"
      },
      tableData: [],
      initParam: {
        param: {
          strategyId: ""
        },
        pageSize: 10,
        pageNum: 1,
        sort:"priority"
      },
      total: 0
    }
  },
  created() {
    this.initData();
  },
  methods: {
    //初始化数据
    async initData() {
      await this.getDictList();
      this.initList()
    },
    async initList() {
      this.initParam.param.strategyId = this.$route.query.strategyId
      let res = await getStrategyMatchIndicator(this.initParam);
      if (!res) { return }
      this.tableData = res.list;
      this.initParam.pageNum = res.pageNum;
      this.initParam.pageSize = res.pageSize;
      this.total = Number(res.total);
    },
    handleConfig(row) {
      this.$store.commit("strategyMatchManage/setStrategyMatchIndicatorObj", row)
      this.changeComponent(row)
    },
    // 获取字典/标签/分组
    async getDictList() {
     await getDicItemList("gen.yesorno.num");
    },
    changeComponent(row) {
      const ctx = this.sourceMatchFieldConfigView;
      ctx.componentName = ctx.currentTab.updateComponent;
      ctx.currentTab.indicatorId = row.indicatorId
    },
  }
}
</script>

<style lang="less">
.strateg-match-indicator-list {}
</style>
