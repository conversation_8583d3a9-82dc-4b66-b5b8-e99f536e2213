<template>
  <div class="source-match-field-list">
    <TableToolTemp :toolListProps="{toolTitle: '匹配型数据列表'}" class="second-tab-tool"></TableToolTemp>
    <el-table :data="tableData" stripe v-hover class="dt-table" style="width: 100%" ref="myTable">
      <template v-for="(item,index) in tableHead">
        <el-table-column :prop="item.column_name" align='center' :label="item.column_comment" :key="index">
          <template slot-scope="scope">
            <div>
              {{typeof(scope.row.data)=='object'? scope.row.data[item.column_name]:scope.row[item.column_name]}}
            </div>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <Pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :pageData="initParam" :total="total" layout="total, sizes, prev, pager, next, jumper"></Pagination>

  </div>
</template>

<script>
import { baseComponent } from "@/utils/common"
import { getSourceMatchFieldList, getSourceDataPageList } from "@/api/strategyMatchManage/strategyExecutedRecord/index.js";
export default {
  name: "sourceMatchFieldInfoList",
  mixins: [baseComponent],
  inject: ["sourceMatchFieldConfigView"],
  computed: {
    currentLoginUser() {
      return this.$store.getters["layoutStore/getCurrentLoginUser"];
    },
    fromProcessAnalysis(){
      const ctx = this.sourceMatchFieldConfigView 
      if(!ctx.subTable){return false}
      return ctx.subTable.fromProcessAnalysis
    }
  },
  data() {
    return {
      tableHead: [],
      tableData: [],
      cloneTableData:[],
      initParam: {
        pageNum: 1,
        pageSize: 20,
        param: {
          recordId: "",
          dataType: "2"
        }
      },
      total: 0
    };
  },
  created() {
    this.init()
  },
  methods: {
    async init() {
      await this.getTableHead()
      const ctx = this.sourceMatchFieldConfigView
      this.initParam.param.recordId = ctx.param.recordId || ""
      if(this.fromProcessAnalysis){
        this.initSubTable()
      }else{
        this.initList()
      }
    },

    async getTableHead() {
      let res = await getSourceMatchFieldList({ strategyId: this.$route.query.strategyId })
      if (res) {
        this.tableHead = this._.map(res, item => {
          return {
            column_name: item.fieldId,
            column_comment: item.name
          }
        })
      }
    },
    async initList() {
      if (!this.initParam.param.recordId) {
        return
      }

      let res = await getSourceDataPageList(this.initParam);
      if (!res) {
        return
      }
      this.tableData = res.list;
      this.initParam.pageNum = res.pageNum;
      this.initParam.pageSize = res.pageSize;
      this.total = Number(res.total);
    },
    initSubTable(){
      const ctx = this.sourceMatchFieldConfigView 
      this.cloneTableData = ctx.subTable.list
      this.filterTableData()
    },
    filterTableData() {
      this.tableData = this.cloneTableData.slice((this.initParam.pageNum - 1) * this.initParam.pageSize, (this.initParam.pageNum - 1) * this.initParam.pageSize + this.initParam.pageSize)
      this.total = this.cloneTableData.length
    },
    handleCurrentChange(val) {
      this.initParam.pageNum = val;
      if (this.fromProcessAnalysis) {
        this.filterTableData();
      } else {
        this.initList()
      }

    },
    handleSizeChange(val) {
      this.initParam.pageSize = val;
      if (this.fromProcessAnalysis) {
        this.filterTableData();
      } else {
        this.initList()
      }
    },
  }
}
</script>

<style lang="less">
.source-match-field-list {}
</style>
