<template>
  <div class="match-result-info-list">
    <TableToolTemp :toolListProps="{toolTitle: '最终匹配型数据列表'}" class="second-tab-tool"></TableToolTemp>
    <el-table :data="tableData" stripe v-hover class="dt-table" style="width: 100%" ref="myTable">
      <template v-for="(item,index) in tableHead">
        <el-table-column :prop="item.column_name" align='center' :label="item.column_comment" :key="index">
          <template slot-scope="scope">
            <div>
              {{scope.row.assignData.data[item.column_name]}}
            </div>
          </template>
        </el-table-column>
      </template>
      <el-table-column label="匹配数据" align='center' width="80">
        <template slot-scope="scope">
          <div class="match-num" @click="handleMatchDataView(scope.row)" :style="{ color: $store.state.layoutStore.themeObj.color }">{{scope.row.matchDataList?scope.row.matchDataList.length:0}}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align='center' fixed="right" width="80">
        <template slot-scope="scope">
          <el-button v-if="!hideProcessAnalysisList" size="mini" @click="handleView(scope.row,'1')" type="text">过程分析</el-button>
          <el-button style="margin-left:0;" size="mini" @click="handleView(scope.row,'2')" type="text">脑图分析</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :pageData="initParam" :total="total" layout="total, sizes, prev, pager, next, jumper"></Pagination>
    <DtPopup :isShow.sync="showMatchDataPopup" @close="closeMatchDataPopup" title="匹配数据" class="no-padding" width="99%" :footer="false">
      <el-table :data="matchDataList" stripe class="dt-table" style="width: 100%" height="60vh" v-hover>
        <template v-for="(item,index) in matchDataTableHead">
          <el-table-column :prop="item.column_name" :label="item.column_comment" :key="index">
            <template slot-scope="scope">
              <div>
                {{scope.row[item.column_name]}}
              </div>
            </template>
          </el-table-column>
        </template>
      </el-table>
    </DtPopup>

  </div>
</template>

<script>
import { baseComponent } from "@/utils/common"
import { getSourceAssignFieldList, getMatchResultDataPageList, getSourceMatchFieldList } from "@/api/strategyMatchManage/strategyExecutedRecord/index.js";

export default {
  name: "matchResultInfoList",
  mixins: [baseComponent],
  computed: {
    currentLoginUser() {
      return this.$store.getters["layoutStore/getCurrentLoginUser"];
    },
    hideProcessAnalysisList() {
      const ctx = this.sourceMatchFieldConfigView
      return ctx.param.hideProcessAnalysisList
    }

  },
  inject: ["sourceMatchFieldConfigView"],
  data() {
    return {
      tableHead: [],
      tableData: [],
      initParam: {
        pageNum: 1,
        pageSize: 20,
        param: {
          recordId: "",
        }
      },
      total: 0,
      matchDataTableHead: [],
      matchDataList: [],
      showMatchDataPopup: false
    };
  },
  created() {
    this.init()
  },
  methods: {
    async init() {
      const ctx = this.sourceMatchFieldConfigView
      this.initParam.param.recordId = ctx.param.recordId || ""
      await this.getTableHead()
      this.initList()
    },


    async getTableHead() {
      let res = await getSourceAssignFieldList({ strategyId: this.$route.query.strategyId })
      if (res) {
        this.tableHead = this._.map(res, item => {
          return {
            column_name: item.fieldId,
            column_comment: item.name
          }
        })
      }
    },
    async initList() {
      if (!this.initParam.param.recordId) {
        return
      }

      let res = await getMatchResultDataPageList(this.initParam);
      if (!res) {
        return
      }
      this.tableData = res.list;
      this.initParam.pageNum = res.pageNum;
      this.initParam.pageSize = res.pageSize;
      this.total = Number(res.total);
    },
    handleView(row, type) {
      const ctx = this.sourceMatchFieldConfigView;
      let obj = {
        name: "过程分析",
        path: "processAnalysisList"
      }
      if (type == "2") {
        obj.name = "脑图分析"
        obj.path = "flowChart"
        ctx.param.flowChartParam = row
      }
      ctx.subComponentName = obj.path
      ctx.param.resultId = row.resultId
      ctx.param.breadcrumbList = [
        {
          name: "最终匹配型数据列表",
          recordId: row.recordId || "",
          path: "matchResultInfoList"
        },
        {
          name: obj.name,
          recordId: row.recordId,
          path: obj.path
        }
      ]

      
    },
    async handleMatchDataView(row) {

      if (!row.matchDataList || !row.matchDataList.length) { return }
      if (this.matchDataTableHead.length == 0) {
        let res = await getSourceMatchFieldList({ strategyId: this.$route.query.strategyId })
        this.matchDataTableHead = this._.map(res, item => {
          return {
            column_name: item.fieldId,
            column_comment: item.name
          }
        })
      }



      this.matchDataList = this._.map(row.matchDataList, item => { return item.data })


      this.showMatchDataPopup = true
    },
    closeMatchDataPopup() {
      this.showMatchDataPopup = false
    }
  }
};
</script>

<style lang="less">
.match-result-info-list {
  .match-num {
    cursor: pointer;
  }
  .no-padding {
    .dt-popup {
      .el-dialog {
        padding: 0
      }
    }
  }
}
</style>
