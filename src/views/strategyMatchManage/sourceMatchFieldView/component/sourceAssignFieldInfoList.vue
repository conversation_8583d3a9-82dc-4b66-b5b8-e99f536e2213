<template>
  <div class="source-assign-field-list">
    <TableToolTemp :toolListProps="{toolTitle: '分配型数据列表'}" class="second-tab-tool"></TableToolTemp>
    <el-table :data="tableData" stripe v-hover class="dt-table" style="width: 100%" ref="myTable">
      <template v-for="(item,index) in tableHead">
        <el-table-column :prop="item.column_name" align='center' :label="item.column_comment" :key="index">
          <template slot-scope="scope">
            <div>
              {{typeof(scope.row.data)=='object'? scope.row.data[item.column_name]:scope.row[item.column_name]}}
            </div>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <Pagination  v-if="!fromProcessAnalysis" @size-change="handleSizeChange" @current-change="handleCurrentChange" :pageData="initParam" :total="total" layout="total, sizes, prev, pager, next, jumper"></Pagination>

  </div>
</template>

<script>
import { baseComponent } from "@/utils/common"
import { getSourceAssignFieldList, getSourceDataPageList } from "@/api/strategyMatchManage/strategyExecutedRecord/index.js";
import domainObj from "@/utils/globalParam";
export default {
  name: "sourceAssignFieldInfoList",
  mixins: [baseComponent],
  inject: ["sourceMatchFieldConfigView"],
  computed: {
    currentLoginUser() {
      return this.$store.getters["layoutStore/getCurrentLoginUser"];
    },
    fromProcessAnalysis(){
      const ctx = this.sourceMatchFieldConfigView 
      if(!ctx.subTable){return false}
      return ctx.subTable.fromProcessAnalysis
    }
  },
  data() {
    return {
      tableHead: [],
      tableData: [],
      initParam: {
        pageNum: 1,
        pageSize: 20,
        param: {
          recordId: "",
          dataType: "1"
        }
      },
      total: 0
    };
  },
  created() {
    this.init()
  },
  methods: {
    async init() {
      await this.getTableHead()
      const ctx = this.sourceMatchFieldConfigView
      this.initParam.param.recordId = ctx.param.recordId  || ""
      if(this.fromProcessAnalysis){
        this.initSubTable()
      }else{
        this.initList()
      }
      
    },

    async getTableHead() {
      let res = await getSourceAssignFieldList({ strategyId: this.$route.query.strategyId })
      if (res) {
        this.tableHead = this._.map(res, item => {
          return {
            column_name: item.fieldId,
            column_comment: item.name
          }
        })
      }
    },
    async initList() {
      if (!this.initParam.param.recordId) {
        return
      }

      let res = await getSourceDataPageList(this.initParam);
      if (!res) {
        return
      }
      this.tableData = res.list;
      this.initParam.pageNum = res.pageNum;
      this.initParam.pageSize = res.pageSize;
      this.total = Number(res.total);
    },

    initSubTable(){
      const ctx = this.sourceMatchFieldConfigView 
      this.tableData = ctx.subTable.list
    },


  }
};
</script>

<style lang="less">
.source-assign-field-list {}
</style>
