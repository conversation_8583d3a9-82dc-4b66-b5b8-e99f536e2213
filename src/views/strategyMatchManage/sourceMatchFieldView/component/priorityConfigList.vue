<template>
  <div class="priority-config-list">
    <el-table :data="tableData" stripe v-hover class="dt-table" style="width: 100%" ref="myTable">
      <el-table-column align='center' property="priorityId" label="优先权ID"></el-table-column>
      <el-table-column align='center' property="priorityType" label="优先权类型">
        <template slot-scope="scope">
          <p>{{scope.row.priorityType |getDicItemName("sct.source.priorityType")}}</p>
        </template>
      </el-table-column>
      <el-table-column align='center' property="priorityName" label="优先权名称"></el-table-column>
      <el-table-column align='center' property="priorityRule" label="优先权规则">
        <template slot-scope="scope">
          <p>{{scope.row.priorityRule |getDicItemName("sct.source.priorityRule")}}</p>
        </template>
      </el-table-column>
      <el-table-column align='center' property="description" label="优先权规则说明"></el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getDicItemList } from "@/config/tool.js";
import { baseComponent } from "@/utils/common"
import { getPriorityConfigList } from "@/api/strategyMatchManage/sourceMatchField/index.js";
export default {
  name: "priorityConfigList",
  inject: ["baseInfoConfig"],
  mixins: [baseComponent],
  computed: {
    currentLoginUser() {
      return this.$store.getters["layoutStore/getCurrentLoginUser"];
    }
  },
  data() {
    return {
      tableData: [],
      strategyId: ""
    };
  },
  created() {
    this.init()
  },
  methods: {
    async init() {
      this.strategyId = this.$route.query.strategyId
      await this.getDictList()
      this.initData()
    },

    async getDictList() {
      await getDicItemList("sct.source.priorityType");
      await getDicItemList("sct.source.priorityRule");
    },
    async initData() {
      let res = await getPriorityConfigList({ strategyId: this.strategyId });
      if (this._.isArray(res)) {
        this.tableData = res
      }
    }
  }
};
</script>

<style lang="less">
.priority-config-list {}
</style>
