<template>
  <div class="executed-record-info-view">
    <el-breadcrumb separator-class="el-icon-arrow-right" class="dt-bread">
      <el-breadcrumb-item>
        <span class="breadcrumb" @click="goBack('1')">执行信息</span>
      </el-breadcrumb-item>
      <span v-if="param.breadcrumbList&&param.breadcrumbList.length>0">
        <el-breadcrumb-item v-for="(item,index) in param.breadcrumbList" :key="index">
          <span class="breadcrumb" :style="{ color: index==param.breadcrumbList.length-1?$store.state.layoutStore.themeObj.color:'' }" @click="goBack('2',item,index)">{{item.name}}</span>
        </el-breadcrumb-item>
      </span>

      <span v-else>
        <el-breadcrumb-item :style="{ color: $store.state.layoutStore.themeObj.color }">{{getText}}</el-breadcrumb-item>
      </span>

    </el-breadcrumb>


    <component :is="subComponentName"></component>
  </div>
</template>
<script>
import sourceAssignFieldInfoList from "@/views/strategyMatchManage/sourceMatchFieldView/component/sourceAssignFieldInfoList.vue";
import sourceMatchFieldInfoList from "@/views/strategyMatchManage/sourceMatchFieldView/component/sourceMatchFieldInfoList.vue";
import matchResultInfoList from "@/views/strategyMatchManage/sourceMatchFieldView/component/matchResultInfoList.vue";
import processAnalysisList from "@/views/strategyMatchManage/sourceMatchFieldView/component/processAnalysisList.vue";
import flowChart from "@/views/strategyMatchManage/sourceMatchFieldView/component/flowChart.vue";

export default {
  name: "executedRecordInfoView",
  components: {
    sourceAssignFieldInfoList,
    sourceMatchFieldInfoList,
    matchResultInfoList,
    processAnalysisList,
    flowChart
  },
  inject: ["sourceMatchFieldConfigView"],
  data() {
    return {
    };
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    currentLoginUser() {
      return this.$store.getters["layoutStore/getCurrentLoginUser"];
    },
    getText() {
      const ctx = this.sourceMatchFieldConfigView
      if (ctx.subComponentName == "sourceAssignFieldInfoList") {
        return "分配型数据列表"
      }
      if (ctx.subComponentName == "sourceMatchFieldInfoList") {
        return "匹配型数据列表"
      }
      if (ctx.subComponentName == "matchResultInfoList") {
        return "最终匹配型数据列表"
      }
    },
    subComponentName() {
      const ctx = this.sourceMatchFieldConfigView
      return ctx.subComponentName
    },
    param() {
      const ctx = this.sourceMatchFieldConfigView
      return ctx.param
    },

  },
  created() {
  },
  watch: {

  },
  methods: {
    goBack(type, item, index) {
      const ctx = this.sourceMatchFieldConfigView
      if (type == "2") {
        ctx.subComponentName = item.path
        ctx.subTable = item.subTable
        ctx.param.recordId = item.recordId
        ctx.param.breadcrumbList = this._.filter(ctx.param.breadcrumbList, (item, idx) => { return idx <= index })
      } else {
        ctx.componentName = "strategyExecutedRecordList"
      }

    },


  },
};
</script>
<style lang="less">
.executed-record-info-view {}
</style>
