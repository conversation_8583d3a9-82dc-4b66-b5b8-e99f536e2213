<template>
  <div class="strategy-match-indicator-config-view">
    <el-breadcrumb separator-class="el-icon-arrow-right" class="dt-bread">
      <el-breadcrumb-item>
        <span class="main-title" @click="goBack">策略指标信息</span>
      </el-breadcrumb-item>
      <el-breadcrumb-item
        :style="{ color: $store.state.layoutStore.themeObj.color }"
        >指标详情页</el-breadcrumb-item
      >
    </el-breadcrumb>
    <TableToolTemp
      :toolListProps="{ toolTitle: '指标基础信息' }"
      class="second-tab-tool"
    ></TableToolTemp>
    <div class="base-info clearfix">
      <div class="base-item">
        指标名称：{{ strategyMatchIndicatorObj.name }}
      </div>
      <div class="base-item">
        指标优先级：{{ strategyMatchIndicatorObj.priority }}
      </div>
      <div class="base-item">
        是否可跳过：{{
          strategyMatchIndicatorObj.isSkipable
            | getDicItemName("gen.yesorno.num")
        }}
      </div>
      <div class="base-item">
        优先权设置：{{ updateForm.priorityFieldsDisplay }}
      </div>
    </div>
    <TableToolTemp
      :toolListProps="titleListPros"
      class="second-tab-tool"
    ></TableToolTemp>
    <div class="rule-group-wrap">
      <div
        class="rule-group-item"
        v-for="(item, index) in updateForm.ruleGroupList"
        :key="index"
      >
        <div class="rule-group-title">
          <div class="title" @click="item.showRule = !item.showRule">
            <i
              :class="
                item.showRule ? 'el-icon-caret-top' : 'el-icon-caret-bottom'
              "
            ></i
            >规则组{{ index + 1 }}
          </div>
        </div>
        <div v-show="item.showRule">
          <!-- 条件型规则 -->
          <div
            class="sub-title"
            v-if="item.conditionRuleList && item.conditionRuleList.length"
          >
            <i
              :style="{ background: $store.state.layoutStore.themeObj.color }"
            ></i>
            <span class="sub-font marr">条件型规则</span>
          </div>
          <div class="bdr">
            <div
              class="rule-content"
              v-for="(el, idx) in item.conditionRuleList"
              :key="idx + 'a'"
            >
              <div class="line" v-if="idx > 0">
                <span
                  :style="{
                    background: $store.state.layoutStore.themeObj.color,
                  }"
                  >{{
                    el.ruleRelation
                      | getDicItemName("sct.indicator.ruleRelation")
                  }}</span
                >
              </div>
              <div class="rule-title marr">条件型规则{{ idx + 1 }}</div>
              <div class="rule-form">
                <div class="info-view clearfix">
                  <!-- <div class="info-item" v-if="idx>0">与条件型规则1的关系：{{el.ruleRelation |getDicItemName("sct.indicator.ruleRelation")}}</div> -->
                  <div class="info-item">
                    分配数据源字段：{{
                      formateValue(el.assignFId, "assignFieldList")
                    }}
                  </div>
                  <div class="info-item">
                    匹配操作符：{{
                      el.oper | getDicItemName("sct.indicator.oper")
                    }}
                  </div>
                  <div class="info-item" v-if="el.oper == 'range'">
                    匹配范围：{{
                      el.matchValueRange.startDic
                        | getDicItemName("sct.indicator.oper")
                    }}{{ el.matchValueRange.startValue }}
                    {{ getType(el.assignFId, el.oper, "assignFieldList") }}
                    {{
                      el.matchValueRange.endDic
                        | getDicItemName("sct.indicator.oper")
                    }}{{ el.matchValueRange.endValue }}
                    {{ getType(el.assignFId, el.oper, "assignFieldList") }}
                  </div>
                  <div class="info-item" v-if="el.oper != 'range'">
                    匹配值：{{ el.matchValue
                    }}{{ getType(el.assignFId, el.oper, "assignFieldList") }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 匹配型规则 -->
          <div
            class="sub-title"
            v-if="item.matchRuleList && item.matchRuleList.length"
          >
            <i
              :style="{ background: $store.state.layoutStore.themeObj.color }"
            ></i>
            <span class="sub-font marr">匹配型规则</span>
          </div>
          <div class="bdr">
            <div
              class="rule-content"
              v-for="(el, idx) in item.matchRuleList"
              :key="idx + 'b'"
            >
              <div class="line" v-if="idx > 0">
                <span
                  :style="{
                    background: $store.state.layoutStore.themeObj.color,
                  }"
                  >{{
                    el.ruleRelation
                      | getDicItemName("sct.indicator.ruleRelation")
                  }}</span
                >
              </div>
              <div class="rule-title marr">匹配型规则{{ idx + 1 }}</div>
              <div class="rule-form">
                <div class="info-view clearfix">
                  <!-- <div class="info-item" v-if="idx>0">与匹配型规则1的关系：{{el.ruleRelation |getDicItemName("sct.indicator.ruleRelation")}}</div> -->
                  <div class="info-item">
                    匹配源选择：{{
                      el.sourceType | getDicItemName("sct.indicator.sourceType")
                    }}
                  </div>
                  <div class="info-item" v-if="el.sourceType == '1'">
                    分配数据源字段：{{
                      formateValue(el.assignFId, "assignFieldList")
                    }}
                  </div>
                  <div class="info-item" v-if="el.sourceType == '1'">
                    匹配操作符：{{
                      el.oper | getDicItemName("sct.indicator.oper")
                    }}
                  </div>
                  <div class="info-item">
                    匹配数据源字段：{{
                      formateValue(el.matchFId, "matchFieldList")
                    }}
                  </div>
                  <div class="info-item" v-if="el.sourceType == '2'">
                    匹配操作符：{{
                      el.oper | getDicItemName("sct.indicator.oper")
                    }}
                  </div>
                  <div
                    class="info-item"
                    v-if="el.sourceType == '2' && el.oper == 'range'"
                  >
                    匹配范围：{{
                      el.matchValueRange.startDic
                        | getDicItemName("sct.indicator.oper")
                    }}{{ el.matchValueRange.startValue }}
                    {{ getType(el.matchFId, el.oper, "matchFieldList") }}
                    {{
                      el.matchValueRange.endDic
                        | getDicItemName("sct.indicator.oper")
                    }}{{ el.matchValueRange.endValue }}
                    {{ getType(el.matchFId, el.oper, "matchFieldList") }}
                  </div>
                  <div
                    class="info-item"
                    v-if="el.sourceType == '2' && el.oper != 'range'"
                  >
                    匹配值：{{ el.matchValue
                    }}{{ getType(el.matchFId, el.oper, "matchFieldList") }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!--公式型规则-->
          <div class="sub-title" v-if="item.indicatorFormulaRule">
            <i
              :style="{ background: $store.state.layoutStore.themeObj.color }"
            ></i>
            <span class="sub-font marr">公式型规则</span>
          </div>
          <div class="bdr" v-if="item.indicatorFormulaRule">
            <div class="rule-content">
              <div class="rule-title marr">公式型规则</div>
              <div class="rule-form">
                <div class="info-view clearfix">
                  <div
                    class="info-item"
                    v-if="item.indicatorFormulaRule.assignFId"
                  >
                    分配数据源字段：{{
                      formateValue(
                        item.indicatorFormulaRule.assignFId,
                        "assignFieldList"
                      )
                    }}
                  </div>
                  <div
                    class="info-item"
                    v-if="item.indicatorFormulaRule.computeFormula"
                  >
                    计算公式：{{ item.indicatorFormulaRule.computeFormula }}
                  </div>
                  <div class="info-item">
                    备注：{{ item.indicatorFormulaRule.remark }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 匹配结果决策 -->
          <div
            class="sub-title"
            v-if="item.resultDecisionList && item.resultDecisionList.length"
          >
            <i
              :style="{ background: $store.state.layoutStore.themeObj.color }"
            ></i>
            <span class="sub-font marr">匹配结果决策</span>
          </div>
          <div v-for="(el, idx) in item.resultDecisionList" :key="idx + 'c'">
            <div class="info-view clearfix">
              <div class="info-item marl20" style="width:100%;">
                {{
                  el.decisionType
                    | getDicItemName("sct.indicator.resultDecisionType")
                }}：{{
                  el.decisionValue
                    | getDicItemName("sct.indicator.resultDecisionValue")
                }}
              </div>
            </div>
            <div
              v-if="
                el.decisionType == '2' &&
                  (el.decisionValue == '3' || el.decisionValue == '4') &&
                  el.skipRule
              "
              class="rule-content mar"
            >
              <div class="bdr">
                <div class="rule-title">匹配多条数据向下规则</div>
                <div class="rule-form">
                  <div class="info-view clearfix">
                    <div class="info-item">
                      是否跳过后续指标：{{
                        el.skipRule.isSkig | getDicItemName("gen.yesorno.num")
                      }}
                    </div>
                    <div
                      class="info-item"
                      v-if="el.skipRule.skipIndicatorIdDisplay"
                    >
                      跳过后续指标选择：{{ el.skipRule.skipIndicatorIdDisplay }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              v-if="el.decisionRuleList && el.decisionRuleList.length > 0"
              class="mar"
            >
              <div class="bdr">
                <div
                  class="rule-content"
                  v-for="(o, i) in el.decisionRuleList"
                  :key="i"
                >
                  <div class="line" v-if="i > 0">
                    <span
                      :style="{
                        background: $store.state.layoutStore.themeObj.color,
                      }"
                      >{{
                        o.ruleRelation
                          | getDicItemName("sct.indicator.ruleRelation")
                      }}</span
                    >
                  </div>
                  <div class="rule-title marr">决策型规则{{ i + 1 }}</div>
                  <div class="rule-form">
                    <div class="info-view clearfix">
                      <!-- <div class="info-item" v-if="i>0">与决策型规则1的关系：{{o.ruleRelation |getDicItemName("sct.indicator.ruleRelation")}}</div> -->
                      <div class="info-item">
                        匹配源选择：{{
                          o.sourceType
                            | getDicItemName("sct.indicator.sourceType")
                        }}
                      </div>
                      <div class="info-item" v-if="o.sourceType == '1'">
                        分配数据源字段：{{
                          formateValue(o.assignFId, "assignFieldList")
                        }}
                      </div>
                      <div class="info-item" v-if="o.sourceType == '1'">
                        匹配操作符：{{
                          o.oper | getDicItemName("sct.indicator.oper")
                        }}
                      </div>
                      <div class="info-item">
                        匹配数据源字段：{{
                          formateValue(o.matchFId, "matchFieldList")
                        }}
                      </div>
                      <div class="info-item" v-if="o.sourceType == '2'">
                        匹配操作符：{{
                          o.oper | getDicItemName("sct.indicator.oper")
                        }}
                      </div>
                      <div
                        class="info-item"
                        v-if="o.sourceType == '2' && o.oper == 'range'"
                      >
                        匹配范围：{{
                          o.matchValueRange.startDic
                            | getDicItemName("sct.indicator.oper")
                        }}{{ o.matchValueRange.startValue }}
                        {{ getType(o.matchFId, o.oper, "matchFieldList") }}
                        {{
                          o.matchValueRange.endDic
                            | getDicItemName("sct.indicator.oper")
                        }}{{ o.matchValueRange.endValue }}
                        {{ getType(o.matchFId, o.oper, "matchFieldList") }}
                      </div>
                      <div
                        class="info-item"
                        v-if="o.sourceType == '2' && o.oper != 'range'"
                      >
                        匹配值：{{ o.matchValue
                        }}{{ getType(o.matchFId, o.oper, "matchFieldList") }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 优先权比例分配设置 -->
          <div class="sub-title" v-if="item.interveneRuleList != undefined">
            <i
              :style="{ background: $store.state.layoutStore.themeObj.color }"
            ></i>
            <span class="sub-font marr">优先权比例分配设置</span>
          </div>
          <div
            v-if="
              item.interveneRuleList != undefined &&
                item.interveneRuleList.length
            "
          >
            <div class="rule-form">
              <div class="info-view clearfix">
                <div class="info-item" style="width:100%;">
                  {{ el.priorityName }}：{{ el.assignCount }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import { getDicItemList } from "@/config/tool.js";
import { Loading } from "element-ui";
import {
  getConfig,
  getSourceTemplate,
  saveConfig,
} from "@/api/strategyMatchManage/strategymatchindicator/index.js";
export default {
  name: "strategyMatchIndicatorConfigView",
  inject: ["sourceMatchFieldConfigView"],
  data() {
    return {
      titleListPros: {
        toolTitle: "指标规则详情",
        // toolList: [
        //   {
        //     name: "切换脑图",
        //     btnCode: "",
        //     type: "change"
        //   }
        // ]
      },
      updateForm: {
        indicatorId: "",
        priorityFields: "",
        priorityFieldsDisplay: "",
        ruleGroupList: [],
      },
      sourceTemplate: {},
      dicGather: {
        status: [],
        assignFieldList: [],
        matchFieldList: [],
        operList: [],
        rangeDicList: [],
        ruleTypeList: [],
        skipRuleIndicatorList: [],
        resultDecisionTypeList: [],
        resultDecisionValueList: [],
        ruleRelationList: [],
        sourceTypeList: [],
      },
    };
  },
  components: {
    TableToolTemp,
  },
  computed: {
    strategyMatchIndicatorObj() {
      return this.$store.state.strategyMatchManage.strategyMatchIndicatorObj;
    },
  },
  created() {
    this.init();
  },

  methods: {
    async init() {
      const ctx = this.sourceMatchFieldConfigView;
      this.updateForm.indicatorId = ctx.currentTab.indicatorId || "";
      let loadingInstance = Loading.service({
        fullscreen: true,
        lock: true,
        text: "加载中...",
        target: document.getElementsByTagName("body")[0],
      });
      await this.getSourceTemplate();
      await this.getDictList();
      await this.getConfig();
      loadingInstance.close();
    },
    async getSourceTemplate() {
      const ctx = this.sourceMatchFieldConfigView;
      let param = {
        strategyId: this.$route.query.strategyId,
        indicatorId: ctx.currentTab.indicatorId,
      };
      let res = await getSourceTemplate(param);
      if (!res) {
        return;
      }
      this.sourceTemplate = res;
    },
    async getDictList() {
      this.dicGather.assignFieldList = this.sourceTemplate.assignFieldList;
      this.dicGather.matchFieldList = this.sourceTemplate.matchFieldList;
      this.dicGather.skipRuleIndicatorList = this.sourceTemplate.skipRuleIndicatorList;
      this.dicGather.ruleTypeList = await getDicItemList(
        "sct.indicator.ruleType"
      );
      this.dicGather.status = await getDicItemList("gen.yesorno.num");
      this.dicGather.operList = await getDicItemList("sct.indicator.oper");
      this.dicGather.resultDecisionTypeList = await getDicItemList(
        "sct.indicator.resultDecisionType"
      );
      this.dicGather.resultDecisionValueList = await getDicItemList(
        "sct.indicator.resultDecisionValue"
      );
      this.dicGather.ruleRelationList = await getDicItemList(
        "sct.indicator.ruleRelation"
      );
      this.dicGather.sourceTypeList = await getDicItemList(
        "sct.indicator.sourceType"
      );
      let arr = ["gt", "gte", "lt", "lte"];
      this.dicGather.rangeDicList = this._.filter(
        this.dicGather.operList,
        (el) => {
          return this._.includes(arr, el.dicItemCode);
        }
      );
      await getDicItemList("sct.source.priorityRule");
    },
    async getConfig() {
      const ctx = this.sourceMatchFieldConfigView;
      let res = await getConfig({ indicatorId: ctx.currentTab.indicatorId });
      if (res) {
        let getList = (list, listType) => {
          if (list != undefined) {
            this._.each(list, (item) => {
              item.matchValueRange = {
                startDic: "",
                startValue: "",
                endDic: "",
                endValue: "",
              };

              if (item.sourceType == "2") {
                item.indicatorOperList = this.getIndicatorOperList(
                  "matchFieldList",
                  item.matchFId
                );
                item.matchListForSelect = this.getMatchListForSelect(
                  "matchFieldList",
                  item.matchFId
                );
              } else {
                item.indicatorOperList = this.getIndicatorOperList(
                  "assignFieldList",
                  item.assignFId
                );
                item.matchListForSelect = this.getMatchListForSelect(
                  "assignFieldList",
                  item.assignFId
                );
              }

              item.ruleRelation = item.ruleRelation || "or";

              let fid = item.assignFId;

              if (listType == "matchFieldList") {
                fid = item.matchFId;
              }

              // 字典转换

              let el = this._.find(this.dicGather[listType], ["fid", fid]);
              if (el && el.isDic == "1" && item.matchValue) {
                let arr = item.matchValue.split("/");
                item.matchValue = this._.map(arr, (o) => {
                  let dic = this._.find(item.matchListForSelect, [
                    "dicItemCode",
                    o,
                  ]);
                  if (dic) {
                    return dic.dicItemName;
                  } else {
                    return o;
                  }
                }).join("/");
              }

              if (item.oper == "range") {
                let o = JSON.parse(item.matchValue);
                let keys = this._.keys(o);
                item.matchValueRange.startDic = keys[0];
                item.matchValueRange.startValue = o[keys[0]];
                item.matchValueRange.endDic = keys[1];
                item.matchValueRange.endValue = o[keys[1]];
              }
            });
          }
          return list || [];
        };

        this.updateForm.priorityFields = res.priorityFields;
        if (this.updateForm.priorityFields) {
          let arr = this.updateForm.priorityFields.split("/");
          if (arr) {
            let priorityRuleList = this._.filter(
              this.dicGather.matchFieldList,
              (el) => {
                return el.propertyType == "2";
              }
            );
            this.updateForm.priorityFieldsDisplay = this._.map(arr, (item) => {
              let o = this._.find(priorityRuleList, ["fid", item]);
              if (o) {
                return o.name;
              } else {
                return "";
              }
            }).join("/");
          }
        }

        this._.each(res.ruleGroupList, (obj) => {
          obj.showRule = true;
          obj.conditionRuleList = getList(
            obj.conditionRuleList,
            "assignFieldList"
          );
          obj.matchRuleList = getList(obj.matchRuleList, "matchFieldList");
          if (obj.resultDecisionList != undefined) {
            this._.each(obj.resultDecisionList, (item, index) => {
              item.decisionRuleList = getList(
                item.decisionRuleList,
                "matchFieldList"
              );
              if (item.skipRule) {
                if (item.skipRule.skipIndicatorId) {
                  item.skipRule.isSkig = "1";
                  let arr1 = item.skipRule.skipIndicatorId.split("/");
                  let str2 = this._.map(arr1, (el) => {
                    let o = this._.find(this.dicGather.skipRuleIndicatorList, [
                      "indicatorId",
                      el,
                    ]);
                    return o.name;
                  }).join("/");

                  item.skipRule.skipIndicatorIdDisplay = str2;
                }
              } else {
                item.skipRule = {
                  isSkig: "0",
                  skipIndicatorId: "",
                  skipIndicatorIdDisplay: "",
                };
              }
            });
          }

          if (obj.interveneRuleList != undefined) {
            let arr = this._.filter(this.dicGather.matchFieldList, (el) => {
              return el.propertyType == "2" || el.propertyType == "3";
            });
            obj.interveneRuleList = this._.map(
              obj.interveneRuleList,
              (item) => {
                let o = this._.find(arr, (el) => {
                  return el.priorityConfig.fid == item.matchFId;
                });
                return {
                  matchFId: item.matchFId,
                  assignCount: item.assignCount,
                  priorityName: o.priorityConfig.priorityName,
                };
              }
            );
          }
        });

        this.updateForm.ruleGroupList = res.ruleGroupList;
      }
    },

    getIndicatorOperList(listType, fid) {
      let list = [];
      let o = this._.find(this.dicGather[listType], ["fid", fid]);
      if (o) {
        let arr = [];
        if (o.isDic == "1") {
          // 字段类型是字典  匹配符 :包含 不包含 等于 不等于
          arr = ["eq", "neq", "contain", "notcontain"];
        } else if (o.dataType == "int" || o.dataType == "float") {
          // 字段类型是整数或浮点 匹配符：等于 不等于 大于 小于 大于等于 小于等于 范围
          arr = ["eq", "neq", "gte", "gt", "lt", "lte", "range"];
        } else {
          // 字段类型是字符串  匹配符：等于 不等于 包含 不包含
          arr = ["eq", "neq", "contain", "notcontain"];
        }
        list = this._.filter(this.dicGather.operList, (el) => {
          return this._.includes(arr, el.dicItemCode);
        });
      }

      return list;
    },
    getMatchListForSelect(listType, fid) {
      let list = [];
      let o = this._.find(this.dicGather[listType], ["fid", fid]);
      if (o) {
        list = this._.map(o.dicList, (item) => {
          return {
            dicItemCode: item.dicKey,
            dicItemName: item.dicValue,
          };
        });
      }
      return list;
    },

    getType(fid, oper, listType) {
      let o = this._.find(this.dicGather[listType], ["fid", fid]);
      let unit = "";
      if (o) {
        unit = o.unit || "";
      }
      return unit;
    },

    formateValue(data, listType) {
      let o = this._.find(this.dicGather[listType], ["fid", data]);
      if (o) {
        return o.name;
      }
      return data;
    },
    goBack() {
      const ctx = this.sourceMatchFieldConfigView;
      ctx.componentName = ctx.currentTab.component;
    },
  },
};
</script>

<style lang="less">
.strategy-match-indicator-config-view {
  .main-title {
    cursor: pointer;
  }
  .bdr {
    border-radius: 7px;
    background: #f5f5f5;
    margin-left: 30px;
  }
  .line {
    width: 100%;
    height: 1px;
    background: #eaeaea;
    position: relative;
    margin: 2px 0;
    span {
      width: 50px;
      height: 20px;
      text-align: center;
      color: #fff;
      line-height: 20px;
      font-size: 12px;
      border-radius: 7px;
      display: inline-block;
      position: absolute;
      top: -10px;
      left: 20px;
    }
  }
  .base-info {
    width: 100%;
    padding-left: 38px;
    .base-item {
      float: left;
      width: 25%;
      margin-bottom: 20px;
    }
  }

  .info-view {
    width: 100%;
    padding-left: 11px;
    .info-item {
      float: left;
      width: 25%;
      margin-bottom: 20px;
    }
  }
  .mar {
    margin-bottom: 20px;
  }
  .marl {
    margin-left: 6px;
  }
  .marr {
    margin-right: 6px;
  }
  .marl20 {
    margin-left: 20px;
  }

  .rule-group-wrap {
    padding-left: 20px;
    .rule-group-item {
      padding: 0 12px;
      margin-bottom: 10px;
      .rule-group-title {
        background: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 42px;
        line-height: 42px;
        padding-left: 10px;
        border-radius: 7px;
        .title {
          font-weight: bold;
          cursor: pointer;
          font-size: 15px;
        }
      }
      .sub-title {
        margin-top: 10px;
        margin-bottom: 10px;
        .sub-font {
          font-size: 15px;
          font-weight: bold;
        }
        i {
          display: inline-block;
          width: 8px;
          height: 8px;
          margin-left: 10px;
          margin-right: 10px;
          border-radius: 50%;
        }
      }
      .rule-content {
        .rule-title {
          height: 42px;
          line-height: 42px;
          font-size: 14px;
          padding-left: 20px;
          font-weight: bold;
        }
        .rule-form {
          padding-left: 10px;
        }
      }
    }
  }
}
</style>
