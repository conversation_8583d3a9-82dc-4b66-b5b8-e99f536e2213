<template>
  <div class="strateg-match-list">
    <TableToolTemp
      :toolListProps="toolListProps"
      @handleTool="handleTool"
    ></TableToolTemp>
    <SearchForm
      :searchForm="initParam"
      :labelWidth="'120px'"
      :searchFormTemp="searchFormTemp"
      @normalSearch="normalSearch"
      @normalResetQuery="normalResetQuery"
    ></SearchForm>
    <el-table
      :data="tableData"
      stripe
      v-hover
      class="dt-table"
      style="width: 100%"
    >
      <el-table-column
        align="center"
        prop="strategyId"
        width="150px"
        label="策略ID"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.strategyId }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="name"
        width="250px"
        label="策略名称"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="matchRule" label="匹配规则">
        <template slot-scope="scope">
          <span>{{
            scope.row.matchRule | getDicItemName("sct.match.rule")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="sourceConfigStatus"
        label="数据源配置状态"
      >
        <template slot-scope="scope">
          <span>{{
            scope.row.sourceConfigStatus
              | getDicItemName("sct.source.config.status")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="indicatorConfigStatus"
        label="指标配置状态"
      >
        <template slot-scope="scope">
          <span>{{
            scope.row.indicatorConfigStatus
              | getDicItemName("sct.source.config.status")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="status" label="是否生效">
        <template slot-scope="scope">
          <span>{{
            scope.row.status | getDicItemName("gen.yesorno.num")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="executedTimes" label="被执行次数">
        <template slot-scope="scope">
          <span>{{ scope.row.executedTimes }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="lastExecutedTime"
        label="最后一次执行时间"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.lastExecutedTime }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="updateId" label="最后一次修改人">
        <template slot-scope="scope">
          <span>{{
            scope.row.updateId | getNickName("scope.row.updateId")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="updateTime"
        label="最后一次修改时间"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.updateTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        header-align="center"
        label="操作"
        fixed="right"
        width="100"
      >
        <template slot-scope="scope">
          <div>
            <el-button type="text" size="mini" @click="handleUpdate(scope.row)"
              >编辑</el-button
            >
            <el-button type="text" size="mini" @click="handleCopy(scope.row)"
              >复制</el-button
            >
          </div>
          <div>
            <el-button
              type="text"
              size="mini"
              v-if="scope.row.status == '0'"
              @click="handleDel(scope.row)"
              >删除</el-button
            >
            <el-button
              type="text"
              size="mini"
              v-if="
                scope.row.sourceConfigStatus == '1' &&
                  scope.row.indicatorConfigStatus == '1'
              "
              @click="handleView(scope.row)"
              >查看</el-button
            >
          </div>
          <div>
            <el-button
              type="text"
              size="mini"
              @click="handleSourceConfig(scope.row)"
              >配置数据源</el-button
            >
          </div>
          <div>
            <el-button
              type="text"
              size="mini"
              @click="handleIndicatorConfig(scope.row)"
              >配置指标</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :pageData="initParam"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
    ></Pagination>
    <DtPopup
      :isShow.sync="showPopup"
      @close="showPopup = false"
      @confirm="confirmUpdate"
      :isSmall="true"
    >
      <div class="popup-text">
        删除后无法恢复，请确认是否删除？
      </div>
    </DtPopup>

    <DtPopup
      :isShow.sync="showCopyPopup"
      @close="showCopyPopup = false"
      title="复制策略"
      size="small"
      :footer="false"
    >
      <el-form
        :model="copyObj"
        label-width="120px"
        :rules="rules"
        ref="copyForm"
        label-position="left"
      >
        <div style="max-height:70vh;overflow:auto">
          <el-form-item label="策略名称" prop="name">
            <el-input v-model="copyObj.name" placeholder="请输入"></el-input>
          </el-form-item>
        </div>
        <el-form-item>
          <el-button
            type="primary"
            class="dt-btn"
            plain
            :style="{ color: $store.state.layoutStore.themeObj.color }"
            @click="cancle"
            >取消</el-button
          >
          <el-button type="primary" class="dt-btn" @click="confirmCopy"
            >保存</el-button
          >
        </el-form-item>
      </el-form>
    </DtPopup>
  </div>
</template>
<script>
import {
  getStrategymatch,
  copyStrategymatch,
  deleteStrategymatch,
} from "@/api/strategyMatchManage/index.js";
import { getDicItemList } from "@/config/tool";
import { baseComponent } from "@/utils/common";
import { validate, validateAlls } from "@/config/validation";

export default {
  name: "strategyMatchList",
  mixins: [baseComponent],
  data() {
    return {
      toolListProps: {
        toolTitle: "分配策略管理",
        toolList: [
          {
            name: "新增策略",
            btnCode: "",
            type: "add",
          },
        ],
      },
      searchFormTemp: [
        {
          label: "策略ID",
          name: "strategyId",
          type: "input",
        },
        {
          label: "策略名称",
          name: "name",
          type: "input",
        },
        {
          label: "策略规则",
          name: "matchRule",
          type: "select",
          list: [],
        },
        {
          label: "数据源配置状态",
          name: "sourceConfigStatus",
          type: "select",
          list: [],
        },
        {
          label: "指标配置状态",
          name: "indicatorConfigStatus",
          type: "select",
          list: [],
        },
        {
          label: "是否生效",
          name: "status",
          type: "select",
          list: [],
        },
      ],
      rules: {
        name: [
          {
            required: true,
            validator: validate,
            trigger: "blur",
            min: 1,
            max: 30,
            regax: [
              {
                message: "请输入30个字符以内不能包含特殊字符",
                ruleFormat: /^[a-zA-Z0-9\u4e00-\u9fa5]+$/i,
              },
            ],
          },
        ],
      },
      tableData: [],
      initParam: {
        param: {
          strategyId: "",
          name: "",
          matchRule: "",
          sourceConfigStatus: "",
          indicatorConfigStatus: "",
          status: "",
        },
        pageSize: 10,
        pageNum: 1,
      },
      total: 0,
      showPopup: false,
      showCopyPopup: false,
      delStrategyId: "",
      copyObj: {
        name: "",
        strategyId: "",
      },
    };
  },
  created() {
    this.initData();
  },
  methods: {
    //初始化数据
    async initData() {
      await this.getDictList();
      this.initList();
    },
    async initList() {
      this.getQueryParam(this.initParam, "strategyMatchParam");
      let res = await getStrategymatch(this.initParam);
      if (!res) {
        return;
      }
      this.tableData = res.list;
      this.initParam.pageNum = res.pageNum;
      this.initParam.pageSize = res.pageSize;
      this.total = Number(res.total);
    },
    handleTool(item) {
      if (item.type == "add") {
        this.$router.push({
          name: "strategyMatchUpdate",
          query: {
            type: item.type,
          },
        });
      }
    },
    handleUpdate(row) {
      this.$store.commit("strategyMatchManage/setStrategyMatchObj", row);
      this.$router.push({
        name: "strategyMatchUpdate",
        query: {
          type: "edit",
          strategyId: row.strategyId,
        },
      });
    },
    handleCopy(row) {
      this.copyObj.strategyId = row.strategyId;
      this.showCopyPopup = true;
    },
    cancle() {
      this.copyObj = {
        strategyId: "",
        name: "",
      };
      this.showCopyPopup = false;
    },
    async confirmCopy() {
      if (!validateAlls(this.$refs.copyForm)) {
        return;
      }
      let res = await copyStrategymatch(this.copyObj);
      if (!res) {
        return;
      }
      this.initList();
      this.showCopyPopup = false;
    },

    handleDel(row) {
      this.delStrategyId = row.strategyId;
      this.showPopup = true;
    },
    async confirmUpdate() {
      let res = await deleteStrategymatch({ strategyId: this.delStrategyId });
      if (!res) {
        return;
      }
      this.initList();
      this.showPopup = false;
    },
    handleView(row) {
      this.setCacheArr("del");
      this.handleRoute(row, "sourceMatchFieldConfigView");
    },
    handleStart(row) {
      this.$store.commit("layoutStore/setCacheArr", {
        status: "del",
        routeName: "executedRecordPage",
      });
      this.handleRoute(row, "executedRecordPage");
    },
    handleSourceConfig(row) {
      this.handleRoute(row, "sourceMatchFieldConfig");
    },
    handleIndicatorConfig(row) {
      this.handleRoute(row, "strategyMatchIndicatorList");
    },
    handleRoute(row, name) {
      this.setQueryParam(this.initParam, "strategyMatchParam");
      this.$store.commit("strategyMatchManage/setStrategyMatchObj", row);
      this.$router.push({
        name: name,
        query: {
          strategyId: row.strategyId,
        },
      });
    },
    setCacheArr(type) {
      let arr = [
        "baseInfoConfig",
        "strategyExecutedRecordList",
        "strategyMatchIndicatorListView",
        "sourceAssignFieldList",
        "sourceMatchFieldList",
        "priorityConfigList",
      ];
      this._.each(arr, (item) => {
        this.$store.commit("layoutStore/setCacheArr", {
          status: type,
          routeName: item,
        });
      });
    },
    // 获取字典/标签/分组
    async getDictList() {
      let res = await getDicItemList("sct.match.rule");
      this.setSearchFormTemp("matchRule", res);
      res = await getDicItemList("sct.source.config.status");
      this.setSearchFormTemp("sourceConfigStatus", res);
      this.setSearchFormTemp("indicatorConfigStatus", res);
      res = await getDicItemList("gen.yesorno.num");
      this.setSearchFormTemp("status", res);
    },
  },
};
</script>

<style lang="less">
.strateg-match-list {
}
</style>
