<template>
  <div class="source-match-field-list">
    <TableToolTemp :toolListProps="toolListProps" @handleTool="handleTool"></TableToolTemp>
    <el-table :data="tableData" stripe v-hover class="dt-table" style="width: 100%" ref="myTable">
      <template v-for="(item,index) in tableHead">
        <el-table-column :prop="item.column_name" align='center' :label="item.column_comment" :key="index">
          <template slot-scope="scope">
            <div>
              {{typeof(scope.row.data)=='object'? scope.row.data[item.column_name]:scope.row[item.column_name]}}
            </div>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <Pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :pageData="initParam" :total="total" layout="total, sizes, prev, pager, next, jumper"></Pagination>

  </div>
</template>

<script>
import { baseComponent } from "@/utils/common"
import { getSourceMatchFieldList, getSourceDataPageList } from "@/api/strategyMatchManage/strategyExecutedRecord/index.js";
import domainObj from "@/utils/globalParam";
export default {
  name: "sourceMatchFieldInfoList",
  mixins: [baseComponent],
  props: {
    fromExecutedRecordPage: {
      type: Boolean,
      default: false
    }
  },
  inject: ["executedRecordInfoView"],
  computed: {
    currentLoginUser() {
      return this.$store.getters["layoutStore/getCurrentLoginUser"];
    },
    fromProcessAnalysis() {
      const ctx = this.executedRecordInfoView
      if (!ctx.subTable) { return false }
      return ctx.subTable.fromProcessAnalysis
    }
  },
  data() {
    return {
      toolListProps: {
        toolTitle: "匹配型数据列表",
        toolList: [
          {
            name: "下载模板",
            btnCode: "",
            downloadURL: '',
            type: "download"
          },
          {
            name: "导入",
            btnCode: "",
            type: "import"
          }
        ]
      },
      downloadURL: "/web/sourcematchfield/downloadDataTemplate",
      loginParam: "",
      tableHead: [],
      tableData: [],
      cloneTableData: [],  //表格副本 用于过滤和显示
      initParam: {
        pageNum: 1,
        pageSize: 20,
        param: {
          recordId: "",
          dataType: "2"
        }
      },
      total: 0
    };
  },
  created() {
    this.init()
  },
  watch: {
    tableData(val){
      const ctx = this.executedRecordInfoView
      ctx.sourceMatchFieldInfoList = val
    }
  },
  methods: {
    async init() {
      this.loginParam = `access_token=${sessionStorage.getItem("LoginAccessToken")}&tenantId=${this.currentLoginUser.tenantId}&funcId=${this.currentLoginUser.funcId}&strategyId=${this.$route.query.strategyId}`;
      await this.getTableHead()
      if (!this.fromExecutedRecordPage) {
        const ctx = this.executedRecordInfoView
        this.initParam.param.recordId = ctx.param.recordId || this.$route.query.recordId || ""
        this.toolListProps.toolList = []
        if (this.fromProcessAnalysis) {
          this.initSubTable()
        } else {
          this.initList()
        }
      } else {
        this.initTool()
        this.initParam.pageSize = 5
      }
    },

    initTool() {
      this._.each(this.toolListProps.toolList, item => {
        if (item.type == "download") {
          console.log(domainObj, "domainObj")
          item.downloadURL = `${domainObj.baseUrl}${this.downloadURL}?${this.loginParam}`
        }
      })
    },


    async getTableHead() {
      let res = await getSourceMatchFieldList({ strategyId: this.$route.query.strategyId })
      if (res) {
        this.tableHead = this._.map(res, item => {
          return {
            column_name: item.fieldId,
            column_comment: item.name
          }
        })
      }
    },
    async initList() {
      if (!this.initParam.param.recordId) {
        return
      }

      let res = await getSourceDataPageList(this.initParam);
      if (!res) {
        return
      }
      this.tableData = res.list;
      this.initParam.pageNum = res.pageNum;
      this.initParam.pageSize = res.pageSize;
      this.total = Number(res.total);
    },
    handleTool(item) {
      let strategyId = this.$route.query.strategyId
      if (item.type == "import") {
        const ctx = this
        this.$dt_upload.show({
          actionUrl: "/web/sourcematchfield/uploadDataTemplate",
          formData: {
            strategyId: strategyId
          },
          onSuccess(res) {
            if (ctx._.isArray(res)) {
              ctx.tableData = res;
              ctx.cloneTableData = ctx._.cloneDeep(ctx.tableData)
              ctx.filterTableData()
            }
          },
          onError(msg) {
            ctx.$message.error(msg)
          }
        });
      }
    },
    initSubTable() {
      const ctx = this.executedRecordInfoView
      this.cloneTableData = ctx.subTable.list
      this.filterTableData()
    },
    filterTableData() {
      this.tableData = this.cloneTableData.slice((this.initParam.pageNum - 1) * this.initParam.pageSize, (this.initParam.pageNum - 1) * this.initParam.pageSize + this.initParam.pageSize)
      this.total = this.cloneTableData.length
    },
    handleCurrentChange(val) {
      this.initParam.pageNum = val;
      if (this.fromExecutedRecordPage||this.fromProcessAnalysis) {
        this.filterTableData();
      } else {
        this.initList()
      }

    },
    handleSizeChange(val) {
      this.initParam.pageSize = val;
      if (this.fromExecutedRecordPage||this.fromProcessAnalysis) {
        this.filterTableData();
      } else {
        this.initList()
      }
    }
  }
}
</script>

<style lang="less">
.source-match-field-list {}
</style>
