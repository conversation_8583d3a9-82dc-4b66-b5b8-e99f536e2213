<template>
  <div class="process-analysis-list">
    <TableToolTemp :toolListProps="{toolTitle:'过程分析'}"></TableToolTemp>
    <el-table :data="tableData" stripe v-hover class="dt-table" style="width: 100%" ref="myTable">
      <el-table-column label="指标名称" prop="indicatorName"></el-table-column>
      <el-table-column label="耗时（毫秒）" prop="executedMillisecond"></el-table-column>
      <el-table-column align='center' prop="skip" label="是否跳过">
        <template slot-scope="scope">
          <span>{{scope.row.skip |getDicItemName("gen.yesorno.num")}}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="分配源数据" width="120px">
        <template slot-scope="scope">
          <el-button size="mini" @click="handleView(scope.row,'sourceAssignFieldInfoList')" type="text">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="匹配源数据" width="120px">
        <template slot-scope="scope">
          <el-button size="mini" @click="handleView(scope.row,'sourceMatchFieldInfoList')" type="text">{{scope.row.matchDataList&&scope.row.matchDataList.length?scope.row.matchDataList.length:0}}</el-button>
        </template>
      </el-table-column>

      
    </el-table>

  </div>
</template>

<script>
import { baseComponent } from "@/utils/common"
import { getDicItemList } from "@/config/tool";
import {getProcessAnalysisList } from "@/api/strategyMatchManage/strategyExecutedRecord/index.js";
export default {
  name: "processAnalysisList",
  mixins: [baseComponent],
  inject: ["executedRecordInfoView"],
  computed: {
    currentLoginUser() {
      return this.$store.getters["layoutStore/getCurrentLoginUser"];
    }
  },
  data() {
    return {
      tableData: [],
    };
  },
  created() {
    this.init()
  },
  methods: {
    async init() {
      await this.getDictList()
      this.initData()
    },

    // 获取字典/标签/分组
    async getDictList() {
      await getDicItemList("gen.yesorno.num");
    },

    async initData() {
      const ctx = this.executedRecordInfoView

      let res = await getProcessAnalysisList({resultId:ctx.param.resultId});
      if (!res) {
        return
      }
      this.tableData = res
    },
    changeComponent(row,componentName) {
      const ctx = this.executedRecordInfoView;
      ctx.componentName = componentName;
      let marName={
        "sourceAssignFieldInfoList":"分配型数据列表",
        "sourceMatchFieldInfoList":"匹配型数据列表",
      }



      let subTable = {
        fromProcessAnalysis:true,
        list:[]
      }
      if(componentName == "sourceAssignFieldInfoList"){
        subTable.list = [row.assignData.data]
      }else{
        subTable.list = row.matchDataList
      }

      ctx.subTable = subTable

      let arr = [
        {
          name:marName[componentName],
          recordId:row.recordId,
          path:componentName,
          subTable:subTable
        }
      ]
      ctx.param.breadcrumbList = this._.concat(ctx.param.breadcrumbList,arr)
    },
    handleView(row,componentName) {
      if(componentName == "sourceMatchFieldInfoList"&&(!row.matchDataList||row.matchDataList.length==0)){return}
      this.changeComponent(row,componentName)
    },
  }
};
</script>

<style lang="less">
.process-analysis-list {}
</style>
