<template>
  <div class="source-assign-field-list">
    <TableToolTemp :toolListProps="toolListProps" @handleTool="handleTool"></TableToolTemp>
    <el-table :data="tableData" stripe v-hover class="dt-table" style="width: 100%" ref="multipleTable" @selection-change="handleSelectionChange">
      <el-table-column align='center' v-if="tableHead.length>0&&fromExecutedRecordPage" type="selection" width="55">
      </el-table-column>
      <template v-for="(item,index) in tableHead">
        <el-table-column :prop="item.column_name" align='center' :label="item.column_comment" :key="index">
          <template slot-scope="scope">
            <div>
              {{typeof(scope.row.data)=='object'? scope.row.data[item.column_name]:scope.row[item.column_name]}}
            </div>
          </template>
        </el-table-column>
      </template>
      <el-table-column align='center' v-if="tableHead.length>0&&fromExecutedRecordPage" label="操作" fixed="right" width="55">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="handleAllot(scope.row)">分配</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination v-if="!fromProcessAnalysis" @size-change="handleSizeChange" @current-change="handleCurrentChange" :pageData="initParam" :total="total" layout="total, sizes, prev, pager, next, jumper"></Pagination>

  </div>
</template>

<script>
import { baseComponent } from "@/utils/common"
import { getSourceAssignFieldList, getSourceDataPageList, executeStrategyRecord } from "@/api/strategyMatchManage/strategyExecutedRecord/index.js";
import domainObj from "@/utils/globalParam";
export default {
  name: "sourceAssignFieldInfoList",
  mixins: [baseComponent],
  props: {
    fromExecutedRecordPage: {
      type: Boolean,
      default: false
    }
  },
  inject: ["executedRecordInfoView"],
  computed: {
    currentLoginUser() {
      return this.$store.getters["layoutStore/getCurrentLoginUser"];
    },
    canAllot() {
      const ctx = this.executedRecordInfoView
      return ctx.sourceMatchFieldInfoList.length > 0
    },
    fromProcessAnalysis() {
      const ctx = this.executedRecordInfoView
      if (!ctx.subTable) { return false }
      return ctx.subTable.fromProcessAnalysis
    }
  },
  data() {
    return {
      toolListProps: {
        toolTitle: "分配型数据列表",
        toolList: [
          {
            name: "下载模板",
            btnCode: "",
            downloadURL: '',
            type: "download"
          },
          {
            name: "导入",
            btnCode: "",
            type: "import"
          },
          {
            name: "批量分配",
            btnCode: "",
            type: "allot"
          }
        ]
      },
      downloadURL: "/web/sourceassignfield/downloadDataTemplate",
      loginParam: "",
      multipleSelection: [],
      cloneMultipleSelection: [],
      tableHead: [],
      tableData: [],
      cloneTableData: [],  //表格副本 用于过滤和显示 
      initParam: {
        pageNum: 1,
        pageSize: 20,
        param: {
          recordId: "",
          dataType: "1"
        }
      },
      total: 0,
      isChangePage: false
    };
  },
  created() {
    this.init()
  },

  watch: {
    "initParam.pageNum": {
      handler(newV, oldV) {
        this.isChangePage = true
        setTimeout(() => {
          this.isChangePage = false
        }, 20);
      }
    },
    "initParam.pageSize": {
      handler(newV, oldV) {
        this.isChangePage = true
        setTimeout(() => {
          this.isChangePage = false
        }, 20);
      }
    }
  },

  methods: {
    async init() {
      this.loginParam = `access_token=${sessionStorage.getItem("LoginAccessToken")}&tenantId=${this.currentLoginUser.tenantId}&funcId=${this.currentLoginUser.funcId}&strategyId=${this.$route.query.strategyId}`;
      await this.getTableHead()
      if (!this.fromExecutedRecordPage) {
        const ctx = this.executedRecordInfoView
        this.initParam.param.recordId = ctx.param.recordId || this.$route.query.recordId || ""
        this.toolListProps.toolList = []
        if (this.fromProcessAnalysis) {
          this.initSubTable()
        } else {
          this.initList()
        }
      } else {
        this.initTool()
        this.initParam.pageSize = 5
      }

    },

    initTool() {
      this._.each(this.toolListProps.toolList, item => {
        if (item.type == "download") {
          console.log(domainObj, "domainObj")
          item.downloadURL = `${domainObj.baseUrl}${this.downloadURL}?${this.loginParam}`
        }
      })
    },


    async getTableHead() {
      let res = await getSourceAssignFieldList({ strategyId: this.$route.query.strategyId })
      if (res) {
        this.tableHead = this._.map(res, item => {
          return {
            column_name: item.fieldId,
            column_comment: item.name
          }
        })
      }
    },
    async initList() {
      if (!this.initParam.param.recordId) {
        return
      }

      let res = await getSourceDataPageList(this.initParam);
      if (!res) {
        return
      }
      this.tableData = res.list;
      this.initParam.pageNum = res.pageNum;
      this.initParam.pageSize = res.pageSize;
      this.total = Number(res.total);
    },
    handleTool(item) {
      let strategyId = this.$route.query.strategyId
      if (item.type == "import") {
        const ctx = this
        this.$dt_upload.show({
          actionUrl: "/web/sourceassignfield/uploadDataTemplate",
          formData: {
            strategyId: strategyId
          },
          onSuccess(res) {
            if (ctx._.isArray(res)) {
              ctx._.each(res, (item, index) => {
                item.index = index + 1
              })
              ctx.cloneTableData = res
              ctx.filterTableData()
            }
          },
          onError(msg) {
            ctx.$message.error(msg)
          }
        });
      } else if (item.type == "allot") {
        if (this.multipleSelection.length === 0) {
          this.$message.error("请先选择需要分配的列表项！");
          return false;
        }
        if (!this.canAllot) {
          this.$message.error("请先上传匹配型数据！");
          return false;
        }

        this.handleAllot(this.multipleSelection)
      }
    },
    handleSelectionChange(val) {
      if (this.isChangePage) { return }
      this.multipleSelection = val
    },

    async handleAllot(data) {
      if (!this.canAllot) {
        this.$message.error("请先上传匹配型数据！");
        return false;
      }
      const ctx = this.executedRecordInfoView
      let assignData = this._.isArray(data) ? data : [data]

      this._.each(assignData, item => {
        delete item.index
      })
      let param = {
        strategyId: this.$route.query.strategyId,
        assignData: assignData,
        matchData: ctx.sourceMatchFieldInfoList
      }
      let res = await executeStrategyRecord(param)
      if (res) {
        this.multipleSelection = []
        this.$store.commit("strategyMatchManage/updatesExecutedRecordList")
      }


    },
    filterTableData() {
      this.tableData = this.cloneTableData.slice((this.initParam.pageNum - 1) * this.initParam.pageSize, (this.initParam.pageNum - 1) * this.initParam.pageSize + this.initParam.pageSize)
      this.total = this.cloneTableData.length
    },
    handleCurrentChange(val) {
      this.initParam.pageNum = val;
      if (this.fromExecutedRecordPage) {
        this.filterTableData();
        this.echo()
      } else {
        this.initList()
      }

    },
    handleSizeChange(val) {
      this.initParam.pageSize = val;
      if (this.fromExecutedRecordPage) {
        this.filterTableData();
        this.echo()
      } else {
        this.initList()
      }
    },
    echo() {
      if (this.multipleSelection.length == 0) { return }
      this.$nextTick(() => {
        this._.each(this.tableData, item => {
          let o = this._.find(this.multipleSelection, ["index", item.index])
          if (o) {
            this.$refs.multipleTable.toggleRowSelection(item, true)
          }
        })
      })
    },

    initSubTable() {
      const ctx = this.executedRecordInfoView
      this.tableData = ctx.subTable.list
    },

  }
};
</script>

<style lang="less">
.source-assign-field-list {}
</style>
