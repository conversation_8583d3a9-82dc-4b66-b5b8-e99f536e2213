<template>
  <div class="executed-record-info-view">
    <el-breadcrumb separator-class="el-icon-arrow-right" class="dt-bread">
      <el-breadcrumb-item :to="{ name: 'strategyMatchList' }">匹配策略管理</el-breadcrumb-item>
      <el-breadcrumb-item>
        <span class="breadcrumb" @click="goBack('1')">执行</span>
      </el-breadcrumb-item>
      <span v-if="param.breadcrumbList&&param.breadcrumbList.length>0">
        <el-breadcrumb-item v-for="(item,index) in param.breadcrumbList" :key="index" >
          <span class="breadcrumb" :style="{ color: index==param.breadcrumbList.length-1?$store.state.layoutStore.themeObj.color:'' }" @click="goBack('2',item,index)">{{item.name}}</span>
        </el-breadcrumb-item>
      </span>

      <span v-else>
        <el-breadcrumb-item :style="{ color: $store.state.layoutStore.themeObj.color }">{{getText}}</el-breadcrumb-item>
      </span>

    </el-breadcrumb>

    <component :is="componentName"></component>
  </div>
</template>
<script>
import sourceAssignFieldInfoList from "@/views/strategyMatchManage/strategyExecutedRecord/component/sourceAssignFieldInfoList.vue";
import sourceMatchFieldInfoList from "@/views/strategyMatchManage/strategyExecutedRecord/component/sourceMatchFieldInfoList.vue";
import matchResultInfoList from "@/views/strategyMatchManage/strategyExecutedRecord/component/matchResultInfoList.vue";
import processAnalysisList from "@/views/strategyMatchManage/strategyExecutedRecord/component/processAnalysisList.vue";
import flowChart from "@/views/strategyMatchManage/strategyExecutedRecord/component/flowChart.vue";


export default {
  name: "executedRecordInfoView",
  components: {
    sourceAssignFieldInfoList,
    sourceMatchFieldInfoList,
    matchResultInfoList,
    processAnalysisList,
    flowChart
  },
  provide() {
    return {
      executedRecordInfoView: this,
    };
  },
  data() {
    return {
      componentName: "",
      param: {},
      subTable:{
        fromProcessAnalysis:false,
        list:[]
      }
    };
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    currentLoginUser() {
      return this.$store.getters["layoutStore/getCurrentLoginUser"];
    },
    getText() {
      if (this.componentName == "sourceAssignFieldInfoList") {
        return "分配型数据列表"
      }
      if (this.componentName == "sourceMatchFieldInfoList") {
        return "匹配型数据列表"
      }
      if (this.componentName == "matchResultInfoList") {
        return "最终匹配型数据列表"
      }
    }
  },
  created() {
    this.init()
  },
  watch: {

  },
  methods: {
    init() {
      this.componentName = this.$route.query.componentName
    },
    goBack(type,item, index) {
      if (type == "2") {
        this.componentName = item.path
        this.param.recordId = item.recordId
        this.param.breadcrumbList = this._.filter(this.param.breadcrumbList,(item,idx)=>{return idx<=index})
      } else {
        this.$router.push({
          name: "executedRecordPage",
          query: {
            strategyId: this.$route.query.strategyId
          }
        })
      }

    }

  },
};
</script>
<style lang="less">
.executed-record-info-view {
  
}
</style>
