<template>
  <div class="executed-record-page">
    <el-breadcrumb separator-class="el-icon-arrow-right" class="dt-bread">
      <el-breadcrumb-item :to="{ name: 'strategyMatchList' }">匹配策略管理</el-breadcrumb-item>
      <el-breadcrumb-item :style="{ color: $store.state.layoutStore.themeObj.color }">执行</el-breadcrumb-item>
    </el-breadcrumb>
    <sourceAssignFieldInfoList :fromExecutedRecordPage="true"></sourceAssignFieldInfoList>
    <sourceMatchFieldInfoList :fromExecutedRecordPage="true"></sourceMatchFieldInfoList>
    <strategyExecutedRecordList :fromExecutedRecordPage="true"></strategyExecutedRecordList>
  </div>
</template>
<script>
import sourceAssignFieldInfoList from "@/views/strategyMatchManage/strategyExecutedRecord/component/sourceAssignFieldInfoList.vue";
import sourceMatchFieldInfoList from "@/views/strategyMatchManage/strategyExecutedRecord/component/sourceMatchFieldInfoList.vue";
import strategyExecutedRecordList from "@/views/strategyMatchManage/strategyExecutedRecord/component/strategyExecutedRecordList.vue";
export default {
  name: "executedRecordPage",
  components: {
    sourceAssignFieldInfoList,
    sourceMatchFieldInfoList,
    strategyExecutedRecordList
  },
  provide() {
    return {
      executedRecordInfoView: this,  //provide  inject必须同时存在  这里的三个组件存在复用，所以必须定义一个同名的 provide
    };
  },
  data() {
    return {
      sourceMatchFieldInfoList: []
    };
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    currentLoginUser() {
      return this.$store.getters["layoutStore/getCurrentLoginUser"];
    }
  },
  created() {
    this.$store.commit("layoutStore/setCacheArr", {
      status: "add",
      routeName: "executedRecordPage"
    });
  },

  watch: {

  },
  methods: {


  },
};
</script>
<style lang="less">
.executed-record-page {}
</style>
