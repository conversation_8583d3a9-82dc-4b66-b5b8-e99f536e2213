<template>
  <div class="strategy-match-update">
    <el-breadcrumb separator-class="el-icon-arrow-right" class="dt-bread">
      <el-breadcrumb-item :to="{ name: 'matchSourceIndex' }"
        >数据源管理</el-breadcrumb-item
      >
      <el-breadcrumb-item
        :style="{ color: $store.state.layoutStore.themeObj.color }"
        >{{ getText }}</el-breadcrumb-item
      >
    </el-breadcrumb>
    <TableToolTemp :toolListProps="titleListPros"></TableToolTemp>
    <el-form
      :model="updateForm"
      label-width="160px"
      :rules="rules"
      ref="updateForm"
      label-position="left"
      class="pdl-20"
    >
      <el-form-item label="数据源编码" prop="bizCode">
        <el-input
          v-model="updateForm.bizCode"
          class="dt-input-width"
          placeholder="请输入数据源编码"
        ></el-input>
      </el-form-item>
      <el-form-item label="数据源名称" prop="name">
        <el-input
          v-model="updateForm.name"
          class="dt-input-width"
          placeholder="请输入数据源名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="数据源类型" prop="sourceType">
        <el-select
          v-model="updateForm.sourceType"
          placeholder="请选择"
          class="dt-select"
        >
          <el-option
            v-for="(item, index) in commonData.sourceType"
            :key="index"
            :label="item.dicItemName"
            :value="item.dicItemCode"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="接口地址" prop="sourceValue">
        <el-input
          v-model="updateForm.sourceValue"
          class="dt-input-width"
          placeholder="请输入接口地址"
        ></el-input>
      </el-form-item>
      <el-form-item label="数据核对API" prop="checkDataApi">
        <el-input
          v-model="updateForm.checkDataApi"
          class="dt-input-width"
          placeholder="请输入数据核对API"
        ></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="updateForm.remark"
          class="dt-input-width"
          placeholder="请输入备注"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submit">保存</el-button>
        <el-button
          @click="goBack"
          type="primary"
          plain
          :style="{ color: $store.state.layoutStore.themeObj.color }"
          >取消</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import { validate, validateAlls } from "@/config/validation";
import { getDicItemList } from "@/config/tool.js";
import { algoSourceSave, algoSourceById } from "@/api/template/index.js";

export default {
  name: "sourceManageUpdate",
  data() {
    return {
      titleListPros: {
        toolTitle: ""
      },
      rules: {
        name: [
          {
            required: true,
            min: 1,
            max: 100,
            validator: validate,
            trigger: "blur"
          }
        ],
        sourceType: [{ required: true, validator: validate, trigger: "blur" }],
        sourceValue: [{ required: true, validator: validate, trigger: "blur" }],
        bizCode: [{ required: true, validator: validate, trigger: "blur" }]
      },
      updateForm: {
        name: "",
        sourceType: "",
        remark: "",
        bizCode: "",
        checkDataApi: "",
        sourceValue: ""
      },
      commonData: {
        sourceType: []
      },
      baseDisabled: false
    };
  },
  components: {
    TableToolTemp
  },
  computed: {
    getText() {
      if (this.type == "add") {
        return "新增数据源";
      }
      if (this.type == "edit") {
        return "编辑数据源";
      }
    }
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      this.type = this.$route.query.type;
      this.titleListPros.toolTitle = this.getText;
      await this.getDictList();
      if (this.type == "edit") {
        this.baseDisabled = true;
        this.initData();
      }
    },
    async initData() {
      let res = await algoSourceById({
        sourceId: this.$route.query.sourceId
      });
      if (!res) {
        return;
      }
      // this.updateForm = res;
      this.updateForm.sourceType = res.sourceType;
      this.updateForm.remark = res.remark;
      this.updateForm.checkDataApi = res.checkDataApi;
      this.updateForm.sourceValue = res.sourceValue;
      this.updateForm.name = res.name;
      this.updateForm.bizCode = res.bizCode;
      this.updateForm.sourceId = res.sourceId;
    },
    // 获取字典/
    async getDictList() {
      this.commonData.sourceType = await getDicItemList(
        "sct.algo.source.status"
      );
    },
    goBack() {
      this.$router.go(-1);
    },
    async submit() {
      if (!validateAlls(this.$refs.updateForm)) {
        return;
      }
      let res;
      if ((this.type = "edit")) {
        this.updateForm.sourceId = this.$route.query.sourceId;
      }
      res = await algoSourceSave(this.updateForm);
      if (!res) {
        return;
      }
      this.goBack();
    }
  }
};
</script>

<style lang="less">
.strategy-match-update {
  .table-tool {
    padding-bottom: 10px;
  }
  .draggable-wrap {
    display: inline-block;
  }
  .tags {
    margin-right: 6px;
  }
  .zidian-box {
    margin-bottom: 20px;
  }
}
</style>
