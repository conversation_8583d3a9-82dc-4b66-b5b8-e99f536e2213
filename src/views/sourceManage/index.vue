<template>
  <div>
    <div class="index-manage-container">
      <UniversalTable
        title="数据源列表"
        subtitle="管理和配置算法数据源"
        title-icon="el-icon-coin"
        :table-data="tableData"
        :loading="loading"
        :columns="tableColumns"
        :actions="tableActions"
        :search-form-config="searchFormTemp"
        :search-params="initParam"
        :pagination-data="initParam"
        :total="total"
        add-button-text="新增数据源"
        empty-title="暂无数据源"
        empty-description="点击上方新增数据源按钮开始创建"
        @search="normalSearch"
        @reset="normalResetQuery"
        @add="handleAdd"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        @action-click="handleAction"
      >
        <!-- 数据源编码 -->
        <template #bizCode="{ row }">
          <div class="code-cell">
            <div class="code-icon">
              <i class="el-icon-document-copy"></i>
            </div>
            <span class="code-text">{{ row.bizCode }}</span>
          </div>
        </template>
        <!-- 数据源名称 -->
        <template #name="{ row }">
          <div class="name-cell">
            <span class="name-text">{{ row.name }}</span>
          </div>
        </template>
        <!-- 来源类型 -->
        <template #sourceType="{ row }">
          <div class="type-cell">
            <!-- <el-tag size="medium" effect="plain">
              {{ row.sourceType | getDicItemName("sct.algo.source.status") }}
            </el-tag> -->
            <el-tag
              :class="getTypeTagClass(row.sourceType)"
              size="small"
            >
              <i :class="getTypeIcon(row.sourceType)"></i>
              {{ row.sourceType | getDicItemName("sct.algo.source.status") }}
            </el-tag>
          </div>
        </template>
        <!-- 同步入参 -->
        <template #fieldList="{ row }">
          <span v-if="row.fieldList">
            {{
              Array.isArray(row.fieldList)
                ? row.fieldList.join(",")
                : row.fieldList
            }}
          </span>
        </template>
        <!-- 备注 -->
        <template #remark="{ row }">
          <div class="remark-cell">
            <el-tooltip
              v-if="row.remark"
              :content="row.remark"
              :enterable="false"
              effect="dark"
              placement="top"
              popper-class="modern-tooltip"
            >
              <span class="remark-text">{{ row.remark }}</span>
            </el-tooltip>
            <span v-else class="no-remark">
              <i class="el-icon-info"></i>
              暂无备注
            </span>
          </div>
        </template>
        <!-- 创建时间 -->
        <template #createTime="{ row }">
          <div class="time-cell">
            <i class="el-icon-time"></i>
            <span>{{ row.createTime }}</span>
          </div>
        </template>
      </UniversalTable>
    </div>
    <!-- <Pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :pageData="initParam"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
    ></Pagination> -->

    <ConfirmDialog
      ref="confirmDialog"
      title="确认删除"
      message="删除后无法恢复，请确认是否删除该数据源？"
      icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
      @confirm="confirmUpdate"
    />

    <UniversalFormDialog
      v-model="showEditDialog"
      :form-data="editForm"
      :form-fields="formFields"
      :form-rules="editRules"
      :is-edit="editForm.isEdit"
      :loading="saveLoading"
      add-title="新增数据源"
      edit-title="编辑数据源"
      @confirm="handleSave"
      @cancel="handleCancel"
      @close="handleDialogClose"
    />
  </div>
</template>
<script>
import {
  getStrategymatch,
  copyStrategymatch,
  deleteStrategymatch
} from "@/api/strategyMatchManage/index.js";
import { getDicItemList } from "@/config/tool";
import { baseComponent } from "@/utils/common";
import { validate, validateAlls } from "@/config/validation";
import {
  algoSourcePage,
  algoSourceDel,
  algoSourceById
} from "@/api/template/index.js";
import UniversalTable from "@/components/layouts/UniversalTable.vue";
import UniversalFormDialog from "@/components/layouts/UniversalFormDialog.vue";
import ConfirmDialog from "@/components/layouts/ConfirmDialog.vue";
import { algoSourceSave } from "@/api/template/index.js";
export default {
  name: "matchSourceIndex",
  mixins: [baseComponent],
  components: {
    UniversalTable,
    UniversalFormDialog,
    ConfirmDialog
  },
  data() {
    return {
      toolListProps: {
        toolTitle: "数据源列表",
        toolList: [
          {
            name: "新增",
            btnCode: "",
            type: "add"
          }
        ]
      },
      searchFormTemp: [
        {
          label: "数据源编码",
          name: "bizCode",
          type: "input",
          placeholder: "请输入数据源编码"
        },
        {
          label: "数据源名称",
          name: "name",
          type: "input",
          placeholder: "请输入数据源名称"
        }
      ],
      tableData: [],
      initParam: {
        param: {
          bizCode: "",
          name: ""
        },
        pageSize: 10,
        pageNum: 1
      },
      total: 0,
      showPopup: false,
      showCopyPopup: false,
      sourceId: "",
      copyObj: {
        name: "",
        sourceId: ""
      },
      loading: false,
      tableColumns: [
        { prop: "bizCode", label: "数据源编码", width: 150, align: "center" },
        { prop: "name", label: "数据源名称", width: 250, align: "center" },
        { prop: "sourceType", label: "来源类型", width: 200, align: "center" },
        {
          prop: "fieldList",
          label: "同步入参",
          minWidth: 200,
          align: "center"
        },
        { prop: "remark", label: "备注", minWidth: 200, align: "center" },
        { prop: "createTime", label: "创建时间", width: 200, align: "center" }
      ],
      tableActions: [
        {
          key: "config",
          label: "配置数据源",
          icon: "el-icon-setting",
          class: "config-btn"
        },
        { key: "edit", label: "编辑", icon: "el-icon-edit", class: "edit-btn" },
        {
          key: "delete",
          label: "删除",
          icon: "el-icon-delete",
          class: "delete-btn"
        }
      ],
      showEditDialog: false,
      editForm: {
        isEdit: false,
        name: "",
        sourceId: "",
        bizCode: "",
        sourceType: "",
        remark: "",
        sourceValue: "",
        checkDataApi: ""
      },
      formFields: [
        {
          label: "数据源编码",
          name: "bizCode",
          type: "input",
          placeholder: "请输入数据源编码",
          prop: "bizCode"
        },
        {
          label: "数据源名称",
          name: "name",
          type: "input",
          placeholder: "请输入数据源名称",
          prop: "name"
        },
        {
          label: "来源类型",
          name: "sourceType",
          type: "select",
          options: [],
          placeholder: "请选择来源类型",
          prop: "sourceType"
        },
        {
          label: "接口地址",
          name: "sourceValue",
          type: "input",
          placeholder: "请输入接口地址",
          prop: "sourceValue"
        },
        {
          label: "数据核对API",
          name: "checkDataApi",
          type: "input",
          placeholder: "请输入数据核对API",
          prop: "checkDataApi"
        },
        {
          label: "备注",
          name: "remark",
          type: "textarea",
          placeholder: "请输入备注",
          prop: "remark"
        }
      ],
      editRules: {
        name: [
          { required: true, message: "请输入数据源名称", trigger: "blur" },
          {
            min: 1,
            max: 100,
            message: "长度在 1 到 100 个字符",
            trigger: "blur"
          }
        ],
        sourceType: [
          { required: true, message: "请选择来源类型", trigger: "change" }
        ],
        sourceValue: [
          { required: true, message: "请输入接口地址", trigger: "blur" }
        ],
        checkDataApi: [
          { required: true, message: "请输入数据核对API", trigger: "blur" }
        ],
        bizCode: [
          { required: true, message: "请输入指标数据源编码", trigger: "blur" }
        ]
      },
      saveLoading: false,
      sourceTypeOptions: []
    };
  },
  created() {
    this.initData();
  },
  methods: {
    //初始化数据
    async initData() {
      await this.getDictList();
      this.initList();
    },
    async initList() {
      this.getQueryParam(this.initParam, "strategyMatchParam");
      let res = await algoSourcePage(this.initParam);
      if (!res) {
        return;
      }
      if (res.list && res.list.length > 0) {
        this.tableData = res.list;
        this.initParam.pageNum = res.pageNum;
        this.initParam.pageSize = res.pageSize;
        this.total = res.total ? Number(res.total) : 0;
      } else {
        this.tableData = [];
      }
    },
    handleTool(item) {
      if (item.type == "add") {
        this.$router.push({
          name: "sourceManageUpdate",
          query: {
            type: "add"
          }
        });
      }
    },
    async handleUpdate(row) {
      // 先关闭弹窗
      this.resetEditForm();
      this.showEditDialog = false;

      // 通过ID获取详情
      const res = await algoSourceById({ sourceId: row.sourceId });
      if (!res) {
        this.$message.error("获取数据源详情失败");
        return;
      }

      this.$nextTick(() => {
        this.editForm = {
          isEdit: true,
          name: res.name,
          sourceId: res.sourceId,
          bizCode: res.bizCode,
          sourceType: res.sourceType,
          remark: res.remark,
          sourceValue: res.sourceValue || "",
          checkDataApi: res.checkDataApi || ""
        };

        this.$nextTick(() => {
          this.showEditDialog = true;
        });
      });
    },
    handleCopy(row) {
      this.copyObj.sourceId = row.sourceId;
      this.showCopyPopup = true;
    },
    cancle() {
      this.copyObj = {
        sourceId: "",
        name: ""
      };
      this.showCopyPopup = false;
    },
    async confirmCopy() {
      if (!validateAlls(this.$refs.copyForm)) {
        return;
      }
      let res = await copyStrategymatch(this.copyObj);
      if (!res) {
        return;
      }
      this.initList();
      this.showCopyPopup = false;
    },
    // 获取类型标签类型
    getFieldTypeTagType(type) {
      const typeMap = {
        1: "success",
        2: "warning",
        3: "info"
      };
      return typeMap[type] || "info";
    },

    // 获取类型标签样式类
    getTypeTagClass(type) {
      const classMap = {
        1: "current-tag",
        2: "draft-tag",
        3: "history-tag"
      };
      return classMap[type] || "history-tag";
    },

    // 获取类型图标
    getTypeIcon(type) {
      const iconMap = {
        1: "el-icon-check",
        2: "el-icon-edit-outline",
        3: "el-icon-time"
      };
      return iconMap[type] || "el-icon-time";
    },

    handleDel(row) {
      this.sourceId = row.sourceId;
      this.$refs.confirmDialog.show();
    },
    async confirmUpdate() {
      let res = await algoSourceDel({ sourceId: this.sourceId });
      if (!res) {
        return;
      }
      this.initList();
      this.$refs.confirmDialog.hide();
    },
    handleView(row) {
      this.setCacheArr("del");
      this.handleRoute(row, "sourceMatchFieldConfigView");
    },
    handleSourceConfig(row) {
      this.handleRoute(row, "sourceInfo");
      this.$router.push({
        name: "sourceInfo",
        query: {
          sourceId: row.sourceId
        }
      });
    },
    handleRoute(row, name) {
      this.setQueryParam(this.initParam, "strategyMatchParam");
      this.$store.commit("strategyMatchManage/setStrategyMatchObj", row);
      this.$router.push({
        name: name,
        query: {
          sourceId: row.sourceId
        }
      });
    },
    setCacheArr(type) {
      let arr = [
        "baseInfoConfig",
        "strategyExecutedRecordList",
        "strategyMatchIndicatorListView",
        "sourceAssignFieldList",
        "sourceMatchFieldList",
        "priorityConfigList"
      ];
      this._.each(arr, item => {
        this.$store.commit("layoutStore/setCacheArr", {
          status: type,
          routeName: item
        });
      });
    },
    // 获取字典/标签/分组
    async getDictList() {
      let res = await getDicItemList("sct.match.rule");
      this.setSearchFormTemp("matchRule", res);

      res = await getDicItemList("sct.algo.source.status");
      console.log("来源类型字典:", res);

      // 为来源类型设置选项
      // this.sourceTypeOptions = res || [];
      this.formFields.find(field => field.name === "sourceType").options =
        res || [];
      const options = (res || []).map(item => ({
        label: item.label || item.dicItemName || item.text,
        value: item.value || item.dicItemCode || item.id
      }));
      this.sourceTypeOptions = options;
      this.formFields.find(
        field => field.name === "sourceType"
      ).options = options;
      res = await getDicItemList("sct.source.config.status");
      this.setSearchFormTemp("sourceConfigStatus", res);
      this.setSearchFormTemp("indicatorConfigStatus", res);

      res = await getDicItemList("gen.yesorno.num");
      this.setSearchFormTemp("status", res);
    },
    handleAction(actionData) {
      const { action, row } = actionData;
      switch (action) {
        case "config":
          this.handleSourceConfig(row);
          break;
        case "edit":
          this.handleUpdate(row);
          break;
        case "delete":
          this.handleDel(row);
          break;
        default:
          break;
      }
    },
    // 重置表单
    resetEditForm() {
      this.editForm = {
        isEdit: false,
        name: "",
        sourceId: "",
        bizCode: "",
        sourceType: "",
        remark: "",
        sourceValue: "",
        checkDataApi: ""
      };
      // 使用nextTick确保DOM更新后再清除验证状态
      this.$nextTick(() => {
        if (this.$refs.editFormRef) {
          this.$refs.editFormRef.clearValidate();
        }
      });
    },
    handleAdd() {
      this.resetEditForm();
      // this.editForm = {
      //   isEdit: false,
      //   name: "",
      //   sourceId: "",
      //   bizCode: "",
      //   sourceType: "",
      //   fieldList: "",
      //   remark: ""
      // };
      this.editForm.isEdit = false;
      this.showEditDialog = true;
    },
    // ... existing code ...
    handleSave(data) {
      // data 结构为 { formData, isEdit }
      console.log("收到的参数", data);
      this.handleSaveStrategy(data);
    },
    // ... existing code ...
    // // 保存策略
    async handleSaveStrategy(data) {
      try {
        this.saveLoading = true;

        const apiData = {
          bizCode: data.formData.bizCode,
          name: data.formData.name,
          remark: data.formData.remark,
          sourceType: data.formData.sourceType,
          sourceValue: data.formData.sourceValue,
          checkDataApi: data.formData.checkDataApi
        };

        if (data.isEdit) {
          apiData.sourceId = data.formData.sourceId;
        }
        // 调用后台API
        let res = data.isEdit
          ? await algoSourceSave(apiData)
          : await algoSourceSave(apiData);

        this.$message.success(data.isEdit ? "更新成功" : "新增成功");
        this.showEditDialog = false;
        this.initList(); // 刷新列表
      } catch (error) {
        console.error("保存失败:", error);
      } finally {
        this.saveLoading = false;
      }
    },
    handleCancel() {
      // 处理取消逻辑
      console.log("Form cancelled");
    },
    handleDialogClose() {
      // 处理对话框关闭逻辑
      console.log("Form dialog closed");
    }
  }
};
</script>

<style lang="less" scoped>
.index-manage-container {
  .code-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    .code-icon {
      width: 24px;
      height: 24px;
      background: rgba(215, 162, 86, 0.1);
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      i {
        color: #d7a256;
        font-size: 12px;
      }
    }
    .code-text {
      font-family: "Courier New", monospace;
      font-weight: 500;
      color: #2c3e50;
      font-size: 13px;
    }
  }
  .name-cell {
    .name-text {
      font-weight: 500;
      color: #2c3e50;
    }
  }
  .type-cell {
    // .el-tag {
    //   background: rgba(64, 158, 255, 0.1);
    //   color: #409eff;
    //   border: 1px solid rgba(64, 158, 255, 0.3);
    //   i {
    //     margin-right: 4px;
    //   }
    // }
    // 类型标签样式
    .current-tag {
      background: rgba(64, 158, 255, 0.1);
      color: #409eff;
      border: 1px solid rgba(64, 158, 255, 0.3);

      i {
        margin-right: 4px;
      }
    }

    .draft-tag {
      background: rgba(230, 162, 60, 0.1);
      color: #e6a23c;
      border: 1px solid rgba(230, 162, 60, 0.3);

      i {
        margin-right: 4px;
      }
    }

    .history-tag {
      background: rgba(144, 147, 153, 0.1);
      color: #909399;
      border: 1px solid rgba(144, 147, 153, 0.3);

      i {
        margin-right: 4px;
      }
    }
  }

  .remark-cell {
    .remark-text {
      color: #606266;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
    }

    .no-remark {
      color: #c0c4cc;
      font-style: italic;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;

      i {
        font-size: 12px;
      }
    }
  }
  .time-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #606266;
    font-size: 13px;
    i {
      color: #d7a256;
      font-size: 14px;
    }
  }
}
/deep/ .modern-tooltip {
  background: #2c3e50 !important;
  color: white !important;
  border-radius: 8px !important;
  padding: 8px 12px !important;
  font-size: 12px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}
/deep/ .el-loading-mask {
  background-color: rgba(251, 246, 238, 0.8) !important;
}
</style>
