<template>
  <div class="strategy-match-update">
    <el-breadcrumb separator-class="el-icon-arrow-right" class="dt-bread">
      <el-breadcrumb-item :to="{ name: 'allocationlist' }"
        >数据配置</el-breadcrumb-item
      >
      <el-breadcrumb-item
        :style="{ color: $store.state.layoutStore.themeObj.color }"
        >{{ getText }}</el-breadcrumb-item
      >
    </el-breadcrumb>
    <TableToolTemp :toolListProps="titleListPros"></TableToolTemp>
    <el-form
      :model="updateForm"
      label-width="160px"
      :rules="rules"
      ref="updateForm"
      label-position="left"
      class="pdl-20"
    >
      <el-form-item label="参数名称" prop="name">
        <el-input
          v-model="updateForm.name"
          class="dt-input-width"
          placeholder="请输入参数名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="参数字段" prop="fieldName">
        <el-input
          v-model="updateForm.fieldName"
          class="dt-input-width"
          placeholder="请输入参数字段"
        ></el-input>
      </el-form-item>
      <el-form-item label="参数类型" prop="fieldType">
        <el-select
          v-model="updateForm.fieldType"
          placeholder="请选择"
          class="dt-select"
        >
          <el-option
            v-for="(item, index) in commonData.fieldType"
            :key="index"
            :label="item.dicItemName"
            :value="item.dicItemCode"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="updateForm.remark"
          class="dt-input-width"
          placeholder="请输入备注"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submit">保存</el-button>
        <el-button
          @click="goBack"
          type="primary"
          plain
          :style="{ color: $store.state.layoutStore.themeObj.color }"
          >取消</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import { validate, validateAlls } from "@/config/validation";
import { getDicItemList } from "@/config/tool.js";
import { algoSourceParamAdd } from "@/api/template/index.js";

export default {
  name: "paramUpdate",
  data() {
    return {
      titleListPros: {
        toolTitle: ""
      },
      rules: {
        name: [
          {
            required: true,
            min: 1,
            max: 100,
            validator: validate,
            trigger: "blur"
          }
        ],
        fieldType: [{ required: true, validator: validate, trigger: "blur" }],
        fieldName: [{ required: true, validator: validate, trigger: "blur" }],
        // remark: [{ required: true, validator: validate, trigger: "blur" }]
      },
      updateForm: {
        name: "",
        fieldType: "",
        remark: "",
        fieldName: ""
      },
      commonData: {
        fieldType: []
      },
      baseDisabled: false
    };
  },
  components: {
    TableToolTemp
  },
  computed: {
    getText() {
      if (this.type == "add") {
        return "新增参数字段";
      }
      if (this.type == "edit") {
        return "编辑参数字段";
      }
    }
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      this.type = this.$route.query.type;
      this.titleListPros.toolTitle = this.getText;
      await this.getDictList();
      if (this.type == "edit") {
        this.baseDisabled = true;
        this.initData();
      }
    },
    async initData() {
      let res = await getAssign({
        fbId: this.$route.query.fbId
      });
      if (!res) {
        return;
      }
      this.updateForm = res;
      this.updateForm.listFieldDicBaseSave = res.listSourceFieldDic || [];
      delete this.updateForm.listSourceFieldDic;
    },
    // 获取字典/
    async getDictList() {
      this.commonData.fieldType = await getDicItemList(
        "sct.algo.source.fieldType"
      );
    },
    goBack() {
      this.$router.go(-1);
    },
    async submit() {
      if (!validateAlls(this.$refs.updateForm)) {
        return;
      }
      let res;
      res = await algoSourceParamAdd(this.updateForm);
      if (!res) {
        return;
      }
      this.goBack();
    },
    handleDel(index, row) {
      console.log(index, row);
      this.updateForm.listFieldDicBaseSave.splice(index, 1);
    }
  }
};
</script>

<style lang="less">
.strategy-match-update {
  .table-tool {
    padding-bottom: 10px;
  }
  .draggable-wrap {
    display: inline-block;
  }
  .tags {
    margin-right: 6px;
  }
  .zidian-box {
    margin-bottom: 20px;
  }
}
</style>
