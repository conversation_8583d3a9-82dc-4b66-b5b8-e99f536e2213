<template>
  <div class="process-info">
    <SecondaryPageHeader
          title="配置数据源"
          subtitle="管理和配数据源"
          icon="el-icon-files"
          back-button-text="返回数据源管理"
          back-route="indexManage"
          :breadcrumb-items="breadcrumbItems"
          @back="goBack"
        />
    <!-- <TableToolTemp :tool-list-props="{ toolTitle: '基本信息' }" /> -->
    <!-- Tab页签切换 -->
    <el-tabs v-model="activeTab" style="margin-top: 20px; padding-left: 32px;
  padding-right: 32px;">
      <el-tab-pane label="基本信息" name="base">
        <div class="base-info-card">
          <!-- <div class="base-info-title">数据源基本信息</div> -->
          <div class="base-info-content">
            <div class="info-item" v-for="item in baseInfoList" :key="item.label">
              <span class="info-label">{{ item.label }}：</span>
              <span class="info-value" :title="item.value">{{ item.value || '-' }}</span>
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="数据字段列表" name="field">
        <div class="field-table-toolbar" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; ">
          <div style="display: flex; align-items: center; flex-wrap: wrap;">
            <label class="search-label">字段名称：</label>
            <el-input
              v-model="initParam.name"
              placeholder="字段名称"
              size="small"
              style="width: 180px; margin-right: 16px;"
              clearable
            />
            <label class="search-label">属性名：</label>
            <el-input
              v-model="initParam.filedName"
              placeholder="属性名"
              size="small"
              style="width: 180px; margin-right: 16px;"
              clearable
            />
            <el-button type="primary" size="small" @click="normalSearch(initParam)" style="margin-right: 8px;">搜索</el-button>
            <el-button size="small" @click="normalResetQuery">重置</el-button>
          </div>
          <div style="flex-shrink: 0;">
            <el-button
              icon="el-icon-plus"
              size="small"
              class="primary-btn"
              @click="addFiled"
              style="margin-left: 12px;"
            >新增字段</el-button>
          </div>
        </div>
        <div class="table-section">
          <div class="table-wrapper">
            <el-table
              :data="FildList"
              stripe
              v-hover
              class="modern-table1"
              style="width: 100%"
              v-loading="fieldSaveLoading"
              empty-text="暂无字段"
            >
              <el-table-column align="center" prop="name" label="字段名称" width="150">
                <template slot-scope="scope">
                  <span class="name-cell">{{ scope.row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="fieldName" label="属性名" width="150">
                <template slot-scope="scope">
                  <span class="fieldname-cell">{{ scope.row.fieldName }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="fieldType" label="字段类型" width="120">
                <template slot-scope="scope">
                  <el-tag
                    :type="getFieldTypeTagType(scope.row.fieldType)"
                    :class="getFieldTypeTagClass(scope.row.fieldType)"
                    size="small"
                  >
                    <i :class="getFieldTypeIcon(scope.row.fieldType)"></i>
                    {{ getFieldTypeText(scope.row.fieldType) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="isIndex" label="是否创建索引" width="120">
                <template slot-scope="scope">
                  <span>{{ scope.row.isIndex == "1" ? "是" : "否" }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="isParam" label="是否是参数" width="120">
                <template slot-scope="scope">
                  <span>{{ scope.row.isParam == "1" ? "是" : "否" }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="isCheck" label="是否核对" width="120">
                <template slot-scope="scope">
                  <span>{{ scope.row.isCheck == "1" ? "是" : "否" }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="paramType" label="参数类型" width="120">
                <template slot-scope="scope">
                  <span v-if="scope.row.paramType == '1'">单个</span>
                  <span v-else-if="scope.row.paramType == '2'">多个</span>
                  <span v-else-if="scope.row.paramType == '3'">范围</span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="fieldLength" label="字符长度" width="100">
                <template slot-scope="scope">
                  <span>{{ scope.row.fieldLength }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="createTime" label="创建时间" width="180">
                <template slot-scope="scope">
                  <span class="time-cell"><i class="el-icon-time"></i>{{ scope.row.createTime }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="updateTime" label="修改时间" width="180">
                <template slot-scope="scope">
                  <span class="time-cell"><i class="el-icon-time"></i>{{ scope.row.updateTime }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="updateId" label="最后修改人" width="120">
                <template slot-scope="scope">
                  <div class="user-cell">
                    <div class="user-avatar">
                      <i class="el-icon-user"></i>
                    </div>
                    <span>{{ scope.row.updateId | getNickName("scope.row.updateId") }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="remark" label="备注" min-width="120">
                <template slot-scope="scope">
                  <span class="remark-cell">{{ scope.row.remark }}</span>
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                header-align="center"
                label="操作"
                width="180"
                fixed="right"
              >
                <template slot-scope="scope">
                  <div class="action-buttons">
                    <el-button
                      size="mini"
                      plain
                      @click="editFiled(scope.row)"
                      class="action-btn edit-btn"
                    >
                      <i class="el-icon-edit"></i>
                      编辑
                    </el-button>
                    <el-button
                      size="mini"
                      plain
                      @click="handleDel(scope.row)"
                      class="action-btn delete-btn"
                    >
                      <i class="el-icon-delete"></i>
                      删除
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <!-- 空状态 -->
            <div v-if="!fieldSaveLoading && FildList.length === 0" class="empty-state">
              <div class="empty-content">
                <div class="empty-icon">
                  <i class="el-icon-files"></i>
                </div>
                <h3>暂无字段</h3>
                <p>点击右上角'新增字段'按钮开始创建</p>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
      <!-- 删除确认弹窗 -->
      <ConfirmDialog
      ref="confirmDialog"
      title="确认删除"
      message="删除后无法恢复，请确认是否删除该版本？"
      icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
      @confirm="confirmUpdate"
    />
    <UniversalFormDialog
      v-model="showEditDialog"
      :form-data="editForm"
      :form-fields="fieldFormFields.filter(f => f.visible !== false)"
      :form-rules="fieldFormRules"
      :is-edit="editForm.isEdit"
      :loading="fieldSaveLoading"
      add-title="新增字段"
      edit-title="编辑字段"
      @confirm="handleFieldSave"
      @cancel="handleFieldCancel"
      @close="handleFieldDialogClose"
    />
  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
// import { validate, validateAlls } from "@/utils/validation";
import { getDicItemList } from "@/config/tool";
import { baseComponent } from "@/utils/common";
import {
  algoSourceParamList,
  algoSourceFieldList,
  algoSourceById,
  algoSourceFieldDel,
  algoSourceFieldAdd,
  algoSourceFieldUpdate
} from "@/api/template/index.js";
import { Loading } from "element-ui";
import UniversalFormDialog from "@/components/layouts/UniversalFormDialog.vue";
import SecondaryPageHeader from "@/components/layouts/SecondaryPageHeader.vue";
import UniversalTable from "@/components/layouts/UniversalTable.vue";
import ConfirmDialog from "@/components/layouts/ConfirmDialog.vue";
export default {
  name: "sourceInfo",
  mixins: [baseComponent],
  components: {
    TableToolTemp,
    UniversalFormDialog,
    SecondaryPageHeader,
    UniversalTable,
    ConfirmDialog
  },
  data() {
    return {
      titleListPros: {
        toolTitle: "参数列表",
        toolList: [
          {
            name: "新增",
            btnCode: "",
            type: "add"
          }
        ]
      },
      titleListPros1: {
        toolTitle: "数据字段列表",
        toolList: [
          {
            name: "新增",
            btnCode: "",
            type: "add"
          }
        ]
      },
      sourceTypeList: [],
      searchFormTemp: [
        {
          label: "字段名称",
          name: "name",
          placeholder: "请输入字段名称",
          type: "input"
        },
        {
          label: "属性名",
          name: "fieldName",
          placeholder: "请输入属性名",
          type: "input"
        }
      ],
      breadcrumbItems: [
        {
          text: "数据源管理",
          icon: "el-icon-back",
          to: { name: "matchSourceIndex" }
        },
        {
          text: "配置数据源",
          icon: "el-icon-files"
        }
      ],
      initParam: {
        // pageNum: 1,
        // pageSize: 10,
        // param: {
        sourceId: "",
        name: "",
        filedName: ""
        // }
      },
      templateList: [],
      showPopup: false,
      baseForm: {},
      addForm: {
        name: "",
        remark: "",
        sourceValue: "",
        sourceType: "",
        bizCode: "",
        checkDataApi:""
      },
      selectList: [],
      ParamData: [],
      FildList: [],
      sourceId: "",
      fieldId: "",
      showEditDialog: false,
      editForm : {
        name: "",
        fieldType: "",
        remark: "",
        fieldName: "",
        isIndex: "",
        isParam: "",
        isCheck: "",
        paramType: "",
        fieldLength: "",
        isEdit: false
      },
      fieldFormFields: [
        { label: "字段名称", name: "name", type: "input", placeholder: "请输入字段名称", prop: 'name' },
        { label: "属性名", name: "fieldName", type: "input", placeholder: "请输入属性名", prop: 'fieldName' },
        { label: "字段类型", name: "fieldType", type: "select", options: [], placeholder: "请选择字段类型", prop: 'fieldType' },
        { label: "是否创建索引", name: "isIndex", type: "select", options: [{label: "是", value: "1"}, {label: "否", value: "0"}], placeholder: "请选择", prop: 'isIndex' },
        { label: "是否是参数", name: "isParam", type: "select", options: [{label: "是", value: "1"}, {label: "否", value: "0"}], placeholder: "请选择", prop: 'isParam' },
        { label: "是否核对", name: "isCheck", type: "select", options: [{label: "是", value: "1"}, {label: "否", value: "0"}], placeholder: "请选择", prop: 'isCheck' },
        { 
          label: "参数类型", 
          name: "paramType", 
          type: "select", 
          options: [
            {label: "单个", value: "1"},
            {label: "多个", value: "2"},
            {label: "范围", value: "3"}
          ], 
          placeholder: "请选择参数类型", 
          prop: 'paramType',
          visible: true
        },
        { label: "字符长度", name: "fieldLength", type: "input", placeholder: "请输入字符长度", prop: 'fieldLength' },
        { label: "备注", name: "remark", type: "input", placeholder: "请输入备注", prop: 'remark' }
      ],
      fieldFormRules: {
        name: [{ required: true, message: '请输入字段名称', trigger: 'blur' }],
        fieldName: [{ required: true, message: '请输入属性名', trigger: 'blur' }],
        fieldType: [{ required: true, message: '请选择字段类型', trigger: 'blur' }]
      },
      fieldSaveLoading: false,
      fieldTableColumns: [
        { prop: 'name', label: '字段名称', width: 150, align: 'center' },
        { prop: 'fieldName', label: '属性名', width: 150, align: 'center' },
        { prop: 'fieldType', label: '字段类型', width: 120, align: 'center' },
        { prop: 'isIndex', label: '是否创建索引', width: 120, align: 'center' },
        { prop: 'isParam', label: '是否是参数', width: 120, align: 'center' },
        { prop: 'isCheck', label: '是否核对', width: 120, align: 'center' },
        { prop: 'paramType', label: '参数类型', width: 120, align: 'center' },
        { prop: 'fieldLength', label: '字符长度', width: 100, align: 'center' },
        { prop: 'createTime', label: '创建时间', width: 180, align: 'center' },
        { prop: 'updateTime', label: '修改时间', width: 180, align: 'center' },
        { prop: 'updateId', label: '最后修改人', width: 120, align: 'center' },
        { prop: 'remark', label: '备注', minWidth: 120, align: 'center' }
      ],
      fieldTableActions: [
        { key: 'edit', label: '编辑', icon: 'el-icon-edit', class: 'edit-btn' },
        { key: 'delete', label: '删除', icon: 'el-icon-delete', class: 'delete-btn' }
      ],
      activeTab: 'base', // 新增：当前激活tab
    };
  },
  computed: {
    currentNode() {
      return this.$store.state.flowManage.currentNode;
    },
    baseInfoList() {
      return [
        { label: '数据源编码', value: this.addForm.bizCode },
        { label: '数据源名称', value: this.addForm.name },
        { label: '来源类型', value: this.addForm.sourceType },
        { label: '接口地址', value: this.addForm.sourceValue },
        { label: '数据核对API', value: this.addForm.checkDataApi },
        { label: '备注', value: this.addForm.remark }
      ];
    }
  },
  created() {
    this.init();
    this.getDictList();
    this.initSourceById();
  },
  methods: {
    // 返回指标管理
    goBack() {
      this.$router.push({ name: 'matchSourceIndex' });
    },
    addParam(item) {
      if (item.type == "add") {
        this.$router.push({
          name: "paramUpdate",
          query: {
            type: "add",
            sourceId: this.$route.query.sourceId
          }
        });
      }
    },
    editParam(item) {
      if (item.type == "add") {
        this.$router.push({
          name: "sourceManageUpdate",
          query: {
            type: "add",
            sourceId: this.$route.query.sourceId
          }
        });
      }
    },
    addFiled() {
      this.editForm  = {
        name: "",
        fieldType: "",
        remark: "",
        fieldName: "",
        isIndex: "",
        isParam: "",
        isCheck: "",
        paramType: "",
        fieldLength: "",
        isEdit: false
      };
      this.showEditDialog = true;
    },
    editFiled(row) {
      this.editForm  = { ...row, isEdit: true };
      this.showEditDialog = true;
    },
    async handleFieldSave({ formData, isEdit }) {
      // 组装所有字段
      const apiData = {
        ...formData,
        sourceId: this.sourceId, // 确保有sourceId
        fieldId: formData.fieldId // 编辑时需要
      };
      this.fieldSaveLoading = true;
      let res;
      if (isEdit) {
        res = await algoSourceFieldUpdate(apiData);
      } else {
        res = await algoSourceFieldAdd(apiData);
      }
      if (res) {
        this.$message.success(isEdit ? '修改成功' : '新增成功');
        this.showEditDialog = false;
        this.initFildList();
      }
      this.fieldSaveLoading = false;
    },
    handleFieldCancel() {
      this.showEditDialog = false;
    },
    handleFieldDialogClose() {
      this.showEditDialog = false;
    },
    async addFieldApi(form) {
      return await algoSourceParamAdd(form);
    },
    async updateFieldApi(form) {
      return await algoSourceFieldUpdate(form);
    },
    normalSearch(data) {
      this.initParam = { ...this.initParam, ...data };
      this.initFildList();
    },
    // 重置表单
    resetEditForm() {
      this.editForm = {
        name: "",
        fieldType: "",
        remark: "",
        fieldName: "",
        isEdit:false
      };
      // 使用nextTick确保DOM更新后再清除验证状态
      this.$nextTick(() => {
        if (this.$refs.editFormRef) {
          this.$refs.editFormRef.clearValidate();
        }
      });
    },
    normalResetQuery() {
      this.initParam = {
        sourceId: this.$route.query.sourceId,
        name: "",
        filedName: ""
      };
      this.initFildList();
    },
    // 返回
    back() {
      this.$router.back(-1);
    },
    async init() {
      this.sourceId = this.$route.query.sourceId;
      const loadingInstance = Loading.service({
        fullscreen: true,
        lock: true,
        text: "加载中...",
        target: document.getElementsByTagName("body")[0]
      });
      this.initFildList();
      loadingInstance.close();
    },
    async initTabList() {
      let res = await algoSourceParamList(this.initParam);
      if (!res) {
        return;
      }
      this.ParamData = res;
    },
    async initFildList() {
      this.initParam.sourceId = this.$route.query.sourceId;
      let res = await algoSourceFieldList(this.initParam);
      if (!res) {
        this.FildList = [];
        return;
      }
      if (res && res.length > 0) {
        this.FildList = res;
      } else {
        this.FildList = [];
      }
    },
    async initSourceById() {
      let res = await algoSourceById({ sourceId: this.sourceId });
      console.log(res);
      if (!res) {
        return;
      }
      this.addForm.name = res.name;
      this.addForm.remark = res.remark;
      this.addForm.sourceValue = res.sourceValue;
      this.addForm.bizCode = res.bizCode;
      this.addForm.checkDataApi = res.checkDataApi;
      this.addForm.sourceType = await this.getSourceType(
        this.sourceTypeList,
        res.sourceType
      );
    },
    async getDictList() {
      this.sourceTypeList = await getDicItemList("sct.algo.source.status");
      // 设置字段类型下拉选项，改为用 sct.algo.source.fieldType
      const fieldTypeOptions = await getDicItemList("sct.algo.source.fieldType");
      this.fieldFormFields.find(f => f.name === 'fieldType').options = (fieldTypeOptions || []).map(item => ({
        label: item.dicItemName,
        value: item.dicItemCode
      }));
    },
    getSourceType(dicList, val) {
      if (dicList) {
        // eslint-disable-next-line no-undef
        let dicItemObj = _.find(dicList, el => {
          return el.dicItemCode == val;
        });
        if (dicItemObj) {
          val = dicItemObj.dicItemName;
        }
      }
      // this.addForm.sourceType = val;
      return val;
    },
    handleDel(row) {
      this.fieldId = row.fieldId;
      this.$refs.confirmDialog.show();
    },
    async confirmUpdate() {
      let res = await algoSourceFieldDel({ fieldId: this.fieldId });
      if (!res) {
        return;
      }
      this.init();
      this.$refs.confirmDialog.hide();
    },
    submit() {
      const obj = this.validateInput();
      console.log(obj);
      if (!obj.pass) {
        this.$message.error(obj.data);
        return;
      }
      this.$router.push({
        path: "/dacS/processNodes",
        query: {
          flowId: this.$route.query.flowId,
          handler: "save"
        }
      });
    },
    handleFieldAction({ action, row }) {
      if (action === 'edit') {
        this.editFiled(row);
      } else if (action === 'delete') {
        this.handleDel(row);
      }
    },
    getFieldTypeTagType(fieldType) {
      // 你可以根据实际字典项调整
      const typeMap = {
        'varchar': 'success', // 例如：字符串
        'int': 'warning', // 例如：数字
        'double': 'info',   
        'datetime': 'danger', // 例如：日期
        'date':'primary'
        // ... 其它类型 ...
      };
      return typeMap[fieldType] || 'info';
    },
    getFieldTypeTagClass(fieldType) {
      const classMap = {
        'varchar': 'current-tag',
        'int': 'draft-tag',
        'double': 'history-tag',
        'datetime': 'json-tag',
        'date': 'boolean-tag'
        // ... 其它类型 ...
      };
      return classMap[fieldType] || 'history-tag';
    },
    getFieldTypeIcon(fieldType) {
      const iconMap = {
        'varchar': 'el-icon-document',
        'int': 'el-icon-s-operation',
        'double': 'el-icon-s-data',
        'date': 'el-icon-date',
        'datetime': 'el-icon-date',
        // ... 其它类型 ...
      };
      return iconMap[fieldType] || 'el-icon-document';
    },
    getFieldTypeText(fieldType) {
      // 直接用字典项中文名
      const options = (this.fieldFormFields.find(f => f.name === 'fieldType') || {}).options || [];
      const item = options.find(opt => opt.value == fieldType);
      return item ? item.label : fieldType;
    },
  },
  watch: {
    'editForm.isParam'(val) {
      // 监听是否是参数，动态控制参数类型显示
      const paramTypeField = this.fieldFormFields.find(f => f.name === 'paramType');
      if (paramTypeField) {
        paramTypeField.visible = val === '1';
        // 如果隐藏，清空参数类型
        if (val !== '1') {
          this.editForm.paramType = '';
        }
      }
    }
  }
};
</script>

<style lang="less" scoped>
.back-btn1 {
  margin-top: 20px;
  margin-bottom: 20px;
  // margin-left:20px;
}

.field-table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // margin-bottom: 24px;
  // margin-top: 24px;
  padding: 24px 0;
  padding-top:16px;
    // border-top: 1px solid #f7ecdd;
    border-bottom: 1px solid #f7ecdd;
}

// 表格区域
.el-table thead{
  color: #2c3e50;
  font-weight: 600;
  font-size: 14px;
}
.table-section {
        .table-wrapper {
          .modern-table1 {
            /deep/ .el-table__header-wrapper {
              .el-table__header {
                th {
                  color: #2c3e50;
                  font-weight: 600;
                  font-size: 14px;
                  border-bottom: 1px solid #ebeef5;
                }
              }
            }

            /deep/ .el-table__body-wrapper {
              .el-table__row {
                transition: all 0.3s ease;

                &:hover {
                  background: rgba(215, 162, 86, 0.05) !important;
                  transform: translateY(-1px);
                  box-shadow: 0 2px 8px rgba(215, 162, 86, 0.1);
                }

                td {
                  border-bottom: 1px solid #f7ecdd;
                }
              }
            }

            /deep/ .el-table--striped .el-table__body tr.el-table__row--striped td {
              background: #fefdfb;
            }
          }
          
          // 类型标签样式
          .current-tag {
            background: rgba(103, 194, 58, 0.1);
            color: #67c23a;
            border: 1px solid rgba(103, 194, 58, 0.3);

            i {
              margin-right: 4px;
            }
          }

          .draft-tag {
            background: rgba(230, 162, 60, 0.1);
            color: #e6a23c;
            border: 1px solid rgba(230, 162, 60, 0.3);

            i {
              margin-right: 4px;
            }
          }

          .history-tag {
            background: rgba(144, 147, 153, 0.1);
            color: #909399;
            border: 1px solid rgba(144, 147, 153, 0.3);

            i {
              margin-right: 4px;
            }
          }
          .json-tag {
              background: rgba(64, 158, 255, 0.1);
              color: #409eff;
              border: 1px solid rgba(64, 158, 255, 0.3);

              i {
                margin-right: 4px;
              }
            }
            .boolean-tag {
              background: rgba(245, 108, 108, 0.1);
              color: #f56c6c;
              border: 1px solid rgba(245, 108, 108, 0.3);

              i {
                margin-right: 4px;
              }
            }

          // 表格单元格样式
          .version-cell {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;

            .version-icon {
              width: 24px;
              height: 24px;
              background: rgba(215, 162, 86, 0.1);
              border-radius: 6px;
              display: flex;
              align-items: center;
              justify-content: center;

              i {
                color: #D7A256;
                font-size: 12px;
              }
            }
            
            .name-cell {
              font-weight: 500;
              color: #2c3e50;
            }
            .type-cell {
              background: rgba(64, 158, 255, 0.1);
              color: #409eff;
              border: 1px solid rgba(64, 158, 255, 0.3);
            }
            .remark-cell {
              display: block;
              max-width: 180px;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
              color: #2c3e50;
            }
            .version-text {
              font-family: 'Courier New', monospace;
              font-weight: 500;
              color: #2c3e50;
              font-size: 13px;
            }
          }

          .time-cell {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            color: #7f8c8d;
            font-size: 13px;

            i {
              color: #c0c4cc;
            }
          }

          .user-cell {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;

            .user-avatar {
              width: 20px;
              height: 20px;
              background: rgba(215, 162, 86, 0.1);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;

              i {
                color: #D7A256;
                font-size: 10px;
              }
            }
          }

          .remark-cell {
            .remark-text {
  display: block;
              max-width: 180px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
              color: #2c3e50;
            }

            .empty-remark {
              color: #c0c4cc;
              font-style: italic;
              font-size: 12px;
            }
          }

          // 类型标签样式
          .current-tag {
            background: rgba(103, 194, 58, 0.1);
            color: #67c23a;
            border: 1px solid rgba(103, 194, 58, 0.3);

            i {
              margin-right: 4px;
            }
          }

          .draft-tag {
            background: rgba(230, 162, 60, 0.1);
            color: #e6a23c;
            border: 1px solid rgba(230, 162, 60, 0.3);

            i {
              margin-right: 4px;
            }
          }

          .history-tag {
            background: rgba(144, 147, 153, 0.1);
            // color: #909399;
            border: 1px solid rgba(144, 147, 153, 0.3);

            i {
              margin-right: 4px;
            }
          }

          // 操作按钮样式
          .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;

            .action-btn {
              font-size: 11px;
              padding: 4px 8px;
              border-radius: 4px;
              font-weight: 500;
              min-width: 56px;
              height: 28px;
              transition: all 0.3s ease;

              i {
                margin-right: 3px;
                font-size: 10px;
              }

              &.config-btn, &.edit-btn {
                background: rgba(215, 162, 86, 0.1);
                border: 1px solid rgba(215, 162, 86, 0.3) !important;
                color: #D7A256;

                &:hover {
                  background: #D7A256 !important;
                  color: white !important;
                  border-color: #D7A256 !important;
                  transform: translateY(-1px);
                  box-shadow: 0 2px 8px rgba(215, 162, 86, 0.3);
                }
              }

              &.delete-btn {
                background: rgba(245, 108, 108, 0.1);
                border: 1px solid rgba(245, 108, 108, 0.3) !important;
                color: #f56c6c;

                &:hover {
                  background: #f56c6c !important;
                  color: white !important;
                  border-color: #f56c6c !important;
                  transform: translateY(-1px);
                  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
                }
              }
            }
          }
        }

        // 空状态样式
        .empty-state {
          padding: 80px 20px;
          text-align: center;

          .empty-content {
            .empty-icon {
              width: 80px;
              height: 80px;
              margin: 0 auto 24px;
              background: rgba(215, 162, 86, 0.1);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;

              i {
                font-size: 36px;
                color: rgba(215, 162, 86, 0.6);
              }
            }

            h3 {
              margin: 0 0 12px 0;
              font-size: 18px;
              font-weight: 600;
              color: #2c3e50;
            }

            p {
              margin: 0;
              color: #7f8c8d;
              font-size: 14px;
              line-height: 1.5;
            }
          }
        }
      }

.remark-text {
  display: block;
  max-width: 180px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  color: #2c3e50;
}
.base-info-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid #e6c088;
  padding: 40px 48px;
  margin: 32px 0 32px 0;
  max-width: 100%;
  transition: box-shadow 0.3s;
  &:hover {
    box-shadow: 0 16px 48px rgba(215, 162, 86, 0.18);
  }
}

.base-info-title {
  font-size: 13px;
  font-weight: normal;
  color: #606266;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #D7A256;
  position: relative;
  
}

.base-info-content {
  display: grid;
  grid-template-columns: repeat(3, 1fr); // 每行3列
  gap: 0 32px; // 列间距
  .info-item {
    display: flex;
    align-items: flex-start;
    padding: 16px 0;
    border-bottom: none;
    // border-bottom: 1px solid #D7A256;
    &:last-child {
      border-bottom: none;
    }
    
    .info-label {
      width: 90px;
      text-align: left;
      color: #000000;
      font-size: 12px;
      font-weight: 550;
      flex-shrink: 0;
      // border-right: 1px solid #ebeef5;
    }
    
    .info-value {
      flex: 1;
      color: #2c3e50;
      font-weight: 400;
      word-break: break-all;
      line-height: 1.5;
    }
  }
}

.primary-btn {
  background: #D7A256 !important;
  color: #fff !important;
  box-shadow: 0 4px 16px rgba(215, 162, 86, 0.25) !important;
      &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(215, 162, 86, 0.4);
      }

      i {
        margin-right: 6px;
        font-size: 12px;
      }
}
.search-label {
  font-size: 13px;
  color: #606266;
  margin-right: 4px;
  min-width: 64px;
  text-align: right;
  display: inline-block;
}
.info-item {
    display: flex;
    align-items: flex-start;
    padding: 20px 0;
    border-bottom: 1px solid rgba(215, 162, 86, 0.08);
    transition: all 0.3s ease;
    position: relative;
    
    &:hover {
      background: rgba(215, 162, 86, 0.02);
      border-radius: 8px;
      padding-left: 12px;
      padding-right: 12px;
      margin-left: -12px;
      margin-right: -12px;
      
      .info-label {
        color: #D7A256;
      }
    }
    
    &:last-child {
      border-bottom: none;
    }
}
.base-info-card {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 16px;
  // box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  padding: 32px 40px;
  margin: 24px 0 32px 0;
  display: block;
  // max-width: 900px;
  // border: 1px solid #D7A256;
  // box-shadow: 0 4px 20px rgba(215, 162, 86, 0.15);
  position: relative;
  overflow: hidden;
  margin-left: 2px;
}
</style>