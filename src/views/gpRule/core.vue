<template>
  <div>
    <el-breadcrumb
      separator-class="el-icon-arrow-right"
      class="dt-bread"
      v-if="fromGP"
    >
      <el-breadcrumb-item class="cursor-pointer">
        <span @click="goBack()">返回</span>
      </el-breadcrumb-item>
      <el-breadcrumb-item
        :style="{ color: $store.state.layoutStore.themeObj.color }"
      >
        配置规则
      </el-breadcrumb-item>
    </el-breadcrumb>
    <TableToolTemp :toolListProps="{ toolTitle: '算法配置' }">
      <TableToolTemp
        :toolListProps="{ toolTitle: '过滤条件', toolList }"
        level="l2"
      >
        <component
          v-bind:is="item.componentName"
          :sort="idx + 1"
          :dicts="dicts"
          :sourceData="sourceData"
          v-for="(item, idx) in sourceConditionList"
          :key="item.key"
          :configData="configData.sourceFilterList[idx]"
          ref="sourceFilterList"
          @del="onDelCondtion"
          :readOnly="channelType === '2'"
        ></component>
      </TableToolTemp>
      <TableToolTemp
        :toolListProps="{ toolTitle: '计算变量', toolList: toolList1 }"
        level="l2"
      >
        <component
          v-bind:is="item.componentName"
          :sort="item.sort"
          :type="item.type"
          :index="idx"
          :dicts="dicts"
          :sourceData="sourceData"
          v-for="(item, idx) in variableList"
          :key="item.key"
          :configData="configData.variableList[idx]"
          ref="variableList"
          @del="onDelVariable"
        ></component>
      </TableToolTemp>
      <TableToolTemp
        :toolListProps="{ toolTitle: '结果决策', toolList: toolList2 }"
        level="l2"
      >
        <div class="bg">
          <el-form
            ref="resutlform"
            :model="formData"
            :rules="rules"
            label-width="160px"
            class="dt-from"
            label-position="left"
          >
            <el-form-item label="返回策略" prop="strategyType">
              <el-select
                v-model="formData.strategyType"
                placeholder="请选择返回策略"
                @change="changeStrategyType"
              >
                <el-option
                  :label="itme.dicItemName"
                  :value="itme.dicItemCode"
                  v-for="itme in dicts.strategy"
                  :key="itme.dicItemId"
                ></el-option>
              </el-select>
              <div class="tips">
                注意:切换返回策略会清空所有结果值配置
              </div>
            </el-form-item>
          </el-form>
          <div v-for="(item, idx) in resultFilterList" :key="item.key">
            <div class="title-box">
              <div class="title-box-val mr-10">过滤条件{{ idx + 1 }}</div>
              <el-button type="text" @click="delResultFilterList(idx)">
                删除
              </el-button>
            </div>
            <ConditionFrom
              :dicts="dicts"
              :sourceData="sourceData"
              :isFirst="idx === 0"
              :title="`结果决策-过滤条件${idx + 1}`"
              :configData="configData.resultFilterList[idx]"
              ref="resultFilterList"
            />
          </div>
        </div>
        <component
          v-bind:is="item.componentName"
          :sort="idx + 1"
          v-for="(item, idx) in decisionList"
          :key="item.key"
          :dicts="dicts"
          :sourceData="sourceData"
          :configData="configData.decisionList[idx]"
          :strategyType="formData.strategyType"
          :fromGP="fromGP"
          ref="decisionList"
          @del="onDelDecision"
        ></component>
      </TableToolTemp>
      <div class="btn-box">
        <el-button type="primary" @click="submit">保存</el-button>
        <el-button
          type="primary"
          plain
          :style="{ color: $store.state.layoutStore.themeObj.color }"
          @click="goBack()"
        >
          取消
        </el-button>
      </div>
    </TableToolTemp>
  </div>
</template>

<script>
// import configData from "./configData.json";
import TableToolTemp from "@/components/layouts/TableToolTemp";
import { getDicItemList } from "@/config/tool.js";
import Condtion from "./components/Condtion";
import Variable from "./components/Variable";
import Decision from "./components/Decision";
import ConditionFrom from "./components/ConditionFrom.vue";
import {
  getSourceData,
  getConfig,
  saveConfig,
  getProductsById
} from "@/api/gpRule";
import { createNamespacedHelpers } from "vuex";
const { mapMutations } = createNamespacedHelpers("gpRule");
export default {
  name: "gprule-core",
  components: {
    TableToolTemp,
    [Condtion.name]: Condtion,
    [Decision.name]: Decision,
    [Variable.name]: Variable,
    ConditionFrom
  },
  data() {
    return {
      // 数据源过滤条件集合
      sourceConditionList: [],
      // 变量集合
      variableList: [],
      // 结果决策结果值集合
      decisionList: [],
      // 结果决策过滤条件集合
      resultFilterList: [],
      toolList: [
        {
          name: "新增过滤条件",
          action: () => {
            this.sourceConditionList.push({
              componentName: "gprule-condtion",
              key: Date.now()
            });
          }
        },
        {
          name: "清空",
          action: () => {
            this.sourceConditionList = [];
          }
        }
      ],
      toolList1: [
        {
          name: "新增全局变量",
          action: () => {
            const sort = this.variableList.filter(e => e.type === "2").length;
            const key = Date.now();
            this.variableList.push({
              componentName: "gprule-variable",
              type: "2",
              sort: sort + 1,
              key
            });
            this.setVariableList({
              key,
              type: "2",
              name: "",
              code: ""
            });
          }
        },
        {
          name: "新增普通变量",
          action: () => {
            const sort = this.variableList.filter(e => e.type === "3").length;
            const key = Date.now();
            this.variableList.push({
              componentName: "gprule-variable",
              type: "3",
              sort: sort + 1,
              key
            });
            this.setVariableList({
              key,
              type: "3",
              name: "",
              code: ""
            });
          }
        },
        {
          name: "清空",
          action: () => {
            this.clearVariableList();
            this.variableList = [];
          }
        }
      ],
      toolList2: [],
      // 字典数据
      dicts: {
        ruleRelation: [],
        oper: [],
        strategy: [],
        calculator: [],
        aggr: [],
        formulaType: []
      },
      sourceData: [],
      configData: {
        decisionList: [],
        sourceFilterList: [],
        variableList: [],
        resultFilterList: []
      },
      formData: {
        // 结果决策 返回策略
        strategyType: ""
      },
      rules: {
        strategyType: [
          { required: true, message: "请选择返回策略", trigger: "change" }
        ]
      },
      fromGP: false,
      channelType: "1",
      stepRuleProduct: []
    };
  },
  async created() {
    const sourceId = this.$route.query.sourceId || "SC00002";
    const ruleId = this.$route.query.ruleId || "";
    this.fromGP = this.$route.query.fromGP === "1";
    // 1=奖励规则 2=爬坡规则
    this.channelType = this.$route.query.channelType || "1";
    // 重置store
    this.clearVariableList();
    const data = await Promise.all([
      // 初始化数据源
      getSourceData(sourceId),
      // 获取配置数据
      getConfig(ruleId),
      // 初始化字典
      this.getDictList(),
      // 爬坡规则初始化产品
      this.initStepRuleProduct()
    ]);
    this.sourceData = data[0];
    this.initConfig(data[1]);
  },
  methods: {
    ...mapMutations([
      "clearVariableList",
      "setVariableList",
      "clearVariableList"
    ]),
    async initStepRuleProduct() {
      const productId = this.$route.query.unifiedProductId;

      if (this.fromGP && this.channelType === "2" && productId) {
        const ret = await getProductsById([productId]);
        this.stepRuleProduct = [
          { unifiedProductId: productId, productName: ret[productId].productName,gpProductId:ret[productId].gpProductId }
        ];
      }
    },
    initConfig(cfgData) {
      this.configData = cfgData;
      this.formData.strategyType = this.configData.strategyType;
      this.configData.sourceFilterList
        .sort((a, b) => a.sort - b.sort)
        .forEach(e => {
          this.sourceConditionList.push({
            componentName: "gprule-condtion",
            key: `sourceCondtion-${e.sort}`
          });
        });
      let gVariabIndex = 0;
      let variabIndex = 0;
      this.configData.variableList
        .sort((a, b) => a.sort - b.sort)
        .forEach((e, index) => {
          let sort = index;
          if (e.type === "2") {
            sort = gVariabIndex++;
          } else if (e.type === "3") {
            sort = variabIndex++;
          }
          this.variableList.push({
            componentName: "gprule-variable",
            type: e.type,
            sort: sort + 1,
            key: `variable-${e.sort}`
          });
          this.setVariableList({
            key: `variable-${e.sort}`,
            type: e.type,
            name: e.name,
            code: e.code
          });
        });

      this.configData.decisionList
        .sort((a, b) => a.sort - b.sort)
        .forEach(e => {
          this.decisionList.push({
            componentName: "gprule-decision",
            key: `decision-${e.sort}`
          });
        });
      this.configData.resultFilterList
        .sort((a, b) => a.sort - b.sort)
        .forEach(e => {
          this.resultFilterList.push({
            key: `decision-${e.sort}`
          });
        });

      if (this.fromGP) {
        // 奖励配置 结果决策 返回策略 默认为返回结果 且无法修改
        if (!this.formData.strategyType) this.formData.strategyType = "1";
        // 同时返回结果值 只能有1个 不能删除
        if (this.decisionList.length === 0) {
          this.decisionList.push({
            componentName: "gprule-decision",
            key: Date.now()
          });
        }
        // 爬坡规则
        if (this.channelType === "2") {
          // 过滤条件默认配置
          if (
            this.$route.query.unifiedProductId &&
            this.configData.sourceFilterList.length === 0
          ) {
            this.configData.sourceFilterList.push({
              fieldType: "1",
              fieldId: "kbcProductId",
              oper: "eq",
              matchValue: this.$route.query.unifiedProductId,
              matchValueProduct: this.stepRuleProduct,
              sort: 1
            });
            this.sourceConditionList.push({
              componentName: "gprule-condtion",
              key: "sourceCondtion-1"
            });
          }
          // 且不可修改 去掉过滤条件编辑按钮
          this.toolList = [];
        }
      } else {
        // 非奖励配置 返回结果值 可有有多个

        this.toolList2.push(
          {
            name: "新增返回结果",
            action: () => {
              this.decisionList.push({
                componentName: "gprule-decision",
                key: Date.now()
              });
            }
          },
          {
            name: "清空",
            action: () => {
              this.decisionList = [];
            }
          }
        );
      }
    },
    async getDictList() {
      const dics = await Promise.all([
        getDicItemList("sct.indicator.ruleRelation"),
        getDicItemList("sct.indicator.oper"),
        getDicItemList("sct.calc.decision.strategy"),
        getDicItemList("sct.calc.formula.calculator"),
        getDicItemList("sct.calc.formula.aggr"),
        getDicItemList("sct.calc.formula.type")
      ]);
      // 与上一条件关系
      this.dicts.ruleRelation = dics[0];
      // 操作符
      this.dicts.oper = dics[1];
      // 结果决策返回策略
      this.dicts.strategy = dics[2];
      // 公式运算符
      this.dicts.calculator = dics[3];
      // 公式聚合方式
      this.dicts.aggr = dics[4];
      // 公式类型
      this.dicts.formulaType = dics[5];
    },
    async checkForm() {
      try {
        await this.$refs["resutlform"].validate();
        return true;
      } catch (error) {
        this.$message.error(`结果决策:录入项有错误,请检查！`);
        return false;
      }
    },
    async submit() {
      const data = {
        strategyId: this.$route.query.ruleId || "",
        strategyType: this.formData.strategyType,
        sourceFilterList: [],
        variableList: [],
        decisionList: [],
        resultFilterList: []
      };

      const submits = [
        ...this.sourceConditionList.map((e, index) => {
          return { index, key: "sourceFilterList" };
        }),
        ...this.variableList.map((e, index) => {
          return {
            index,
            key: "variableList"
          };
        }),
        ...this.resultFilterList.map((e, index) => {
          return {
            index,
            key: "resultFilterList"
          };
        }),
        ...this.decisionList.map((e, index) => {
          return {
            index,
            key: "decisionList"
          };
        })
      ];
      if (submits.length === 0) {
        this.$message.error("请添加配置！");
        return false;
      }
      for (let index = 0; index < submits.length; index++) {
        const key = submits[index].key;
        const refIndex = submits[index].index;
        const ref = this.$refs[key][refIndex];
        if (ref && ref.submit) {
          const submitData = await ref.submit();
          if (submitData) data[key].push(submitData);
          else return false;
        }
      }

      // resultFilterList 添加sort
      data.resultFilterList = data.resultFilterList.map((e, index) => {
        return {
          sort: index + 1,
          ...e
        };
      });
      // 结果决策 form 检查
      const check = await this.checkForm();
      if (!check) return false;

      const res = await saveConfig(data);
      if (res) {
        this.$message.success("保存成功");
        this.goBack();
      }
      return true;
    },
    onDelCondtion(index) {
      this.sourceConditionList.splice(index, 1);
    },
    onDelVariable(index) {
      this.variableList.splice(index, 1);
      const variable = this.variableList.filter(e => e.type === "2");
      variable.forEach((e, index) => (e.sort = index + 1));
      const Gvariable = this.variableList.filter(e => e.type === "3");
      Gvariable.forEach((e, index) => (e.sort = index + 1));
    },
    onDelDecision(index) {
      this.decisionList.splice(index, 1);
    },
    onDelResultFilterList(index) {
      this.resultFilterList.splice(index, 1);
    },
    delResultFilterList(index) {
      this.resultFilterList.splice(index, 1);
    },
    changeStrategyType() {
      if (this.decisionList.length > 0) {
        this.decisionList = [
          {
            componentName: "gprule-decision",
            key: Date.now()
          }
        ];
      }
    },
    goBack() {
      const backRouter = sessionStorage.getItem("cbs-gp-sct-back-router");
      console.log("backRouter:", backRouter);
      if (backRouter) window.location.href = backRouter;
      else this.$router.go(-1);
    }
  }
};
</script>

<style lang="less">
.cursor-pointer {
  cursor: pointer;
}
.bg {
  width: 680px;
  box-sizing: border-box;
  // padding: 10px;
  padding: 10px 10px 1px 10px;
  background: #f5f5f5;
  margin-bottom: 10px;
  .title-box {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    line-height: 24px;
    &-val {
      font-weight: 600;
      cursor: pointer;
    }
  }
  .title-btn {
    margin-bottom: 18px;
  }
  .chl-box {
    box-sizing: border-box;
    padding: 0 20px;
  }
  .el-button--text {
    padding: 0;
  }
  .mr-10 {
    margin-right: 10px;
  }
}
.dt-from {
  .el-select {
    width: 372px;
  }
  .el-input {
    width: 372px;
  }
  .el-date-editor.el-input {
    width: 372px;
  }
  .el-input-number {
    .el-input__inner {
      text-align: left;
    }
  }
}
.dt-range {
  display: flex;
  flex-direction: column;
  width: 372px;
  .dt-range-line {
    width: 100%;
    display: flex;
    justify-content: space-between;
    .el-select {
      width: 180px;
    }
    .el-input {
      width: 180px;
    }
    .el-date-editor.el-input {
      width: 180px;
    }
  }
}
.btn-box {
  margin-left: 20px;
  margin-top: 10px;
  margin-bottom: 25px;
}
.tag-box {
  width: 100%;
  .el-tag {
    margin-right: 5px;
  }
}
.tips {
  width: 100%;
  color: #f56c6c;
  font-size: 12px;
}
</style>
