/**
 * 根据数据类型获取操作符列表
 */
export const getOperList = (dataType, opers) => {
  // 字符串操作符 等于/不等于/包含/不包含
  const operString = ["eq", "neq", "contain", "notcontain"];
  //  数字操作符 等于/不等于/大于/大于等于/小于/小于等于/范围
  const operNumber = ["eq", "neq", "gt", "gte", "lt", "lte", "range"];
  // 范围操作符 大于/大于等于/小于/小于等于
  const operRange = ["gt", "gte", "lt", "lte"];
  const operRange1 = ["gt", "gte"];
  const operRange2 = ["lt", "lte"];
  let oper = opers;
  switch (dataType) {
    case "range":
      oper = opers.filter(e => operRange.includes(e.dicItemCode));
      break;
    case "range1":
      oper = opers.filter(e => operRange1.includes(e.dicItemCode));
      break;
    case "range2":
      oper = opers.filter(e => operRange2.includes(e.dicItemCode));
      break;
    case "string":
      oper = opers.filter(e => operString.includes(e.dicItemCode));
      break;
    case "int":
    case "float":
    case "date":
      oper = opers.filter(e => operNumber.includes(e.dicItemCode));
      break;
    default:
      break;
  }

  return oper;
};
