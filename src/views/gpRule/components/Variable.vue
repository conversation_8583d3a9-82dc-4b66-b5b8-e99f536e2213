<template>
  <div class="bg">
    <div class="title-box">
      <div class="title-box-val mr-10" @click="collapse = !collapse">
        <i
          :class="collapse ? 'el-icon-caret-bottom' : 'el-icon-caret-right'"
        ></i>
        {{ type === "2" ? `全局变量${sort}` : `普通变量${sort}` }}
      </div>
      <el-button type="text" @click="delVariable">删除</el-button>
    </div>
    <div v-show="collapse">
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="160px"
        class="dt-from"
        label-position="left"
      >
        <el-form-item label="变量名" prop="name">
          <el-input
            v-model.trim="formData.name"
            maxlength="199"
            @blur="onBlurVariableList"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="变量编码" prop="code">
          <el-input v-model.trim="formData.code" maxlength="199"></el-input>
        </el-form-item> -->
      </el-form>
      <div class="title-box">
        <div
          class="title-box-val mr-10"
          @click="collapseValue = !collapseValue"
        >
          <i
            :class="
              collapseValue ? 'el-icon-caret-bottom' : 'el-icon-caret-right'
            "
          ></i>
          变量值
        </div>
        <el-button type="text" @click="valueList = []">清空</el-button>
      </div>
      <div class="chl-box" v-show="collapseValue">
        <div v-for="(item, idx) in valueList" :key="item.key">
          <div class="title-box">
            <div
              class="title-box-val mr-10"
              @click="collapseValueChl = !collapseValueChl"
            >
              <i
                :class="
                  collapseValueChl
                    ? 'el-icon-caret-bottom'
                    : 'el-icon-caret-right'
                "
              ></i>
              变量值{{ idx + 1 }}
            </div>
            <el-button type="text" @click="delValue(idx)">删除</el-button>

            <el-button type="text" @click="clearCondition(idx)">
              清空条件
            </el-button>
          </div>
          <div v-show="collapseValueChl">
            <div
              v-for="(filter, idx2) in getFilter(idx)"
              :key="filter.key"
              class="chl-box"
            >
              <div class="title-box">
                <div class="title-box-val mr-10">过滤条件{{ idx2 + 1 }}</div>
                <el-button type="text" @click="delFilter(idx, idx2)">
                  删除
                </el-button>
              </div>
              <ConditionFrom
                :dicts="dicts"
                :sourceData="sourceData"
                :isFirst="idx2 === 0"
                :variableSort="index"
                :configData="filter"
                :title="
                  `计算变量-${
                    type === '2' ? '全局' : '普通'
                  }变量${sort}-变量值${idx + 1}-过滤条件${idx2 + 1}`
                "
                :ref="`filterRef${idx}`"
              />
            </div>
            <!-- <div class="title-box chl-box title-btn">
              <el-button
                type="text"
                icon="iconfont icondt8"
                @click="addfilter(idx)"
              >
                新增过滤条件
              </el-button>
            </div> -->
            <div
              class="chl-box"
              v-for="(condition, idx1) in getCondition(idx)"
              :key="condition.key"
            >
              <div class="title-box">
                <div class="title-box-val mr-10">匹配条件{{ idx1 + 1 }}</div>
                <el-button type="text" @click="delCondition(idx, idx1)">
                  删除
                </el-button>
              </div>
              <ConditionFrom
                type="2"
                :dicts="dicts"
                :sourceData="sourceData"
                :isFirst="idx1 === 0"
                :variableSort="index"
                :configData="condition"
                :onlyGloble="type === '2'"
                :title="
                  `计算变量-${
                    type === '2' ? '全局' : '普通'
                  }变量${sort}-变量值${idx + 1}-匹配条件${idx1 + 1}`
                "
                :ref="`conditionRef${idx}`"
              />
            </div>
            <div class="title-box chl-box title-btn">
              <el-button
                type="text"
                icon="iconfont icondt8"
                @click="addCondition(idx)"
              >
                新增匹配条件
              </el-button>
            </div>
            <div class="chl-box">
              <Formula
                :dicts="dicts"
                :sourceData="sourceData"
                ref="formulaRef"
                :hasAggr="type === '2'"
                :variableSort="index"
                :configData="valueList[idx].formula"
                :title="
                  `计算变量-${
                    type === '2' ? '全局' : '普通'
                  }变量${sort}-变量值${idx + 1}-运算公式`
                "
              />
            </div>
          </div>
        </div>
        <div class="title-box">
          <el-button type="text" icon="iconfont icondt8" @click="addValue">
            新增变量值
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ConditionFrom from "./ConditionFrom.vue";
import Formula from "./Formula.vue";
import { createNamespacedHelpers } from "vuex";
const { mapMutations, mapGetters } = createNamespacedHelpers("gpRule");
export default {
  name: "gprule-variable",
  components: { ConditionFrom, Formula },
  props: {
    index: {
      type: Number,
      default: 0
    },
    sort: {
      type: Number,
      default: 1
    },
    type: {
      type: String,
      // 2=全局变量 3=普通变量
      default: "2"
    },
    dicts: {
      type: Object,
      default: () => {
        return { ruleRelation: [], oper: [] };
      }
    },
    sourceData: {
      type: Array,
      default: () => []
    },
    configData: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      formData: {
        name: "",
        code: `VAR${Date.now()}`
      },
      rules: {
        name: [{ required: true, message: "请输入变量名", trigger: "blur" }],
        code: [
          { required: true, message: "请输入变量编码", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (!this.formData.name || !value) {
                callback();
                return;
              }
              if (!this.checkVariable(this.type, value, this.sort - 1)) {
                callback(new Error("变量编码已存在!"));
                return;
              }
              this.onBlurVariableList();
              callback();
            },
            trigger: "blur"
          }
        ]
      },
      valueList: [
        {
          key: Date.now(),
          conditionList: [],
          filterList: [],
          formula: []
        }
      ],
      collapse: true,
      collapseValue: true,
      collapseValueChl: true
    };
  },
  computed: {
    ...mapGetters(["checkVariable", "getVariableList"])
  },
  created() {
    if (this.configData) {
      this.formData.name = this.configData.name || "";
      this.formData.code = this.configData.code || "";
      if (
        this.configData.valueList &&
        Array.isArray(this.configData.valueList)
      ) {
        this.valueList = this.configData.valueList
          .sort((a, b) => a.sort - b.sort)
          .map(e => {
            const conditionList = e.conditionList || [];
            const filterList = e.filterList || [];
            const formula = e.formulaArray || [];
            return {
              key: `variable-${this.index}-${e.sort}`,
              filterList: filterList
                .sort((a, b) => a.sort - b.sort)
                .map(c => {
                  return {
                    ...c,
                    key: `variable-${this.index}-${e.sort}-${c.sort}-t`
                  };
                }),
              conditionList: conditionList
                .sort((a, b) => a.sort - b.sort)
                .map(c => {
                  return {
                    ...c,
                    key: `variable-${this.index}-${e.sort}-${c.sort}-c`
                  };
                }),
              formula: formula
                .sort((a, b) => a.sort - b.sort)
                .map(f => {
                  return {
                    ...f,
                    key: `variable-${this.index}-${e.sort}-${f.sort}-f`
                  };
                })
            };
          });
      }
    }
  },
  methods: {
    ...mapMutations(["updateVariableList", "delVariableList"]),
    hasGVariable() {
      return this.getVariableList("2", this.index).length > 0;
    },
    onBlurVariableList() {
      this.updateVariableList({
        index: this.sort - 1,
        type: this.type,
        code: this.formData.code,
        name: this.formData.name
      });
    },
    delVariable() {
      this.delVariableList({
        type: this.type,
        index: this.sort - 1
      });
      this.$emit("del", this.index);
    },
    addValue() {
      const key = Date.now();
      this.valueList.push({
        key,
        conditionList: [],
        formula: [],
        filterList: []
      });
    },
    delValue(index) {
      this.valueList.splice(index, 1);
    },
    getCondition(index) {
      return this.valueList[index].conditionList;
    },
    addCondition(index) {
      if (this.type === "2" && !this.hasGVariable()) {
        this.$message.error("当前无有效全局变量,无法新增匹配条件!");
        return;
      }
      const key = Date.now();
      this.valueList[index].conditionList.push({
        key: key
      });
    },
    addfilter(index) {
      const key = Date.now();
      this.valueList[index].filterList.push({
        key: key
      });
    },
    delCondition(index, conditionIdx) {
      this.valueList[index].conditionList.splice(conditionIdx, 1);
    },
    getFilter(index) {
      return this.valueList[index].filterList;
    },
    delFilter(index, filterIndex) {
      this.valueList[index].filterList.splice(filterIndex, 1);
    },
    clearCondition(index) {
      this.valueList[index].conditionList = [];
      this.valueList[index].filterList = [];
    },
    async checkForm() {
      try {
        await this.$refs["form"].validate();
        return true;
      } catch (error) {
        this.$message.error(
          `计算变量-${this.type === "2" ? "全局" : "普通"}变量${
            this.sort
          }:录入项有错误,请检查！`
        );
        return false;
      }
    },
    async submit() {
      const check = await this.checkForm();
      if (!check) return null;
      const retData = {
        sort: this.index + 1,
        type: this.type,
        name: this.formData.name,
        code: this.formData.code,
        valueList: []
      };

      for (let idx = 0; idx < this.valueList.length; idx++) {
        retData.valueList.push({
          sort: idx + 1,
          conditionList: [],
          filterList: [],
          formulaArray: []
        });
        const conditionRef = this.$refs["conditionRef" + idx];
        if (conditionRef) {
          for (let idx1 = 0; idx1 < conditionRef.length; idx1++) {
            const ref = conditionRef[idx1];

            const from = await ref.submit();
            if (!from) return null;
            retData.valueList[idx].conditionList.push({
              sort: idx1 + 1,
              ...from
            });
          }
        }
        const filterRef = this.$refs["filterRef" + idx];
        if (filterRef) {
          for (let idx2 = 0; idx2 < filterRef.length; idx2++) {
            const ref = filterRef[idx2];

            const from = await ref.submit();
            if (!from) return null;
            retData.valueList[idx].filterList.push({
              sort: idx2 + 1,
              ...from
            });
          }
        }
      }

      const formulaRef = this.$refs["formulaRef"];
      if (formulaRef) {
        for (let index = 0; index < formulaRef.length; index++) {
          const ref = formulaRef[index];
          const from = ref.submit();
          if (!from) return null;
          retData.valueList[index].formulaArray = from;
        }
      }

      return retData;
    }
  }
};
</script>
