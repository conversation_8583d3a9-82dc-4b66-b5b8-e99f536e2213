<template>
  <div class="bg">
    <div class="title-box">
      <span class="title-box-val mr-10" @click="collapse = !collapse"
        ><i
          :class="collapse ? 'el-icon-caret-bottom' : 'el-icon-caret-right'"
        ></i>
        过滤条件{{ sort }}
      </span>
      <el-button type="text" @click="delCondtion" v-if="!readOnly">
        删除
      </el-button>
    </div>
    <div v-show="collapse">
      <ConditionFrom
        :dicts="dicts"
        :sourceData="sourceData"
        :isFirst="sort === 1"
        :title="`过滤条件-过滤条件${sort}`"
        :configData="configData"
        :readOnly="readOnly"
        onlySource
        ref="ConditionFrom"
      />
    </div>
  </div>
</template>

<script>
import ConditionFrom from "./ConditionFrom.vue";
export default {
  name: "gprule-condtion",
  components: { ConditionFrom },
  data() {
    return {
      collapse: true
    };
  },
  props: {
    sort: {
      type: Number,
      default: 1
    },
    dicts: {
      type: Object,
      default: () => {
        return { ruleRelation: [], oper: [] };
      }
    },
    sourceData: {
      type: Array,
      default: () => []
    },
    configData: {
      type: Object,
      default: () => null
    },
    readOnly: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    async submit() {
      const form = await this.$refs["ConditionFrom"].submit();
      if (form) {
        return {
          ...form,
          sort: this.sort
        };
      }
      return null;
    },

    delCondtion() {
      this.$emit("del", this.sort - 1);
    }
  }
};
</script>
