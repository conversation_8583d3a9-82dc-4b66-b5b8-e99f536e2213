<template>
  <div class="bg">
    <div class="title-box">
      <div class="title-box-val mr-10" @click="collapse = !collapse">
        <i
          :class="collapse ? 'el-icon-caret-bottom' : 'el-icon-caret-right'"
        ></i>
        返回结果{{ sort }}
      </div>
      <el-button type="text" @click="delDecision" v-if="!fromGP">
        删除
      </el-button>
      <!-- <el-button type="text" @click="addValue">新增结果值</el-button> -->
      <el-button type="text" @click="valueList = []">清空</el-button>
    </div>
    <div v-show="collapse">
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="160px"
        class="dt-from"
        label-position="left"
      >
        <el-form-item label="结果名称" prop="name">
          <el-input
            v-model.trim="formData.name"
            maxlength="199"
            :disabled="fromGP"
          ></el-input>
        </el-form-item>
        <el-form-item label="结果编码" prop="code">
          <el-input
            v-model.trim="formData.code"
            maxlength="199"
            :disabled="fromGP"
          ></el-input>
        </el-form-item>
      </el-form>
      <div v-for="(item, idx) in valueList" :key="item.key" class="chl-box">
        <div class="title-box" @click="collapseValue = !collapseValue">
          <div class="title-box-val mr-10">
            <i
              :class="
                collapseValue ? 'el-icon-caret-bottom' : 'el-icon-caret-right'
              "
            ></i>
            结果值{{ idx + 1 }}
          </div>
          <el-button type="text" @click="delValue(idx)">删除</el-button>

          <el-button type="text" @click="clearCondition(idx)"
            >清空条件</el-button
          >
        </div>
        <div class="chl-box" v-show="collapseValue">
          <!-- 返回策略是合并至数据源后返回才能选择结果的过滤条件 -->
          <!-- <div class="title-box  title-btn">
            <el-button
              type="text"
              icon="iconfont icondt8"
              @click="addfilter(idx)"
              :disabled="strategyType === '1'"
            >
              新增过滤条件
            </el-button>
          </div> -->
          <div v-for="(filter, idx2) in getFilter(idx)" :key="filter.key">
            <div class="title-box">
              <div class="title-box-val mr-10">过滤条件{{ idx2 + 1 }}</div>
              <el-button type="text" @click="delFilter(idx, idx2)">
                删除
              </el-button>
            </div>
            <ConditionFrom
              :dicts="dicts"
              :sourceData="sourceData"
              :isFirst="idx2 === 0"
              :configData="filter"
              :title="
                `结果决策-返回结果${sort}-结果值${idx + 1}-过滤条件${idx2 + 1}`
              "
              :ref="`filterRef${idx}`"
            />
          </div>
          <div class="title-box  title-btn">
            <el-button
              type="text"
              icon="iconfont icondt8"
              @click="addCondition(idx)"
            >
              新增匹配条件
            </el-button>
          </div>
          <div
            v-for="(condition, idx1) in getCondition(idx)"
            :key="condition.key"
          >
            <div class="title-box">
              <div class="title-box-val mr-10">匹配条件{{ idx1 + 1 }}</div>
              <el-button type="text" @click="delCondition(idx, idx1)">
                删除
              </el-button>
            </div>
            <ConditionFrom
              type="2"
              :dicts="dicts"
              :sourceData="sourceData"
              :isFirst="idx1 === 0"
              :configData="condition"
              :onlyGloble="strategyType === '1'"
              :title="
                `结果决策-返回结果${sort}-结果值${idx + 1}-匹配条件${idx1 + 1}`
              "
              :ref="`conditionRef${idx}`"
            />
          </div>

          <Formula
            :dicts="dicts"
            :sourceData="sourceData"
            :configData="valueList[idx].formula"
            :hasAggr="strategyType === '1'"
            ref="formulaRef"
            :title="`结果决策-返回结果${sort}-结果值${idx + 1}-运算公式`"
          />
        </div>
      </div>
      <div class="title-box chl-box title-btn">
        <el-button type="text" icon="iconfont icondt8" @click="addValue">
          新增结果值
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import ConditionFrom from "./ConditionFrom.vue";
import Formula from "./Formula.vue";
import { createNamespacedHelpers } from "vuex";
const { mapGetters } = createNamespacedHelpers("gpRule");
export default {
  name: "gprule-decision",
  components: { ConditionFrom, Formula },
  props: {
    fromGP: {
      type: Boolean,
      default: false
    },
    sort: {
      type: Number,
      default: 1
    },
    strategyType: {
      type: String,
      default: "1"
    },
    dicts: {
      type: Object,
      default: () => {
        return { ruleRelation: [], oper: [], strategy: [] };
      }
    },
    sourceData: {
      type: Array,
      default: () => []
    },
    configData: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      formData: {
        name: "",
        code: ""
      },
      rules: {
        name: [{ required: true, message: "请输入变量名", trigger: "blur" }],
        code: [{ required: true, message: "请输入变量编码", trigger: "blur" }]
      },
      valueList: [
        {
          key: Date.now(),
          conditionList: [],
          filterList: [],
          formula: []
        }
      ],
      collapse: true,
      collapseValue: true
    };
  },
  computed: {
    ...mapGetters(["getVariableList"])
  },
  created() {
    if (this.configData) {
      this.formData.name = this.configData.name || "";
      this.formData.code = this.configData.code || "";
      if (
        this.configData.valueList &&
        Array.isArray(this.configData.valueList)
      ) {
        this.valueList = this.configData.valueList
          .sort((a, b) => a.sort - b.sort)
          .map(e => {
            const conditionList = e.conditionList || [];
            const formula = e.formulaArray || [];
            const filterList = e.filterList || [];
            return {
              key: `decision-${this.index}-${e.sort}`,
              filterList: filterList
                .sort((a, b) => a.sort - b.sort)
                .map(c => {
                  return {
                    ...c,
                    key: `decision-${this.index}-${e.sort}-${c.sort}-filter`
                  };
                }),
              conditionList: conditionList
                .sort((a, b) => a.sort - b.sort)
                .map(c => {
                  return {
                    ...c,
                    key: `decision-${this.index}-${e.sort}-${c.sort}-c`
                  };
                }),
              formula: formula
                .sort((a, b) => a.sort - b.sort)
                .map(f => {
                  return {
                    ...f,
                    key: `decision-${this.index}-${e.sort}-${f.sort}-f`
                  };
                })
            };
          });
      }
    }
    // 奖励结算 设置默认值
    if (this.fromGP) {
      if (!this.formData.name) this.formData.name = "结算金额";
      if (!this.formData.code) this.formData.code = "settlementAmount";
    }
  },
  methods: {
    delDecision() {
      this.$emit("del", this.sort - 1);
    },
    addValue() {
      const key = Date.now();
      this.valueList.push({
        key,
        conditionList: [],
        formula: [],
        filterList: []
      });
    },
    delValue(index) {
      this.valueList.splice(index, 1);
    },
    getCondition(index) {
      return this.valueList[index].conditionList;
    },
    hasGVariable() {
      return this.getVariableList("2").length > 0;
    },
    addCondition(index) {
      // if (!this.hasGVariable()) {
      //   this.$message.error("当前无有效全局变量,无法新增匹配条件!");
      //   return;
      // }
      const key = Date.now();
      this.valueList[index].conditionList.push({
        key
      });
    },
    delCondition(index, conditionIdx) {
      this.valueList[index].conditionList.splice(conditionIdx, 1);
    },
    clearCondition(index) {
      this.valueList[index].conditionList = [];
      this.valueList[index].filterList = [];
    },
    addfilter(index) {
      const key = Date.now();
      this.valueList[index].filterList.push({
        key: key
      });
    },
    getFilter(index) {
      return this.valueList[index].filterList;
    },
    delFilter(index, filterIndex) {
      this.valueList[index].filterList.splice(filterIndex, 1);
    },
    async checkForm() {
      try {
        await this.$refs["form"].validate();
        return true;
      } catch (error) {
        this.$message.error(
          `结果决策-返回结果${this.sort}:录入项有错误,请检查！`
        );
        return false;
      }
    },
    async submit() {
      const check = await this.checkForm();
      if (!check) return null;
      const retData = {
        sort: this.sort + 1,
        name: this.formData.name,
        code: this.formData.code,
        valueList: []
      };

      for (let idx = 0; idx < this.valueList.length; idx++) {
        retData.valueList.push({
          sort: idx + 1,
          conditionList: [],
          formulaArray: [],
          filterList: []
        });
        const conditionRef = this.$refs["conditionRef" + idx];
        if (conditionRef) {
          for (let idx1 = 0; idx1 < conditionRef.length; idx1++) {
            const ref = conditionRef[idx1];

            const from = await ref.submit();
            if (!from) return null;
            retData.valueList[idx].conditionList.push({
              sort: idx1 + 1,
              ...from
            });
          }
        }
        const filterRef = this.$refs["filterRef" + idx];
        if (filterRef) {
          for (let idx2 = 0; idx2 < filterRef.length; idx2++) {
            const ref = filterRef[idx2];

            const from = await ref.submit();
            if (!from) return null;
            retData.valueList[idx].filterList.push({
              sort: idx2 + 1,
              ...from
            });
          }
        }
      }

      const formulaRef = this.$refs["formulaRef"];
      if (formulaRef) {
        for (let index = 0; index < formulaRef.length; index++) {
          const ref = formulaRef[index];
          const from = ref.submit();
          if (!from) return null;
          retData.valueList[index].formulaArray = from;
        }
      }

      return retData;
    }
  }
};
</script>
