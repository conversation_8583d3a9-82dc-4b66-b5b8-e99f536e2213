<template>
  <div>
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="160px"
      class="dt-from"
      label-position="left"
      :disabled="readOnly"
    >
      <el-form-item label="与上一条件关系" v-if="!isFirst" prop="ruleRelation">
        <el-select
          v-model="formData.ruleRelation"
          placeholder="请选择与上一条件关系"
        >
          <el-option
            :label="itme.dicItemName"
            :value="itme.dicItemCode"
            v-for="itme in dicts.ruleRelation"
            :key="itme.dicItemId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="匹配操作符" v-if="!onlySource" required>
        <!-- 仅可选择全局变量 -->
        <el-radio-group v-model="formData.fieldType" v-if="onlyGloble">
          <el-radio label="2">
            全局变量
          </el-radio>
        </el-radio-group>
        <el-radio-group
          v-model="formData.fieldType"
          @change="changeFieldType"
          v-else
        >
          <el-radio label="1">数据源字段</el-radio>
          <!-- 过滤条件 不能选择全局变量作为过滤条件-->
          <el-radio
            label="2"
            :disabled="disableVariable('2')"
            v-if="type !== '1'"
          >
            全局变量
          </el-radio>
          <el-radio label="3" :disabled="disableVariable('3')">
            普通变量
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="字段"
        prop="fieldId"
        v-if="formData.fieldType === '2' || formData.fieldType === '3'"
      >
        <el-select
          v-model="formData.fieldId"
          placeholder="请选择字段"
          @change="changeSourceData"
        >
          <el-option
            :label="itme.label"
            :value="itme.value"
            v-for="itme in variableList(formData.fieldType)"
            :key="itme.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        :label="onlySource ? '数据源字段' : '字段'"
        prop="fieldId"
        v-else
      >
        <el-select
          v-model="formData.fieldId"
          :placeholder="onlySource ? '请选择数据源字段' : '请选择字段'"
          @change="changeSourceData"
        >
          <el-option
            :label="itme.name"
            :value="itme.fieldId"
            v-for="itme in sourceData"
            :key="itme.fieldId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="匹配操作符" v-if="formData.fieldId" prop="oper">
        <el-select
          v-model="formData.oper"
          placeholder="请选择匹配操作符"
          @change="changeOper"
        >
          <el-option
            :label="itme.dicItemName"
            :value="itme.dicItemCode"
            v-for="itme in operList"
            :key="itme.dicItemId"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 匹配产品 -->
      <el-form-item label="匹配值" v-if="findProduct && formData.oper" required>
        <div class="tag-box">
          <el-tag
            v-for="(tag, idx) in products"
            :key="tag.unifiedProductId"
            :closable="!readOnly"
            @close="delProduct(idx)"
          >
            {{ tag.productName }}
          </el-tag>
        </div>

        <el-button type="text" @click="showFindProduct = true">
          查找产品
        </el-button>
      </el-form-item>
      <!-- 范围 -->
      <el-form-item
        label="匹配值"
        v-else-if="formData.oper === 'range'"
        required
      >
        <div class="dt-range">
          <div class="dt-range-line">
            <el-select
              v-model="formData.rangeOper1"
              placeholder="请选择匹配操作符"
            >
              <el-option
                :label="itme.dicItemName"
                :value="itme.dicItemCode"
                v-for="itme in rangeOper1"
                :key="itme.dicItemId"
              ></el-option>
            </el-select>
            <!-- 数字 -->
            <el-input-number
              v-model="formData.rangeMatchValue1"
              v-if="sourceDataType === 'int' || sourceDataType === 'float'"
              :controls="false"
            ></el-input-number>

            <!-- 日期 -->
            <el-date-picker
              v-else-if="sourceDataType === 'date'"
              v-model="formData.rangeMatchValue1"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
            >
            </el-date-picker>
          </div>
          <div class="dt-range-line">
            <el-select
              v-model="formData.rangeOper2"
              placeholder="请选择匹配操作符"
            >
              <el-option
                :label="itme.dicItemName"
                :value="itme.dicItemCode"
                v-for="itme in rangeOper2"
                :key="itme.dicItemId"
              ></el-option>
            </el-select>
            <!-- 数字 -->
            <el-input-number
              v-model="formData.rangeMatchValue2"
              v-if="sourceDataType === 'int' || sourceDataType === 'float'"
              :controls="false"
            ></el-input-number>
            <!-- 日期 -->
            <el-date-picker
              v-else-if="sourceDataType === 'date'"
              v-model="formData.rangeMatchValue2"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
            >
            </el-date-picker>
          </div>
        </div>
      </el-form-item>
      <!-- 非空 -->
      <el-form-item label="匹配值" v-else-if="formData.oper !== ''" required>
        <!-- 字符串 -->
        <div v-if="sourceDataType === 'string'">
          <el-select
            v-if="options.length > 0"
            v-model="formData.matchValue"
            :key="
              formData.oper === 'contain' || formData.oper === 'notcontain'
                ? 'multiple'
                : 'nomultiple'
            "
            :multiple="
              formData.oper === 'contain' || formData.oper === 'notcontain'
            "
            placeholder="请选择"
          >
            <el-option
              v-for="item in options"
              :key="item.dicKey"
              :label="item.dicValue"
              :value="item.dicKey"
            >
            </el-option>
          </el-select>
          <el-input
            v-model.trim="formData.matchValue"
            v-else
            maxlength="199"
          ></el-input>
        </div>
        <!-- 数字 -->
        <el-input-number
          v-model="formData.matchValue"
          v-else-if="sourceDataType === 'int' || sourceDataType === 'float'"
          :controls="false"
        ></el-input-number>
        <!-- 日期 -->
        <el-date-picker
          v-else-if="sourceDataType === 'date'"
          v-model="formData.matchValue"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
        >
        </el-date-picker>
      </el-form-item>
    </el-form>
    <FindProduct
      :show.sync="showFindProduct"
      :selectd="products.map(item => item.gpProductId)"
      @success="findProductSuccess"
      :isList="formData.oper === 'contain' || formData.oper === 'notcontain'"
    />
  </div>
</template>

<script>
import { getOperList } from "../utils";
import FindProduct from "./FindProduct.vue";
import { createNamespacedHelpers } from "vuex";
const { mapGetters } = createNamespacedHelpers("gpRule");

export default {
  name: "gprule-condtion-from",
  components: { FindProduct },
  props: {
    // 1-过滤条件 2=匹配条件
    type: {
      type: String,
      default: "1"
    },
    // 仅可选择全局变量
    onlyGloble: {
      type: Boolean,
      default: false
    },
    onlySource: {
      type: Boolean,
      default: false
    },
    isFirst: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ""
    },
    variableSort: {
      type: Number,
      default: null
    },
    dicts: {
      type: Object,
      default: () => {
        return { ruleRelation: [], oper: [] };
      }
    },
    sourceData: {
      type: Array,
      default: () => []
    },
    configData: {
      type: Object,
      default: () => null
    },
    readOnly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      operList: [],
      sourceDataType: "string",
      options: [],
      rangeOper1: [],
      rangeOper2: [],
      showFindProduct: false,
      findProduct: false,
      products: [],
      formData: {
        // 字段类型
        fieldType: "1",
        // 字段标识
        fieldId: "",
        // 匹配符
        oper: "",
        // 匹配值
        matchValue: "",
        // 与上一条件关系
        ruleRelation: "",

        // 范围选择参数
        rangeOper1: "gte",
        rangeOper2: "lte",
        rangeMatchValue1: "",
        rangeMatchValue2: ""
      },
      rules: {
        ruleRelation: [
          {
            required: true,
            message: "请选择与上一条件关系",
            trigger: "change"
          }
        ],
        fieldId: [
          {
            required: true,
            message: `请选择${this.onlySource ? "数据源" : ""}字段`,
            trigger: "change"
          }
        ],
        oper: [
          { required: true, message: "请选择匹配操作符", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.rangeOper1 = getOperList("range1", this.dicts.oper);
    this.rangeOper2 = getOperList("range2", this.dicts.oper);
    this.initData();
    // 仅可选择全局变量
    if (this.onlyGloble) this.initOnlyGloble();
  },
  computed: {
    ...mapGetters(["getVariableList"])
  },
  methods: {
    initOnlyGloble() {
      this.formData.fieldType = "2";
      this.sourceDataType = "float";
      this.operList = getOperList(this.sourceDataType, this.dicts.oper);
      this.options = [];
      this.products = [];
      this.findProduct = false;
    },
    initData() {
      if (!this.configData) return;
      this.formData.fieldType = this.configData.fieldType || "1";
      this.formData.fieldId = this.configData.fieldId || "";
      this.changeSourceData(this.formData.fieldId);
      this.formData.oper = this.configData.oper || "";
      this.formData.ruleRelation = this.configData.ruleRelation || "";
      if (this.findProduct) {
        // 产品
        this.products = this.configData.matchValueProduct;
        this.formData.matchValue = this.configData.matchValue.split(",");
      } else if (
        this.formData.oper === "contain" ||
        this.formData.oper === "notcontain"
      ) {
        // 包含不包含
        this.formData.matchValue = this.configData.matchValue.split(",");
      } else if (this.formData.oper === "range") {
        // 范围
        try {
          const value = JSON.parse(this.configData.matchValue);
          const keys = Object.keys(value);
          this.formData.rangeOper1 = keys[0] || "";
          if (this.formData.rangeOper1)
            this.formData.rangeMatchValue1 = value[this.formData.rangeOper1];
          this.formData.rangeOper2 = keys[1] || "";
          if (this.formData.rangeOper2)
            this.formData.rangeMatchValue2 = value[this.formData.rangeOper2];
        } catch (error) {
          console.log(error);
        }
      } else {
        this.formData.matchValue = this.configData.matchValue;
      }
    },
    disableVariable(type) {
      return this.getVariableList(type, this.variableSort).length <= 0;
    },
    variableList(type) {
      return this.getVariableList(type, this.variableSort).map(e => {
        return { label: e.name, value: e.code };
      });
    },
    changeSourceData(data) {
      this.formData.oper = "";
      this.formData.matchValue = "";
      this.formData.rangeMatchValue1 = "";
      this.formData.rangeMatchValue2 = "";
      if (!data) {
        this.sourceDataType = "string";
        this.options = [];
        this.operList = [];
        this.products = [];
        this.findProduct = false;
        return;
      }

      if (this.formData.fieldType === "2" || this.formData.fieldType === "3") {
        this.sourceDataType = "float";
        this.operList = getOperList(this.sourceDataType, this.dicts.oper);
        this.options = [];
        this.products = [];
        this.findProduct = false;
        return;
      }

      const sourceData = this.sourceData.find(e => e.fieldId === data);
      // 标记查找产品
      this.findProduct =
        sourceData.fieldId === "kbcProductId" ||
        sourceData.fieldId === "mainProductId";
      this.products = [];

      if (sourceData.isDic === "1") {
        this.options = sourceData.dicList;
      } else {
        this.options = [];
      }
      this.sourceDataType = sourceData ? sourceData.dataType : "";
      this.operList = getOperList(this.sourceDataType, this.dicts.oper);
    },
    changeOper() {
      this.formData.matchValue = "";
      this.formData.rangeMatchValue1 = "";
      this.formData.rangeMatchValue2 = "";
      this.products = [];
    },
    async changeFieldType() {
      this.formData.fieldId = "";
      this.changeSourceData();
      await this.$nextTick();
      this.$refs["form"].clearValidate();
    },
    async checkForm() {
      try {
        await this.$refs["form"].validate();
        if (this.formData.oper === "range") {
          if (
            this.formData.rangeMatchValue1 === "" ||
            this.formData.rangeMatchValue2 === ""
          ) {
            this.$message.error(`${this.title}:匹配值不能为空！`);
            return false;
          }
        } else {
          if (Array.isArray(this.formData.matchValue)) {
            if (this.formData.matchValue.length === 0) {
              this.$message.error(`${this.title}:匹配值不能为空！`);
              return false;
            }
          } else {
            if (this.formData.matchValue === "") {
              this.$message.error(`${this.title}:匹配值不能为空！`);
              return false;
            }
          }
        }
        return true;
      } catch (error) {
        console.log(error);
        this.$message.error(`${this.title}:录入项有错误,请检查！`);
        return false;
      }
    },
    async submit() {
      const check = await this.checkForm();
      if (check) {
        const ret = {
          fieldType: this.formData.fieldType,
          fieldId: this.formData.fieldId,
          oper: this.formData.oper,
          matchValue: ""
        };
        if (!this.isFirst) {
          ret.ruleRelation = this.formData.ruleRelation;
        }
        if (Array.isArray(this.formData.matchValue)) {
          ret.matchValue = this.formData.matchValue.join(",");
        } else {
          ret.matchValue = this.formData.matchValue;
        }

        if (this.formData.oper === "range") {
          ret.matchValue = JSON.stringify({
            [this.formData.rangeOper1]: this.formData.rangeMatchValue1,
            [this.formData.rangeOper2]: this.formData.rangeMatchValue2
          });
        }

        return ret;
      }
      return null;
    },
    findProductSuccess(data) {
      if (
        this.formData.oper === "contain" ||
        this.formData.oper === "notcontain"
      ) {
        const p = this.products.map(e => e.unifiedProductId);
        const filterD = data.filter(e => !p.includes(e.unifiedProductId));
        this.products.push(...filterD);
      } else {
        this.products = data;
      }
      this.formData.matchValue = this.products
        .map(e => e.unifiedProductId)
        .join(",");
    },
    delProduct(index) {
      this.products.splice(index, 1);
      this.formData.matchValue.splice(index, 1);
    }
  }
};
</script>
