<template>
  <DtPopup
    :isShow.sync="showPopup"
    @close="handelClose"
    @confirm="handleConfirm"
    confirmBeforeClose
    title="查找产品"
    width="800px"
  >
    <SearchForm
      :searchForm="initParam"
      :searchFormTemp="searchFormTemp"
      @normalSearch="normalSearch"
      @normalResetQuery="normalResetQuery"
    />
    <el-checkbox-group v-model="productIds" style="width: 100%" v-if="isList">
      <el-table
        :data="tableData"
        stripe
        v-hover
        class="dt-table"
        style="width: 100%"
      >
        <el-table-column label="选择" align="center" width="70">
          <template #default="scope">
            <el-checkbox :label="scope.row.unifiedProductId">
              &nbsp;
            </el-checkbox>
          </template>
        </el-table-column>
        <el-table-column
          prop="unifiedProductId"
          label="产品编码"
          align="center"
          min-width="140"
        />

        <el-table-column
          prop="productName"
          label="产品名称"
          align="center"
          min-width="120"
        />
      </el-table>
    </el-checkbox-group>
    <el-radio-group v-model="productId" style="width: 100%" v-else>
      <el-table
        :data="tableData"
        stripe
        v-hover
        class="dt-table"
        style="width: 100%"
      >
        <el-table-column label="选择" align="center" width="70">
          <template #default="scope">
            <el-radio :label="scope.row.unifiedProductId">
              &nbsp;
            </el-radio>
          </template>
        </el-table-column>
        <el-table-column
          prop="unifiedProductId"
          label="产品编码"
          align="center"
          min-width="140"
        />

        <el-table-column
          prop="productName"
          label="产品名称"
          align="center"
          min-width="120"
        />
      </el-table>
    </el-radio-group>
    <Pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :pageData="initParam"
      :total="total"
    ></Pagination>
  </DtPopup>
</template>

<script>
import DtPopup from "@/components/layouts/DtPopup";
import SearchForm from "@/components/layouts/SearchForm";
import Pagination from "@/components/layouts/Pagination";
import { agreementSelectProduct } from "@/api/gpRule";
export default {
  name: "FindProduct",
  components: {
    DtPopup,
    SearchForm,
    Pagination
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    isList: {
      type: Boolean,
      default: true
    },
    selectd:{
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      showPopup: false,
      tableData: [],
      initParam: {
        param: {
          unifiedProductId: "",
          productName: ""
        },
        pageSize: 5,
        pageNum: 1
      },
      total: 0,
      searchFormTemp: [
        {
          label: "产品编码",
          name: "unifiedProductId",
          type: "input",
          placeholder: "请输入产品编码"
        },
        {
          label: "产品名称",
          name: "productName",
          type: "input",
          placeholder: "请输入产品名称"
        }
      ],
      productIds: [],
      productId: ""
    };
  },
  watch: {
    show(val) {
      this.showPopup = val;
      if (val) {
        this.normalResetQuery();
      }
    }
  },
  methods: {
    handelClose() {
      this.showPopup = false;
      this.$emit("update:show", false);
    },
    handleConfirm() {
      const products = this.isList
        ? this.tableData.filter(e =>
            this.productIds.includes(e.unifiedProductId)
          )
        : [this.tableData.find(e => this.productId === e.unifiedProductId)];
      this.$emit("success", products);
      this.handelClose();
    },
    async getData() {
      this.productIds = [];
      this.productId = [];
      const data = {
        ...this.initParam
      };
      const companyId = this.$route.query.companyId || "";
      const agreementId = this.$route.query.agreementId || "";
      const entrustId = this.$route.query.entrustId || "";
      data.param = {
        ...data.param,
        agreementId,
        companyId,
        entrustId,
        gpProductIds:[...this.selectd]
      };

      const res = await agreementSelectProduct(data);
      if (res) {
        this.total = Number(res.total);
        this.tableData = res.list;
      }
    },
    handleSizeChange(val) {
      this.initParam.pageSize = val;
      this.getData();
    },
    handleCurrentChange(val) {
      this.initParam.pageNum = val;
      this.getData();
    },
    normalResetQuery() {
      this.initParam.pageNum = 1;
      this.initParam.pageSize = 5;
      this.initParam.param = {};
      this.getData();
    },
    normalSearch(data) {
      this.initParam.pageNum = 1;
      this.initParam.pageSize = 5;
      this.initParam = this._.cloneDeep(data);
      this.getData();
    }
  }
};
</script>

<style></style>
