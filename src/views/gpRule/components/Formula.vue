<template>
  <div class="formula">
    <div class="formula-title">
      <div class="formula-lable">运算公式</div>
      <div class="formula-btn">
        <el-button
          type="text"
          icon="iconfont icondt8"
          v-for="item in dicts.formulaType"
          :key="item.dicItemCode"
          :class="{ 'symbol-btn': item.dicItemCode === '99' }"
          @click="addFormula(item.dicItemCode)"
          :disabled="disableVariable(item.dicItemCode)"
        >
          {{ item.dicItemName }}
        </el-button>
      </div>
    </div>
    <div class="formula-content">
      <div
        class="formula-box"
        v-for="(item, idx) in formula"
        :key="item.key"
        :class="{
          'formula-box-tow': hasAggr && (item.type === '1' || item.type === '3')
        }"
      >
        <div
          class="formula-box-top"
          :style="{
            background:
              item.type === '99'
                ? '#67c23a'
                : $store.state.layoutStore.themeObj.color
          }"
        >
          <span>{{ formulaTypeVal(item.type) }}</span>
          <i class="el-icon-remove" @click="delFormula(idx)"></i>
        </div>
        <div class="formula-box-b" v-if="item.type === '4'">
          <el-input-number
            v-model="item.input"
            :controls="false"
          ></el-input-number>
        </div>
        <div class="formula-box-b" v-else>
          <el-select
            size="small"
            v-model="item.aggr"
            v-if="hasAggr && (item.type === '1' || item.type === '3')"
          >
            <el-option
              :label="aggr.dicItemName"
              :value="aggr.dicItemCode"
              v-for="aggr in dicts.aggr"
              :key="aggr.dicItemCode"
            ></el-option>
          </el-select>
          <el-select size="small" v-model="item.input">
            <el-option
              :label="option.label"
              :value="option.value"
              v-for="option in formulaOptions(item.type)"
              :key="option.value"
            ></el-option>
          </el-select>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { createNamespacedHelpers } from "vuex";
const { mapGetters } = createNamespacedHelpers("gpRule");
export default {
  name: "gprule-formula",
  props: {
    // 是否需要选择聚合方式
    hasAggr: {
      type: Boolean,
      default: false
    },
    variableSort: {
      type: Number,
      default: null
    },
    title: {
      type: String,
      default: ""
    },
    dicts: {
      type: Object,
      default: () => {
        return { calculator: [], aggr: [], formulaType: [] };
      }
    },
    sourceData: {
      type: Array,
      default: () => []
    },
    configData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      formula: []
    };
  },
  computed: {
    ...mapGetters(["getVariableList"])
  },
  created() {
    if (this.configData && Array.isArray(this.configData)) {
      this.formula = this.configData.map(e => {
        return {
          ...e,
          aggr: e.aggr || ""
        };
      });
    }
  },
  methods: {
    disableVariable(type) {
      if (type === "2" || type === "3") {
        return this.getVariableList(type, this.variableSort).length <= 0;
      } else {
        return false;
      }
    },
    variableList(type) {
      if (type === "2" || type === "3") {
        return this.getVariableList(type, this.variableSort).map(e => {
          return { label: e.name, value: e.code };
        });
      } else {
        return [];
      }
    },
    formulaOptions(type) {
      let option = [];
      switch (type) {
        case "1": // 数据源
          option = this.sourceData.map(e => {
            return { label: e.name, value: e.fieldId };
          });
          break;
        case "2": // 全局变量
          option = this.variableList(type);
          break;
        case "3": // 普通变量
          option = this.variableList(type);
          break;
        case "99": // 运算符
          option = this.dicts.calculator.map(e => {
            return { label: e.dicItemName, value: e.dicItemCode };
          });
          break;
        default:
          break;
      }
      return option;
    },
    addFormula(type) {
      const data = {
        key: Date.now(),
        type,
        input: ""
      };
      if (this.hasAggr && (type === "1" || type === "3")) data.aggr = "";
      this.formula.push(data);
    },
    delFormula(index) {
      this.formula.splice(index, 1);
    },
    formulaTypeVal(val) {
      const type = this.dicts.formulaType.find(e => e.dicItemCode === val);
      return type ? type.dicItemName : "";
    },
    submit() {
      const retData = [];
      for (let index = 0; index < this.formula.length; index++) {
        const formula = this.formula[index];
        if (!formula.input) {
          this.$message.error(`${this.title}:有录入项未输入,请检查！`);
          return null;
        }
        if (this.hasAggr && (formula.type === "1" || formula.type === "3")) {
          if (!formula.aggr) {
            this.$message.error(`${this.title}:有录入项未输入,请检查！`);
            return null;
          }
        }
        const data = {
          sort: index + 1,
          type: formula.type,
          input: formula.input
        };
        if (this.hasAggr && (formula.type === "1" || formula.type === "3")) {
          data.aggr = formula.aggr;
        }
        retData.push(data);
      }
      return retData;
    }
  }
};
</script>

<style lang="less">
.formula {
  margin-bottom: 10px;
  &-lable {
    width: 160px;
    text-align: left;
    font-size: 14px;
    // color: #606266;
    line-height: 36px;
    float: left;
    font-weight: 600;
  }
  .symbol-btn {
    color: #67c23a;
  }
  .symbol-btn:hover {
    color: #67c23a;
  }
  &-title {
    display: flex;
    align-items: center;
  }
  &-content {
    margin-left: 160px;
    width: 430px;
    .formula-box {
      display: inline-flex;
      flex-direction: column;
      width: 116px;
      margin-right: 8px;
      margin-bottom: 8px;
      &-top {
        color: #ffffff;
        font-size: 12px;
        display: flex;
        box-sizing: border-box;
        padding: 6px 8px;
        justify-content: space-between;
        line-height: 12px;
        i {
          color: #f56c6c;
          cursor: pointer;
        }
      }
      &-b {
        border: 1px solid #dcdfe6;
        border-radius: 0 0 4px 4px;
        box-sizing: border-box;
        padding: 8px;
        justify-content: space-evenly;
        display: flex;
        .el-select {
          width: 90px;
        }
        .el-input {
          width: 90px;
        }
      }
    }
    .formula-box-tow {
      width: 214px;
    }
  }
}
</style>
