<template>
  <div class="group-aggregation-container">
    <!-- 基本信息 -->
    <div class="config-section">
      <div class="section-header">
        <div class="header-left">
          <div class="section-icon">
            <i class="el-icon-setting"></i>
          </div>
          <div class="section-title">
            <h4>基本信息</h4>
            <span class="section-subtitle">配置节点基本信息</span>
          </div>
        </div>
      </div>
      
      <div class="section-content">
        <el-form
        label-width="80px"
        label-position="right"
        :model="editForm1"
        class="editUserForm"
        ref="editUserForm"
        :inline="false"
      >
        <el-form-item label="名称" prop="name">
          <el-input
            style="width: 200px;"
            v-model="editForm1.name"
            placeholder="请输入名称"
            size="small"
            @change="blurName"
          />
        </el-form-item>
      </el-form>
      </div>
    </div>

    <!-- 分组字段区域 -->
    <div class="table-section">
      <div class="section-header">
        <div class="header-left">
          <div class="section-icon">
            <i class="el-icon-files"></i>
          </div>
          <div class="section-title">
            <h4>分组字段</h4>
            <span class="section-subtitle">配置数据分组规则</span>
          </div>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-plus"
            @click="handleAdd1()"
            class="add-btn"
          >
            新增分组字段
          </el-button>
        </div>
      </div>
      
      <div class="table-wrapper">
        <el-table 
          :data="fileNameList1" 
          class="modern-table"
          stripe 
          v-hover
          empty-text="暂无分组字段数据"
        >
          <el-table-column align="center" prop="fieldDesc" label="分组字段名">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.fieldDesc"
                clearable
                placeholder="请选择字段"
                filterable
                size="small"
                @change="blur1(scope.row.fieldDesc, scope.$index)"
              >
                <el-option
                  v-for="option in optionList"
                  :key="option.fieldName"
                  :label="option.fieldDesc"
                  :value="option.fieldName"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="fieldName" label="分组字段" width="200">
            <template slot-scope="scope">
              <el-input
                :disabled="true"
                v-model="scope.row.fieldName"
                placeholder="自动生成"
                size="small"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" >
            <template slot-scope="scope">
              <el-button
                type="text"
                icon="el-icon-delete"
                @click="deleteRow1(scope.$index)"
                class="delete-btn"
                size="small"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 默认组合函数区域 -->
    <div class="table-section">
      <div class="section-header">
        <div class="header-left">
          <div class="section-icon default">
            <i class="el-icon-s-operation"></i>
          </div>
          <div class="section-title">
            <h4>默认组合函数</h4>
            <span class="section-subtitle">配置默认聚合函数规则</span>
          </div>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-plus"
            @click="handleAdd()"
            class="add-btn"
          >
            新增默认组合函数
          </el-button>
        </div>
      </div>
      
      <div class="table-wrapper">
        <el-table
          :data="fileNameList"
          class="modern-table"
          stripe
          v-hover
          empty-text="暂无默认组合函数数据"
        >
          <el-table-column align="center" prop="function" label="组合函数">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.function"
                clearable
                placeholder="请选择函数"
                filterable
                size="small"
                @change="blurGroup1(scope.row.function, scope.$index)"
              >
                <el-option
                  v-for="option in optionList1"
                  :key="option.id"
                  :label="option.name"
                  :value="option.id"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="aggrFieldName" label="运算字段">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.aggrFieldName"
                clearable
                placeholder="请选择字段"
                filterable
                size="small"
                @change="blurGroup2(scope.row.aggrFieldName, scope.$index)"
              >
                <el-option
                  v-for="option in optionList"
                  :key="option.fieldName"
                  :label="option.fieldDesc"
                  :value="option.fieldName"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="fieldDesc" label="字段名">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.fieldDesc"
                @change="blurGroup3(scope.row.fieldDesc, scope.$index)"
                placeholder="请输入字段名"
                size="small"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" prop="fieldName" label="字段">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.fieldName"
                @change="blurGroup4(scope.row.fieldName, scope.$index)"
                placeholder="请输入字段"
                size="small"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                icon="el-icon-delete"
                @click="deleteRow(scope.$index)"
                class="delete-btn"
                size="small"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 自定义组合函数区域 -->
    <div class="table-section">
      <div class="section-header">
        <div class="header-left">
          <div class="section-icon custom">
            <i class="el-icon-edit-outline"></i>
          </div>
          <div class="section-title">
            <h4>自定义组合函数</h4>
            <span class="section-subtitle">配置自定义聚合函数规则</span>
          </div>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-plus"
            @click="handleAdd2()"
            class="add-btn"
          >
            新增自定义组合函数
          </el-button>
        </div>
      </div>
      
      <div class="table-wrapper">
        <el-table
          :data="fileNameList2"
          class="modern-table"
          stripe
          v-hover
          empty-text="暂无自定义组合函数数据"
        >
          <el-table-column align="center" prop="fieldDesc" label="字段名" width="300">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.fieldDesc"
                @change="blurcust2(scope.row.fieldDesc, scope.$index)"
                placeholder="请输入字段名"
                size="small"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" prop="fieldName" label="字段" width="300">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.fieldName"
                @change="blurcust3(scope.row.fieldName, scope.$index)"
                placeholder="请输入字段"
                size="small"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" prop="formula" label="聚合条件">
            <template slot-scope="scope">
              <!-- <el-tooltip :content="scope.row.formula?scope.row.formula:'请输入聚合条件，支持变量引用'" placement="top"> -->
                <FormulaTextarea
                  v-model="scope.row.formula"
                  :options="optionList"
                  placeholder="请输入聚合条件"
                  @input="val => blurcust1(val, scope.$index)"
                />
              <!-- </el-tooltip> -->
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                icon="el-icon-delete"
                @click="deleteRow2(scope.$index)"
                class="delete-btn"
                size="small"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import ClickOutside from "vue-click-outside";
import DtPopup from "@/components/layouts/DtPopup";
import FormulaTextarea from "./FormulaTextarea.vue";
export default {
  name: "groupAggregation",
  components: { DtPopup,FormulaTextarea },
  directives: {
    ClickOutside
  },
  props: {
    typeTitle: String,
    idKey: String,
    dataItem: Object,
    idName: String,
    groupOutPutList: Array
  },
  watch: {
    groupOutPutList(newValue, oldValue) {
      console.log(newValue, oldValue);
      this.optionList = newValue;
    },
    dataItem(newValue, oldValue) {
      console.log(newValue, oldValue);
      // if (newValue != oldValue) {
      this.nodeObjList = newValue;
      this.changeName();
      // }
    },
    idKey(newVal, oldVal) {
      if (newVal != oldVal) {
        // this.changeName();
      }
    }
  },
  data() {
    return {
      editForm1: {
        name: ""
      },
      fileNameList: [
        // {
        //   fieldName: "",
        //   fieldDesc: "",
        //   aggrFieldName: "",
        //   function: "1"
        // }
      ],
      fileNameList2: [
        // {
        //   fieldName: "",
        //   fieldDesc: "",
        //   function: "1"
        // }
      ],
      fileNameList1: [
        // {
        //   fieldDesc: "",
        //   fieldName: ""
        // }
      ],
      showAdd: false,
      addForm: {
        name: "",
        value: ""
      },
      optionList: [],
      optionList1: [
        {
          name: "SUM",
          id: "SUM"
        },
        {
          name: "AVG",
          id: "AVG"
        },
        {
          name: "MAX",
          id: "MAX"
        },
        {
          name: "MIN",
          id: "MIN"
        },
        {
          name: "COUNT",
          id: "COUNT"
        }
      ],
      editForm: {
        channel_name: "",
        channel_name1: ""
      },
      nodeObjList: []
    };
  },
  created() {
    this.editForm1.name = this.idName;
    this.nodeObjList = this.dataItem;
    console.log("输出节点", this.nodeObjList);
    this.changeName();
    this.optionList = this.groupOutPutList;
    console.log("输出节点optionList", this.optionList);
    console.log(this.groupOutPutList);
  },
  mounted() {
    // this.nodeObjList = this.$store.state.templateEngine.nodeItemList;
    // // console.log(this.nodeObjList)
    // if (this.nodeObjList.length > 0) {
    //   this.changeName();
    // }
  },
  methods: {
    blurName(val) {
      console.log(val);
      let data = {
        name: "strName",
        key: this.idKey,
        value: val
      };
      this.$emit("changefiled", data);
    },
    changeName() {
      if (this.nodeObjList.nodeConfig.groupFields) {
        this.fileNameList1 = this.nodeObjList.nodeConfig.groupFields
          ? this.nodeObjList.nodeConfig.groupFields
          : [];
      }
      if (this.nodeObjList.nodeConfig.group) {
        this.fileNameList = this.nodeObjList.nodeConfig.group.default
          ? this.nodeObjList.nodeConfig.group.default
          : [];
      }
      if (this.nodeObjList.nodeConfig.group) {
        this.fileNameList2 = this.nodeObjList.nodeConfig.group.custom
          ? this.nodeObjList.nodeConfig.group.custom
          : [];
      }
      // // // 设置输出字段
      // let arr = Array.from(this.nodeObjList);
      // console.log(arr);
      // for (let i = 0; i < arr.length; i++) {
      //   // console.log(i, arr[i].nodeId, this.idKey)
      //   if (arr[i].nodeId == this.idKey) {
      //     if (arr[i].nodeConfig.groupFields) {
      //       this.fileNameList1 = arr[i].nodeConfig.groupFields;
      //     }
      //     if (arr[i].nodeConfig.group.default) {
      //       this.fileNameList = arr[i].nodeConfig.group.default;
      //     }
      //     if (arr[i].nodeConfig.group.custom) {
      //       this.fileNameList2 = arr[i].nodeConfig.group.custom;
      //     }
      //     // // 设置输出字段
      //   }
      // }
    },
    //聚合字段
    blur1(val, index) {
      console.log(val);
      let fieldDesc = "";
      // let fieldName = "";
      for (let i = 0; i < this.optionList.length; i++) {
        if (this.optionList[i].fieldName == val) {
          fieldDesc = this.optionList[i].fieldDesc;
        }
      }
      this.fileNameList1[index].fieldName = val;
      this.fileNameList1[index].fieldDesc = fieldDesc;
      // this.getDisabledList(val);
      let data = {
        name: "fieldName",
        key: this.idKey,
        value: this.fileNameList1[index],
        index: index,
        type: 0 //聚合字段
      };
      console.log(this.fileNameList1);
      this.$emit("outGroupChange", data);
    },
    // 组合函数应用
    blurGroup1(val, index) {
      let data = {
        name: "function",
        key: this.idKey,
        value: val,
        index: index,
        type: 1 //组合函数应用
      };
      this.fileNameList[index].function = val;
      this.$emit("outGroupChange", data);
    },
    blurGroup2(val, index) {
      // this.getDisabledList(val);
      let data = {
        name: "aggrFieldName",
        key: this.idKey,
        value: val,
        index: index,
        type: 1
      };
      this.fileNameList[index].aggrFieldName = val;
      this.$emit("outGroupChange", data);
    },
    blurGroup3(val, index) {
      let data = {
        name: "fieldDesc",
        key: this.idKey,
        value: val,
        index: index,
        type: 1
      };
      this.fileNameList[index].fieldDesc = val;
      this.$emit("outGroupChange", data);
    },
    blurGroup4(val, index) {
      let data = {
        name: "fieldName",
        key: this.idKey,
        value: val,
        index: index,
        type: 1 //原始字段输出
      };
      this.fileNameList[index].fieldName = val;
      this.$emit("outGroupChange", data);
    },
    blurcust1(val, index) {
      // this.getDisabledList(val);
      let data = {
        name: "formula",
        key: this.idKey,
        value: val,
        index: index,
        type: 2
      };
      this.fileNameList2[index].formula = val;
      this.$emit("outGroupChange", data);
    },
    blurcust2(val, index) {
      let data = {
        name: "fieldDesc",
        key: this.idKey,
        value: val,
        index: index,
        type: 2
      };
      this.fileNameList2[index].fieldDesc = val;
      this.$emit("outGroupChange", data);
    },
    blurcust3(val, index) {
      let data = {
        name: "fieldName",
        key: this.idKey,
        value: val,
        index: index,
        type: 2 //原始字段输出
      };
      this.fileNameList2[index].fieldName = val;
      this.$emit("outGroupChange", data);
    },
    handleAdd() {
      this.fileNameList.push({
        fieldDesc: "",
        fieldName: "",
        aggrFieldName: "",
        function: ""
      });
      let data = {
        value: this.fileNameList,
        type: 1,
        key: this.idKey
      };
      this.$emit("outGroupAdd", data);
    },
    handleAdd1() {
      this.fileNameList1.push({
        fieldDesc: "",
        fieldName: ""
        // isIndex: "0"
      });
      let data = {
        value: this.fileNameList1,
        type: 0,
        key: this.idKey
      };
      console.log(data);
      this.$emit("outGroupAdd", data);
    },
    handleAdd2() {
      this.fileNameList2.push({
        fieldDesc: "",
        fieldName: "",
        formula: ""
      });
      let data = {
        value: this.fileNameList2,
        type: 2,
        key: this.idKey
      };
      this.$emit("outGroupAdd", data);
    },
    // 删除行
    deleteRow(index) {
      console.log("index", index);
      // this.$confirm("确定删除？", "提示").then(() => {
      for (let i = 0; i < this.fileNameList.length; i++) {
        if (index == i) {
          this.fileNameList.splice(i, 1);
          this.$message({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          let data = {
            index: index,
            type: 1,
            key: this.idKey,
            value: this.fileNameList
          };
          this.$emit("outGroupDel", data);
        }
      }
      // });
    },
    // 删除行
    deleteRow1(index) {
      // console.log('index', index)
      // this.$confirm("确定删除？", "提示").then(() => {
      for (let i = 0; i < this.fileNameList1.length; i++) {
        if (index == i) {
          this.fileNameList1.splice(i, 1);
          this.$message({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });

          let data = {
            index: index,
            type: 0,
            key: this.idKey,
            value: this.fileNameList1
          };
          this.$emit("outGroupDel", data);
        }
      }
      // });
    },
    // 删除行
    deleteRow2(index) {
      // console.log('index', index)
      // this.$confirm("确定删除？", "提示").then(() => {
      for (let i = 0; i < this.fileNameList2.length; i++) {
        if (index === i) {
          this.fileNameList2.splice(i, 1);
          this.$message({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          let data = {
            index: index,
            type: 2,
            key: this.idKey,
            value: this.fileNameList2
          };
          this.$emit("outGroupDel", data);
        }
      }
      // });
    },
    onContextmenu() {
      this.$contextmenu({
        items: [
          {
            label: "删除",
            disabled: false,
            icon: "",
            onClick: () => {
              this.deleteNode();
            }
          }
        ],
        event,
        customClass: "custom-class",
        zIndex: 9999,
        minWidth: 180
      });
    },
    getGoalName(val, index, num) {
      console.log(val, index, num);
    },
    // 新增切片
    async toAddForm() {
      const data = {
        ...this.addForm
      };
      //   await api.bsChannelAdd(data);
      this.showAdd = false;
      //   this.$refs["addUserForm"].resetFields();
    },
    //关闭新增弹窗
    closePopup() {
      this.showAdd = false;
      this.addForm = this.$options.data().addForm;
      this.$nextTick(() => {
        this.$refs.addUserForm.clearValidate();
      });
    },
    handleTool() {
      this.title = "新增分组字段";
      this.showAdd = true;
    },
    setNotActive() {
      if (!window.event.ctrlKey) {
        this.isSelected = false;
      }
      if (!this.isActive) {
        return;
      }
      this.$emit("changeLineState", this.node, false);
      this.isActive = false;
    }
  }
};
</script>

<style lang="less" scoped>
@import './shared-styles.less';

.group-aggregation-container {
  padding: 60px;
  .basic-info-section {
    .config-section();
   
    
    .editUserForm {
      .unified-form();
    }
  }

  .table-section {
    .config-section();
  }
}
</style>
