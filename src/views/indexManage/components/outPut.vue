<template>
  <div class="output-container">
    <!-- 原始输出区域 -->
    <div class="table-section">
      <div class="section-header">
        <div class="header-left">
          <div class="section-icon">
            <i class="el-icon-document-copy"></i>
          </div>
          <div class="section-title">
            <h4>原始输出</h4>
            <span class="section-subtitle">配置原始字段输出规则</span>
          </div>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-plus"
            @click="handleAdd1()"
            class="add-btn"
          >
            新增原始输出
          </el-button>
        </div>
      </div>
      
      <div class="table-wrapper">
        <el-table
          :data="fileNameList1"
          class="modern-table"
          stripe
          v-hover
          empty-text="暂无原始输出数据"
        >
          <el-table-column
            v-if="
              optionIndexList &&
                optionIndexList.length > 0 &&
                toolTypeStatus == '2'
            "
            align="center"
            width="300"
            prop="fieldDesc"
            label="节点"
          >
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.fieldNodeId"
                clearable
                placeholder="请选择节点"
                filterable
                style="width:100%"
                size="small"
                @change="blur(scope.row.fieldNodeId, scope.$index)"
              >
                <el-option
                  v-for="(option, index) in optionIndexList"
                  :key="index"
                  :label="option.nodeName"
                  :value="
                    typeof scope.row.fieldNodeId == 'number'
                      ? Number(option.nodeId)
                      : String(option.nodeId)
                  "
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            v-if="toolTypeStatus == '2'"
            prop="fieldDesc"
            label="输出字段名"
          >
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.fieldDesc"
                clearable
                placeholder="请选择字段"
                filterable
                size="small"
                @change="blur1(scope.row.fieldDesc, scope.$index)"
              >
                <el-option
                  v-for="(option, index) in getOptionAndList(scope.row.fieldNodeId)"
                  :key="index"
                  :label="option.fieldDesc"
                  :value="option.fieldName"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            v-if="toolTypeStatus != '2'"
            prop="fieldDesc"
            label="输出字段名"
          >
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.fieldDesc"
                clearable
                placeholder="请选择字段"
                filterable
                size="small"
                @change="blur1(scope.row.fieldDesc, scope.$index)"
              >
                <el-option
                  v-for="(option, index) in optionList"
                  :key="index"
                  :label="option.fieldDesc"
                  :value="option.fieldName"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="fieldName" label="输出字段">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.fieldName"
                :disabled="true"
                placeholder="自动生成"
                size="small"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" prop="isIndex" label="索引"  width="70">
            <template slot-scope="scope">
              <el-checkbox
                v-model="scope.row.isIndex"
                :true-label="'1'"
                :false-label="'0'"
                @change="blur3(scope.row.isIndex, scope.$index)"
                size="small"
              ></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                icon="el-icon-delete"
                @click="deleteRow1(scope.$index)"
                class="delete-btn"
                size="small"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 自定义输出区域 -->
    <div class="table-section">
      <div class="section-header">
        <div class="header-left">
          <div class="section-icon custom">
            <i class="el-icon-edit-outline"></i>
          </div>
          <div class="section-title">
            <h4>自定义输出</h4>
            <span class="section-subtitle">配置自定义字段输出规则</span>
          </div>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-plus"
            @click="handleAdd()"
            class="add-btn"
          >
            新增自定义输出
          </el-button>
        </div>
      </div>
      
      <div class="table-wrapper">
        <el-table
          :data="fileNameList"
          class="modern-table"
          stripe
          v-hover
          empty-text="暂无自定义输出数据"
        >
          <el-table-column align="center" prop="fileName1" label="输出字段名">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.fieldDesc"
                @change="blurSelf1(scope.row.fieldDesc, scope.$index)"
                placeholder="请输入字段名"
                size="small"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" prop="fileName2" label="输出字段">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.fieldName"
                @change="blurSelf2(scope.row.fieldName, scope.$index)"
                placeholder="请输入字段"
                size="small"
              />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            width="900px"
            prop="fileName3"
            label="输出公式"
          >
            <template slot-scope="scope">
              <!-- <el-tooltip
                effect="dark"
                :content="scope.row.formula?scope.row.formula:'请输入输出公式，支持变量引用'"
                placement="top"
              > -->
                <FormulaTextarea
                  v-model="scope.row.formula"
                  :options="optionList"
                  placeholder="请输入内容"
                  @input="val => blurSelf3(val, scope.$index)"
                />
              <!-- </el-tooltip> -->
            </template>
          </el-table-column>
          <el-table-column align="center" prop="isIndex" label="索引" width="70">
            <template slot-scope="scope">
              <el-checkbox
                v-model="scope.row.isIndex"
                :true-label="'1'"
                :false-label="'0'"
                @change="blurSelf4(scope.row.isIndex, scope.$index)"
                size="small"
              ></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                icon="el-icon-delete"
                @click="deleteRow(scope.$index)"
                class="delete-btn"
                size="small"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import ClickOutside from "vue-click-outside";
import FormulaTextarea from "./FormulaTextarea.vue";
export default {
  name: "outPut",
  directives: {
    ClickOutside
  },
  components: {
    FormulaTextarea
  },
  props: {
    idKey: String,
    idName: String,
    dataItem: Object,
    outPutList: Array,
    toolType: String,
    childList: Array
  },
  watch: {
    dataItem(newValue, oldValue) {
      this.nodeObjList = newValue;
    },
    idName(newVal, oldVal) {
      this.editForm.name = newVal;
    },
    idKey(newVal, oldVal) {
      if (newVal != oldVal) {
        this.nodeObjList = newVal;
      }
    },
    childList(newValue, oldValue) {
      // console.log(newValue, oldValue);
      this.optionIndexList = newValue;
    },
    outPutList: {
      handler(newValue, oldValue) {
        this.optionList = newValue;
        // 只在 toolTypeStatus == '2' 时初始化
        if (this.toolTypeStatus === '2' && this.fileNameList1 && this.fileNameList1.length > 0) {
          this.batchInitOptionAndList();
        }
      },
      immediate: true
    },
    toolType(newVal, oldVal) {
      this.toolTypeStatus = newVal;
      // toolTypeStatus 变化时也批量初始化
      if (newVal === '2' && this.fileNameList1 && this.fileNameList1.length > 0) {
        this.batchInitOptionAndList();
      }
    }
  },
  data() {
    return {
      fileNameList: [
        // {
        //   fieldDesc: "",
        //   fieldName: "",
        //   isIndex: "0",
        //   formula: ""
        // }
      ],
      fileNameList1: [
        // {
        //   fieldDesc: "",
        //   fieldName: "",
        //   isIndex: "0"
        // }
      ],
      showAdd: false,
      addForm: {
        name: "",
        value: ""
      },
      optionIndexList: [],
      optionAndList: [], //关联项optionList
      optionList: [],
      optionList2: [
        {
          name: "保单号",
          id: "0"
        },
        {
          name: "产品ID",
          id: "1"
        },
        {
          name: "产品名称",
          id: "2"
        },
        {
          name: "佣金",
          id: "3"
        },
        {
          name: "保费",
          id: "4"
        }
      ],
      optionList3: [
        {
          name: "保单号",
          id: "0"
        },
        {
          name: "产品ID",
          id: "1"
        },
        {
          name: "产品名称",
          id: "2"
        },
        {
          name: "佣金",
          id: "3"
        },
        {
          name: "保费",
          id: "4"
        }
      ],
      optionList5: [
        {
          name: "否",
          id: "0"
        },
        {
          name: "是",
          id: "1"
        }
      ],
      editForm: {
        channel_name: "",
        channel_name1: ""
      },
      toolTypeStatus: "0"
    };
  },
  created() {
    this.nodeObjList = this.dataItem;
    this.optionIndexList = this.childList;
    console.log(this.childList, this.toolType);
    if (this.nodeObjList.output.fields.default) {
      // 初始化时移除optionAndList字段
      this.fileNameList1 = this.nodeObjList.output.fields.default.map(item => {
        const cleanItem = { ...item };
        delete cleanItem.optionAndList;
        return cleanItem;
      });
    }
    if (this.nodeObjList.output.fields.custom) {
      this.fileNameList = this.nodeObjList.output.fields.custom;
    }
    this.optionList = this.outPutList;
    console.log(this.optionList);
    this.toolTypeStatus = this.toolType;
    console.log("toolTypeStatus", this.toolTypeStatus);

    // 只在 toolTypeStatus == '2' 时初始化
    this.$nextTick(() => {
      if (this.toolTypeStatus === '2' && this.fileNameList1 && this.fileNameList1.length > 0) {
        this.batchInitOptionAndList();
      }
    });
  },
  methods: {
    // 批量初始化，减少多次 set
  batchInitOptionAndList() {
    if (!this.optionList || this.optionList.length === 0) return;
    // 只遍历一次 optionList，按 fieldNodeId 分组
    const nodeIdMap = {};
    this.optionList.forEach(opt => {
      if (!nodeIdMap[opt.fieldNodeId]) nodeIdMap[opt.fieldNodeId] = [];
      nodeIdMap[opt.fieldNodeId].push({
        fieldName: opt.fieldName,
        fieldDesc: opt.fieldDesc,
        fieldNodeId: opt.fieldNodeId
      });
    });
    // 批量赋值 - 注意：optionAndList字段仅用于前端展示，提交时会被移除
    this.fileNameList1.forEach((item, index) => {
      if (item.fieldNodeId && nodeIdMap[item.fieldNodeId]) {
        // 只 set 有 fieldNodeId 的
        this.$set(this.fileNameList1[index], 'optionAndList', nodeIdMap[item.fieldNodeId]);
      } else {
        this.$set(this.fileNameList1[index], 'optionAndList', []);
      }
    });
  },
    createFilter(queryString) {
      console.log(queryString);
      return restaurant => {
        // console.log(queryString.toLowerCase().indexOf('$'));
        // if(queryString.toLowerCase().indexOf('$')==0){
        //   return (-1)
        // }
        return (
          restaurant.fieldName
            .toLowerCase()
            .indexOf(queryString.toLowerCase()) === 0
        );
      };
    },
    showAnchor() {
      this.mouseEnter = true;
    },
    hideAnchor() {
      this.mouseEnter = false;
    },
    // 设置disabled
    getDisabledList(val) {
      for (let i = 0; i < this.optionList.length; i++) {
        this.optionList[i].disabled = true;
      }
    },
    //原始字段编辑存储
    blur(val, index) {
      console.log(val, this.optionList);
      // 找出所有 fieldNodeId 匹配的 option
      const matchedOptions = this.optionList.filter(opt => opt.fieldNodeId == val);
      this.$set(this.fileNameList1[index], 'optionAndList', matchedOptions);

      // 检查是否有option的fieldDesc和当前fieldDesc一致
      const hasSameFieldDesc = matchedOptions.some(
        opt => opt.fieldDesc === this.fileNameList1[index].fieldDesc
      );

      if (!hasSameFieldDesc) {
        this.fileNameList1[index].fieldDesc = "";
      }

      // 提交时去掉 optionAndList 字段
      const submitData = { ...this.fileNameList1[index] };
      delete submitData.optionAndList;

      this.$emit("outPutChange", {
        name: "fieldNodeId",
        key: this.idKey,
        value: submitData,
        index: index,
        type: 0 //原始字段输出
      });
    },
    //原始字段编辑存储
    blur1(val, index) {
      let fieldName = "";
      let fieldNodeId = "";
      let fieldDesc = "";
      let fieldDesc1 = "";
      for (let i = 0; i < this.optionList.length; i++) {
        if (this.optionList[i].fieldName == val) {
          fieldName = this.optionList[i].fieldName;
          fieldDesc = this.optionList[i].fieldDesc;
          fieldDesc1 = this.optionList[i].fieldDesc1
            ? this.optionList[i].fieldDesc1
            : this.optionList[i].fieldDesc;
          fieldNodeId = this.optionList[i].fieldNodeId
            ? this.optionList[i].fieldNodeId
            : "";
        }
      }
      //基本信息输出
      if (this.toolTypeStatus == "0") {
        // console.log('111')
        this.fileNameList1[index].fieldName = val;
        this.fileNameList1[index].fieldDesc = fieldDesc;
        // 移除optionAndList字段后再传递数据
        const cleanItem = { ...this.fileNameList1[index] };
        delete cleanItem.optionAndList;
        let data = {
          name: "fieldName",
          key: this.idKey,
          value: cleanItem,
          index: index,
          type: 0 //原始字段输出
        };
        this.$emit("outPutChange", data);
      } else {
        this.fileNameList1[index].fieldName = val;
        this.fileNameList1[index].fieldDesc = fieldDesc1;
        // this.fileNameList1[index].fieldDesc1 = fieldDesc1;
        this.fileNameList1[index].fieldNodeId = fieldNodeId;
        let  dataobj={
          fieldName:val,
          fieldDesc:fieldDesc1,
          fieldNodeId:fieldNodeId
        }
        // 移除optionAndList字段后再传递数据
        const cleanItem = { ...this.fileNameList1[index] };
        delete cleanItem.optionAndList;
        let data = {
          name: "fieldNodeId",
          key: this.idKey,
          value: cleanItem,
          index: index,
          type: 0 //原始字段输出
        };
        console.log(data);
        this.$emit("outPutChange", data);
      }
    },
    blur3(val, index) {
      let data = {
        name: "isIndex",
        key: this.idKey,
        value: val,
        index: index,
        type: 0 //原始字段输出
      };
      this.$emit("outPutChange", data);
    },
    // 自定义字段编辑存储
    blurSelf1(val, index) {
      console.log(val, index);
      let data = {
        name: "fieldDesc",
        key: this.idKey,
        value: val,
        index: index,
        type: 1 //自定义字段输出
      };
      this.$emit("outPutChange", data);
    },
    blurSelf2(val, index) {
      console.log(val, index);
      let data = {
        name: "fieldName",
        key: this.idKey,
        value: val,
        index: index,
        type: 1 //自定义字段输出
      };
      this.$emit("outPutChange", data);
    },
    blurSelf3(val, index) {
      console.log(val, index);
      let data = {
        name: "formula",
        key: this.idKey,
        value: val,
        index: index,
        type: 1 //自定义字段输出
      };
      this.$emit("outPutChange", data);
    },
    blurSelf4(val, index) {
      let data = {
        name: "isIndex",
        key: this.idKey,
        value: val,
        index: index,
        type: 1 //自定义字段输出
      };
      this.$emit("outPutChange", data);
    },
    handleAdd1() {
      this.fileNameList1.push({
        fieldDesc: "",
        fieldName: "",
        isIndex: "0",
        fieldNodeId: "",
        optionAndList: [] // 初始化空的optionAndList
      });
      // 移除optionAndList字段后再传递数据
      const cleanData = this.fileNameList1.map(item => {
        const cleanItem = { ...item };
        delete cleanItem.optionAndList;
        return cleanItem;
      });
      let data = {
        value: cleanData,
        type: 0,
        key: this.idKey
      };
      console.log(data);
      this.$emit("outPutAdd", data);
    },
    handleAdd() {
      this.fileNameList.push({
        fieldDesc: "",
        fieldName: "",
        isIndex: "0",
        formula: ""
      });
      let data = {
        value: this.fileNameList,
        type: 1,
        key: this.idKey
      };
      this.$emit("outPutAdd", data);
    },
    // 删除行
    deleteRow(index) {
      console.log("index", index);
      // this.$confirm("确定删除？", "提示").then(() => {
      for (let i = 0; i < this.fileNameList.length; i++) {
        if (index === i) {
          this.fileNameList.splice(i, 1);
          this.$message({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          let data = {
            index: index,
            type: 1,
            key: this.idKey,
            value: this.fileNameList
          };
          this.$emit("outPutDel", data);
        }
      }
      console.log(this.fileNameList);
      // });
    },
    // 删除行
    deleteRow1(index) {
      // console.log('index', index)
      // this.$confirm("确定删除？", "提示").then(() => {
      for (let i = 0; i < this.fileNameList1.length; i++) {
        if (index === i) {
          this.fileNameList1.splice(i, 1);
          this.$message({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          // 移除optionAndList字段后再传递数据
      const cleanData = this.fileNameList1.map(item => {
        const cleanItem = { ...item };
        delete cleanItem.optionAndList;
        return cleanItem;
      });
      let data = {
        index: index,
        type: 0,
        key: this.idKey,
        value: cleanData
      };

      this.$emit("outPutDel", data);
        }
      }
      // });
    },
    onContextmenu() {
      this.$contextmenu({
        items: [
          {
            label: "删除",
            disabled: false,
            icon: "",
            onClick: () => {
              this.deleteNode();
            }
          }
        ],
        event,
        customClass: "custom-class",
        zIndex: 9999,
        minWidth: 180
      });
    },
    // 新增切片
    async toAddForm() {
      const data = {
        ...this.addForm
      };
      //   await api.bsChannelAdd(data);
      this.showAdd = false;
      //   this.$refs["addUserForm"].resetFields();
    },
    //关闭新增弹窗
    closePopup() {
      this.showAdd = false;
      this.addForm = this.$options.data().addForm;
      this.$nextTick(() => {
        this.$refs.addUserForm.clearValidate();
      });
    },
    handleTool() {
      this.title = "新增分组字段";
      this.showAdd = true;
    },
    setNotActive() {
      if (!window.event.ctrlKey) {
        this.isSelected = false;
      }
      if (!this.isActive) {
        return;
      }
      this.$emit("changeLineState", this.node, false);
      this.isActive = false;
    },
    getOptionAndList(fieldNodeId) {
      if (!fieldNodeId) return [];
      return this.optionList.filter(opt => opt.fieldNodeId == fieldNodeId);
    }
  }
};
</script>

<style lang="less" scoped>
.output-container {
  .table-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #f7ecdd;
    overflow: hidden;
    transition: all 0.3s ease;
    margin-bottom: 20px;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      background: #fbf6ee;
      border-bottom: 1px solid #f7ecdd;

      .header-left {
        display: flex;
        align-items: center;
        gap: 12px;

        .section-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;

          &.custom {
            i {
              color: #D7A256;
            }
          }

          i {
            font-size: 18px;
            color: #D7A256;
          }
        }

        .section-title {
          h4 {
            margin: 0 0 4px 0;
            font-size: 14px;
            font-weight: 600;
            color: #2c3e50;
          }

          .section-subtitle {
            font-size: 13px;
            color: #909399;
            font-weight: 400;
          }
        }
      }

      .header-actions {
        .add-btn {
          height: 32px;
          padding: 0 16px;
          border-radius: 6px;
          font-weight: 500;
          font-size: 14px;
          transition: all 0.3s ease;
          background: #D7A256;
          color: white;
          border: none;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(215, 162, 86, 0.3);
            background: #E6B366;
          }

          i {
            margin-right: 4px;
          }
        }
      }
    }

    .table-wrapper {
      .modern-table {
        border-radius: 0;
        overflow: hidden;
        border: none;
        font-size: 14px;

        /deep/ .el-table__header-wrapper {
          .el-table__header {
            th {
              background: #fbf6ee;
              font-weight: 600;
              font-size: 13px;
              color: #2c3e50;
              border-bottom: 2px solid #f7ecdd;
              padding: 12px 0;

              .cell {
                padding: 0 16px;
              }
            }
          }
        }

        /deep/ .el-table__body-wrapper {
          .el-table__body {
            tr {
              transition: all 0.2s ease;

              &:hover {
                background: #f0f9ff !important;
              }

              td {
                padding: 12px 0;
                border-bottom: 1px solid #f5f7fa;

                .cell {
                  padding: 0 16px;
                }

                .el-select {
                  .el-input__inner {
                    border-radius: 6px;
                    border-color: #dcdfe6;
                    height: 32px;
                    line-height: 32px;
                    transition: all 0.3s ease;

                    &:focus {
                      border-color: #D7A256;
                      box-shadow: 0 0 0 2px rgba(128, 128, 128, 0.1);
                    }

                    &:hover {
                      border-color: #c0c4cc;
                    }
                  }
                }

                .el-input {
                  .el-input__inner {
                    border-radius: 6px;
                    border-color: #dcdfe6;
                    height: 32px;
                    line-height: 32px;
                    transition: all 0.3s ease;

                    &:focus {
                      border-color: #D7A256;
                      box-shadow: 0 0 0 2px rgba(128, 128, 128, 0.1);
                    }

                    &:hover {
                      border-color: #c0c4cc;
                    }

                    &:disabled {
                      background: #f5f7fa;
                      color: #909399;
                    }
                  }
                }

                .delete-btn {
                  color: #f56c6c;
                  font-size: 14px;
                  padding: 4px 8px;
                  border-radius: 4px;
                  transition: all 0.3s ease;
                  background: transparent;
                  border: none;

                  &:hover {
                    background: #fef0f0;
                    color: #f56c6c;
                    transform: scale(1.05);
                  }
                }
              }
            }
          }
        }

        /deep/ .el-table__empty-block {
          background: #fafbfc;
          
          .el-table__empty-text {
            color: #909399;
            font-size: 14px;
            padding: 40px 0;
          }
        }
      }
    }
  }
}
</style>
