<template>
  <div class="filter-config-container">
    <!-- 基本信息 -->
    <div class="config-section">
      <div class="section-header">
        <div class="header-left">
          <div class="section-icon">
            <i class="el-icon-setting"></i>
          </div>
          <div class="section-title">
            <h4>基本信息</h4>
            <span class="section-subtitle">配置节点基本信息</span>
          </div>
        </div>
      </div>
      
      <div class="section-content">
        <el-form
          label-width="80px"
          label-position="right"
          :model="editForm"
          class="editUserForm"
          ref="editUserForm"
          :inline="false"
        >
          <el-form-item label="名称" prop="name">
            <el-input
              v-model="editForm.name"
              auto-complete="off"
              @change="blurName"
              placeholder="请输入节点名称"
              class="modern-input"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 默认过滤条件 -->
    <div class="config-section">
      <div class="section-header">
        <div class="header-left">
          <div class="section-icon default">
            <i class="el-icon-search"></i>
          </div>
          <div class="section-title">
            <h4>默认过滤条件</h4>
            <span class="section-subtitle">配置默认数据过滤规则</span>
          </div>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-plus"
            @click="handleAdd()"
            class="add-btn"
          >
            新增默认过滤条件
          </el-button>
        </div>
      </div>
      
      <div class="table-wrapper">
        <el-table
          :data="fileNameList"
          stripe
          v-hover
          class="modern-table"
          empty-text="暂无默认过滤条件数据"
        >
          <el-table-column align="center" prop="left" label="字段名">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.left"
                clearable
                placeholder="请选择字段"
                filterable
                size="small"
                @change="blurGroup1(scope.row.left, scope.$index)"
                class="modern-select"
              >
                <el-option
                  v-for="option in optionList"
                  :key="option.fieldDesc"
                  :label="option.fieldDesc"
                  :value="option.fieldName"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="left" label="字段">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.left"
                clearable
                placeholder="自动生成"
                filterable
                size="small"
                disabled
                class="modern-select"
              >
                <el-option
                  v-for="option in optionList"
                  :key="option.fieldDesc"
                  :label="option.fieldName"
                  :value="option.fieldName"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="operator" label="匹配符">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.operator"
                clearable
                placeholder="请选择匹配符"
                filterable
                size="small"
                @change="blurGroup2(scope.row.operator, scope.$index)"
                class="modern-select"
              >
                <el-option
                  v-for="option in optionList2"
                  :key="option.id"
                  :label="option.name"
                  :value="option.id"
                />
              </el-select>
            </template>
          </el-table-column>
          <!-- <el-table-column align="center" prop="right" label="公式">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.right"
                placeholder="请输入公式"
                size="small"
                @change="blurGroup3(scope.row.right, scope.$index)"
                class="modern-input"
              />
            </template>
          </el-table-column> -->
          <el-table-column align="center" prop="right" label="公式">
            <template slot-scope="scope">
              <!-- <el-tooltip :content="scope.row.right?scope.row.right:'请输入公式，支持变量引用'" placement="top"> -->
                <FormulaTextarea
                  v-model="scope.row.right"
                  :options="optionList"
                  @input="val => blurGroup3(val, scope.$index)"
                />
              <!-- </el-tooltip> -->
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                icon="el-icon-delete"
                @click="deleteRow(scope.$index)"
                class="delete-btn"
                size="small"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 自定义过滤条件 -->
    <div class="config-section">
      <div class="section-header">
        <div class="header-left">
          <div class="section-icon custom">
            <i class="el-icon-edit"></i>
          </div>
          <div class="section-title">
            <h4>自定义过滤条件</h4>
            <span class="section-subtitle">配置自定义数据过滤规则</span>
          </div>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-plus"
            @click="handleAdd1()"
            class="add-btn"
          >
            新增自定义过滤条件
          </el-button>
        </div>
      </div>
      
      <div class="table-wrapper">
        <el-table
          :data="fileNameList1"
          stripe
          v-hover
          class="modern-table"
          empty-text="暂无自定义过滤条件数据"
        >
          <el-table-column align="center" prop="formula" label="自定义条件">
            <template slot-scope="scope">
              <!-- <el-tooltip :content="scope.row.formula?scope.row.formula:'请输入自定义条件，支持变量引用'" placement="top"> -->
                <FormulaTextarea
                  v-model="scope.row.formula"
                  :options="optionList"
                  @input="val => blurcustom1(val, scope.$index)"
                />
              <!-- </el-tooltip> -->
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                icon="el-icon-delete"
                @click="deleteRow1(scope.$index)"
                class="delete-btn"
                size="small"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import ClickOutside from "vue-click-outside";
import FormulaTextarea from './FormulaTextarea';
export default {
  name: "groupAggregation",
  components: {
    FormulaTextarea
  },
  directives: {
    ClickOutside
  },
  props: {
    typeTitle: String,
    filterOutPutList: Array,
    idKey: String,
    dataItem: Object,
    idName: String
  },
  watch: {
    filterOutPutList(newValue, oldValue) {
      console.log(newValue, oldValue);
      this.optionList = newValue;
    },
    dataItem(newValue, oldValue) {
      // if (newValue != oldValue) {
      this.nodeObjList = newValue;
      // this.changeName();
      // }
    },
    idName(newVal, oldVal) {
      console.log(newVal);
      this.editForm.name = newVal;
    },
    idKey(newVal, oldVal) {
      if (newVal != oldVal) {
        // this.changeName();
      }
    }
  },
  data() {
    return {
      editForm: {
        name: ""
      },
      fileNameList: [
        // {
        //   operator: "",
        //   left: "", //字段名
        //   right: "" //比较值
        // }
      ],
      fileNameList1: [
        // {
        //   formula: "" //自定义条件
        // }
      ],
      showAdd: false,
      addForm: {
        name: "",
        value: ""
      },
      optionList: [],
      optionList2: [
        {
          name: ">",
          id: ">"
        },
        {
          name: "=",
          id: "="
        },
        {
          name: ">=",
          id: ">="
        },
        {
          name: "<",
          id: "<"
        },
        {
          name: "<=",
          id: "<="
        }
      ]
    };
  },
  created() {
    this.editForm.name = this.idName;
    this.nodeObjList = this.dataItem;
    if (this.nodeObjList.nodeConfig.filter.default) {
      this.fileNameList = this.nodeObjList.nodeConfig.filter.default;
    }
    if (this.nodeObjList.nodeConfig.filter.custom) {
      this.fileNameList1 = this.nodeObjList.nodeConfig.filter.custom;
    }
    this.optionList = this.filterOutPutList;
  },
  mounted() {
    // this.nodeObjList = this.$store.state.templateEngine.nodeItemList;
    // console.log(this.nodeObjList);
    // if (this.nodeObjList.length > 0) {
    //   this.changeName();
    // }
  },
  methods: {
    blurName(val) {
      console.log(val);
      let data = {
        name: "strName",
        key: this.idKey,
        value: val
      };
      this.$emit("changefiled", data);
    },
    // changeName() {
    //   // 设置输出字段
    //   let arr = Array.from(this.nodeObjList);
    //   console.log(arr);
    //   for (let i = 0; i < arr.length; i++) {
    //     console.log(i, arr[i].nodeId, this.idKey);
    //     if (arr[i].nodeId == this.idKey) {
    //       this.$nextTick(() => {
    //         this.fileNameList = arr[i].output.fields.default;
    //         console.log(this.fileNameList);
    //       });
    //     }
    //   }
    // },
    // 组合函数应用
    blurGroup1(val, index) {
      let left = "";
      for (let i = 0; i < this.optionList.length; i++) {
        if (this.optionList[i].fieldDesc == val) {
          left = this.optionList[i].fromNodeId;
        }
      }
      let data = {
        name: "left",
        key: this.idKey,
        value: val,
        value1: left,
        index: index,
        type: 0
      };
      this.fileNameList[index].fieldName = val;
      this.$emit("outFilterChange", data);
    },
    blurGroup2(val, index) {
      let data = {
        name: "operator",
        key: this.idKey,
        value: val,
        index: index,
        type: 0
      };
      this.fileNameList[index].operator = val;
      this.$emit("outFilterChange", data);
    },
    blurGroup3(val, index) {
      let data = {
        name: "right",
        key: this.idKey,
        value: val,
        index: index,
        type: 0
      };
      this.fileNameList[index].right = val;
      this.$emit("outFilterChange", data);
    },
    blurcustom1(val, index) {
      let data = {
        name: "formula",
        key: this.idKey,
        value: val,
        index: index,
        type: 1
      };
      this.fileNameList1[index].formula = val;
      this.$emit("outFilterChange", data);
    },
    // allFieldsList() {
    //   // 获取所有子节点的所有输出字段，并带上节点信息
    //   let fields = [];
    //   if (this.optionList) {
    //     this.optionList.forEach(node => {
    //       if (node.output && node.output.fields && node.output.fields.default) {
    //         node.output.fields.default.forEach(field => {
    //           fields.push({
    //             nodeId: node.nodeId,
    //             nodeName: node.nodeName,
    //             ...field // fieldName, fieldDesc等
    //           });
    //         });
    //       }
    //     });
    //   }
    //   return fields;
    // },
    handleAdd() {
      this.fileNameList.push({
        left: "",
        operator: "",
        right: ""
      });
      let data = {
        value: this.fileNameList,
        type: 0,
        key: this.idKey
      };
      this.$emit("outFilterAdd", data);
    },
    handleAdd1() {
      this.fileNameList1.push({
        formula: ""
      });
      let data = {
        value: this.fileNameList1,
        type: 1,
        key: this.idKey
      };
      this.$emit("outFilterAdd", data);
    },
    // 删除行
    deleteRow(index) {
      // console.log('index', index)
      // this.$confirm("确定删除？", "提示").then(() => {
      for (let i = 0; i < this.fileNameList.length; i++) {
        if (index === i) {
          this.fileNameList.splice(i, 1);
          this.$message({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          let data = {
            index: index,
            type: 0,
            key: this.idKey,
            value: this.fileNameList
          };
          this.$emit("outFilterDel", data);
        }
      }
      // });
    },
    // 删除行
    deleteRow1(index) {
      // console.log('index', index)
      // this.$confirm("确定删除？", "提示").then(() => {
      for (let i = 0; i < this.fileNameList1.length; i++) {
        if (index === i) {
          this.fileNameList1.splice(i, 1);
          this.$message({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000
          });
          let data = {
            index: index,
            type: 1,
            key: this.idKey,
            value: this.fileNameList1
          };
          this.$emit("outFilterDel", data);
        }
      }
      // });
    },
    onContextmenu() {
      this.$contextmenu({
        items: [
          {
            label: "删除",
            disabled: false,
            icon: "",
            onClick: () => {
              this.deleteNode();
            }
          }
        ],
        event,
        customClass: "custom-class",
        zIndex: 9999,
        minWidth: 180
      });
    },
    getGoalName(val, index, num) {
      console.log(val, index, num);
    },
    // 新增切片
    async toAddForm() {
      const data = {
        ...this.addForm
      };
      //   await api.bsChannelAdd(data);
      this.showAdd = false;
      //   this.$refs["addUserForm"].resetFields();
    },
    //关闭新增弹窗
    closePopup() {
      this.showAdd = false;
      this.addForm = this.$options.data().addForm;
      this.$nextTick(() => {
        this.$refs.addUserForm.clearValidate();
      });
    },
    handleTool() {
      this.title = "新增分组字段";
      this.showAdd = true;
    },
    setNotActive() {
      if (!window.event.ctrlKey) {
        this.isSelected = false;
      }
      if (!this.isActive) {
        return;
      }
      this.$emit("changeLineState", this.node, false);
      this.isActive = false;
    }
  }
};
</script>

<style lang="less" scoped>
@import './shared-styles.less';

.filter-config-container {
  .config-section {
    .config-section();
    
    .section-icon {
      &.custom {
        i {
          color: @primary-color;
        }
      }
    }
  }
}
</style>
