const nodeTypeList = [
  {
    logImg: "el-icon-files",
    nodeName: "分组聚合",
    nodeType: "tool",
    type: "1",
    nodeId: "",
    positionLeft: "",
    positionTop: "",
    nodeTypeSub: "group", // # 子节点类型
    // children: [],
    childIds: [],
    nodeConfig: {
      groupFields: [
        // {
        //   fieldName: "", //# 字段别名
        //   fieldDesc: "" //# 字段中文名
        // }
      ], // # 分组字段
      group: {
        default: [
          // {
          //   aggrFieldName: "", //# 聚合字段
          //   fieldName: "", //# 字段别名
          //   fieldDesc: "", //# 字段中文名
          //   function: "1" // # 聚合函数
          // }
        ],
        custom: [
          // {
          //   fieldName: "",
          //   fieldDesc: "",
          //   formula: ""
          // }
        ]
      }
    },
    output: {
      fields: {
        default: [
          // {
          //   fieldDesc: "",
          //   fieldName: "",
          //   isIndex: "",
          //   fieldNodeId: ""
          // }
        ],
        custom: [
          // {
          //   fieldDesc: "",
          //   fieldName: "",
          //   isIndex: "",
          //   fieldNodeId: "",
          //   formula: ""
          // }
        ]
      }
    }
  },
  {
    logImg: "el-icon-link",
    nodeName: "数据连接",
    nodeType: "tool",
    positionLeft: "",
    positionTop: "",
    nodeId: "",
    nodeTypeSub: "connect", // # 子节点类型
    // children: [],
    childIds:[],
    type: "2",
    nodeConfig: {
      connectCondition: [
        // {
        //   fromFieldName: "", // # from连接字段
        //   fromNodeId: "", //from节点id
        //   toFieldName: "", //to连接字段
        //   toNodeId: "", //to节点id
        //   connectType: "" //# 连接方式
        // }
      ],
      custom: [
        // {
        //   connectType: "",//# 连接方式
        //   formula: "",//# 关联条件
        //   fromNodeId: "", //from节点id
        //   toNodeId: ""//to节点id
        // }
      ]
    },
    output: {
      fields: {
        default: [
          // {
          //   fieldDesc: "",
          //   fieldName: "",
          //   isIndex: "",
          //   fieldNodeId: ""
          // }
        ],
        custom: [
          // {
          //   fieldDesc: "",
          //   fieldName: "",
          //   isIndex: "",
          //   fieldNodeId: "",
          //   formula: ""
          // }
        ]
      }
    }
  },
  {
    logImg: "el-icon-switch-button",
    nodeName: "条件函数",
    type: "3",
    nodeType: "tool", //# 节点类型
    nodeTypeSub: "case", // # 子节点类型
    positionLeft: "",
    positionTop: "",
    nodeId: "",
    // children: [],
    childIds:[],
    nodeConfig: {
      handle: [
        // {
        //   default: [
        //     // {
        //     //   operator: ">",
        //     //   left: "",
        //     //   right: "",
        //     //   caseValue: ""
        //     // }
        //   ],
        //   custom: [
        //     // {
        //     //   formula: "",
        //     //   caseValue: ""
        //     // }
        //   ],
        //   fieldName: "", //# 字段别名
        //   fieldDesc: "" //# 字段中文名
        // }
      ]
    },
    output: {
      fields: {
        default: [
          // {
          //   fieldDesc: "",
          //   fieldName: "",
          //   isIndex: "",
          //   fieldNodeId: ""
          // }
        ],
        custom: [
          // {
          //   fieldDesc: "",
          //   fieldName: "",
          //   isIndex: "",
          //   fieldNodeId: "",
          //   formula: ""
          // }
        ]
      }
    }
  },
  {
    logImg: "el-icon-search",
    nodeName: "数据过滤",
    nodeType: "tool",
    type: "4",
    positionLeft: "",
    positionTop: "",
    nodeId: "",
    // children: [],
    childIds:[],
    nodeTypeSub: "filter", // # 子节点类型
    nodeConfig: {
      filter: {
        default: [
          // {
          //   operator: "",
          //   left: "", //字段名
          //   right: "" //比较值
          // }
        ],
        custom: [
          // {
          //   formula: ""
          // }
        ]
      }
    },
    output: {
      fields: {
        default: [
          // {
          //   fieldDesc: "",
          //   fieldName: "",
          //   isIndex: "",
          //   fieldNodeId: ""
          // }
        ],
        custom: [
          // {
          //   fieldDesc: "",
          //   fieldName: "",
          //   isIndex: "",
          //   fieldNodeId: "",
          //   formula: ""
          // }
        ]
      }
    }
  }
];

console.log(nodeTypeList);

export { nodeTypeList };
