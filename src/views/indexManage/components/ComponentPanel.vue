<template>
  <div class="component-drawer" :class="{ open: visible }">
    <!-- 抽屉遮罩已移除 -->
    
    <div class="drawer-panel">
      <!-- 抽屉头部 -->
      <div class="drawer-header">
        <div class="header-title">
          <div class="title-icon">
            <i class="el-icon-s-grid"></i>
          </div>
          <div class="title-text">
            <h3>组件面板</h3>
            <span class="subtitle">拖拽组件到画布</span>
          </div>
        </div>
        <div class="header-actions">
          <el-button
            type="text"
            size="mini"
            @click="toggleAllSections"
            class="toggle-all-btn"
            :title="allSectionsExpanded ? '全部收起' : '全部展开'"
          >
            <i :class="allSectionsExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
            <span>{{ allSectionsExpanded ? '收起' : '展开' }}</span>
          </el-button>
          <div class="close-btn" @click="handleClose">
            <i class="el-icon-close"></i>
          </div>
        </div>
      </div>

      <!-- 抽屉内容 -->
      <div class="drawer-body">
        <!-- 输入数据区域 -->
        <div class="menu-section input-section">
          <div class="menu-title" @click="toggleInputData">
            <div class="title-content">
              <span class="title-text">输入数据</span>
              <div class="component-count">{{ inputDataList.length }}</div>
            </div>
            <div class="expand-control">
              <i 
                :class="['expand-icon', inputDataExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"
                @click.stop="toggleInputData"
              ></i>
            </div>
          </div>
          <div class="menu-content" :class="{ 'collapsed': !inputDataExpanded }">
            <div v-if="inputDataList.length == 0" class="empty-menu">
              <div class="empty-icon">
                <i class="el-icon-info"></i>
              </div>
              <p>暂无数据源</p>
            </div>
            <div class="component-list" v-else>
              <div
                draggable="true"
                class="component-item input-data-item"
                v-for="(item, index) in inputDataList"
                @dragstart="handleDragStart($event, item, item.type)"
                @dragend="handleDragEnd"
                @mouseenter="handleItemHover(item, true)"
                @mouseleave="handleItemHover(item, false)"
                :key="index"
              >
                <div class="item-icon">
                  <i :class="item.logImg" />
                  <div class="icon-glow"></div>
                </div>
                <div class="item-content">
                  <span class="component-name">{{ item.nodeName }}</span>
                  <span class="component-desc">数据源组件</span>
                </div>
                <div class="drag-indicator">
                  <i class="el-icon-d-arrow-right"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 数据处理区域 -->
        <div class="menu-section process-section">
          <div class="menu-title" @click="toggleProcessData">
            <div class="title-content">
              <span class="title-text">数据处理</span>
              <div class="component-count">{{ processDataList.length }}</div>
            </div>
            <div class="expand-control">
              <i 
                :class="['expand-icon', processDataExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"
                @click.stop="toggleProcessData"
              ></i>
            </div>
          </div>
          <div class="menu-content" :class="{ 'collapsed': !processDataExpanded }">
            <div v-if="processDataList.length == 0" class="empty-menu">
              <div class="empty-icon">
                <i class="el-icon-info"></i>
              </div>
              <p>暂无处理组件</p>
            </div>
            <div class="component-list" v-else>
              <div
                draggable="true"
                class="component-item process-data-item"
                :class="getProcessItemClass(item.type)"
                v-for="(item, index) in processDataList"
                @dragstart="handleDragStart($event, item, item.type)"
                @dragend="handleDragEnd"
                @mouseenter="handleItemHover(item, true)"
                @mouseleave="handleItemHover(item, false)"
                :key="'process-' + index"
              >
                <div class="item-icon">
                  <i :class="item.logImg" />
                  <div class="icon-glow"></div>
                </div>
                <div class="item-content">
                  <span class="component-name">{{ item.nodeName }}</span>
                  <span class="component-desc">{{ getComponentDesc(item.type) }}</span>
                </div>
                <div class="drag-indicator">
                  <i class="el-icon-d-arrow-right"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 抽屉底部 -->
      <div class="drawer-footer">
        <div class="footer-stats">
          <span class="stats-item">
            <i class="el-icon-data-line"></i>
            总计 {{ inputDataList.length + processDataList.length }} 个组件
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ComponentPanel",
  props: {
    // 是否显示面板
    visible: {
      type: Boolean,
      default: false
    },
    // 输入数据列表
    inputDataList: {
      type: Array,
      default: () => []
    },
    // 处理数据列表
    processDataList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 展开/收起状态
      inputDataExpanded: true,
      processDataExpanded: true
    };
  },
  computed: {
    // 是否全部展开
    allSectionsExpanded() {
      return this.inputDataExpanded && this.processDataExpanded;
    }
  },
  created() {
    // 从localStorage读取用户展开/收起偏好
    const savedInputExpanded = localStorage.getItem('inputDataExpanded');
    const savedProcessExpanded = localStorage.getItem('processDataExpanded');
    if (savedInputExpanded !== null) {
      this.inputDataExpanded = JSON.parse(savedInputExpanded);
    }
    if (savedProcessExpanded !== null) {
      this.processDataExpanded = JSON.parse(savedProcessExpanded);
    }
  },
  methods: {
    // 关闭面板
    handleClose() {
      this.$emit('close');
    },

    // 切换输入数据展开状态
    toggleInputData() {
      this.inputDataExpanded = !this.inputDataExpanded;
      localStorage.setItem('inputDataExpanded', JSON.stringify(this.inputDataExpanded));
      this.$emit('section-toggle', 'input', this.inputDataExpanded);
    },

    // 切换处理数据展开状态
    toggleProcessData() {
      this.processDataExpanded = !this.processDataExpanded;
      localStorage.setItem('processDataExpanded', JSON.stringify(this.processDataExpanded));
      this.$emit('section-toggle', 'process', this.processDataExpanded);
    },

    // 切换所有区域展开状态
    toggleAllSections() {
      const newState = !this.allSectionsExpanded;
      this.inputDataExpanded = newState;
      this.processDataExpanded = newState;
      localStorage.setItem('inputDataExpanded', JSON.stringify(newState));
      localStorage.setItem('processDataExpanded', JSON.stringify(newState));
      this.$emit('section-toggle', 'all', newState);
    },

    // 处理拖拽开始
    handleDragStart(event, item, type) {
      this.$emit('drag-start', event, item, type);
    },

    // 处理拖拽结束
    handleDragEnd() {
      // 拖拽结束后的清理工作
      document.body.style.cursor = '';
      document.body.classList.remove('dragging', 'drag-over');
      this.$emit('drag-end');
    },

    // 处理组件项悬停效果
    handleItemHover(item, isHover) {
      this.$emit('item-hover', item, isHover);
    },

    // 获取处理组件的样式类
    getProcessItemClass(type) {
      const classMap = {
        '1': 'group-item',     // 分组聚合
        '2': 'connect-item',   // 数据连接
        '3': 'function-item',  // 条件函数
        '4': 'filter-item'     // 数据过滤
      };
      return classMap[type] || '';
    },

    // 获取组件描述
    getComponentDesc(type) {
      const descMap = {
        '1': '数据聚合处理',
        '2': '多源数据连接',
        '3': '条件逻辑处理',
        '4': '数据筛选过滤'
      };
      return descMap[type] || '数据处理组件';
    }
  }
};
</script>

<style lang="less" scoped>
.component-drawer {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  z-index: 1000;
  pointer-events: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &.open {
    pointer-events: auto;
    
    .drawer-panel {
      transform: translateX(0);
    }
  }
  
  .drawer-panel {
    position: absolute;
    top: 0;
    left: 0;
    width: 320px;
    height: 100%;
    background: white;
    box-shadow: 2px 0 12px rgba(0, 0, 0, 0.15);
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    border-right: 1px solid #f7ecdd;
    
    .drawer-header {
      padding: 20px 24px;
      border-bottom: 1px solid #f7ecdd;
      background: linear-gradient(135deg, #fbf6ee 0%, #faf8f2 100%);
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-shrink: 0;
      
      .header-title {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .title-icon {
          width: 36px;
          height: 36px;
          background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 2px 8px rgba(215, 162, 86, 0.3);
          
          i {
            color: white;
            font-size: 18px;
          }
        }
        
        .title-text {
          h3 {
            margin: 0 0 2px 0;
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
          }
          
          .subtitle {
            font-size: 12px;
            color: #7f8c8d;
            font-weight: 400;
          }
        }
      }
      
      .header-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .toggle-all-btn {
          height: 28px;
          padding: 0 8px;
          font-size: 12px;
          color: #606266;
          border: 1px solid #e8dcc0;
          border-radius: 4px;
          
          &:hover {
            color: #D7A256;
            border-color: #D7A256;
          }
          
          i {
            margin-right: 4px;
            font-size: 12px;
          }
        }
        
        .close-btn {
          width: 28px;
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          border-radius: 4px;
          color: #909399;
          transition: all 0.3s ease;
          
          &:hover {
            background: rgba(215, 162, 86, 0.1);
            color: #D7A256;
          }
          
          i {
            font-size: 14px;
          }
        }
      }
    }
    
    .drawer-body {
      flex: 1;
      overflow-y: auto;
      padding: 0;
      
      .menu-section {
        
        &:last-child {
          border-bottom: none;
        }
        &:first-child{
          .menu-title {
            border-top: none;
          }
        }
        
        
        .menu-title {
          padding: 16px 20px;
          cursor: pointer;
          transition: all 0.3s ease;
          user-select: none;
          border-top: 1px solid #f7ecdd;
          border-bottom: 1px solid #f7ecdd;
          display: flex;
          align-items: center;
          justify-content: space-between;
          &:hover {
            background: linear-gradient(135deg, rgba(215, 162, 86, 0.1) 0%, rgba(215, 162, 86, 0.1) 100%);
          }
          
          .title-content {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
            
            .title-text {
              font-size: 14px;
              font-weight: 600;
              color: #2c3e50;
            }
            
            .component-count {
              padding: 2px 6px;
              border-radius: 10px;
              font-size: 11px;
              font-weight: 600;
              min-width: 18px;
              text-align: center;
              background: rgba(215, 162, 86, 0.1);
              color: #D7A256;
              border: 1px solid rgba(215, 162, 86, 0.2);
            }
          }
          
          .expand-control {
            .expand-icon {
              width: 24px;
              height: 24px;
              border-radius: 6px;
              background: rgba(144, 147, 153, 0.1);
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12px;
              color: #909399;
              transition: all 0.3s ease;
              cursor: pointer;
              
              &:hover {
                background: rgba(215, 162, 86, 0.1);
                color: #D7A256;
                transform: scale(1.05);
              }
            }
          }
        }
        
        .menu-content {
          max-height: none;
          overflow-y: auto;
          transition: all 0.3s ease;
          
          &.collapsed {
            max-height: 0;
            overflow: hidden;
          }
          
          .empty-menu {
            padding: 40px 20px;
            text-align: center;
            color: #909399;
            
            .empty-icon {
              width: 48px;
              height: 48px;
              margin: 0 auto 12px;
              background: rgba(144, 147, 153, 0.1);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              
              i {
                font-size: 20px;
                color: #c0c4cc;
              }
            }
            
            p {
              margin: 0;
              font-size: 13px;
            }
          }
          
          .component-list {
            overflow-x: hidden;
            
            .component-item {
              display: flex;
              align-items: center;
              padding: 12px 20px;
              cursor: grab;
              transition: all 0.3s ease;
              background: white;
              
              &:hover {
                background: #fcfaf7;
                border-color: rgba(215, 162, 86, 0.3);
                transform: translateX(4px);
                box-shadow: 0 2px 8px rgba(215, 162, 86, 0.15);
              }
              
              &:active {
                cursor: grabbing;
                transform: scale(0.98);
              }
              
              .item-icon {
                position: relative;
                width: 32px;
                height: 32px;
                border-radius: 6px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 12px;
                background: rgba(215, 162, 86, 0.1);
                border: 1px solid rgba(215, 162, 86, 0.2);
                
                i {
                  font-size: 16px;
                  color: #D7A256;
                }
                
                .icon-glow {
                  position: absolute;
                  top: 0;
                  left: 0;
                  right: 0;
                  bottom: 0;
                  border-radius: 6px;
                  background: linear-gradient(135deg, rgba(215, 162, 86, 0.1) 0%, rgba(215, 162, 86, 0.05) 100%);
                  opacity: 0;
                  transition: opacity 0.3s ease;
                }
              }
              
              .item-content {
                flex: 1;
                
                .component-name {
                  display: block;
                  font-size: 13px;
                  font-weight: 600;
                  color: #2c3e50;
                  margin-bottom: 2px;
                }
                
                .component-desc {
                  display: block;
                  font-size: 11px;
                  color: #7f8c8d;
                }
              }
              
              .drag-indicator {
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #c0c4cc;
                
                i {
                  font-size: 12px;
                }
              }
              
              &:hover {
                .icon-glow {
                  opacity: 1;
                }
                
                .drag-indicator {
                  color: #D7A256;
                }
              }
              
              // 不同类型的组件样式
              &.group-item .item-icon {
                background: rgba(103, 194, 58, 0.1);
                border-color: rgba(103, 194, 58, 0.2);
                
                i {
                  color: #67c23a;
                }
              }
              
              &.connect-item .item-icon {
                background: rgba(64, 158, 255, 0.1);
                border-color: rgba(64, 158, 255, 0.2);
                
                i {
                  color: #409eff;
                }
              }
              
              &.function-item .item-icon {
                background: rgba(230, 162, 60, 0.1);
                border-color: rgba(230, 162, 60, 0.2);
                
                i {
                  color: #e6a23c;
                }
              }
              
              &.filter-item .item-icon {
                background: rgba(245, 108, 108, 0.1);
                border-color: rgba(245, 108, 108, 0.2);
                
                i {
                  color: #f56c6c;
                }
              }
            }
          }
        }
      }
    }
    
    .drawer-footer {
      padding: 16px 20px;
      border-top: 1px solid #f7ecdd;
      background: linear-gradient(135deg, #fbf6ee 0%, #faf8f2 100%);
      flex-shrink: 0;
      
      .footer-stats {
        display: flex;
        align-items: center;
        justify-content: center;
        
        .stats-item {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 12px;
          color: #7f8c8d;
          
          i {
            color: #D7A256;
            font-size: 14px;
          }
        }
      }
    }
  }
}
</style> 