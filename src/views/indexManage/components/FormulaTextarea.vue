<template>
  <div class="auto-container" :id="id">
    <el-input
      type="textarea"
      v-model="localValue"
      @input="handleInput"
      class="autoInput"
      :rows="2"
      :placeholder="placeholder"
    />
    <div
      class="fix-el"
      v-show="showOptions"
      ref="fixEl"
    >
      <div
        class="auto-item"
        :class="{ check: idx === selectIndex }"
        v-for="(item, idx) in filterOptions"
        :key="item.fieldName + '_' + idx"
        @mousedown.prevent="itemClick(item)"
      >
      <span v-if="item.fieldNodeId">{{ item.fieldNodeId }}.</span><span v-if="item.fieldName"> {{ item.fieldName }}</span><span v-if="item.fieldDesc">（{{ item.fieldDesc }}）</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "FormulaTextarea",
  props: {
    value: String,
    options: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: "请输入内容"
    }
  },
  data() {
    return {
      localValue: this.value || "",
      showOptions: false,
      filterOptions: [],
      selectIndex: 0,
      id: "formula-" + Date.now(),
      cursorPos: 0,
      currentNodeId: null
    };
  },
  watch: {
    value(val) {
      this.localValue = val;
    }
  },
  mounted() {
    document.addEventListener("keydown", this.handleKeybord);
  },
  beforeDestroy() {
    document.removeEventListener("keydown", this.handleKeybord);
  },
  methods: {
    handleInput(val) {
      this.localValue = val;
      this.$emit("input", val);
      const textarea = this.$el.querySelector("textarea");
      this.cursorPos = textarea ? textarea.selectionStart : val.length;

      // 检查是否输入了 $ 或 $+数字
      const beforeCursor = val.slice(0, this.cursorPos);
      const matchNode = beforeCursor.match(/\$(\d+)$/);
      const matchAll = beforeCursor.match(/\$$/);

      if (
        matchNode &&
        this.options &&
        this.options.length > 0
      ) {
        const nodeId = matchNode[1];
        // 过滤出该节点下的输出字段
        this.filterOptions = this.options.filter(opt => String(opt.fieldNodeId) === nodeId);
        this.showOptions = this.filterOptions.length > 0;
        this.selectIndex = 0;
        this.currentNodeId = nodeId;
        this.$nextTick(() => {
          this.setFixElPosition();
        });
      } else if (
        matchAll &&
        this.options &&
        this.options.length > 0
      ) {
        // 只输入了$，展示全部字段
        this.filterOptions = this.options;
        this.showOptions = this.filterOptions.length > 0;
        this.selectIndex = 0;
        this.currentNodeId = null;
        this.$nextTick(() => {
          this.setFixElPosition();
        });
      } else {
        this.showOptions = false;
        this.currentNodeId = null;
      }
    },
    setFixElPosition() {
      const textarea = this.$el.querySelector('textarea');
      const fixEl = this.$refs.fixEl;
      if (textarea && fixEl) {
        // 获取textarea样式
        const style = getComputedStyle(textarea);
        const rect = textarea.getBoundingClientRect();
        const scrollY = window.scrollY;
        const scrollX = window.scrollX;

        // 获取光标前的文本
        const cursorPos = textarea.selectionStart;
        const value = textarea.value;
        const textBeforeCursor = value.substring(0, cursorPos);

        // 创建隐藏div模拟textarea内容
        const div = document.createElement('div');
        document.body.appendChild(div);
        const properties = [
          'boxSizing', 'width', 'height', 'fontSize', 'fontFamily', 'fontWeight', 'fontStyle',
          'letterSpacing', 'textTransform', 'wordSpacing', 'textIndent', 'padding', 'border',
          'lineHeight', 'textAlign'
        ];
        properties.forEach(prop => {
          div.style[prop] = style[prop];
        });
        div.style.position = 'absolute';
        div.style.visibility = 'hidden';
        div.style.whiteSpace = 'pre-wrap';
        div.style.wordWrap = 'break-word';
        div.style.overflow = 'auto';
        div.style.left = rect.left + 'px';
        div.style.top = rect.top + 'px';

        // 关键：用innerHTML并替换\n为<br>
        // 防止XSS，推荐用replaceAll
        let html = textBeforeCursor.replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/\n/g, "<br>");
        div.innerHTML = html;

        // 插入span标记光标
        const span = document.createElement('span');
        span.textContent = '|';
        div.appendChild(span);

        // 计算span相对div的位置
        const spanRect = span.getBoundingClientRect();
        const divRect = div.getBoundingClientRect();

        // 计算光标的绝对位置
        const caretTop = rect.top + (spanRect.top - divRect.top);
        const caretLeft = rect.left + (spanRect.left - divRect.left);

        // 下拉框高度
        fixEl.style.visibility = "hidden";
        fixEl.style.display = "block";
        const fixElHeight = fixEl.offsetHeight || 200;
        const gap = 0;
        const windowHeight = window.innerHeight;

        // 判断下方空间是否足够
        let top;
        if (windowHeight - (caretTop + parseInt(style.lineHeight)) >= fixElHeight + gap) {
          // 下方空间足够
          top = caretTop + parseInt(style.lineHeight) -gap + scrollY -250;
        } else {
          // 上方显示
          top = caretTop - fixElHeight - gap - scrollY-250;
        }

        // 设置下拉框位置
        fixEl.style.position = 'fixed';
        fixEl.style.left = (caretLeft + scrollX) + "px";
        fixEl.style.top = top + "px";
        fixEl.style.width = "250px";
        fixEl.style.visibility = "visible";
        fixEl.style.display = "block";

        // 移除div
        document.body.removeChild(div);
      }
    },
    itemClick(item) {
      this.insertVariable(item.fieldName, item.fieldNodeId);
    },
    insertVariable(variableCode, nodeId) {
      let textarea = this.$el.querySelector("textarea");
      let value = this.localValue;
      let pos = textarea ? textarea.selectionStart : value.length;
      let before = value.slice(0, pos);
      let matchNode = before.match(/\$(\d+)$/);
      let matchAll = before.match(/\$$/);
      let newVal = value;
      if (matchNode) {
        let nodeId = matchNode[1];
        newVal =
          value.slice(0, matchNode.index) + '${' + nodeId + '.' + variableCode + '}' + value.slice(pos);
      } else if (matchAll) {
        // 只输入了$，插入${字段名}
        newVal =
          value.slice(0, matchAll.index) + '${' + variableCode + '}' + value.slice(pos);
      }
      this.localValue = newVal;
      this.$emit("input", newVal);
      this.$nextTick(() => {
        textarea.focus();
        let newPos = newVal.length; // 光标移到末尾
        textarea.selectionStart = textarea.selectionEnd = newPos;
      });
      this.showOptions = false;
    },
    handleKeybord(e) {
      if (!this.showOptions) return;
      if (["ArrowDown", "ArrowUp"].includes(e.code)) {
        e.preventDefault();
        if (e.code === "ArrowDown") {
          this.selectIndex =
            (this.selectIndex + 1) % this.filterOptions.length;
        } else if (e.code === "ArrowUp") {
          this.selectIndex =
            (this.selectIndex - 1 + this.filterOptions.length) %
            this.filterOptions.length;
        }
      }
      if (["Enter", "Tab"].includes(e.code)) {
        e.preventDefault();
        if (this.filterOptions[this.selectIndex]) {
          // 传递节点ID
          this.insertVariable(
            this.filterOptions[this.selectIndex].fieldName,
            this.filterOptions[this.selectIndex].fieldNodeId
          );
        }
      }
      if (e.code === "Escape") {
        this.showOptions = false;
      }
    }
  }
};
</script>

<style scoped>
.auto-container {
  position: relative;
  max-height: 300px;
}
.autoInput {
  width: 100% !important;
}
.fix-el {
  position: fixed;
  z-index: 99999;
  min-width: 200px;
  max-width: 400px;
  max-height: 300px;
  background-color: #fff;
  overflow-y: auto;
  box-shadow: 0px 1px 8px rgba(181, 176, 176, 0.3);
  /* top/left 由js动态设置 */
}
.auto-item {
  padding-left: 5px;
  line-height: 1;
  padding: 10px;
  font-size: 14px;
  cursor: pointer;
}
.auto-item.check {
  background-color: #d7a256;
  color: #fff;
}
</style>