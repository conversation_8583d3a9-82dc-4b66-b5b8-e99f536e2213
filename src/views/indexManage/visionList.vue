<template>
  <div class="vision-list-container">
    <!-- 主容器 -->
    <div class="main-container">
      <div class="container-content">
        <!-- 页面头部和导航区域 -->
        <SecondaryPageHeader
          title="版本库管理"
          subtitle="管理和配置指标的版本信息"
          icon="el-icon-files"
          back-button-text="返回指标管理"
          back-route="indexManage"
          :breadcrumb-items="breadcrumbItems"
          @back="goBack"
        />

        <!-- 表格区域 -->
        <div class="table-section">
          <div class="table-wrapper">
    <el-table
      :data="tableData"
      stripe
      v-hover
              class="modern-table"
      style="width: 100%"
              v-loading="loading"
              empty-text="暂无版本数据"
    >
                             <el-table-column align="center" prop="version" label="版本号" width="200">
        <template slot-scope="scope">
                  <div class="version-cell">
                    <div class="version-icon">
                      <i class="el-icon-collection-tag"></i>
                    </div>
                    <span class="version-text">{{ scope.row.version }}</span>
                  </div>
        </template>
      </el-table-column>
              
              <el-table-column align="center" prop="type" label="类型" width="120">
        <template slot-scope="scope">
                  <el-tag
                    :type="getTypeTagType(scope.row.type)"
                    :class="getTypeTagClass(scope.row.type)"
                    size="small"
                  >
                    <i :class="getTypeIcon(scope.row.type)"></i>
                    {{ getTypeText(scope.row.type) }}
                  </el-tag>
        </template>
      </el-table-column>
              
      <el-table-column
        align="center"
        prop="createTime"
                width="180"
        label="创建时间"
                sortable
      >
        <template slot-scope="scope">
                  <div class="time-cell">
                    <i class="el-icon-time"></i>
          <span>{{ scope.row.createTime }}</span>
                  </div>
        </template>
      </el-table-column>
              
              <el-table-column align="center" prop="createBy" label="创建人" width="120">
        <template slot-scope="scope">
                  <div class="user-cell">
                    <div class="user-avatar">
                      <i class="el-icon-user-solid"></i>
                    </div>
                    <span>{{ scope.row.createBy | getNickName("scope.row.createBy") }}</span>
                  </div>
        </template>
      </el-table-column>
              
                             <el-table-column align="center" prop="remark" label="备注" min-width="120">
        <template slot-scope="scope">
                  <div class="remark-cell">
          <el-tooltip
                      v-if="scope.row.remark && scope.row.remark.length > 20"
            :content="scope.row.remark"
            :enterable="false"
            effect="dark"
            placement="top"
                      class="modern-tooltip"
          >
                      <span class="remark-text">{{ scope.row.remark }}</span>
          </el-tooltip>
                    <span v-else-if="scope.row.remark" class="remark-text">{{ scope.row.remark }}</span>
                    <span v-else class="empty-remark">暂无备注</span>
                  </div>
        </template>
      </el-table-column>
              
      <el-table-column
        align="center"
        header-align="center"
        label="操作"
                width="240"
                fixed="right"
      >
        <template slot-scope="scope">
                  <div class="action-buttons">
            <el-button
              size="mini"
                      plain
              @click="handleSourceConfig(scope.row)"
                      class="action-btn config-btn"
                    >
                      <i class="el-icon-setting"></i>
                      配置
                    </el-button>
                    <el-button
                      size="mini"
                      plain
                      @click="handleUpdate(scope.row)"
                      class="action-btn edit-btn"
                    >
                      <i class="el-icon-edit"></i>
                      编辑
                    </el-button>
                    <el-button
                      size="mini"
                      plain
                      @click="handleDel(scope.row)"
                      class="action-btn delete-btn"
                    >
                      <i class="el-icon-delete"></i>
                      删除
                    </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
          </div>

          <!-- 空状态 -->
          <div v-if="!loading && tableData.length === 0" class="empty-state">
            <div class="empty-content">
              <div class="empty-icon">
                <i class="el-icon-files"></i>
              </div>
              <h3>暂无版本数据</h3>
              <p>该指标还没有创建任何版本</p>
            </div>
          </div>
        </div>

        <!-- 分页区域 -->
        <div class="pagination-section" v-if="total > 0">
    <Pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :pageData="initParam"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
          />
        </div>
      </div>
    </div>

    <!-- 删除确认弹窗 -->
    <ConfirmDialog
      ref="confirmDialog"
      title="确认删除"
      message="删除后无法恢复，请确认是否删除该版本？"
      icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
      @confirm="confirmUpdate"
    />

    <!-- 编辑版本弹窗 -->
    <el-dialog
      title="编辑版本"
      :visible.sync="showAdd"
      width="500px"
      :close-on-click-modal="false"
      @close="handleEditDialogClose"
      class="version-edit-dialog"
    >
      <el-form
        :model="addForm"
        :rules="editRules"
        ref="editFormRef"
        label-width="80px"
        class="edit-form"
      >
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="addForm.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入版本备注信息"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancelEdit">取消</el-button>
        <el-button
          type="primary"
          @click="toAddForm"
          :loading="saveLoading"
          class="save-btn"
        >
          保存
        </el-button>
      </div>
    </el-dialog>

    <!-- 指标配置弹窗 -->
    <DtPopup
      :isShow.sync="showMatch"
      @close="showMatch = false"
      title="指标配置"
      :footer="false"
      :isFullscreen="true"
    >
      <matchSource v-if="showMatch" :indicatorId="indicatorId" :id="id" />
    </DtPopup>
  </div>
</template>

<script>
import {
  algoindicatorPage,
  algoindicatorDel,
  versionPage,
  algoindicatorVersionUpdate,
  algoindicatorVersionDel
} from "@/api/template/index.js";
import { getDicItemList } from "@/config/tool";
import { baseComponent } from "@/utils/common";
import { validate, validateAlls } from "@/config/validation";
import matchSource from "./matchSource";
import SecondaryPageHeader from "@/components/layouts/SecondaryPageHeader";
import ConfirmDialog from "@/components/layouts/ConfirmDialog.vue";

export default {
  name: "visionList",
  components: {
    matchSource,
    SecondaryPageHeader,
    ConfirmDialog
  },
  mixins: [baseComponent],
  data() {
    return {
      tableData: [],
      initParam: {
        param: {
          indicatorId: "",
          version: "",
          indicatorType: ""
        },
        pageSize: 10,
        pageNum: 1
      },
      total: 0,
      loading: false,
      showMatch: false,
      showAdd: false,
      saveLoading: false,
      indicatorId: "",
      id: "",
      addForm: {
        remark: ""
      },
      editRules: {
        remark: [
          { max: 200, message: "备注长度不能超过200个字符", trigger: "blur" }
        ]
      },
      breadcrumbItems: [
        {
          text: "指标管理",
          icon: "el-icon-back",
          to: { name: "indexManage" }
        },
        {
          text: "版本库管理",
          icon: "el-icon-files"
        }
      ]
    };
  },
  created() {
    this.indicatorId = this.$route.query.indicatorId;
    this.initParam.param.indicatorId = this.$route.query.indicatorId;
    this.initData();
  },
  activated() {
    const indicatorId = this.$route.query.indicatorId;
    if (indicatorId !== this.indicatorId) {
      this.indicatorId = indicatorId;
      this.initParam.param.indicatorId = indicatorId;
      this.initData();
    }
  },
  watch: {
    '$route.query.indicatorId'(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.indicatorId = newVal;
        this.initParam.param.indicatorId = newVal;
        this.initData();
      }
    }
  },
  methods: {
    // 初始化数据
    async initData() {
      this.initList();
    },
    
    async initList() {
      this.loading = true;
      try {
      let res = await versionPage(this.initParam);
      if (!res) {
        return;
      }
      if (res.list && res.list.length > 0) {
        this.tableData = res.list;
        this.initParam.pageNum = res.pageNum;
        this.initParam.pageSize = res.pageSize;
        this.total = Number(res.total);
      } else {
        this.tableData = [];
        this.total = Number(res.total);
      }
      } catch (error) {
        console.error('获取版本列表失败:', error);
        this.$message.error('获取版本列表失败');
      } finally {
        this.loading = false;
      }
    },

    // 返回指标管理
    goBack() {
      this.$router.push({ name: 'indexManage' });
    },

    // 编辑版本
    handleUpdate(row) {
      this.showAdd = true;
      this.addForm.remark = row.remark || "";
      this.id = row.id;
    },

    // 保存编辑
    async toAddForm() {
      if (!this.$refs.editFormRef) return;
      
      try {
        await this.$refs.editFormRef.validate();
        this.saveLoading = true;
        
      const data = {
        ...this.addForm,
        id: this.id
      };
        
      let res = await algoindicatorVersionUpdate(data);
      if (!res) {
        return;
      }
        
        this.$message.success('版本编辑成功');
      this.initList();
      this.showAdd = false;
        this.resetEditForm();
      } catch (error) {
        console.error('保存失败:', error);
      } finally {
        this.saveLoading = false;
      }
    },

    // 重置编辑表单
    resetEditForm() {
      this.addForm = {
        remark: ""
      };
      this.$nextTick(() => {
        if (this.$refs.editFormRef) {
          this.$refs.editFormRef.clearValidate();
        }
      });
    },

    // 取消编辑
    handleCancelEdit() {
      this.showAdd = false;
      this.$nextTick(() => {
        this.resetEditForm();
      });
    },

    // 编辑弹窗关闭处理
    handleEditDialogClose() {
      this.$nextTick(() => {
        if (this.$refs.editFormRef) {
          this.$refs.editFormRef.clearValidate();
        }
      });
    },

    // 删除版本
    handleDel(row) {
      this.indicatorId = row.indicatorId;
      this.id = row.id;
      this.$refs.confirmDialog.show();
    },

    // 确认删除
    async confirmUpdate() {
      try {
      let res = await algoindicatorVersionDel({ id: this.id });
      if (!res) {
        return;
      }
        this.$message.success('删除成功');
      this.initList();
      this.$refs.confirmDialog.hide();
      } catch (error) {
        console.error('删除失败:', error);
        this.$message.error('删除失败');
      }
    },

    // 指标配置
    handleSourceConfig(row) {
      this.showMatch = true;
      this.indicatorId = row.indicatorId;
      this.id = row.id;
    },

    // 分页处理
    handleSizeChange(size) {
      this.initParam.pageSize = size;
      this.initList();
    },

    handleCurrentChange(num) {
      this.initParam.pageNum = num;
      this.initList();
    },

    // 获取类型标签类型
    getTypeTagType(type) {
      const typeMap = {
        1: 'success',
        2: 'warning', 
        3: 'info'
      };
      return typeMap[type] || 'info';
    },

    // 获取类型标签样式类
    getTypeTagClass(type) {
      const classMap = {
        1: 'current-tag',
        2: 'draft-tag',
        3: 'history-tag'
      };
      return classMap[type] || 'history-tag';
    },

    // 获取类型图标
    getTypeIcon(type) {
      const iconMap = {
        1: 'el-icon-check',
        2: 'el-icon-edit-outline',
        3: 'el-icon-time'
      };
      return iconMap[type] || 'el-icon-time';
    },

    // 获取类型文本
    getTypeText(type) {
      const textMap = {
        1: '当前应用',
        2: '草稿',
        3: '历史版本'
      };
      return textMap[type] || '未知';
    }
  }
};
</script>

<style lang="less" scoped>
.vision-list-container {
  min-height: 100vh;

  // 主容器
  .main-container {
    background: white;
    overflow: hidden;

    .container-content {

      // 表格区域
      .table-section {
        .table-wrapper {
          .modern-table {
            /deep/ .el-table__header-wrapper {
              .el-table__header {
                th {
                  color: #2c3e50;
                  font-weight: 600;
                  font-size: 14px;
                }
              }
            }

            /deep/ .el-table__body-wrapper {
              .el-table__row {
                transition: all 0.3s ease;

                &:hover {
                  background: rgba(215, 162, 86, 0.05) !important;
                  transform: translateY(-1px);
                  box-shadow: 0 2px 8px rgba(215, 162, 86, 0.1);
                }

                td {
                  border-bottom: 1px solid #f7ecdd;
                }
              }
            }

            /deep/ .el-table--striped .el-table__body tr.el-table__row--striped td {
              background: #fefdfb;
            }
          }

          // 表格单元格样式
          .version-cell {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;

            .version-icon {
              width: 24px;
              height: 24px;
              background: rgba(215, 162, 86, 0.1);
              border-radius: 6px;
              display: flex;
              align-items: center;
              justify-content: center;

              i {
                color: #D7A256;
                font-size: 12px;
              }
            }

            .version-text {
              font-family: 'Courier New', monospace;
              font-weight: 500;
              color: #2c3e50;
              font-size: 13px;
            }
          }

          .time-cell {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            color: #7f8c8d;
            font-size: 13px;

            i {
              color: #c0c4cc;
            }
          }

          .user-cell {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;

            .user-avatar {
              width: 20px;
  height: 20px;
              background: rgba(215, 162, 86, 0.1);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;

              i {
                color: #D7A256;
                font-size: 10px;
              }
            }
          }

          .remark-cell {
            .remark-text {
  display: block;
              max-width: 180px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
              color: #2c3e50;
            }

            .empty-remark {
              color: #c0c4cc;
              font-style: italic;
              font-size: 12px;
            }
          }

          // 类型标签样式
          .current-tag {
            background: rgba(103, 194, 58, 0.1);
            color: #67c23a;
            border: 1px solid rgba(103, 194, 58, 0.3);

            i {
              margin-right: 4px;
            }
          }

          .draft-tag {
            background: rgba(230, 162, 60, 0.1);
            color: #e6a23c;
            border: 1px solid rgba(230, 162, 60, 0.3);

            i {
              margin-right: 4px;
            }
          }

          .history-tag {
            background: rgba(144, 147, 153, 0.1);
            color: #909399;
            border: 1px solid rgba(144, 147, 153, 0.3);

            i {
              margin-right: 4px;
            }
          }

          // 操作按钮样式
          .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;

            .action-btn {
              font-size: 11px;
              padding: 4px 8px;
              border-radius: 4px;
              font-weight: 500;
              min-width: 56px;
              height: 28px;
              transition: all 0.3s ease;

              i {
                margin-right: 3px;
                font-size: 10px;
              }

              &.config-btn, &.edit-btn {
                background: rgba(215, 162, 86, 0.1);
                border: 1px solid rgba(215, 162, 86, 0.3) !important;
                color: #D7A256;

                &:hover {
                  background: #D7A256 !important;
                  color: white !important;
                  border-color: #D7A256 !important;
                  transform: translateY(-1px);
                  box-shadow: 0 2px 8px rgba(215, 162, 86, 0.3);
                }
              }

              &.delete-btn {
                background: rgba(245, 108, 108, 0.1);
                border: 1px solid rgba(245, 108, 108, 0.3) !important;
                color: #f56c6c;

                &:hover {
                  background: #f56c6c !important;
                  color: white !important;
                  border-color: #f56c6c !important;
                  transform: translateY(-1px);
                  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
                }
              }
            }
          }
        }

        // 空状态样式
        .empty-state {
          padding: 80px 20px;
          text-align: center;

          .empty-content {
            .empty-icon {
              width: 80px;
              height: 80px;
              margin: 0 auto 24px;
              background: rgba(215, 162, 86, 0.1);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;

              i {
                font-size: 36px;
                color: rgba(215, 162, 86, 0.6);
              }
            }

            h3 {
              margin: 0 0 12px 0;
              font-size: 18px;
              font-weight: 600;
              color: #2c3e50;
            }

            p {
              margin: 0;
              color: #7f8c8d;
              font-size: 14px;
              line-height: 1.5;
            }
          }
        }
      }

      // 分页区域
      .pagination-section {
        padding: 20px 28px;
        border-top: 1px solid #f7ecdd;
      }
    }
  }

  // 弹窗样式
  .popup-content {
    text-align: center;
    padding: 30px 20px 20px;

    .popup-icon {
      width: 60px;
      height: 60px;
      background: rgba(230, 162, 60, 0.1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 20px;

      i {
        font-size: 28px;
        color: #e6a23c;
      }
    }

    .popup-text {
      h4 {
        margin: 0 0 8px 0;
        color: #2c3e50;
        font-size: 18px;
        font-weight: 600;
      }

      p {
        margin: 0;
        color: #7f8c8d;
        font-size: 14px;
        line-height: 1.5;
      }
    }
  }

  // 编辑弹窗样式
  /deep/ .version-edit-dialog {
    .el-dialog {
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 8px 32px rgba(215, 162, 86, 0.15);
      background: white;
    }

    .el-dialog__header {
      background: #D7A256;
      padding: 16px 28px;
      border-bottom: 1px solid #C4933C;
  position: relative;

      .el-dialog__title {
        font-size: 16px;
        font-weight: 600;
        color: white;
        display: flex;
        align-items: center;
        gap: 10px;

        &::before {
          content: '';
          width: 24px;
          height: 24px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='14' fill='white' viewBox='0 0 16 16'%3E%3Cpath d='M8 1a2.5 2.5 0 0 1 2.5 2.5V4h-5v-.5A2.5 2.5 0 0 1 8 1zm3.5 3v-.5a3.5 3.5 0 1 0-7 0V4H1v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4h-3.5z'/%3E%3C/svg%3E");
          background-repeat: no-repeat;
          background-position: center;
          background-size: 14px;
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.3);
        }
      }

      .el-dialog__headerbtn {
        .el-dialog__close {
          color: rgba(255, 255, 255, 0.8);
          font-size: 18px;
          transition: all 0.3s ease;

          &:hover {
            color: white;
            transform: rotate(90deg);
          }
        }
      }
    }

    .el-dialog__body {
      padding: 28px 32px;
      background: #fcfaf7;

      .edit-form {
        .el-form-item {
          margin-bottom: 20px;
          display: flex;
          align-items: flex-start;

          .el-form-item__label {
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
            line-height: 44px;
            height: 44px;
            padding-bottom: 0;
            display: flex;
            align-items: center;
            flex-shrink: 0;
            width: 80px;
            margin-right: 12px;
          }

          .el-form-item__content {
            flex: 1;
            margin-left: 0 !important;

            .el-textarea {
              .el-textarea__inner {
                border: 2px solid #e8dcc0;
                border-radius: 8px;
                padding: 12px 16px;
                font-size: 14px;
                transition: all 0.3s ease;
                background: white;
                resize: vertical;
                min-height: 88px;

                &:hover {
                  border-color: rgba(215, 162, 86, 0.6);
                  background: white;
                }
                
                &:focus {
                  border-color: #D7A256;
                  box-shadow: 0 0 0 3px rgba(215, 162, 86, 0.15);
                  background: white;
                }

                &::placeholder {
                  color: #c0c4cc;
                  font-size: 13px;
                }
              }
            }
          }

          &.is-error {
            .el-textarea__inner {
              border-color: #f56c6c !important;
              box-shadow: 0 0 0 3px rgba(245, 108, 108, 0.1) !important;
            }
          }
        }
      }
    }

    .el-dialog__footer {
      padding: 20px 28px 20px;
      background: #f8f4ec;
      border-top: 1px solid #C4933C;
      text-align: right;

      .el-button {
        height: 40px;
        padding: 0 24px;
        border-radius: 8px;
        font-weight: 500;
        font-size: 14px;
        transition: all 0.3s ease;
        margin-left: 12px;

        &:first-child {
          margin-left: 0;
        }

        &.el-button--default {
          background: white;
          border: 2px solid #e8dcc0;
          color: #8b7355;

          &:hover {
            background: #fcfaf7;
            border-color: #D7A256;
            color: #D7A256;
            transform: translateY(-1px);
          }
        }

        &.save-btn {
          background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
          border: none;
          color: white;
          box-shadow: 0 4px 12px rgba(215, 162, 86, 0.3);

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(215, 162, 86, 0.4);
          }

          &.is-loading {
            transform: none;
            box-shadow: 0 2px 8px rgba(215, 162, 86, 0.2);
          }
        }
      }
    }
  }
}

// 全局样式
/deep/ .modern-tooltip {
  background: #2c3e50 !important;
  color: white !important;
  border-radius: 8px !important;
  padding: 8px 12px !important;
  font-size: 12px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/deep/ .el-loading-mask {
  background-color: rgba(251, 246, 238, 0.8) !important;
}

// 指标配置弹窗样式 - 全屏显示
/deep/ .dt-popup {
  // 确保弹窗全屏显示
  .el-dialog {
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    border-radius: 0 !important;
    max-width: none !important;
    max-height: none !important;
  }
  
  .el-dialog__body {
    height: calc(100vh - 80px) !important; // 减去头部和底部的高度
    padding: 0 !important;
    overflow: hidden !important;
    // height: 100vh;
  }
  
  // 弹窗内容区域
  .dt-popup-content {
    position: relative !important;
    height: 100% !important;
    overflow: hidden !important;
  }
  
  // 底部悬浮工具栏 - 始终在可视化区域内
  .floating-toolbar {
    position: absolute !important;
    bottom: 20px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 3000 !important;
    pointer-events: auto !important;
    
    .toolbar-content {
      background: rgba(255, 255, 255, 0.95) !important;
      backdrop-filter: blur(20px) !important;
      border: 1px solid rgba(215, 162, 86, 0.2) !important;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
      border-radius: 50px !important;
      padding: 8px 16px !important;
    }
  }
  
  // 确保画布区域正确显示
  .match-source-container {
    height: 100% !important;
    overflow: hidden !important;
    
    .main-content {
      height: calc(100% - 60px) !important; // 减去头部高度
    }
    
    .canvas-container {
      height: 100% !important;
      
      .flow-canvas {
        height: 100% !important;
      }
    }
  }
}
</style>
