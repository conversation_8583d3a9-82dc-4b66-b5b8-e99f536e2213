<template>
  <div class="strategy-match-update">
    <el-breadcrumb separator-class="el-icon-arrow-right" class="dt-bread">
      <el-breadcrumb-item
        :to="{
          name: 'indexManage'
        }"
        >指标管理</el-breadcrumb-item
      >
      <el-breadcrumb-item
        :style="{ color: $store.state.layoutStore.themeObj.color }"
        >{{ getText }}</el-breadcrumb-item
      >
    </el-breadcrumb>
    <TableToolTemp :toolListProps="titleListPros"></TableToolTemp>
    <el-form
      :model="updateForm"
      label-width="160px"
      :rules="rules"
      ref="updateForm"
      label-position="left"
      class="pdl-20"
    >
      <el-form-item label="字段名称" prop="name">
        <el-input
          v-model="updateForm.name"
          class="dt-input-width"
          placeholder="请输入字段名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="属性名" prop="fieldName">
        <el-input
          v-model="updateForm.fieldName"
          class="dt-input-width"
          placeholder="请输入属性名"
        ></el-input>
      </el-form-item>
      <el-form-item label="字段类型" prop="fieldType">
        <el-select
          v-model="updateForm.fieldType"
          placeholder="请选择"
          @change="changeSourceData"
          class="dt-select"
        >
          <el-option
            v-for="(item, index) in commonData.fieldType"
            :key="index"
            :label="item.dicItemName"
            :value="item.dicItemCode"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否创建索引" prop="fieldType">
        <el-select
          v-model="updateForm.isIndex"
          placeholder="请选择"
          class="dt-select"
        >
          <el-option
            v-for="(item, index) in commonData.fieldList"
            :key="index"
            :label="item.dicItemName"
            :value="item.dicItemCode"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="initState" label="字段长度" prop="fieldLength">
        <el-input
          v-model="updateForm.fieldLength"
          class="dt-input-width"
          placeholder="请输入字段长度"
        ></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="updateForm.remark"
          class="dt-input-width"
          placeholder="请输入备注"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submit">保存</el-button>
        <el-button
          @click="goBack"
          type="primary"
          plain
          :style="{ color: $store.state.layoutStore.themeObj.color }"
          >取消</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import { validate, validateAlls } from "@/config/validation";
import { getDicItemList } from "@/config/tool.js";
import {
  algoSourceFieldAdd,
  algoSourceFieldUpdate
} from "@/api/template/index.js";

export default {
  name: "filedUpdate",
  data() {
    return {
      titleListPros: {
        toolTitle: ""
      },
      rules: {
        name: [
          {
            required: true,
            min: 1,
            max: 100,
            validator: validate,
            trigger: "blur"
          }
        ],
        fieldType: [{ required: true, validator: validate, trigger: "blur" }],
        fieldName: [{ required: true, validator: validate, trigger: "blur" }],
        remark: [{ required: true, validator: validate, trigger: "blur" }],
        isIndex: [{ required: true, validator: validate, trigger: "blur" }]
      },
      updateForm: {
        name: "",
        fieldType: "",
        remark: "",
        fieldName: "",
        isIndex: "",
        sourceId: "",
        fieldLength: 1
      },
      commonData: {
        fieldType: [],
        fieldList: [
          {
            dicItemCode: "0",
            dicItemName: "否"
          },
          {
            dicItemCode: "1",
            dicItemName: "是"
          }
        ]
      },
      baseDisabled: false,
      initState: false
    };
  },
  components: {
    TableToolTemp
  },
  computed: {
    getText() {
      if (this.type == "add") {
        return "新增字段";
      }
      if (this.type == "edit") {
        return "编辑字段";
      }
    }
  },
  created() {
    this.updateForm.sourceId = this.$route.query.sourceId;
    this.init();
  },
  methods: {
    async init() {
      this.type = this.$route.query.type;
      this.titleListPros.toolTitle = this.getText;
      await this.getDictList();
      if (this.type == "edit") {
        this.baseDisabled = true;
        this.initData();
      }
    },
    async initData() {
      let res = this._.cloneDeep(
        this.$store.state.templateEngine.filedUpdateObj
      );
      if (!res) {
        return;
      }
      this.updateForm = res;
      if (
        this.updateForm.fieldType == "init" ||
        this.updateForm.fieldType == " datetime" ||
        this.updateForm.fieldType == " date"
      ) {
        this.initState = false;
      } else {
        this.initState = true;
      }
    },
    changeSourceData(val) {
      if (val == "init" || val == " datetime" || val == " date") {
        this.initState = false;
      } else {
        this.initState = true;
      }
    },
    // 获取字典/
    async getDictList() {
      this.commonData.fieldType = await getDicItemList(
        "sct.algo.source.fieldType"
      );
    },
    goBack() {
      this.$router.go(-1);
    },
    async submit() {
      if (!validateAlls(this.$refs.updateForm)) {
        return;
      }
      let res;
      let data = {
        name: this.updateForm.name,
        fieldType: this.updateForm.fieldType,
        remark: this.updateForm.remark,
        fieldName: this.updateForm.fieldName,
        isIndex: this.updateForm.isIndex,
        sourceId: this.updateForm.sourceId,
        fieldLength: this.initState ? this.updateForm.fieldLength : ""
      };
      if (this.type == "add") {
        res = await algoSourceFieldAdd(data);
      } else {
        res = await algoSourceFieldUpdate({
          ...data,
          fieldId: this.updateForm.fieldId
        });
      }

      if (!res) {
        return;
      }
      this.goBack();
    },
    handleDel(index, row) {
      console.log(index, row);
      this.updateForm.listFieldDicBaseSave.splice(index, 1);
    }
  }
};
</script>

<style lang="less">
.strategy-match-update {
  .table-tool {
    padding-bottom: 10px;
  }
  .draggable-wrap {
    display: inline-block;
  }
  .tags {
    margin-right: 6px;
  }
  .zidian-box {
    margin-bottom: 20px;
  }
}
</style>
