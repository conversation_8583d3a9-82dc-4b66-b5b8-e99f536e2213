<template>
  <div class="config-view-container">
    <!-- 页面头部 -->
    <div class="config-header">
      <div class="header-content">
        <div class="header-left">
          <div class="indicator-info">
            <div class="indicator-icon">
              <i class="el-icon-data-analysis"></i>
            </div>
            <div class="indicator-details">
              <h2 class="indicator-name">{{ indicatorName }}</h2>
              <div class="indicator-code">
                <span class="code-label">指标编码：</span>
                <span class="code-value">{{ indicatorCode }}</span>
                <template v-if="isVersionSource && version">
                  <span class="version-separator">|</span>
                  <span class="version-label">版本号：</span>
                  <span class="code-value">{{ version }}</span>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- JSON配置展示区域 -->
    <div class="config-content">
      <div class="config-card">
        <div class="card-header">
          <div class="card-title">
            <i class="el-icon-document"></i>
            <span>指标配置详情</span>
          </div>
          <div class="card-actions">
            <el-button
              type="text"
              icon="el-icon-refresh"
              @click="refreshConfig"
              :loading="loading"
              style="margin-left: 10px;"
            >
              刷新
            </el-button>
            <el-button
              type="text"
              icon="el-icon-document-copy"
              @click="copyConfig"
              :loading="copyLoading"
            >
              复制
            </el-button>
            <el-button
              type="text"
              icon="el-icon-download"
              @click="exportConfig"
              :loading="exportLoading"
              style="margin-left: 10px;"
            >
              下载
            </el-button>
            <el-checkbox
              v-model="showOnlyEssentialFields"
              :loading="loading"
              style="margin-left: 10px;"
            >
              关键节点
            </el-checkbox>
          </div>
        </div>
        
        <div class="card-content">
          <div v-if="loading" class="loading-container">
            <el-skeleton :rows="10" animated />
          </div>
          
          <div v-else-if="error" class="error-container">
            <div class="error-content">
              <i class="el-icon-warning-outline"></i>
              <h3>加载失败</h3>
              <p>{{ error }}</p>
              <el-button type="primary" @click="refreshConfig">重新加载</el-button>
            </div>
          </div>
          
          <div v-else class="json-container">
            <json-viewer
              :value="formattedData"
              :expand-depth="Infinity"
              :copyable="false"
              :collapsed="false"
              :wrap="false"
                :expanded="true"
                boxed
            ></json-viewer>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { algoindicatorGetJSONConfig, algoindicatorGetVersionJSONConfig } from '@/api/template/index.js'
import JsonViewer from 'vue-json-viewer'
import 'vue-json-viewer/style.css'

export default {
    name: 'IndicatorConfigView',
    components: {
      JsonViewer
    },
    data() {
      return {
        indicatorId: null,
        versionId: null,
        indicatorCode: '',
        indicatorName: '',
        version: '',
        source: '', // 来源标识：'version' 表示从版本列表页面跳转
        configData: null,
        loading: false,
        copyLoading: false,
        exportLoading: false,
        error: null,
        showOnlyEssentialFields: true // 默认勾选关键节点
      }
    },
  computed: {
    formattedData() {
      if (!this.configData) return {}
      // 按key字母顺序排序JSON
      let sortedData = this.sortJsonByKey(this.configData)
      // 如果勾选了只显示关键节点字段，则应用过滤
      if (this.showOnlyEssentialFields) {
        sortedData = this.filterEssentialFields(sortedData)
      }
      return sortedData
    },
    isVersionSource() {
      return this.source === 'version'
    }
  },
  created() {
    this.initData()
  },
  mounted() {
    // 设置页面标题
    this.updatePageTitle()
  },
  methods: {
    initData() {
      // 从URL参数获取指标信息
      const query = this.$route.query
      this.indicatorId = query.indicatorId
      this.versionId = query.versionId
      this.indicatorCode = query.bizCode || ''
      this.indicatorName = query.name || ''
      this.version = query.version || ''
      this.source = query.source || ''

      if (this.isVersionSource && this.versionId) {
        this.loadVersionConfig()
      } else if (this.indicatorId) {
        this.loadConfig()
      } else {
        this.error = '缺少必要参数'
      }

      // 更新页面标题
      this.updatePageTitle()
    },

    updatePageTitle() {
      let title = '指标配置查看'
      if (this.isVersionSource && this.version) {
        title = `版本配置查看 - ${this.version}`
      }
      if (this.indicatorName) {
        title += ` - ${this.indicatorName}`
      }
      document.title = title
    },
    
    async loadConfig() {
      this.loading = true
      this.error = null

      try {
        const res = await algoindicatorGetJSONConfig({
          indicatorId: this.indicatorId
        })

        if (res) {
          this.configData = res
        } else {
          this.error = '未获取到配置数据'
        }
      } catch (error) {
        console.error('加载配置失败:', error)
        this.error = error.message || '加载配置失败'
      } finally {
        this.loading = false
      }
    },

    async loadVersionConfig() {
      this.loading = true
      this.error = null

      try {
        const res = await algoindicatorGetVersionJSONConfig({
          id: this.versionId
        })

        if (res) {
          this.configData = res
        } else {
          this.error = '未获取到版本配置数据'
        }
      } catch (error) {
        console.error('加载版本配置失败:', error)
        this.error = error.message || '加载版本配置失败'
      } finally {
        this.loading = false
      }
    },
    
    refreshConfig() {
      if (this.isVersionSource) {
        this.loadVersionConfig()
      } else {
        this.loadConfig()
      }
    },
    
    async copyConfig() {
      if (!this.configData) {
        this.$message.warning('暂无配置数据可复制')
        return
      }
      
      this.copyLoading = true
      try {
        const jsonString = JSON.stringify(this.configData, null, 2)
        await navigator.clipboard.writeText(jsonString)
        this.$message.success('配置已复制到剪贴板')
      } catch (error) {
        // 降级方案：使用传统方法复制
        const textArea = document.createElement('textarea')
        textArea.value = JSON.stringify(this.configData, null, 2)
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$message.success('配置已复制到剪贴板')
      } finally {
        this.copyLoading = false
      }
    },
    
    exportConfig() {
      if (!this.configData) {
        this.$message.warning('暂无配置数据可导出')
        return
      }
      
      this.exportLoading = true
      try {
        // 按key字母顺序排序JSON
        let sortedData = this.sortJsonByKey(this.configData)
        // 如果勾选了只显示关键节点字段，则应用过滤
        if (this.showOnlyEssentialFields) {
          sortedData = this.filterEssentialFields(sortedData)
        }
        const jsonString = JSON.stringify(sortedData, null, 4)
        const blob = new Blob([jsonString], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        
        const link = document.createElement('a')
        link.href = url
        link.download = `${this.indicatorCode}-${this.indicatorName}.json`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        URL.revokeObjectURL(url)
        this.$message.success('配置文件导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败')
      } finally {
        this.exportLoading = false
      }
    },
    
    // 递归按key字母顺序排序JSON
    sortJsonByKey(obj) {
      // 如果不是对象或为null，直接返回
      if (obj === null || typeof obj !== 'object') {
        return obj
      }

      // 如果是数组，递归排序每个元素
      if (Array.isArray(obj)) {
        return obj.map(item => this.sortJsonByKey(item))
      }

      // 如果是对象，按key排序
      const sortedObj = {}
      Object.keys(obj).sort().forEach(key => {
        // 递归处理子对象
        sortedObj[key] = this.sortJsonByKey(obj[key])
      })
      return sortedObj
    },

    // 递归过滤只保留关键节点字段
    filterEssentialFields(obj) {
      // 定义需要保留的字段
      const essentialFields = ['nodeId', 'nodeConfig', 'nodeName', 'nodeType', 'nodeTypeSub', 'output', 'children']

      // 如果不是对象或为null，直接返回
      if (obj === null || typeof obj !== 'object') {
        return obj
      }

      // 如果是数组，递归过滤每个元素
      if (Array.isArray(obj)) {
        return obj.map(item => this.filterEssentialFields(item))
      }

      // 如果是对象，只保留关键字段
      const filteredObj = {}
      Object.keys(obj).forEach(key => {
        // 如果是关键字段，则保留
        if (essentialFields.includes(key)) {
          if (key === 'children' && Array.isArray(obj[key])) {
            // 特别处理children字段，递归过滤每个子节点
            filteredObj[key] = obj[key].map(child => this.filterEssentialFields(child))
          } else {
            // 对于其他关键字段，直接保留其原始值
            filteredObj[key] = obj[key]
          }
        }
      })
      return filteredObj
    },


  }
}
</script>

<style lang="less" scoped>
.config-view-container {
  min-height: 100vh;
  background: #F8F9FA;
  padding: 20px;
}

.config-header {
  background: #fbf6ee;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f7ecdd;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .indicator-info {
    display: flex;
    align-items: center;
    gap: 16px;

    .indicator-icon {
      width: 56px;
      height: 56px;
      background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 24px;
      box-shadow: 0 2px 8px rgba(215, 162, 86, 0.3);
    }

    .indicator-details {
      .indicator-name {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #2c3e50;
      }

      .indicator-code {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .code-label {
          color: #7f8c8d;
          font-size: 14px;
        }
        
        .code-value {
          background: #f8f9fa;
          padding: 4px 12px;
          border-radius: 6px;
          font-family: 'Courier New', monospace;
          font-weight: 500;
          font-size: 13px;
          color: #2c3e50;
          border: 1px solid #e9ecef;
        }

        .version-separator {
          margin: 0 12px;
          color: #c0c4cc;
          font-size: 14px;
        }

        .version-label {
          color: #7f8c8d;
          font-size: 14px;
          margin-left: 8px;
        }
      }
    }
  }

  .header-right {
    display: flex;
    gap: 12px;

    .el-button--primary {
      background-color: #D7A256;
      border-color: #D7A256;

      &:hover,
      &:focus {
        background-color: #C58C3A;
        border-color: #C58C3A;
      }
    }

    .el-button--success {
      background-color: #67c23a;
      border-color: #67c23a;

      &:hover,
      &:focus {
        background-color: #5daf34;
        border-color: #5daf34;
      }
    }
  }
}

.config-content {
  /* 确保复选框文字颜色与刷新按钮一致 - 使用更具体的选择器提高优先级 */
  .card-actions {
    :deep(.el-checkbox__label) {
        color: #D7A256 !important;
      }
  }

  .config-card {
    background: #FFFFFF;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    overflow: hidden;

    .card-header {
      padding: 20px 24px;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .card-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;

        i {
          color: #D7A256;
        }
      }
    }

    .card-content {
      min-height: 500px;

      .loading-container {
        padding: 24px;
      }

      .error-container {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 400px;

        .error-content {
          text-align: center;
          color: #e74c3c;

          i {
            font-size: 48px;
            margin-bottom: 16px;
          }

          h3 {
            margin: 0 0 8px 0;
            font-size: 20px;
          }

          p {
            margin: 0 0 20px 0;
            color: #7f8c8d;
          }
        }
      }

      .json-container {
        .json-content {
          margin: 0;
          padding: 24px;
          background: #FFFFFF;
          font-family: 'Courier New', monospace;
          font-size: 13px;
          line-height: 1.6;
          overflow-x: auto;
          white-space: pre-wrap;
          word-wrap: break-word;

          // JSON语法高亮样式 - 调整为与黄色系协调
          /* 保留JSON高亮样式以保持一致性 */
          :deep(.json-key) {
            color: #D7A256;
            font-weight: 500;
          }

          :deep(.json-string) {
            color: #67c23a;
          }

          :deep(.json-number) {
            color: #409eff;
          }

          :deep(.json-boolean) {
            color: #f56c6c;
            font-weight: 500;
          }

          :deep(.json-null) {
            color: #90a4ae;
            font-style: italic;
          }
        }
      }
    }
  }
}

/* 响应式设计 - 仅保留必要的布局调整 */
@media (max-width: 768px) {
  .config-view-container {
    padding: 12px;
  }

  .config-header {
    padding: 16px;

    .header-content {
      flex-direction: column;
      gap: 16px;
      align-items: flex-start;
    }

    .indicator-info {
      .indicator-details {
        .indicator-name {
          font-size: 20px;
        }
      }
    }

    .header-right {
      align-self: stretch;
      justify-content: center;
    }
  }

  .config-card {
    .card-header {
      padding: 16px;
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;
    }
  }
}
</style>
