<template>
  <div class="strategy-match-update">
    <el-breadcrumb separator-class="el-icon-arrow-right" class="dt-bread">
      <el-breadcrumb-item
        :to="{
          name: 'indexManage'
        }"
        >指标管理</el-breadcrumb-item
      >
      <el-breadcrumb-item
        :style="{ color: $store.state.layoutStore.themeObj.color }"
        >{{ getText }}</el-breadcrumb-item
      >
    </el-breadcrumb>
    <TableToolTemp :toolListProps="titleListPros"></TableToolTemp>
    <el-form
      :model="updateForm"
      label-width="160px"
      :rules="rules"
      ref="updateForm"
      label-position="left"
      class="pdl-20"
    >
      <el-form-item label="指标编码" prop="bizCode">
        <el-input
          v-model="updateForm.bizCode"
          class="dt-input-width"
          placeholder="请输入指标编码"
        ></el-input>
      </el-form-item>
      <el-form-item label="指标名称" prop="name">
        <el-input
          v-model="updateForm.name"
          class="dt-input-width"
          placeholder="请输入指标名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="指标类型" prop="indicatorType">
        <el-select
          v-model="updateForm.indicatorType"
          placeholder="请选择"
          class="dt-select"
        >
          <el-option
            v-for="(item, index) in commonData.fieldList"
            :key="index"
            :label="item.dicItemName"
            :value="item.dicItemCode"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="updateForm.remark"
          class="dt-input-width"
          placeholder="请输入备注"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submit">保存</el-button>
        <el-button
          @click="goBack"
          type="primary"
          plain
          :style="{ color: $store.state.layoutStore.themeObj.color }"
          >取消</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import { validate, validateAlls } from "@/config/validation";
import { getDicItemList } from "@/config/tool.js";
import {
  algoindicatorAdd
} from "@/api/template/index.js";

export default {
  name: "strAdd",
  data() {
    return {
      titleListPros: {
        toolTitle: ""
      },
      rules: {
        name: [
          {
            required: true,
            min: 1,
            max: 100,
            validator: validate,
            trigger: "blur"
          }
        ],
        bizCode: [{ required: true, validator: validate, trigger: "blur" }]
        // fieldName: [{ required: true, validator: validate, trigger: "blur" }],
        // remark: [{ required: true, validator: validate, trigger: "blur" }],
        // isIndex: [{ required: true, validator: validate, trigger: "blur" }]
      },
      updateForm: {
        name: "",
        indicatorType: "",
        bizCode:"",
        remark: "",
        indicatorId: ""
      },
      commonData: {
        indicatorType: [],
        fieldList: [
          {
            dicItemCode: "1",
            dicItemName: "计算指标"
          },
          {
            dicItemCode: "2",
            dicItemName: "结果指标"
          }
        ]
      },
      baseDisabled: false,
      indicatorId:""
    };
  },
  components: {
    TableToolTemp
  },
  computed: {
    getText() {
      if (this.type == "add") {
        return "新增字段";
      }
      if (this.type == "edit") {
        return "编辑字段";
      }
    }
  },
  created() {
    this.updateForm.indicatorId = this.$route.query.indicatorId;
    this.init();
  },
  methods: {
    async init() {
      this.type = this.$route.query.type;
      this.titleListPros.toolTitle = this.getText;
      //   await this.getDictList();
      if (this.type == "edit") {
        this.baseDisabled = true;
        this.initData();
      }
    },
    async initData() {
      let res = this._.cloneDeep(this.$store.state.templateEngine.indexManage);
      if (!res) {
        return;
      }
      this.updateForm = res;
    },
    changeSourceData(val) {
      if (val == "init" || val == " datetime" || val == " date") {
        this.initState = false;
      } else {
        this.initState = true;
      }
    },
    // 获取字典/
    async getDictList() {
      this.commonData.indicatorType = await getDicItemList(
        "sct.algo.source.indicatorType"
      );
    },
    goBack() {
      this.$router.go(-1);
    },
    async submit() {
      if (!validateAlls(this.$refs.updateForm)) {
        return;
      }
      let res;
      let data = {
        name: this.updateForm.name,
        indicatorType: this.updateForm.indicatorType,
        bizCode: this.updateForm.bizCode,
        remark: this.updateForm.remark
      };
      if (this.type == "add") {
        res = await algoindicatorAdd(data);
      } else {
        res = await algoindicatorAdd({
          ...data,
          indicatorId: this.updateForm.indicatorId
        });
      }

      if (!res) {
        return;
      }
      this.goBack();
    },
  }
};
</script>

<style lang="less">
.strategy-match-update {
  .table-tool {
    padding-bottom: 10px;
  }
  .draggable-wrap {
    display: inline-block;
  }
  .tags {
    margin-right: 6px;
  }
  .zidian-box {
    margin-bottom: 20px;
  }
}
</style>
