<!-- eslint-disable no-self-assign -->
<template>
  <!-- 指标配置页面 -->
  <div class="match-source-container">
    <!-- 页面头部和导航区域 -->
    <SecondaryPageHeader
       v-if="showHeader && !indicatorId"
      title="指标配置"
      subtitle="拖拽组件构建指标计算逻辑"
      icon="el-icon-data-analysis"
      :show-actions="false"
      :breadcrumb-items="breadcrumbItems"
      @back="goBack"
    />

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 画布区域最大化 -->
      <div class="canvas-container">
        <!-- 左侧组件面板 -->
        <ComponentPanel
          :visible="showComponentDrawer"
          :input-data-list="paneList"
          :process-data-list="paneList1"
          @close="showComponentDrawer = false"
          @drag-start="drag"
          @drag-end="handleDragEnd"
          @item-hover="handleItemHover"
          @section-toggle="handleSectionToggle"
        />

        <div
          id="flowWrap"
          ref="flowWrap"
          class="flow-canvas"
          @drop="drop($event)"
          @dragover="allowDrop($event)"
        >
          <!-- 悬浮标题 -->
          <div class="floating-title"
               :class="{ 'panel-opened': showComponentDrawer }">
            <div class="title-content">
              <span class="subtitle-text">拖拽组件构建指标</span>
              <!-- 展开/收起按钮，增加鼠标悬浮提示 -->
        
            </div>
            <el-tooltip  v-if="!indicatorId":content="showHeader ? '收起顶部内容' : '展开顶部内容'" placement="bottom">
                <div
                  class="center-toggle-btn"
                  @click="showHeader = !showHeader"
                >
                  <!-- <i v-if="showHeader" class="el-icon-arrow-up"></i>
                  <i v-if="!showHeader" class="el-icon-arrow-down"></i> -->
                  <i class="el-icon-rank"></i>
                </div>
             </el-tooltip>
          </div>
          
          <div id="flow" class="flow-content">
            <!-- 辅助线 -->
            <div
              v-show="auxiliaryLine.isShowXLine"
              :style="{
                borderColor: themeObj.color,
                width: auxiliaryLinePos.width,
                top: auxiliaryLinePos.y + 'px',
                left: auxiliaryLinePos.offsetX + 'px'
              }"
              class="auxiliary-line-x"
            />
            <div
              v-show="auxiliaryLine.isShowYLine"
              :style="{
                borderColor: themeObj.color,
                height: auxiliaryLinePos.height,
                left: auxiliaryLinePos.x + 'px',
                top: auxiliaryLinePos.offsetY + 'px'
              }"
              class="auxiliary-line-y"
            />
            
            <!-- 节点列表 -->
            <nodeItem
              v-for="item in data.nodeList"
              :id="item.nodeId"
              :key="item.nodeId"
              :node="item"
              :active-node-id="activeNodeId"
              @deleteNode="deleteNode"
              @changeLineState="changeLineState"
              @nodeClick="handleNodeClick"
            />

            <!-- 空画布提示 -->
            <div v-if="data.nodeList.length === 0" class="empty-canvas">
              <div class="empty-content">
                <i class="el-icon-edit-outline empty-icon"></i>
                <h3>开始构建您的指标流程</h3>
                <p>从左侧拖拽组件到画布中开始配置</p>
          </div>
        </div>
          </div>

          <!-- 底部悬浮工具栏 -->
          <div class="floating-toolbar" :class="{ 'toolbar-hidden': showConfigDrawer }">
            <div class="toolbar-content">
              <el-button
                type="text"
                class="toolbar-icon"
                :class="{ active: showComponentDrawer }"
                @click="toggleComponentDrawer"
                :title="showComponentDrawer ? '关闭组件面板 (Esc)' : '打开组件面板 (Ctrl+M)'"
              >
                <i class="el-icon-menu"></i>
              </el-button>
              
              <el-button
                type="text"
                class="toolbar-icon"
                @click="clearCanvas"
                title="清空画布"
              >
                <i class="el-icon-delete"></i>
              </el-button>
              
              <el-button
                type="text"
                class="toolbar-icon"
                @click="autoLayout"
                title="自动布局"
              >
                <i class="el-icon-s-grid"></i>
              </el-button>
              
              <el-button
                type="text"
                class="toolbar-icon"
                @click="handleTool({ type: 'edit' })"
                title="保存草稿"
              >
                <i class="el-icon-document"></i>
              </el-button>
              
              <el-button
                type="text"
                class="toolbar-icon primary save-btn"
                @click="handleTool({ type: 'add' })"
                title="保存"
              >
                <i class="el-icon-check"></i>
              </el-button>
            </div>
          </div>
        </div>

        <!-- 底部配置抽屉组件 -->
        <div class="config-drawer-wrapper">
          <ConfigDrawer
            :visible="showConfigDrawer"
            :selected-node="selectedNode"
            :active-tab="select"
            :type-get="typeGet"
            :type-title="typeTitle"
            :id-key="idKey"
            :id-name="idName"
            :data-item="dataItem"
            :tool-type="toolType"
            :out-put-list="outPutList"
            :filed-out-put-list="filedOutPutList"
            :child-list="childList"
            :filed-list="filedList"
            @close="closeConfigDrawer"
            @tab-change="handleTabChange"
            @changefiled="filedChange"
            @outPutChange="outPutChange"
            @outPutAdd="outPutAdd"
            @outPutDel="outPutDel"
            @outGroupChange="outGroupChange"
            @outGroupAdd="outGroupAdd"
            @outGroupDel="outGroupDel"
            @outConnectChange="outConnectChange"
            @outConnectAdd="outConnectAdd"
            @outConnectDel="outConnectDel"
            @outCoFnChange="outCoFnChange"
            @outCoFnAdd="outCoFnAdd"
            @outCoFnDel="outCoFnDel"
            @outFilterChange="outFilterChange"
            @outFilterAdd="outFilterAdd"
            @outFilterDel="outFilterDel"
          />
        </div>


      </div>
    </div>

    <!-- 删除确认弹窗 -->
    <ConfirmDialog
      ref="confirmDialog"
      title="确认删除"
      message="删除后无法恢复，请确认是否删除？"
      icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
      @confirm="confirmUpdate"
    />

    <!-- 清空画布确认弹窗 -->
    <ConfirmDialog
      ref="clearCanvasDialog"
      title="确认清空画布"
      message="确认清空画布吗？此操作将删除所有节点和连线。"
      icon="el-icon-warning"
      confirm-text="确定"
      cancel-text="取消"
      @confirm="confirmClearCanvas"
    />

    <!-- 保存弹窗 -->
    <el-dialog
      :visible.sync="showAdd"
      title="保存配置"
      size="small"
      width="30%"
      :append-to-body="true"
      :footer="false"
      :center="true"
      class="modern-dialog"
      :class="{ 'has-config-drawer': showConfigDrawer }"
    >
      <el-form
        :model="addForm"
        label-width="70px"
        ref="addUserForm"
        label-position="left"
      >
        <el-form-item label="备注" prop="remark">
          <el-input 
            v-model="addForm.remark" 
            placeholder="请输入备注信息"
            type="textarea"
            :rows="3"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          plain
          :style="{
            color: $store.state.layoutStore.themeObj.color,
            textAlign: 'center'
          }"
          @click="closePopup"
        >
          取消
        </el-button>
        <el-button type="primary" @click="toAddForm">
          保存
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import { getDicItemList } from "@/config/tool.js";
import { guid, getguid } from "@/utils/utils";
import panzoom from "panzoom";
import { nodeTypeList } from "./components/init";
import nodeItem from "@/components/canvas/nodeItem";
import groupAg from "./components/groupAggregation";
import connec from "./components/connection";
import basic from "./components/basic";
import coFn from "./components/coFn";
import filterCo from "./components/filterCo";
import outPut from "./components/outPut";
import { jsPlumb } from "jsplumb";
import {
  algoindicatorGetNodeList,
  algoindicatorConfig,
  algoindicatorGetById,
  algoSourceField,
  algoindicatorGetConfig,
  versionConfig,
  algoindicatorSaveDraft,
  algoindicatorCheck
} from "@/api/template/index.js";
import DtPopup from "@/components/layouts/DtPopup";
import SecondaryPageHeader from "@/components/layouts/SecondaryPageHeader";
import ComponentPanel from "./components/ComponentPanel";
import ConfigDrawer from "./components/ConfigDrawer";
import ConfirmDialog from "@/components/layouts/ConfirmDialog.vue";
export default {
  name: "matchSource",
  components: {
    TableToolTemp,
    nodeItem,
    basic,
    DtPopup,
    groupAg,
    connec,
    coFn,
    filterCo,
    outPut,
    SecondaryPageHeader,
    ComponentPanel,
    ConfigDrawer,
    ConfirmDialog
  },
  props: {
    indicatorId: {
      type: Number,
      default: 0 //策略id
    },
    id: {
      type: Number,
      default: 0 //策略id
    }
  },
  provide() {
    return {
      baseInfoConfig: this
    };
  },
  data() {
    return {
      showHeader: true, // 控制头部显示
      titleListPros: {
        toolTitle: "基础信息"
      },
      titleListPros2: {
        toolTitle: "指标配置",
        toolList: [
          {
            name: "保存",
            btnCode: "",
            type: "add"
          },
          {
            name: "保存为草稿",
            btnCode: "",
            type: "edit"
          }
        ]
      },
      jsPlumb: null,
      delConn: "", // 删除的连线状态
      data: {
        nodeList: [],
        // lineList: [],
        indicatorId: ""
      },
      paneList: [],
      paneList1: nodeTypeList,
      // tabsIndex: "0",
      componentName: "",
      currentItem: null,
      currentTab: {},
      tableData: [],
      auxiliaryLine: { isShowXLine: false, isShowYLine: false }, // 对齐辅助线是否显示
      auxiliaryLinePos: {
        width: "100%",
        height: "100%",
        offsetX: 0,
        offsetY: 0,
        x: 20,
        y: 20
      },

      selectedList: [],
      commonGrid: [5, 5], // 节点移动最小距离
      jsplumbSetting: {
        grid: [10, 10],
        // 动态锚点、位置自适应
        Anchors: ["TopCenter", "RightMiddle", "BottomCenter", "LeftMiddle"],
        Container: "flow",
        // 连线的样式 StateMachine、Flowchart,有四种默认类型：Bezier（贝塞尔曲线），Straight（直线），Flowchart（流程图），State machine（状态机）
        Connector: [
          "Flowchart",
          { cornerRadius: 8, alwaysRespectStubs: true, stub: 10 }
        ],
        // 鼠标不能拖动删除线
        ConnectionsDetachable: false,
        // 删除线的时候节点不删除
        DeleteEndpointsOnDetach: false,
        // 连线的端点
        Endpoint: ["Dot", {radius: 5}],
        // Endpoint: [
        //   "Rectangle",
        //   {
        //     height: 10,
        //     width: 10
        //   }
        // ],
        // 线端点的样式
        EndpointStyle: {
          fill: "rgba(255,255,255,0)",
          outlineWidth: 1
        },
        LogEnabled: false, // 是否打开jsPlumb的内部日志记录
        // 绘制线
        PaintStyle: {
          stroke: "#ccc",
          strokeWidth: 2,
          outlineStroke: "transparent",
          outlineWidth: 2
        },
        HoverPaintStyle: { 
          stroke: "#D7A256", 
          strokeWidth: 4,
          outlineStroke: "rgba(215, 162, 86, 0.2)",
          outlineWidth: 4
        },
        // 绘制箭头
        Overlays: [
          [
            "Arrow",
            {
              width: 14,
              length: 14,
              location: 1,
              foldback: 0.8,
              cssClass: "connection-arrow"
            }
          ],
          [
            "Label",
            {
              label: "点击删除",
              location: 0.5,
              cssClass: "connection-label",
              visible: false,
              id: "connection-label"
            }
          ]
        ],
        RenderMode: "svg"
      },
      jsplumbConnectOptions: {
        isSource: true,
        isTarget: true,
        // 动态锚点、提供了4个方向 Continuous、AutoDefault
        anchor: ["TopCenter", "RightMiddle", "BottomCenter", "LeftMiddle"],
        // 设置连线上面的label样式
        labelStyle: {
          cssClass: "flowLabel"
        }
      },
      jsplumbSourceOptions: {
        // 使用锚点及其子元素选择器
        filter: ".anchor-point, .anchor-dot, .anchor-ring", 
        filterExclude: false,
        anchor: ["TopCenter", "RightMiddle", "BottomCenter", "LeftMiddle"],
        allowLoopback: false,
        maxConnections: -1, // 允许无限连接
        onMaxConnections: function(info, e) {
          console.log("Maximum connections (" + info.maxConnections + ") reached");
        }
      },
      jsplumbTargetOptions: {
        // 使用锚点及其子元素选择器
        filter: ".anchor-point, .anchor-dot, .anchor-ring",
        filterExclude: false,
        anchor: ["TopCenter", "RightMiddle", "BottomCenter", "LeftMiddle"],
        allowLoopback: false,
        maxConnections: -1, // 允许无限连接
        onMaxConnections: function(info, e) {
          console.log("Maximum connections (" + info.maxConnections + ") reached");
        }
      },
      typeTitle: "基本信息",
      type: null,
      typeGet: null,
      basicName: null,
      select: "1",
      idKey: "",
      idName: "",
      dataItem: {}, //节点缓存
      outPutList: [], //基本信息输出字段
      toolType: "", //主节点类型判断
      groupLineStatus: false, //判断是否分组聚合连线
      groupId: "",
      conId: "", //数据连接id
      groupOutPutList: [], //聚合函数分组字段列表
      connectOutPutList: [], //连接字段输出列表
      filedList: [], // 连接字段列表
      filterId: "", //数据过滤判断ID
      filterOutPutList: [], //数据过滤字段列表
      coFnOutPutList: [], //条件函数字段列表
      coFoId: "", //条件函数判断ID
      listTemp: {}, //源数据接口字段输出调用
      listTemp1: {}, //新生成数据结构调用
      listTemp2: [], //新增连线设置子节点
      listTemp3: [], //历史连线设置子节点
      idList: [],
      filedOutPutList: [],
      filedNameList: [], //输出字段list
      childList: [], //子节点集合
      outFiledId: "", // 基本数据源信息节点
      content: "",
      height: "",
      count: 0,
      showAdd: false,
      addForm: {
        remark: ""
      },
      showConfigDrawer: false,
      selectedNode: null,
      showComponentDrawer: false,
      // 拖拽状态标志
      isDragging: false,
      // 当前激活的节点ID
      activeNodeId: null,
      // 面包屑导航数据
      breadcrumbItems: [
        {
          text: "指标管理",
          icon: "el-icon-back",
          to: { name: "indexManage" }
        },
        {
          text: "指标配置",
          icon: "el-icon-data-analysis"
        }
      ]
    };
  },
  watch: {
    idKey(newVal, oldVal) {
      if (newVal != oldVal) {
        this.select = "1";
      }
    }
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    strategyMatchObj() {
      return this.$store.state.strategyMatchManage.strategyMatchObj;
    },
    cacheArr() {
      return this.$store.state["layoutStore"].cacheArrName;
    },
    notCacheArr() {
      return this.$store.state["layoutStore"].notCacheArrName;
    }
  },
  created() {
    this.data.nodeList = [];
    this.$store.commit("templateEngine/nodeItemList", this.data.nodeList);
    this.height = document.body.offsetHeight - 20 - 43 - 48;
    this.jsplumbSetting.HoverPaintStyle.stroke = this.themeObj.color;
    this.data.indicatorId = this.$route.query.indicatorId || this.indicatorId;
    
    this.initData();
    document.oncontextmenu = function() {
      event.returnValue = false;
    };
  },
  mounted() {
    this.initNode();
    // 添加键盘事件监听
    document.addEventListener('keydown', this.handleKeydown);
    // 添加滚轮事件监听，使用捕获阶段
    document.addEventListener('wheel', this.handleWheel, { passive: false, capture: true });

  },
  beforeDestroy() {
    // 移除事件监听
    document.removeEventListener('keydown', this.handleKeydown);
    document.removeEventListener('wheel', this.handleWheel, { capture: true });
  },
  methods: {
    // 返回指标管理
    goBack() {
      this.$router.push({ name: 'indexManage' });
    },
    
    // 获取节点类型文本
    getNodeTypeText(type) {
      const typeMap = {
        '0': '数据源',
        '1': '分组聚合',
        '2': '数据连接',
        '3': '条件函数',
        '4': '数据过滤'
      };
      return typeMap[type] || '未知类型';
    },
    


    //关闭弹窗
    closePopup() {
      this.showAdd = false;
      this.addForm = this.$options.data().addForm;
    },
    // 保存
    async toAddForm() {
      // console.log(this.addForm.remark);
      // console.log(this.content);
      // 获取节点数据并深拷贝，避免直接修改store中的数据
      let data = JSON.parse(JSON.stringify(this.$store.state.templateEngine.nodeItemList));
      // 递归移除所有节点及其子节点中的optionAndList字段
      this.removeOptionAndList(data);
      if (data.length == 0) {
        return this.$message.error("请配置节点");
      }
      let rescheck = await algoindicatorCheck({
        indicatorId: this.data.indicatorId,
        nodeList: data
      });
      if (!rescheck) {
        return;
      }
      let res = await algoindicatorConfig({
        indicatorId: this.data.indicatorId,
        nodeList: data,
        remark: this.addForm.remark
      });
      if (!res) {
        return;
      } else {
        this.initData();
        this.$message.success("保存成功");
      }
      this.showAdd = false;
      this.$refs["addUserForm"].resetFields();
    },
    // 保存和保存草稿
    async handleTool(item) {
      // console.log(item);
      // 获取节点数据并深拷贝，避免直接修改store中的数据
      let data = JSON.parse(JSON.stringify(this.$store.state.templateEngine.nodeItemList));
      // 递归移除所有节点及其子节点中的optionAndList字段
      this.removeOptionAndList(data);
      if (item && item.type == "add") {
        this.showAdd = true;
        return;
      }
     
      // 判断连线最后一个节点
      // let list = [];
      // for (let i = 0; i < data.length; i++) {
      //   let that = this;
      //   let obj = {
      //     num: 0,
      //     id: data[i].nodeId
      //   };
      //   that.savetraverseTree(data[i], obj, function(node) {
      //     // that.data.nodeList.push(node);
      //     list.push(obj);
      //   });
      // }
      // // console.log(list);
      // const maxItem = list.reduce((prev, current) =>
      //   prev.num > current.num ? prev : current
      // );
      // console.log("具有最大值的项是：", maxItem);
      // for (let i = 0; i < data.length; i++) {
      //   if (data[i].nodeId == maxItem.id) {
      //     this.content = data[i];
      //   }
      // }
      // if (
      //   this.content.nodeTypeSub == "source" ||
      //   this.content.nodeTypeSub == "indicator"
      // ) {
      //   this.$message.error("数据源节点不能作为结束节点");
      //   return false;
      // }
      // console.log(data);
      let res = await algoindicatorSaveDraft({
        indicatorId: this.data.indicatorId,
        nodeList: data
      });
      if (!res) {
        return;
      } else {
        this.initData();
        // this.initNode();
        this.$message.success("保存成功");
      }
    },
    async initData() {
      // 基础节点回显
      let res = await algoindicatorGetNodeList();
      if (!res) {
        return;
      }
      if (res && res.length > 0) {
        for (let i = 0; i < res.length; i++) {
          let logImg;
          let nodeTypeSub;
          if (res[i].nodeType == "1") {
            //设置数据源图标
            logImg = "el-icon-coin";
            nodeTypeSub = "source";
          } else if (res[i].nodeType == "2") {
            logImg = "el-icon-data-line";
            nodeTypeSub = "indicator";
          } else {
            logImg = "el-icon-data-line";
            nodeTypeSub = "indicator";
          }
          this.paneList.push({
            bizCode: res[i].bizCode,
            nodeName: res[i].name,
            nodeType: res[i].nodeType,
            type: "0",
            logImg: logImg,
            nodeTypeSub: nodeTypeSub
          });
        }
      }
    },
    // 初始化节点回显
    async initNode() {
      try {
        // 确保jsPlumb库已加载
        if (typeof jsPlumb === 'undefined') {
          console.error('jsPlumb library not loaded');
          return;
        }
        
      this.jsPlumb = jsPlumb.getInstance();
        if (!this.jsPlumb) {
          console.error('Failed to get jsPlumb instance');
          return;
        }
        
      // 原接口
      // let res = await algoindicatorGetById({
      //   indicatorId: this.data.indicatorId
      // });
      // 草稿回显
      let res;
      if (this.id) {
        res = await versionConfig({
          id: this.id
        });
      } else {
        //正常回显
        res = await algoindicatorGetConfig({
          indicatorId: this.data.indicatorId
        });
      }
      if (!res) {
        return;
      }
      let max = -Infinity;
      if (res && res.nodeList) {
        // let that = this;
        // that.traverseTree(res.content, function(node) {
        //   that.data.nodeList.push(node);
        // });
        this.data.nodeList = res.nodeList;
        // this.fixNodesPosition();
        // console.log(this.data.nodeList);
        for (let i = 0; i < this.data.nodeList.length; i++) {
          let logImg;
          let type;
          if (this.data.nodeList[i].nodeTypeSub == "group") {
            logImg = "el-icon-files";
            type = "1";
          } else if (this.data.nodeList[i].nodeTypeSub == "connect") {
            logImg = "el-icon-link";
            type = "2";
          } else if (this.data.nodeList[i].nodeTypeSub == "case") {
            logImg = "el-icon-switch-button";
            type = "3";
          } else if (this.data.nodeList[i].nodeTypeSub == "filter") {
            logImg = "el-icon-search";
            type = "4";
          } else if (this.data.nodeList[i].nodeTypeSub == "source") {
            logImg = "el-icon-coin";
            type = "0";
          } else if (this.data.nodeList[i].nodeTypeSub == "indicator") {
            logImg = "el-icon-data-line";
            type = "0";
          }
          this.data.nodeList[i].type = type;
          this.data.nodeList[i].logImg = logImg;
          this.data.nodeList[i].nodeId = this.data.nodeList[i].nodeId.toString();
          
          // 如果节点没有位置信息，设置默认位置
          if (!this.data.nodeList[i].positionLeft || !this.data.nodeList[i].positionTop) {
            // 为多个节点创建网格布局，避免重叠
            const gridSize = 140; // 节点间距
            const cols = 3; // 每行3个节点
            const row = Math.floor(i / cols);
            const col = i % cols;
            
            // 计算基础中心位置
            const baseX = 400; // 画布中心X
            const baseY = 300; // 画布中心Y
            
            // 计算网格偏移
            const offsetX = (col - 1) * gridSize; // -1 让中间列居中
            const offsetY = (row - 1) * gridSize; // -1 让第一行居中
            
            this.data.nodeList[i].positionLeft = (baseX + offsetX) + "px";
            this.data.nodeList[i].positionTop = (baseY + offsetY) + "px";
          }
          
          if (this.data.nodeList[i].nodeId > max) {
            max = this.data.nodeList[i].nodeId;
          }
        }
        this.count = Number(max);
        this.$store.commit("templateEngine/nodeItemList", this.data.nodeList);
        this.$nextTick(() => {
          this.init();
        });
      } else {
          this.$nextTick(() => {
            this.init();
          });
        }
      } catch (error) {
        console.error('Error in initNode:', error);
        // 即使出错也要尝试初始化基本功能
        this.$nextTick(() => {
          this.init();
        });
      }
    },
    //回显回调 包含children
    traverseTree(node, callback) {
      // 对当前节点执行回调函数
      callback(node);
      // 如果节点有子节点，则递归遍历子节点
      if (node.children) {
        let that = this;
        node.children.forEach(function(child) {
          that.traverseTree(child, callback);
        });
      }
    },
    //子节点连线回调
    savetraverseTree(node, obj, callback) {
      // 如果节点有子节点，则递归遍历子节点
      if (node.children) {
        let that = this;
        obj.num = obj.num + 1;
        node.children.forEach(function(child) {
          that.savetraverseTree(child, obj, callback);
        });
      } else {
        // 对当前节点执行回调函数
        callback(node);
      }
    },
    // 基本信息输出
    filedChange(val) {
      for (let i = 0; i < this.data.nodeList.length; i++) {
        let node = this.data.nodeList[i];
        let that = this;
        // that.traverseTree(that.data.nodeList[i], function(node) {
        // console.log(val.key, node.nodeId);
        if (val.key == node.nodeId) {
          // console.log(1111);
          if (val.name == "strName") {
            node.nodeName = val.value;
          }
          if (val.name == "remark") {
            node.remark = val.value;
          }
        }
        // });
      }
      this.$store.commit("templateEngine/nodeItemList", this.data.nodeList);
    },
    // 原始字段输出
    outPutAdd(val) {
      for (let i = 0; i < this.data.nodeList.length; i++) {
        // if (this.data.nodeList[i].nodeId == val.key) {
        //   if (val.type == 0) {
        //     this.data.nodeList[i].output.fields.default.push(val.value);
        //   } else {
        //     this.data.nodeList[i].output.fields.custom.push(val.value);
        //   }
        // }
        let node = this.data.nodeList[i];
        let that = this;
        let data;
        // that.traverseTree(that.data.nodeList[i], function(node) {
        if (val.key == node.nodeId) {
          if (val.type == 0) {
            data = val.value;
            node.output.fields.default = data;
          } else {
            data = val.value;
            node.output.fields.custom = data;
          }
        }
        // });
      }
      // console.log(this.data.nodeList);
      this.$store.commit("templateEngine/nodeItemList", this.data.nodeList);
    },
    //基本信息删除
    outPutDel(val) {
      // console.log(val);
      for (let i = 0; i < this.data.nodeList.length; i++) {
        let node = this.data.nodeList[i];
        // let that = this;
        // that.traverseTree(that.data.nodeList[i], function(node) {
        if (val.key == node.nodeId) {
          if (val.type == 0) {
            // node.output.fields.default.splice(val.index, 1);
            node.output.fields.default = val.value;
            console.log("删除", node.output.fields.default);
          } else {
            node.output.fields.custom = val.value;
            // node.output.fields.custom.splice(val.index, 1);
          }
        }
        // });
      }
      // }
      this.$store.commit("templateEngine/nodeItemList", this.data.nodeList);
    },
    // 原始字段输出 公共组件
    outPutChange(val) {
      // console.log('初始化',this.$store.state.templateEngine.nodeItemList)
      // console.log(val);
      let data = this.$store.state.templateEngine.nodeItemList;
      for (let i = 0; i < data.length; i++) {
        let node = data[i];
        //   let that = this;
        //   that.traverseTree(data[i], function(node) {
        if (val.key == node.nodeId) {
          if (val.type == 0) {
            if (val.name == "fieldDesc") {
              node.output.fields.default[val.index].fieldDesc = val.value;
            }
            if (val.name == "fieldName") {
              // node.output.fields.default[val.index] = val.value;
              node.output.fields.default[val.index].fieldName =
                val.value.fieldName;
            }
            //数据连接单独处理逻辑，需要加连接点ID
            if (val.name == "fieldNodeId") {
              // node.output.fields.default = val.value;
              node.output.fields.default[val.index].fieldName =
                val.value.fieldName;
              node.output.fields.default[val.index].fieldDesc =
                val.value.fieldDesc;
              node.output.fields.default[val.index].fieldNodeId =
                val.value.fieldNodeId;
            }
            if (val.name == "isIndex") {
              node.output.fields.default[val.index].isIndex = val.value;
            }
          } else {
            if (val.name == "fieldDesc") {
              node.output.fields.custom[val.index].fieldDesc = val.value;
            }
            if (val.name == "fieldName") {
              node.output.fields.custom[val.index].fieldName = val.value;
            }
            if (val.name == "isIndex") {
              node.output.fields.custom[val.index].isIndex = val.value;
            }
            if (val.name == "formula") {
              node.output.fields.custom[val.index].formula = val.value;
            }
          }
          // })
        }
      }
      // console.log("基本信息参数", data);
      this.$store.commit("templateEngine/nodeItemList", data);
    },
    // 聚合函数变化字段
    outGroupChange(val) {
      // console.log(this.data.nodeList);
      for (let i = 0; i < this.data.nodeList.length; i++) {
        let node = this.data.nodeList[i];
        // let that = this;
        // that.traverseTree(that.data.nodeList[i], function(node) {
        if (val.key == node.nodeId) {
          if (val.type == 0) {
            if (val.name == "fieldName") {
              node.nodeConfig.groupFields[val.index] = val.value;
            }
          } else if (val.type == 1) {
            // console.log(val);
            if (val.name == "function") {
              node.nodeConfig.group.default[val.index].function = val.value;
            }
            if (val.name == "aggrFieldName") {
              node.nodeConfig.group.default[val.index].aggrFieldName =
                val.value;
            }
            if (val.name == "fieldName") {
              node.nodeConfig.group.default[val.index].fieldName = val.value;
            }
            if (val.name == "fieldDesc") {
              node.nodeConfig.group.default[val.index].fieldDesc = val.value;
            }
          } else if (val.type == 2) {
            // console.log(val);
            if (!node.nodeConfig.group.custom) {
              node.nodeConfig.group.custom = [
                {
                  fieldDesc: "",
                  fieldName: "",
                  formula: ""
                }
              ];
            }
            // console.log("222", node);
            if (val.name == "formula") {
              node.nodeConfig.group.custom[val.index].formula = val.value;
            }
            if (val.name == "fieldName") {
              node.nodeConfig.group.custom[val.index].fieldName = val.value;
            }
            if (val.name == "fieldDesc") {
              node.nodeConfig.group.custom[val.index].fieldDesc = val.value;
            }
          }
        }
        // });
      }
      // console.log("基本信息参数", this.data.nodeList);
      this.$store.commit("templateEngine/nodeItemList", this.data.nodeList);
    },
    // 聚合函数新增
    outGroupAdd(val) {
      for (let i = 0; i < this.data.nodeList.length; i++) {
        let node = this.data.nodeList[i];
        // let that = this;
        let data;
        // that.traverseTree(that.data.nodeList[i], function(node) {
        if (val.key == node.nodeId) {
          if (val.type == 0) {
            data = val.value;
            node.nodeConfig.groupFields = data;
          } else if (val.type == 1) {
            data = val.value;
            node.nodeConfig.group.default = data;
          } else if (val.type == 2) {
            data = val.value;
            node.nodeConfig.group.custom = data;
          }
        }
        // });
      }
      // console.log("基本信息参数1", this.data.nodeList);
      this.$store.commit("templateEngine/nodeItemList", this.data.nodeList);
    },
    //聚合函数删除
    outGroupDel(val) {
      for (let i = 0; i < this.data.nodeList.length; i++) {
        let node = this.data.nodeList[i];
        // let that = this;
        // that.traverseTree(that.data.nodeList[i], function(node) {
        if (val.key == node.nodeId) {
          if (val.type == 0) {
            node.nodeConfig.groupFields = val.value;
          } else if (val.type == 1) {
            node.nodeConfig.group.default = val.value;
          } else if (val.type == 2) {
            node.nodeConfig.group.custom = val.value;
          }
        }
        // });
      }
      // console.log("基本信息参数", this.data.nodeList);
      this.$store.commit("templateEngine/nodeItemList", this.data.nodeList);
    },
    // 设置基本信息输出节点
    async outPutFiled(temp) {
      console.log("temp", temp);
      let res = await algoSourceField({
        sourceCode: temp.bizCode,
        nodeType: temp.nodeType
      });
      if (!res) {
        return;
      }
      let data = [];
      if (res && res.length > 0) {
        this.outPutList = res;
        for (let i = 0; i < this.data.nodeList.length; i++) {
          if (temp.nodeId == this.data.nodeList[i].nodeId) {
            let item = {
              nodeConfig: {
                bizCode: temp.bizCode
              },
              bizCode: temp.bizCode,
              nodeId: temp.nodeId,
              nodeName: temp.nodeName,
              positionLeft: temp.positionLeft,
              positionTop: temp.positionTop,
              logImg: "el-icon-menu",
              type: "0",
              remark: "",
              nodeType: "input", //# 节点类型
              nodeTypeSub: temp.nodeTypeSub, // # 子节点类型
              output: {
                fields: {
                  // default: data,
                  default: [],
                  custom: [
                    // {
                    //   fieldDesc: "",
                    //   fieldName: "",
                    //   formula: "",
                    //   isIndex: "0"
                    // }
                  ]
                }
              }
            };
            this.dataItem = item;
            this.$set(this.data.nodeList, i, item);
            this.$store.commit(
              "templateEngine/nodeItemList",
              this.data.nodeList
            );
          }
        }
      }
    },
    // 输出节点
    async outPutNameFiled(temp) {
      // console.log("temp2",temp);
      let nodeType = "";
      for (let i = 0; i < this.paneList.length; i++) {
        if (
          temp.nodeConfig &&
          temp.nodeConfig.bizCode &&
          temp.nodeConfig.bizCode == this.paneList[i].bizCode
        ) {
          //匹配type
          nodeType = this.paneList[i].nodeType;
        } else if (temp.bizCode && temp.bizCode == this.paneList[i].bizCode) {
          nodeType = this.paneList[i].nodeType;
        }
      }
      let res = await algoSourceField({
        sourceCode:
          temp.nodeConfig && temp.nodeConfig.bizCode
            ? temp.nodeConfig.bizCode
            : temp.bizCode,
        nodeType: nodeType
      });
      if (!res) {
        return;
      }
      if (res && res.length > 0) {
        this.outPutList = res;
      }
    },
    // 数据连接变化字段
    outConnectChange(val) {
      // console.log(this.data.nodeList);
      for (let i = 0; i < this.data.nodeList.length; i++) {
        let node = this.data.nodeList[i];
        // let that = this;
        // that.traverseTree(that.data.nodeList[i], function(node) {
        if (val.key == node.nodeId) {
          if (val.type == 0) {
            if (val.name == "fromNodeId") {
              node.nodeConfig.connectCondition[val.index].fromNodeId =
                val.value;
            }
            if (val.name == "fromFieldName") {
              node.nodeConfig.connectCondition[val.index].fromFieldName =
                val.value;
            }
            if (val.name == "toNodeId") {
              node.nodeConfig.connectCondition[val.index].toNodeId = val.value;
            }
            if (val.name == "toFieldName") {
              node.nodeConfig.connectCondition[val.index].toFieldName =
                val.value;
            }
            if (val.name == "connectType") {
              node.nodeConfig.connectCondition[val.index].connectType =
                val.value;
            }
          } else {
            if (val.name == "fromNodeId") {
              node.nodeConfig.custom[val.index].fromNodeId = val.value;
            }
            if (val.name == "fromFieldName") {
              node.nodeConfig.custom[val.index].fromFieldName = val.value;
            }
            if (val.name == "toNodeId") {
              node.nodeConfig.custom[val.index].toNodeId = val.value;
            }
            if (val.name == "toFieldName") {
              node.nodeConfig.custom[val.index].toFieldName = val.value;
            }
            if (val.name == "connectType") {
              node.nodeConfig.custom[val.index].connectType = val.value;
            }
            if (val.name == "formula") {
              node.nodeConfig.custom[val.index].formula = val.value;
            }
          }
        }
        // });
      }
      // console.log("基本信息参数", this.data.nodeList);
      this.$store.commit("templateEngine/nodeItemList", this.data.nodeList);
    },
    // 数据连接新增
    outConnectAdd(val) {
      for (let i = 0; i < this.data.nodeList.length; i++) {
        let node = this.data.nodeList[i];
        let that = this;
        let data;
        // that.traverseTree(that.data.nodeList[i], function(node) {
        if (val.key == node.nodeId) {
          if (val.type == 0) {
            data = val.value;
            node.nodeConfig.connectCondition = data;
          } else {
            node.nodeConfig.custom = val.value;
          }
        }
        // });
      }
      // console.log("基本信息参数", this.data.nodeList);
    },
    //数据连接删除
    outConnectDel(val) {
      for (let i = 0; i < this.data.nodeList.length; i++) {
        let node = this.data.nodeList[i];
        // let that = this;
        // that.traverseTree(that.data.nodeList[i], function(node) {
        if (val.key == node.nodeId) {
          if (val.type == 0) {
            node.nodeConfig.connectCondition = val.value;
          } else {
            node.nodeConfig.custom = val.value;
          }
        }
        // });
      }
    },
    // 数据过滤变化字段
    outFilterChange(val) {
      for (let i = 0; i < this.data.nodeList.length; i++) {
        let node = this.data.nodeList[i];
        // let that = this;
        // that.traverseTree(that.data.nodeList[i], function(node) {
        if (val.key == node.nodeId) {
          if (val.type == 0) {
            if (val.name == "left") {
              node.nodeConfig.filter.default[val.index].left = val.value;
            }
            if (val.name == "toFieldName") {
              node.nodeConfig.filter.default[val.index].operator = val.value;
            }
            if (val.name == "connectType") {
              node.nodeConfig.filter.default[val.index].function = val.value;
            }
          } else {
            if (!node.nodeConfig.filter.custom) {
              node.nodeConfig.filter.custom = [
                {
                  operator: ""
                }
              ];
            }
            if (val.name == "formula") {
              node.nodeConfig.filter.custom[val.index].operator = val.value;
            }
          }
        }
        // });
      }
      // console.log("基本信息参数", this.data.nodeList);
      this.$store.commit("templateEngine/nodeItemList", this.data.nodeList);
    },
    // 数据过滤新增
    outFilterAdd(val) {
      for (let i = 0; i < this.data.nodeList.length; i++) {
        let node = this.data.nodeList[i];
        // let that = this;
        let data;
        // that.traverseTree(that.data.nodeList[i], function(node) {
        if (val.key == node.nodeId) {
          if (val.type == 0) {
            data = val.value;
            node.nodeConfig.filter.default = data;
          } else {
            data = val.value;
            node.nodeConfig.filter.custom = data;
          }
        }
        // });
      }
    },
    //数据过滤删除
    outFilterDel(val) {
      for (let i = 0; i < this.data.nodeList.length; i++) {
        let node = this.data.nodeList[i];
        // let that = this;
        // that.traverseTree(that.data.nodeList[i], function(node) {
        if (val.key == node.nodeId) {
          if (val.type == 0) {
            node.nodeConfig.filter.default = val.value;
          } else {
            node.nodeConfig.filter.custom = val.value;
          }
        }
        // });
      }
    },
    // 条件函数新增
    outCoFnAdd(val) {
      console.log(val);
      for (let i = 0; i < this.data.nodeList.length; i++) {
        let node = this.data.nodeList[i];
        // let that = this;
        let data;
        // that.traverseTree(that.data.nodeList[i], function(node) {
        if (val.key == node.nodeId) {
          if (val.type == 0) {
            data = val.value;
            node.nodeConfig.handle = data;
          } else if (val.type == 1) {
            data = val.value;
            node.nodeConfig.handle[val.index].default = data;
          } else if (val.type == 2) {
            data = val.value;
            node.nodeConfig.handle[val.index].custom = data;
          }
        }
        // });
      }
      console.log(this.data.nodeList);
    },
    //条件函数删除
    outCoFnDel(val) {
      for (let i = 0; i < this.data.nodeList.length; i++) {
        let node = this.data.nodeList[i];
        // let that = this;
        // that.traverseTree(that.data.nodeList[i], function(node) {
        if (val.key == node.nodeId) {
          if (val.type == 0) {
            node.nodeConfig.handle = val.value;
          } else if (val.type == 1) {
            node.nodeConfig.handle.default[val.rowIndex] = val.value;
          } else if (val.type == 2) {
            node.nodeConfig.handle.custom[val.rowIndex] = val.value;
          }
        }
        // });
      }
    },
    // 条件函数变化字段
    outCoFnChange(val) {
      // console.log(this.data.nodeList);
      for (let i = 0; i < this.data.nodeList.length; i++) {
        let node = this.data.nodeList[i];
        // let that = this;
        // that.traverseTree(that.data.nodeList[i], function(node) {
        if (val.key == node.nodeId) {
          if (val.type == 0) {
            if (val.name == "fieldName") {
              node.nodeConfig.handle[val.index].fieldName = val.value;
            }
            if (val.name == "fieldDesc") {
              node.nodeConfig.handle[val.index].fieldDesc = val.value;
            }
          } else if (val.type == 1) {
            if (val.name == "operator") {
              node.nodeConfig.handle[val.defaultIndex].default[
                val.index
              ].operator = val.value;
            }
            if (val.name == "left") {
              node.nodeConfig.handle[val.defaultIndex].default[val.index].left =
                val.value;
            }
            if (val.name == "right") {
              node.nodeConfig.handle[val.defaultIndex].default[
                val.index
              ].right = val.value;
            }
            if (val.name == "caseValue") {
              node.nodeConfig.handle[val.defaultIndex].default[
                val.index
              ].caseValue = val.value;
            }
          } else if (val.type == 2) {
            if (!node.nodeConfig.handle.custom) {
              node.nodeConfig.handle.custom = [
                {
                  formula: "",
                  caseValue: ""
                }
              ];
            }
            if (val.name == "formula") {
              node.nodeConfig.handle[val.defaultIndex].custom[
                val.index
              ].formula = val.value;
            }
            if (val.name == "caseValue") {
              node.nodeConfig.handle[val.defaultIndex].custom[
                val.index
              ].caseValue = val.value;
            }
          }
        }
        // });
      }
      // console.log("基本信息参数", this.data.nodeList);
      this.$store.commit("templateEngine/nodeItemList", this.data.nodeList);
    },
    // 子节点判断数据
    async getChildren(sourceId, targetId, from, to) {
      let temp; //源数据接口字段输出调用
      let data;
      for (let i = 0; i < this.data.nodeList.length; i++) {
        if (sourceId == this.data.nodeList[i].nodeId) {
          temp = this.data.nodeList[i];
        }
        if (from.id == this.data.nodeList[i].nodeId) {
          data = this.data.nodeList[i];
        }
      }
      if (!temp) return;
      for (let i = 0; i < this.data.nodeList.length; i++) {
        // const obj = { ...data };
        let that = this;
        that.traverseTree(this.data.nodeList[i], function(node) {
          // that.data.nodeList.push(node);
          // 设置相关节点及输出等
          if (node.nodeId == targetId) {
            const obj = { ...data };
            if (node.children && node.children.length > 0) {
              let res = node.children.find(function(item) {
                return item.nodeId == obj.nodeId;
              });
              // console.log(res)
              if (!res) {
                // console.log("obj", to);
                node.children.push(obj);
                node.childIds.push(obj.nodeId);
              }
            } else {
              // console.log("obj1");
              node.children = [obj];
              node.childIds = [obj.nodeId];
            }
            // eslint-disable-next-line no-self-assign
            node.nodeId = that.data.nodeList[i].nodeId;
            node.nodeName = that.data.nodeList[i].nodeName;
            node.positionLeft = that.data.nodeList[i].positionLeft;
            node.positionTop = that.data.nodeList[i].positionTop;
            that.dataItem = node;
            that.$set(that.data.nodeList, i, node);
          }
        });
        // if (targetId == this.data.nodeList[i].nodeId) {
        // 设置相关节点及输出等
        // if (
        //   this.data.nodeList[i].children &&
        //   this.data.nodeList[i].children.length > 0
        // ) {
        //   console.log("obj", to);
        //   this.data.nodeList[i].children.push(obj);
        // } else {
        //   console.log("obj1");
        //   this.data.nodeList[i].children = [obj];
        // }
        // this.data.nodeList[i].nodeId = this.data.nodeList[i].nodeId;
        // this.data.nodeList[i].nodeName = this.data.nodeList[i].nodeName;
        // this.data.nodeList[i].positionLeft = this.data.nodeList[
        //   i
        // ].positionLeft;
        // this.data.nodeList[i].positionTop = this.data.nodeList[i].positionTop;
        // this.data.nodeList[i].output.fields.default =
        //   temp.output.fields.default;
        // this.option=this.data.nodeList[i].output.fields.default
        // this.dataItem = this.data.nodeList[i];
        // this.$set(this.data.nodeList, i, this.data.nodeList[i]);
        // }
      }
      // console.log("node", this.data.nodeList);
      this.$store.commit("templateEngine/nodeItemList", this.data.nodeList);
    },
    handleTabsClick1() {
      this.select = "1";
    },
    handleTabsClick2() {
      this.getNewChild(this.dataItem);
      this.select = "2";
    },
    // 处理ConfigDrawer组件的标签页切换
    handleTabChange(tab) {
      if (tab === '1') {
        this.select = "1";
      } else if (tab === '2') {
        this.getNewChild(this.dataItem);
        this.select = "2";
      }
    },
    initOptions() {
      this.height = document.body.offsetHeight - 20 - 43 - 48;
      this.jsplumbSetting.HoverPaintStyle.stroke = this.themeObj.color;
    },
    init() {
      if (!this.jsPlumb) {
        console.warn('jsPlumb not available, initializing basic functionality');
        // 即使没有jsPlumb也要初始化基本功能
        this.initPanZoom();
        return;
      }
      
      try {
        this.jsPlumb.ready(() => {
          // 导入默认配置
          this.jsPlumb.importDefaults(this.jsplumbSetting);
          // 完成连线前的校验
          this.jsPlumb.bind("beforeDrop", evt => {
            console.log('Before drop event:', evt);
            console.log('Source:', evt.sourceId, 'Target:', evt.targetId);
            const res = () => {}; // 此处可以添加是否创建连接的校验， 返回 false 则不添加；
            return res;
          });
          // 连线创建成功后，维护本地数据
          this.jsPlumb.bind("connection", evt => {
            console.log('Connection created:', evt);
            console.log('Source element:', evt.source);
            console.log('Target element:', evt.target);
            this.addLine(evt);
          });
          // 连线点击事件
          this.jsPlumb.bind("click", (conn, originalEvent) => {
            console.log('Connection clicked:', conn, originalEvent);
            // originalEvent.preventDefault(); // 阻止默认的右键点击行为
            this.handleLine(conn, originalEvent);
          });
          
          // 连线悬停事件 - 显示删除提示
          this.jsPlumb.bind("connectionMouseover", (conn, originalEvent) => {
            // 显示删除标签
            if (conn.getOverlay && conn.getOverlay("connection-label")) {
              conn.getOverlay("connection-label").setVisible(true);
            }
            // 添加悬停样式
            if (conn.canvas) {
              conn.canvas.style.filter = 'drop-shadow(0 2px 8px rgba(215, 162, 86, 0.4))';
            }
          });
          
          // 连线鼠标离开事件 - 隐藏删除提示
          this.jsPlumb.bind("connectionMouseout", (conn, originalEvent) => {
            // 隐藏删除标签
            if (conn.getOverlay && conn.getOverlay("connection-label")) {
              conn.getOverlay("connection-label").setVisible(false);
            }
            // 移除悬停样式
            if (conn.canvas) {
              conn.canvas.style.filter = '';
            }
          });
          
          // 断开连线后，维护本地数据
          this.jsPlumb.bind("connectionDetached", evt => {
            this.deleLine(evt);
          });
          this.loadEasyFlow();
          // 会使整个jsPlumb立即重绘。
          this.jsPlumb.setSuspendDrawing(false, true);
        });
      } catch (error) {
        console.error('Error in jsPlumb initialization:', error);
      }
      
      this.initPanZoom();
    },
    // 加载流程图
    loadEasyFlow() {
      if (!this.jsPlumb) {
        console.warn('jsPlumb not available for loadEasyFlow');
        return;
      }
      
      // 初始化节点
      for (let i = 0; i < this.data.nodeList.length; i++) {
        const node = this.data.nodeList[i];
        
        // 检查锚点元素是否存在
        const nodeElement = document.getElementById(node.nodeId);
        const anchorPoints = nodeElement ? nodeElement.querySelectorAll('.anchor-point') : [];
        console.log(`Existing node ${node.nodeId} anchor points found:`, anchorPoints.length);
        
        // 设置源点，可以拖出线连接其他节点
        this.jsPlumb.makeSource(node.nodeId, this.jsplumbSourceOptions);
        // // 设置目标点，其他源点拖出的线可以连接该节点
        this.jsPlumb.makeTarget(node.nodeId, this.jsplumbTargetOptions);
        // this.jsPlumb.draggable(node.nodeId);
        this.draggableNode(node.nodeId);
      }

      // 初始化连线
      this.jsPlumb.unbind("connection"); // 取消连接事件
      this._.each(this.data.nodeList, item => {
        this._.each(item.childIds, el => {
          this.jsPlumb.connect(
            {
              source: el,
              target: item.nodeId,
              // label: el.label || "",
              id: el
            },
            this.jsplumbConnectOptions
          );
        });
      });
      this.jsPlumb.bind("connection", evt => {
        const from = evt.source;
        const to = evt.targetId;
        if (evt.target.innerText == "开始") {
          this.$message.error("开始节点只允许在全流程的起点");
          this.jsPlumb.deleteConnection(evt.connection);
          return false;
        }
        if (evt.source.innerText == "结束") {
          this.$message.error("该节点流程以结束");
          this.jsPlumb.deleteConnection(evt.connection);
          return false;
        }

        this.setLine(from, to, evt);
      });
    },
    deleLine(line) {
      this._.each(this.data.nodeList, item => {
        this._.each(item.childIds, (el, index) => {
          if (item.nodeId == line.targetId && el == line.sourceId) {
            item.childIds.splice(index, 1);
          }
        });
      });
      // console.log(this.data.nodeList);
      this.$store.commit("templateEngine/nodeItemList", this.data.nodeList);
    },
    //连线设置
    setLine(from, to, evt) {
      const from1 = from.id;
      let node = this._.find(this.data.nodeList, ["nodeId", to]);
      // const index = this._.findIndex(this.data.nodeList, node => node.nodeId === to);
      let data = this._.find(this.data.nodeList, ["nodeId", from1]);
      // console.log(from1);
      if (!node) {
        return;
      }
      let obj = {
        // sort: 1,
        // nextNodeId: from.id,
        // nodeName: from.innerText.replace(/\n.*$/gm, ""),
        // nodeId: from.id,
        // label: ""
        ...data
        // id: guid()
      };
      if (this._.has(node, "childIds")) {
        // obj.sort = node.childIds.length + 1;
        node.childIds.push(from1);
        // node.children.push(obj);
      } else {
        node.childIds = [from1];
        // node.children = [obj];
      }
      // console.log('node',data)
      // this.dataItem = data;
      this.$store.commit("templateEngine/nodeItemList", this.data.nodeList);
      // console.log(this.data.nodeList);
      // this.getChildren(evt.sourceId, evt.targetId, from, to);
    },
    drag(ele, item, type) {
      this.type = type;
      this.currentItem = item;
      
      // 添加拖拽状态
      document.body.classList.add('dragging');
      
      // 设置拖拽数据
      if (ele.dataTransfer) {
        ele.dataTransfer.effectAllowed = 'copy';
        ele.dataTransfer.setData('text/plain', JSON.stringify({
          type: type,
          item: item
        }));
      }
    },
    drop(event) {
      // 移除拖拽状态
      document.body.classList.remove('dragging', 'drag-over');
      
      if (!this.jsPlumb) {
        console.warn('jsPlumb not available for drop operation');
        return;
      }
      
      const container = this.jsPlumb.getContainer();
      const containerRect = container.getBoundingClientRect();
      const scale = this.getScale();
      let left, top;
      
      // 定义画布的有效拖拽区域（考虑节点尺寸160x48）
      const canvasWidth = 2000; // 限制画布宽度
      const canvasHeight = 1500; // 限制画布高度
      const nodeWidth = 140;
      const nodeHeight = 50; // 考虑可能的多行文本高度
      const padding = 0; // 边距
      
      // 如果是在画布中心区域拖拽，使用鼠标位置
      if (event.pageX && event.pageY) {
        left = (event.pageX - containerRect.left) / scale;
        top = (event.pageY - containerRect.top) / scale;
      } else {
        // 否则默认在画布中心
        left = (canvasWidth / 2 - nodeWidth / 2);
        top = (canvasHeight / 2 - nodeHeight / 2);
      }
      
      // 限制拖拽范围，确保节点不会超出画布边界
      left = Math.max(padding, Math.min(left, canvasWidth - nodeWidth - padding));
      top = Math.max(padding, Math.min(top, canvasHeight - nodeHeight - padding));
      
      this.count++;
      var temp = {
        ...this.currentItem,
        nodeId: String(this.count),
        positionTop: Math.round(top / 20) * 20 + "px",
        positionLeft: Math.round(left / 20) * 20 + "px"
      };
      temp = JSON.parse(JSON.stringify(temp));
      this.addNode(temp);
    },
    // dragover默认事件就是不触发drag事件，取消默认事件后，才会触发drag事件
    allowDrop(event) {
      event.preventDefault();
      // 添加拖拽悬停效果
      document.body.classList.add('drag-over');
    },
    getScale() {
      if (!this.jsPlumb) {
        return 1;
      }
      
      let scale1;
      if (this.jsPlumb.pan) {
        const { scale } = this.jsPlumb.pan.getTransform();
        scale1 = scale;
      } else {
        const matrix = window.getComputedStyle(this.jsPlumb.getContainer())
          .transform;
        scale1 = matrix.split(", ")[3] * 1;
      }
      this.jsPlumb.setZoom(scale1);
      return scale1;
    },
    
    // 拖拽
    draggableNode(nodeId) {
      this.jsPlumb.draggable(nodeId, {
        grid: this.commonGrid,
        drag: params => {
          this.alignForLine(nodeId, params.pos);
        },
        start: () => {
          // 拖拽开始时设置标志位
          this.isDragging = true;
        },
        stop: params => {
        this.auxiliaryLine.isShowXLine = false;
        this.auxiliaryLine.isShowYLine = false;
          this.changeNodePosition(nodeId, params.pos);

        setTimeout(() => {
          this.isDragging = false;
          }, 100);
        }
      });
    },
    
    // 移动节点时，动态显示对齐线
    alignForLine(nodeId, position) {
      let showXLine = false,
        showYLine = false;
      this.data.nodeList.some(el => {
        if (el.nodeId !== nodeId && el.positionLeft == position[0] + "px") {
          this.auxiliaryLinePos.x = position[0] + 60;
          showYLine = true;
        }
        if (el.nodeId !== nodeId && el.positionTop == position[1] + "px") {
          this.auxiliaryLinePos.y = position[1] + 20;
          showXLine = true;
        }
      });
      this.auxiliaryLine.isShowYLine = showYLine;
      this.auxiliaryLine.isShowXLine = showXLine;
    },
    changeNodePosition(nodeId, pos) {
      this.data.nodeList.some((v, index) => {
        if (nodeId == v.nodeId) {
          v.positionLeft = pos[0] + "px";
          v.positionTop = pos[1] + "px";
          this.$set(this.data.nodeList, index, v);
          return true;
        } else {
          return false;
        }
      });
      // this.$store.commit("templateEngine/nodeItemList", this.data.nodeList);
    },
    addLine(line) {
      // console.log("123", line);
      const from = line.source.id;
      const to = line.target.id;
      this.setLine(from, to, line);
    },
    handleLine(conn, originalEvent) {
      console.log("conn", conn);
      this.delConn = conn;
      this.$refs.confirmDialog.show();
    },
    confirmUpdate(originalEvent) {
      if (this.jsPlumb) {
      this.jsPlumb.deleteConnection(this.delConn);
      }
      this.$refs.confirmDialog.hide();
    },
    async getDictList() {
      await getDicItemList("sct.source.config.status");
      await getDicItemList("gen.yesorno.num");
    },
    // 添加新的节点
    async addNode(temp) {
      // 如果没有指定位置，则在画布中心创建节点
      if (!temp.positionLeft || !temp.positionTop) {
        // 使用固定的画布尺寸计算中心位置
        const canvasWidth = 2000;
        const canvasHeight = 1500;
        const nodeWidth = 140;
        const nodeHeight = 50;
        
        // 计算画布中心位置，考虑节点自身尺寸
        const centerX = (canvasWidth / 2 - nodeWidth / 2);
        const centerY = (canvasHeight / 2 - nodeHeight / 2);
        
        // 对齐到网格（20px网格）
        temp.positionLeft = Math.round(centerX / 20) * 20 + "px";
        temp.positionTop = Math.round(centerY / 20) * 20 + "px";
      }
      
      if (!this._.find(this.data.nodeList, ["type", "0"]) && temp.type != "0") {
        this.$message.error("请先添加数据节点");
        return false;
      }
       // 添加成功提示动画
       this.$message({
        message: `已添加 ${temp.nodeName} 组件`,
        type: 'success',
        duration: 2000,
        customClass: 'modern-message'
      });
      this.data.nodeList.push(temp);
      this.idKey = temp.nodeId;
      this.idName = temp.nodeName;
      this.toolType = temp.type;
      this.typeGet = this.type;
      this.select = "1";
      this.$store.commit("templateEngine/nodeItemList", this.data.nodeList);
      
      if (this.typeGet == "0") {
        this.typeTitle = "基本信息";
        await this.outPutFiled(temp); //基本信息输出
      } else if (this.typeGet == "1") {
        this.typeTitle = "分组聚合";
        this.groupId = temp.nodeId;
        this.dataItem = temp;
      } else if (this.typeGet == "2") {
        this.typeTitle = "数据连接";
        this.conId = temp.nodeId;
        this.dataItem = temp;
      } else if (this.typeGet == "3") {
        this.typeTitle = "条件函数";
        this.coFoId = temp.nodeId;
        this.dataItem = temp;
      } else if (this.typeGet == "4") {
        this.typeTitle = "数据过滤";
        this.dataItem = temp;
        this.filterId = temp.nodeId;
      }
      
      this.$nextTick(() => {
        if (this.jsPlumb) {
          console.log('Setting up jsPlumb for node:', temp.nodeId);
          console.log('Source options:', this.jsplumbSourceOptions);
          console.log('Target options:', this.jsplumbTargetOptions);
          
          // 使用双层$nextTick确保DOM完全渲染
          this.$nextTick(() => {
            this.$nextTick(() => {
              const nodeElement = document.getElementById(temp.nodeId);
              console.log(`Node ${temp.nodeId} element:`, nodeElement);
              
              if (nodeElement) {
                const anchorPoints = nodeElement.querySelectorAll('.anchor-point');
                const edgeAreas = nodeElement.querySelectorAll('.edge-area');
                console.log(`Node ${temp.nodeId} anchor points found:`, anchorPoints.length);
                console.log(`Node ${temp.nodeId} edge areas found:`, edgeAreas.length);
                
                try {
                  // 绑定jsPlumb
                  this.jsPlumb.makeSource(temp.nodeId, this.jsplumbSourceOptions);
                  this.jsPlumb.makeTarget(temp.nodeId, this.jsplumbTargetOptions);
                  
                  // 测试jsPlumb是否正确绑定
                  const sourceInfo = this.jsPlumb.isSource(temp.nodeId);
                  const targetInfo = this.jsPlumb.isTarget(temp.nodeId);
                  console.log(`Node ${temp.nodeId} - isSource: ${sourceInfo}, isTarget: ${targetInfo}`);
                  
                  // 强制重绘jsPlumb
                  this.jsPlumb.revalidate(temp.nodeId);
                } catch (error) {
                  console.error(`Error setting up jsPlumb for node ${temp.nodeId}:`, error);
                }
              } else {
                console.error(`Node element ${temp.nodeId} not found!`);
              }
            });
          });
          
          this.draggableNode(temp.nodeId);
          
          // 添加新节点入场动画
          const nodeElement = document.getElementById(temp.nodeId);
          if (nodeElement) {
            nodeElement.style.transform = 'scale(0.3)';
            nodeElement.style.opacity = '0';
            nodeElement.style.transition = 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
            
            setTimeout(() => {
              nodeElement.style.transform = 'scale(1)';
              nodeElement.style.opacity = '1';
            }, 50);
          }
        }
      });
    },
    getFiled() {
      let data = [];
      for (let i = 0; i < this.data.nodeList.length; i++) {
        // if (this.data.nodeList[i].type == "0") {
        // 数据连接，字段的下拉filedList
        data.push({
          nodeName: this.data.nodeList[i].nodeName,
          nodeId: this.data.nodeList[i].nodeId,
          bizCode: this.data.nodeList[i].nodeConfig.bizCode,
          nodeType: this.data.nodeList[i].nodeType
        });
        // }
      }
      this.filedList = data;
    },
    initPanZoom() {
      if (!this.jsPlumb) {
        console.warn('jsPlumb not available, skipping panzoom initialization');
        return;
      }
      
      try {
      const mainContainer = this.jsPlumb.getContainer();
        if (!mainContainer) {
          console.warn('jsPlumb container not available');
          return;
        }
        
      const mainContainerWrap = mainContainer.parentNode;
      const pan = panzoom(mainContainer, {
        smoothScroll: false,
        bounds: true,
        // autocenter: true,
        zoomDoubleClickSpeed: 1,
        minZoom: 0.5,
        maxZoom: 2,
        // 设置滚动缩放的组合键，默认不需要组合键
        beforeWheel: e => {
          // let shouldIgnore = !e.ctrlKey
          // return shouldIgnore
        },
        beforeMouseDown: function(e) {
          // allow mouse-down panning only if altKey is down. Otherwise - ignore
          var shouldIgnore = e.ctrlKey;
          return shouldIgnore;
        }
      });
      this.jsPlumb.mainContainerWrap = mainContainerWrap;
      this.jsPlumb.pan = pan;
      // 缩放时设置jsPlumb的缩放比率
      pan.on("zoom", e => {
        const { x, y, scale } = e.getTransform();
        this.jsPlumb.setZoom(scale);
        // 根据缩放比例，缩放对齐辅助线长度和位置
        this.auxiliaryLinePos.width = (1 / scale) * 100 + "%";
        this.auxiliaryLinePos.height = (1 / scale) * 100 + "%";
        this.auxiliaryLinePos.offsetX = -(x / scale);
        this.auxiliaryLinePos.offsetY = -(y / scale);
      });
      pan.on("panend", e => {
        const { x, y, scale } = e.getTransform();
        this.auxiliaryLinePos.width = (1 / scale) * 100 + "%";
        this.auxiliaryLinePos.height = (1 / scale) * 100 + "%";
        this.auxiliaryLinePos.offsetX = -(x / scale);
        this.auxiliaryLinePos.offsetY = -(y / scale);
      });

      // 平移时设置鼠标样式
      mainContainerWrap.style.cursor = "grab";
      mainContainerWrap.addEventListener("mousedown", function wrapMousedown() {
        this.style.cursor = "grabbing";
        mainContainerWrap.addEventListener("mouseout", function wrapMouseout() {
          this.style.cursor = "grab";
        });
      });
      mainContainerWrap.addEventListener("mouseup", function wrapMouseup() {
        this.style.cursor = "grab";
      });
      } catch (error) {
        console.error('Error in initPanZoom:', error);
      }
    },
    // 删除节点
    deleteNode(node) {
      // this.typeGet = "";
      // this.type = "";
      let data = this.$store.state.templateEngine.nodeItemList;
      // console.log(data, node);
      data.some((v, index) => {
        if (v.nodeId === node.nodeId) {
          data.splice(index, 1);
          // console.log(v.nodeId);
          if (this.jsPlumb) {
          this.jsPlumb.remove(v.nodeId);
          }
          // console.log(data);
          this.typeGet = null;
          
          // 如果删除的是当前激活的节点，清除激活状态
          if (this.activeNodeId === node.nodeId) {
            this.selectedNode = null;
            this.activeNodeId = null;
            this.showConfigDrawer = false;
          }
          
          this.$store.commit("templateEngine/nodeItemList", data);
          return true;
        } else {
          return false;
        }
      });
    },

    // 更改连线状态  及节点回显
    changeLineState(node, val) {
      // 如果正在拖拽中，不触发编辑框
      if (this.isDragging) {
        return;
      }
      
      console.log("node", node);
      if (this.jsPlumb) {
      const lines = this.jsPlumb.getAllConnections();
      lines.forEach(line => {
        if (line.targetId === node.nodeId || line.sourceId === node.nodeId) {
          if (val) {
            line.canvas.classList.add("active");
          } else {
            line.canvas.classList.remove("active");
          }
        }
      });
      }
      
      // 根据val值同步节点激活状态和配置抽屉状态
      if (val) {
        this.selectedNode = node;
        this.activeNodeId = node.nodeId;
        this.showConfigDrawer = true;
      } else {
        this.selectedNode = null;
        this.activeNodeId = null;
        this.showConfigDrawer = false;
      }
      
      this.idKey = node.nodeId;
      this.idName = node.nodeName;
      this.typeGet = node.type;
      this.toolType = node.type;
      // this.select = "1";
      this.dataItem = node;
      //获取子节点ChildList
      if (val) {
        this.getNewChild(node);
      }
      if (this.typeGet == "0") {
        this.typeTitle = "基本信息";
        // this.outPutList = this.outPutList;
        if (val) {
          this.outPutNameFiled(node);
        }
      } else if (this.typeGet == "1") {
        this.typeTitle = "分组聚合";
      } else if (this.typeGet == "2") {
        this.typeTitle = "数据连接";
      } else if (this.typeGet == "3") {
        this.typeTitle = "条件函数";
      } else if (this.typeGet == "4") {
        this.typeTitle = "数据过滤";
      }
    },
    getNewChild(node) {
      this.filedNameList=[]
      let arr = [];
      if (node.childIds && node.childIds.length > 0 && node.type !== "0") {
        for (let i = 0; i < node.childIds.length; i++) {
          for (let k = 0; k < this.data.nodeList.length; k++) {
            if (node.childIds[i] == this.data.nodeList[k].nodeId) {
              arr.push(this.data.nodeList[k]);
            }
          }
        }
      }
      // 条件函数
      if (node.type == "3") {
        let nameDataList = [];
        if (node.nodeConfig.handle && node.nodeConfig.handle.length > 0) {
          for (let i = 0; i < node.nodeConfig.handle.length; i++) {
            nameDataList.push({
              fieldDesc: node.nodeConfig.handle[i].fieldDesc,
              fieldNodeId: node.nodeId,
              fieldName: node.nodeConfig.handle[i].fieldName,
              fieldDesc1: node.nodeConfig.handle[i].fieldDesc //回显时用
            });
          }
        }
        this.filedNameList = nameDataList;
      }
      //聚合函数
      if (node.type == "1") {
        let namedataList1 = [];
        if (
          node.nodeConfig.group &&
          node.nodeConfig.group.default &&
          node.nodeConfig.group.default.length > 0
        ) {
          for (let i = 0; i < node.nodeConfig.group.default.length; i++) {
            namedataList1.push({
              fieldDesc: node.nodeConfig.group.default[i].fieldDesc,
              fieldNodeId: node.nodeId,
              fieldName: node.nodeConfig.group.default[i].fieldName,
              fieldDesc1: node.nodeConfig.group.default[i].fieldDesc //回显时用
            });
          }
        }
        let namedataList2 = [];
        if (
          node.nodeConfig.group &&
          node.nodeConfig.group.custom &&
          node.nodeConfig.group.custom.length > 0
        ) {
          for (let i = 0; i < node.nodeConfig.group.custom.length; i++) {
            namedataList2.push({
              fieldDesc: node.nodeConfig.group.custom[i].fieldDesc,
              fieldNodeId: node.nodeId,
              fieldName: node.nodeConfig.group.custom[i].fieldName,
              fieldDesc1: node.nodeConfig.group.custom[i].fieldDesc //回显时用
            });
          }
        }
        this.filedNameList = namedataList2.concat(namedataList1);
      }
      console.log("arr", arr);
      // 子节点列表
      this.childList = arr;
      if (arr.length > 0) {
        this.getChildrenList(arr);
      } else {
        // 分组聚合和条件函数展示当前字段输出
        if (node.type == "1" || node.type == "3"){
          this.filedOutPutList = this.filedNameList;
        }
      }
    },
    getChildrenList(node) {
      this.filedOutPutList = [];
      // if (node.children && node.children.length > 0 && node.type !== "0") {
      let arr = node;
      let data = [];
      let data1 = [];
      let data2 = [];
      // 默认字段拼接下拉option
      for (let i = 0; i < arr.length; i++) {
        if (
          arr[i].output &&
          arr[i].output.fields &&
          arr[i].output.fields.default &&
          arr[i].output.fields.default.length
        ) {
          for (let k = 0; k < arr[i].output.fields.default.length; k++) {
            // // 循环展示字段
            // data.push({
            //   fieldDesc:
            //     // arr[i].nodeName +
            //     // "-" +
            //     arr[i].output.fields.default[k].fieldDesc,
            //   fieldName: arr[i].output.fields.default[k].fieldName,
            //   isIndex: arr[i].output.fields.default[k].isIndex
            //     ? arr[i].output.fields.default[k].isIndex
            //     : "0",
            //   fieldDesc1: arr[i].output.fields.default[k].fieldDesc //回显时用
            // });
            // 下拉option
            data2.push({
              fieldDesc:
                // arr[i].nodeName +
                // "-" +
                arr[i].output.fields.default[k].fieldDesc,
              fieldNodeId: arr[i].nodeId,
              fieldName: arr[i].output.fields.default[k].fieldName,
              fieldDesc1: arr[i].output.fields.default[k].fieldDesc //回显时用
            });
          }
        }

        if (arr[i].output.fields.custom && arr[i].output.fields.custom.length) {
          for (let j = 0; j < arr[i].output.fields.custom.length; j++) {
            data1.push({
              fieldDesc: arr[i].output.fields.custom[j].fieldDesc,
              fieldName: arr[i].output.fields.custom[j].fieldName,
              fieldNodeId: arr[i].nodeId,
              isIndex: arr[i].output.fields.custom[j].isIndex
                ? arr[i].output.fields.custom[j].isIndex
                : "0"
            });
          }
        }
      }
      // console.log("this.filedNameList", this.filedNameList);
      this.filedOutPutList = data2.concat(data1).concat(this.filedNameList); //自定义字段包含前面字段输出
      // console.log("filedOutPutList", this.filedOutPutList);
      // }
    },
    // 初始化节点位置（以画布中心为基准的网格布局）
    fixNodesPosition() {
      if (this.data.nodeList && this.data.nodeList.length > 0) {
        const container = this.jsPlumb ? this.jsPlumb.getContainer() : document.getElementById('flow');
        if (!container) return;
        
        const containerRect = container.getBoundingClientRect();
        const scale = this.getScale();
        
        // 计算画布中心
        const centerX = containerRect.width / 2 / scale;
        const centerY = containerRect.height / 2 / scale;
        
        // 节点布局参数
        const gridSpacing = 200; // 节点间距
        const maxCols = 4; // 每行最多4个节点
        
        // 计算布局尺寸
        const totalNodes = this.data.nodeList.length;
        const cols = Math.min(totalNodes, maxCols);
        const rows = Math.ceil(totalNodes / maxCols);
        
        // 计算起始位置（让整个布局居中）
        const totalWidth = (cols - 1) * gridSpacing;
        const totalHeight = (rows - 1) * gridSpacing;
        const startX = centerX - totalWidth / 2;
        const startY = centerY - totalHeight / 2;
        
        // 为每个节点分配位置
        this.data.nodeList.forEach((node, index) => {
          const row = Math.floor(index / maxCols);
          const col = index % maxCols;
          
          // 如果某一行的节点数少于maxCols，让该行居中
          const currentRowNodes = Math.min(maxCols, totalNodes - row * maxCols);
          const rowOffset = (maxCols - currentRowNodes) * gridSpacing / 2;
          
          const x = startX + col * gridSpacing + rowOffset;
          const y = startY + row * gridSpacing;
          
          // 对齐到20px网格
          node.positionLeft = Math.round(x / 20) * 20 + "px";
          node.positionTop = Math.round(y / 20) * 20 + "px";
        });
        
        // 更新store
        this.$store.commit("templateEngine/nodeItemList", this.data.nodeList);
        
        // 重新绘制连线
        if (this.jsPlumb) {
          this.$nextTick(() => {
            this.jsPlumb.repaintEverything();
          });
        }
      }
    },
    // 清空画布
    clearCanvas() {
      this.$refs.clearCanvasDialog.show();
    },

    // 确认清空画布
    confirmClearCanvas() {
      this.data.nodeList = [];
      if (this.jsPlumb) {
        this.jsPlumb.reset();
      }
      
      // 清空画布时清除激活状态
      this.selectedNode = null;
      this.activeNodeId = null;
      this.showConfigDrawer = false;
      this.data.nodeList=[]
      this.$store.commit("templateEngine/nodeItemList", this.data.nodeList);
      this.$message.success('画布已清空');
      this.$refs.clearCanvasDialog.hide();
    },

    // 自动布局
    autoLayout() {
      if (this.data.nodeList.length === 0) {
        this.$message.warning('画布中没有节点，无法进行自动布局');
        return;
      }
      
      this.fixNodesPosition();
      this.$message.success('自动布局完成');
    },
    
    // 递归移除所有节点及其子节点中的optionAndList字段
    removeOptionAndList(nodes) {
      if (!Array.isArray(nodes)) return;
      
      nodes.forEach(node => {
        // 移除当前节点的optionAndList字段
        if (node.output && node.output.fields && node.output.fields.default) {
          node.output.fields.default.forEach(field => {
            if (field.optionAndList) {
              delete field.optionAndList;
            }
          });
        }
        
        // 处理子节点
        if (node.childIds && node.childIds.length > 0) {
          // 查找并处理所有子节点
          const childNodes = nodes.filter(child => node.childIds.includes(child.nodeId));
          this.removeOptionAndList(childNodes);
        }
      });
    },
    handleNodeClick(node) {
      // 如果正在拖拽中，不触发点击事件
      if (this.isDragging) {
        console.log('Node click ignored due to dragging state');
        return;
      }
      
      console.log('Node clicked:', node.nodeName, 'Opening config drawer');
      this.selectedNode = node;
      this.activeNodeId = node.nodeId;
      this.showConfigDrawer = true;
    },
    closeConfigDrawer() {
      this.showConfigDrawer = false;
      this.selectedNode = null;
      this.activeNodeId = null;
    },
    
    // 切换组件面板显示状态
    toggleComponentDrawer() {
      this.showComponentDrawer = !this.showComponentDrawer;
      
      // 用户体验反馈
      if (this.showComponentDrawer) {
        console.log('组件面板已打开');
        // 可以添加打开时的额外逻辑，比如焦点管理
      } else {
        console.log('组件面板已关闭');
        // 关闭时可以将焦点返回到画布
        this.$nextTick(() => {
          const canvas = document.querySelector('#flow');
          if (canvas) {
            canvas.focus();
          }
        });
      }
    },
    handleKeydown(e) {
      if (e.key === 'Escape') {
        // 如果组件面板打开，先关闭组件面板
        if (this.showComponentDrawer) {
          this.showComponentDrawer = false;
        } else if (this.showConfigDrawer) {
          // 否则关闭配置面板
          this.closeConfigDrawer();
        }
      }
      
      // Ctrl+M 或 Cmd+M 切换组件面板
      if ((e.ctrlKey || e.metaKey) && e.key === 'm') {
        e.preventDefault();
        this.toggleComponentDrawer();
      }
    },
    
    // 处理鼠标滚轮事件
    handleWheel(e) {
      // ConfigDrawer组件现在自己处理滚轮事件，这里不再需要特殊处理
    },

    // 处理组件面板章节切换
    handleSectionToggle(sectionType, expanded) {
      // 可以在这里添加切换时的额外逻辑，比如记录用户偏好等
      console.log(`${sectionType} section ${expanded ? 'expanded' : 'collapsed'}`);
    },
    
    // 处理组件项悬停
    handleItemHover(item, isHover) {
      // 可以在这里添加悬停时的额外逻辑，比如显示详细信息等
      if (isHover) {
        console.log('Hovering over component:', item.nodeName);
      }
    },
    
    // 处理拖拽结束
    handleDragEnd() {
      // 拖拽结束后的清理工作
      document.body.style.cursor = '';
      document.body.classList.remove('dragging', 'drag-over');
    }
  }
};
</script>
<style lang="less" scoped>
.match-source-container {
  width: 100%;
  height: 100vh;
  background: #fbf6ee;
  display: flex;
  flex-direction: column;

  // 页面头部
  .page-header {
    background: white;
    padding: 20px 24px;
    border-bottom: 1px solid #f7ecdd;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-section {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 20px;
          font-weight: 600;
          color: #2c3e50;
          display: flex;
          align-items: center;
          gap: 12px;

          i {
            color: #D7A256;
            font-size: 24px;
          }
        }

        .page-subtitle {
          margin: 0;
          color: #7f8c8d;
          font-size: 14px;
        }
      }

      .action-section {
        display: flex;
        gap: 12px;

        .save-btn, .draft-btn {
          height: 36px;
          padding: 0 20px;
          border-radius: 6px;
          font-weight: 500;
          transition: all 0.3s ease;

      &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }

          i {
            margin-right: 6px;
          }
        }
      }
    }
  }

  // 主要内容区域
  .main-content {
    flex: 1;
    display: flex;
    overflow: hidden;

    // 画布区域
    .canvas-container {
      flex: 1;
      background: white;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      position: relative; // 添加相对定位



      .flow-canvas {
        flex: 1;
        position: relative;
        width: 100%;
        height: 100%;
        
        // 添加深度和质感的多层覆盖
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          // 创建微妙的径向渐变，增强中心聚焦感，使用更轻的透明度
          background: 
            radial-gradient(ellipse at center, transparent 0%, rgba(215, 162, 86, 0.01) 50%, rgba(215, 162, 86, 0.02) 100%),
            // 添加纸张质感
            radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
          pointer-events: none;
          z-index: 1;
        }
        
        // 现代化边框设计和中心线
        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          border: 1px solid rgba(215, 162, 86, 0.04);
          border-radius: 12px;
          // 添加内阴影效果，增强深度感
          box-shadow: 
            inset 0 1px 0 rgba(255, 255, 255, 0.3),
            inset 0 -1px 0 rgba(215, 162, 86, 0.02);
          pointer-events: none;
          z-index: 1;
        }

        // 响应式网格优化
        @media (max-width: 1200px) {
          background-size: 
            80px 100%,
            100% 80px,
            16px 100%,
            100% 16px,
            16px 16px;
        }

        @media (max-width: 768px) {
          background-size: 
            60px 100%,
            100% 60px,
            12px 100%,
            100% 12px,
            12px 12px;
        }

        @media (max-width: 480px) {
          background-size: 
            40px 100%,
            100% 40px,
            10px 100%,
            100% 10px,
            10px 10px;
        }

        .flow-content {
          width: 2001px;  // 设置合理的画布宽度
          height: 1501px; // 设置合理的画布高度
          position: relative;
          z-index: 2; // 确保内容在背景网格之上
          // 现代化渐变背景 - 与整体UI风格保持一致，使用更浅的色调
          background: linear-gradient(135deg, 
            #fefdfb 0%, 
            #ffffff 25%, 
            #ffffff 50%, 
            #ffffff 75%, 
            #fefdfb 100%);
          
          // 专业级网格系统 - 参考Figma/Sketch等设计工具，使用更微妙的透明度
          background-image: 
            // 主要网格线 - 100px间距（包含加强的中心线）
            linear-gradient(to right, rgba(128, 128, 128, 0.06) 1px, transparent 1px),
            linear-gradient(to bottom, rgba(128, 128, 128, 0.06) 1px, transparent 1px),
            // 次要网格线 - 20px间距
            linear-gradient(to right, rgba(215, 162, 86, 0.03) 1px, transparent 1px),
            linear-gradient(to bottom, rgba(215, 162, 86, 0.03) 1px, transparent 1px),
            // 微细网格点 - 增强对齐感
            radial-gradient(circle, rgba(215, 162, 86, 0.04) 0.5px, transparent 0.5px);
          
          background-size: 
            100px 100%,    // 主要垂直网格线
            100% 100px,    // 主要水平网格线
            20px 100%,     // 次要垂直网格线
            100% 20px,     // 次要水平网格线
            20px 20px;     // 网格点
          
          background-position: 
            0 0,           // 主要垂直网格线
            0 0,           // 主要水平网格线
            0 0,           // 次要垂直网格线
            0 0,           // 次要水平网格线
            0 0;           // 网格点
        }

        // 悬浮标题样式
        .floating-title {
          position: absolute;
          top: 20px;
          left: 20px;
          // transform: translateX(-50%);
          z-index: 1100;
          display: flex;
          justify-content: center;
          align-items: center;
          // 现代化半透明毛玻璃风格
          gap: 12px;
          // background: rgba(230, 213, 213, 0.15);
          background:rgba(251, 246, 238, 0.2);
          // background:#fbf6ee;
          backdrop-filter: blur(20px);
          // border: 1px solid rgba(202, 69, 69, 0.3);
          border-radius: 10px;
          padding: 8px 12px;
          // box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
          // min-height: 40px;
          // transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          transition: left 0.3s cubic-bezier(0.4,0,0.2,1);
          &.panel-opened {
            left: 330px; // 这里的220px可以根据你的组件面板宽度调整
          }

          .title-content {
            display: flex;
            align-items: center;
            // gap: 8px;

            .title-text {
              font-size: 16px;
              font-weight: 600;
              color: #2c3e50;
            }

            .subtitle-text {
              font-size: 13px;
              font-weight: 400;
              color: #7f8c8d;
              margin-left: 4px;
            }
          }
        }


        
        // 现代化辅助线样式
        .auxiliary-line-x {
          position: absolute;
          height: 2px;
          background: linear-gradient(90deg, 
            transparent 0%, 
            rgba(215, 162, 86, 0.8) 10%, 
            #D7A256 50%, 
            rgba(215, 162, 86, 0.8) 90%, 
            transparent 100%);
          border-radius: 1px;
          z-index: 100;
          pointer-events: none;
          box-shadow: 
            0 0 8px rgba(215, 162, 86, 0.4),
            0 1px 0 rgba(255, 255, 255, 0.8);
          
          // 添加动态指示器
          &::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: #D7A256;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 0 6px rgba(215, 162, 86, 0.6);
          }
        }

        .auxiliary-line-y {
          position: absolute;
          width: 2px;
          background: linear-gradient(180deg, 
            transparent 0%, 
            rgba(215, 162, 86, 0.8) 10%, 
            #D7A256 50%, 
            rgba(215, 162, 86, 0.8) 90%, 
            transparent 100%);
          border-radius: 1px;
          z-index: 100;
          pointer-events: none;
          box-shadow: 
            0 0 8px rgba(215, 162, 86, 0.4),
            1px 0 0 rgba(255, 255, 255, 0.8);
          
          // 添加动态指示器
          &::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: #D7A256;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 0 6px rgba(215, 162, 86, 0.6);
          }
        }

        // 现代化空画布提示
        .empty-canvas {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          text-align: center;
          z-index: 10;
          
          .empty-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px 32px;
            border: 1px solid rgba(128, 128, 128, 0.1);
            box-shadow: 
              0 8px 32px rgba(215, 162, 86, 0.15),
              inset 0 1px 0 rgba(255, 255, 255, 0.8);
            max-width: 360px;
            transition: all 0.3s ease;
            
            &:hover {
              transform: translateY(-2px);
              box-shadow: 
                0 12px 40px rgba(215, 162, 86, 0.25),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            }
            
            .empty-icon {
              font-size: 56px;
              margin-bottom: 20px;
              background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
              position: relative;
              
              // 添加发光效果
              &::before {
                content: attr(class);
                position: absolute;
                top: 0;
                left: 50%;
                transform: translateX(-50%);
                background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                filter: blur(8px);
                opacity: 0.3;
                z-index: -1;
              }
            }

            h3 {
              margin: 0 0 12px 0;
              font-size: 18px;
              font-weight: 600;
              color: #2c3e50;
              background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
            }

            p {
              margin: 0 0 24px 0;
              font-size: 14px;
              color: #7f8c8d;
              line-height: 1.6;
            }
            
            // 添加装饰性元素
            &::before {
              content: '';
              position: absolute;
              top: -2px;
              left: -2px;
              right: -2px;
              bottom: -2px;
              border-radius: 22px;
              z-index: -1;
              opacity: 0;
              transition: opacity 0.3s ease;
            }
            
            &:hover::before {
              opacity: 1;
            }
          }
        }


      }
    }
  }

  // 删除确认弹窗
  .modern-dialog {
    /deep/ .el-dialog {
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);

      .el-dialog__header {
        background: #fbf6ee;
        padding: 20px 24px;
        border-bottom: 1px solid #f7ecdd;

        .el-dialog__title {
          font-weight: 600;
          color: #2c3e50;
        }
      }

      .el-dialog__body {
        display: flex;
        flex-direction: column;
        height: 100%;
        position: relative;
      }

      .el-dialog__footer {
        padding: 16px 24px;
        background: #fbf6ee;
        border-top: 1px solid #f7ecdd;
        text-align: right;

        .dialog-footer {
          .el-button {
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-1px);
            }
          }
        }
      }
    }
  }

  .dialog-content {
    text-align: center;
    padding: 20px 0;

    .dialog-icon {
      font-size: 48px;
      color: #e6a23c;
      margin-bottom: 16px;
    }

    .dialog-text {
      h4 {
        margin: 0 0 8px 0;
        color: #2c3e50;
        font-size: 16px;
      }

      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .match-source-container {
    .canvas-container {
      .flow-canvas {
        .floating-toolbar {
          .toolbar-content {
            padding: 8px 12px;
            gap: 12px;
            .toolbar-icon {
              height: 24px;
              width: 24px;
              font-size: 14px; // 统一为14px - 响应式按钮文字
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .match-source-container {
    .canvas-container {
      .flow-canvas {
        .floating-toolbar {
          .toolbar-content {
            padding: 8px 12px;
            gap: 8px;
            
            .toolbar-icon {
              height: 18px;
              width: 18px;
              font-size: 13px; // 统一为13px - 小屏幕按钮文字
            }
          }
        }
      }
    }
  }
}

.floating-toolbar {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  background: transparent;
  border: none;
  box-shadow: none;
  z-index: 1100; // 降低z-index，确保在底部抽屉之下
  transition: all 0.3s ease;
  padding: 12px;
  
  .toolbar-content {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 50px;
    padding: 8px 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    
    .toolbar-icon {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: transparent;
      border: none;
      color: #606266;
      font-size: 16px; // 统一为16px - 工具栏图标
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
      
      &:hover {
        background: rgba(255, 255, 255, 0.15);
        color: #D7A256;
        transform: translateY(-2px) scale(1.1);
        box-shadow: 0 4px 20px rgba(255, 255, 255, 0.3);
      }
      
      &:active {
        transform: translateY(0) scale(1.05);
      }
      
      // 激活状态按钮
      &.active {
        background: #D7A256;
        color: white;
        
        &:hover {
          background: #D7A256;
          box-shadow: 0 4px 20px rgba(255, 255, 255, 0.5);
        }
      }
      
      // 主要操作按钮（保存）
      &.primary {
        background: #67c23a;
        color: white;
        
        &:hover {
          background: #85ce61;
          box-shadow: 0 4px 20px rgba(103, 194, 58, 0.5);
        }
      }
      
      // 危险操作按钮（清空）
      &:nth-child(2):hover {
        color: #f56c6c;
        box-shadow: 0 4px 20px rgba(245, 108, 108, 0.3);
      }
      
      i {
        font-size: 16px; // 统一为16px - 工具栏图标
        margin: 0;
      }
      
      // 添加涟漪效果
      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: translate(-50%, -50%);
        transition: width 0.3s, height 0.3s;
      }
      
      &:active::before {
        width: 100%;
        height: 100%;
      }
    }
  }
  
  // 工具栏显示/隐藏动画
  &.toolbar-hidden {
    transform: translateX(-50%) translateY(100%);
    opacity: 0;
    pointer-events: none;
  }
}

// 动画增强
@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}



// 辅助线发光动画
@keyframes auxiliaryLineGlow {
  0% {
    box-shadow: 
      0 0 8px rgba(215, 162, 86, 0.4),
      0 1px 0 rgba(255, 255, 255, 0.8);
  }
  100% {
    box-shadow: 
      0 0 16px rgba(215, 162, 86, 0.6),
      0 1px 0 rgba(255, 255, 255, 1);
  }
}

// 辅助线指示点脉冲
@keyframes auxiliaryDotPulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.3);
    opacity: 0.7;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

@keyframes slideOutLeft {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  70% {
    transform: scale(0.9);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
}

// 工具提示样式增强
.el-tooltip__popper {
  background: rgba(0, 0, 0, 0.9) !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 8px 12px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  backdrop-filter: blur(10px) !important;
  
  .popper__arrow {
    border-top-color: rgba(0, 0, 0, 0.9) !important;
  }
}

// 拖拽时的全局样式
.dragging {
  cursor: grabbing;
}

// 拖拽目标高亮
.drag-over {
  .flow-canvas {
    background-color: rgba(215, 162, 86, 0.05);
    
    &::after {
      content: '释放以添加组件';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: white;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 600;
      font-size: 14px;
      z-index: 1000;
    }
  }
}

// 现代化消息提示样式
/deep/ .modern-message {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(215, 162, 86, 0.2) !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
  color: #2c3e50 !important;
  font-weight: 500 !important;
  
  .el-message__icon {
    color: #67c23a !important;
    font-size: 18px !important;
  }
  
  .el-message__content {
    font-size: 14px !important;
    line-height: 1.4 !important;
  }
}

// 连线箭头样式
/deep/ .connection-arrow {
  fill: #D7A256 !important;
  stroke: #D7A256 !important;
}

// 新增拖拽相关样式
// 可能的连接目标高亮
.possible-target {
  .node-content {
    border: 2px dashed #67c23a !important;
    background: rgba(103, 194, 58, 0.05) !important;
  }
  
  .connection-anchors .anchor-point {
    opacity: 1 !important;
    
    .anchor-dot {
      background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%) !important;
      box-shadow: 0 0 12px rgba(103, 194, 58, 0.6) !important;
    }
    
    .anchor-ring {
      border-color: rgba(103, 194, 58, 0.6) !important;
      opacity: 1 !important;
    }
  }
}

// 节点拖拽中心状态
.dragging-center {
  z-index: 1000 !important;
  opacity: 0.9;
  transform: scale(1.02);
  filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.15));
  
  .node-content {
    cursor: grabbing !important;
    border-color: #D7A256 !important;
    box-shadow: 0 8px 25px rgba(215, 162, 86, 0.25) !important;
  }
  
  .connection-anchors {
    opacity: 0.3;
  }
}

// 边缘拖拽连线状态
.dragging-edge {
  .connection-anchors {
    .anchor-point {
      opacity: 1 !important;
      
      .anchor-dot {
        background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%) !important;
        box-shadow: 0 0 15px rgba(215, 162, 86, 0.8) !important;
        transform: scale(1.3);
      }
      
      .anchor-ring {
        opacity: 1 !important;
        transform: scale(1.8);
      }
    }
  }
}

// 动画定义
@keyframes targetPulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(103, 194, 58, 0.1);
  }
}

@keyframes connectionPulse {
  0%, 100% {
    border-color: rgba(215, 162, 86, 0.6);
    transform: scale(1.8);
  }
  50% {
    border-color: rgba(215, 162, 86, 0.9);
    transform: scale(2.2);
  }
}

@keyframes anchorPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

@keyframes float-bounce {
  0% {
    transform: translateY(0) scale(1);
    box-shadow: none;
  }
  30% {
    transform: translateY(-6px) scale(1.08);
    box-shadow: 0 4px 12px rgba(215,162,86,0.10);
  }
  50% {
    transform: translateY(-10px) scale(1.12);
    box-shadow: 0 8px 24px rgba(215,162,86,0.15);
  }
  70% {
    transform: translateY(-6px) scale(1.08);
    box-shadow: 0 4px 12px rgba(215,162,86,0.10);
  }
  100% {
    transform: translateY(0) scale(1);
    box-shadow: none;
  }
}

.toggle-header-btn {
  font-size: 12px;
  color: #D7A256;
  vertical-align: middle;
  padding: 0 4px;
  // animation: float-bounce 1.8s infinite cubic-bezier(.4,0,.2,1);
  transition: color 0.2s;
}
// ... existing code ...
.center-toggle-btn {
  // position: fixed;
  // top: 140px;
  // left: 50%;
  // z-index: 2000;
  // transform: translatex(-50%);
  background: transparent;
  border: none !important;
  min-width: 48px;
  // min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  /* animation: float-bounce 1.8s infinite cubic-bezier(.4,0,.2,1);  // 移除动画 */
  // transition: color 0.2s;
}
.center-toggle-btn1 {
  // position: fixed;
  // top: 5px;
  // left: 50%;
  // z-index: 2000;
  // transform: translatex(-50%);
  background: transparent;
  border: none !important;
  min-width: 48px;
  // min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  /* animation: float-bounce 1.8s infinite cubic-bezier(.4,0,.2,1);  // 移除动画 */
  // transition: color 0.2s;
}
.center-toggle-btn {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: transparent;
      border: none;
      color: #606266;
      font-size: 16px; // 统一为16px - 工具栏图标
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
      
      &:hover {
        background: rgba(255, 255, 255, 0.15);
        color: #D7A256;
        transform: translateY(-2px) scale(1.1);
        box-shadow: 0 4px 20px rgba(255, 255, 255, 0.3);
      }
      
      &:active {
        transform: translateY(0) scale(1.05);
      }
    }
@keyframes icon-bounce-loop {
  0% {
    transform: scale(1);
  }
  20% {
    transform: scale(1.5);   // 放大幅度更大
  }
  50% {
    transform: scale(1);
  }
  70% {
    transform: scale(0.7);   // 缩小幅度更大
  }
  100% {
    transform: scale(1);
  }
}

// ... existing code ...

.center-toggle-btn i,
.center-toggle-btn1 i {
  font-size: 16px;
  display: inline-block;
  // animation: icon-bounce-loop 2s infinite cubic-bezier(.4,0,.2,1);
}

// ... existing code ...

/* 底部配置抽屉组件外层贴底部 */
.config-drawer-wrapper {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  margin-bottom: 0;
  width: 100%;
  /* 如果需要高度自适应，可以加上 height: auto; */
}

.dt-popup .el-dialog__body {
  padding-bottom: 120px;
}
.dt-popup.has-config-drawer .el-dialog__body {
  padding-bottom: 0 !important;
}
</style>
