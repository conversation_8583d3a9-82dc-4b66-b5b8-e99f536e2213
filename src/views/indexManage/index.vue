<template>
  <div class="index-manage-container">
    <!-- 使用通用表格组件 -->
    <UniversalTable
      title="指标管理"
      subtitle="管理和配置算法指标，支持计算指标和结果指标"
      title-icon="el-icon-data-analysis"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :actions="tableActions"
      :search-form-config="searchFormTemp"
      :search-params="initParam"
      :pagination-data="initParam"
      :total="total"
      add-button-text="新增指标"
      empty-title="暂无指标数据"
      empty-description="点击上方新增指标按钮开始创建"
      action-column-width="400"
      @search="normalSearch"
      @reset="normalResetQuery"
      @add="handleAdd"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @action-click="handleAction"
    >
      <!-- 自定义列内容插槽 -->
      <template #bizCode="{ row }">
        <div class="code-cell">
          <div class="code-icon">
            <i class="el-icon-document-copy"></i>
          </div>
          <span class="code-text">{{ row.bizCode }}</span>
        </div>
      </template>

      <template #name="{ row }">
        <div class="name-cell">
          <span class="name-text">{{ row.name }}</span>
        </div>
      </template>

      <template #indicatorType="{ row }">
        <div class="type-cell">
          <el-tag
            :class="row.indicatorType == 1 ? 'calculate-tag' : 'result-tag'"
            size="medium"
            effect="plain"
          >
            <i :class="row.indicatorType == 1 ? 'el-icon-cpu' : 'el-icon-data-line'"></i>
            {{ row.indicatorType == 1 ? "计算指标" : "结果指标" }}
          </el-tag>
        </div>
      </template>

      <template #remark="{ row }">
        <div class="remark-cell">
          <el-tooltip
            v-if="row.remark"
            :content="row.remark"
            :enterable="false"
            effect="dark"
            placement="top"
            popper-class="modern-tooltip"
          >
            <span class="remark-text">{{ row.remark }}</span>
          </el-tooltip>
          <span v-else class="no-remark">
            <i class="el-icon-info"></i>
            暂无备注
          </span>
        </div>
      </template>

      <template #updateTime="{ row }">
        <div class="time-cell">
          <i class="el-icon-time"></i>
          <span>{{ row.updateTime }}</span>
        </div>
      </template>

      <template #updateId="{ row }">
        <div class="user-cell">
          <div class="user-avatar">
            <i class="el-icon-user"></i>
          </div>
          <span>{{ row.updateId | getNickName("row.updateId") }}</span>
        </div>
      </template>
    </UniversalTable>

    <!-- 新增/编辑指标弹窗 -->
    <UniversalFormDialog
      v-model="showEditDialog"
      :form-data="editForm"
      :form-fields="indicatorFormFields"
      :form-rules="editRules"
      :is-edit="editForm.isEdit"
      :loading="saveLoading"
      add-title="新增指标"
      edit-title="编辑指标"
      @confirm="handleSaveIndicator"
      @cancel="handleCancel"
      @close="handleDialogClose"
    />

    <!-- 删除确认弹窗 -->
    <ConfirmDialog
      ref="confirmDialog"
      title="确认删除"
      message="删除后无法恢复，请确认是否删除该指标？"
      icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
      @confirm="confirmUpdate"
    />
  </div>
</template>
<script>
import { algoindicatorPage, algoindicatorDel, algoindicatorAdd, algoindicatorUpdate, algoindicatorGetJSONConfig } from "@/api/template/index.js";
import { getDicItemList } from "@/config/tool";
import { baseComponent } from "@/utils/common";
import { validate, validateAlls } from "@/config/validation";
import ConfirmDialog from "@/components/layouts/ConfirmDialog.vue";
import UniversalTable from "@/components/layouts/UniversalTable.vue";
import UniversalFormDialog from "@/components/layouts/UniversalFormDialog.vue";

export default {
  name: "indexManage",
  mixins: [baseComponent],
  components: {
    ConfirmDialog,
    UniversalTable,
    UniversalFormDialog
  },
  data() {
    return {
      toolListProps: {
        toolTitle: "指标管理",
        toolList: [
          {
            name: "新增",
            btnCode: "",
            type: "add"
          }
        ]
      },
      searchFormTemp: [
        {
          label: "指标名称",
          name: "name",
          type: "input",
          placeholder: "请输入指标名称"
        },
        {
          label: "指标类型",
          name: "indicatorType",
          type: "select",
          placeholder: "请选择指标类型",
          list: [
            {
              dicItemCode: "1",
              dicItemName: "计算指标"
            },
            {
              dicItemCode: "2",
              dicItemName: "结果指标"
            }
          ]
        }
      ],
      rules: {
        name: [
          {
            required: true,
            validator: validate,
            trigger: "blur",
            min: 1,
            max: 30,
            regax: [
              {
                message: "请输入30个字符以内不能包含特殊字符",
                ruleFormat: /^[a-zA-Z0-9\u4e00-\u9fa5]+$/i
              }
            ]
          }
        ]
      },
      tableData: [],
      initParam: {
        param: {
          indicatorType: "",
          name: ""
        },
        pageSize: 10,
        pageNum: 1
      },
      total: 0,
      showCopyPopup: false,
      indicatorId: "",
      copyObj: {
        name: "",
        indicatorId: ""
      },
      loading: false,
      // 新增/编辑弹窗相关
      showEditDialog: false,
      saveLoading: false,
      editForm: {
        indicatorId: "",
        bizCode: "",
        name: "",
        indicatorType: "",
        remark: "",
        isEdit: false
      },
      indicatorTypeOptions: [
        { value: 1, label: "计算指标" },
        { value: 2, label: "结果指标" }
      ],
      editRules: {
        bizCode: [
          { required: true, message: "请输入指标编码", trigger: "blur" },
          { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" }
        ],
        name: [
          { required: true, message: "请输入指标名称", trigger: "blur" },
          { min: 1, max: 100, message: "长度在 1 到 100 个字符", trigger: "blur" }
        ],
        indicatorType: [
          { required: true, message: "请选择指标类型", trigger: "change" }
        ]
      },
      // 表格列配置
      tableColumns: [
        {
          prop: 'bizCode',
          label: '指标编码',
          width: 150,
          align: 'center'
        },
        {
          prop: 'name',
          label: '指标名称',
          width: 250,
          align: 'center'
        },
        {
          prop: 'indicatorType',
          label: '指标类型',
          width: 140,
          align: 'center'
        },
        {
          prop: 'remark',
          label: '备注',
          minWidth: 200,
          align: 'center'
        },
        {
          prop: 'updateTime',
          label: '修改时间',
          width: 180,
          align: 'center'
        },
        {
          prop: 'updateId',
          label: '修改人',
          width: 120,
          align: 'center'
        }
      ],
      // 表格操作按钮配置
      tableActions: [
        {
          key: 'config',
          label: '配置',
          icon: 'el-icon-setting',
          class: 'config-btn'
        },
        {
          key: 'view',
          label: '查看',
          icon: 'el-icon-view',
          class: 'config-btn'
        },
        {
          key: 'edit',
          label: '编辑',
          icon: 'el-icon-edit',
          class: 'edit-btn'
        },
        {
          key: 'version',
          label: '版本库',
          icon: 'el-icon-files',
          class: 'version-btn'
        },
        {
          key: 'delete',
          label: '删除',
          icon: 'el-icon-delete',
          class: 'delete-btn'
        }
      ],
      // 表单字段配置
      indicatorFormFields: [
        {
          type: 'input',
          prop: 'bizCode',
          label: '指标编码',
          placeholder: '请输入指标编码',
          maxlength: 50,
          disabledOnEdit: true
        },
        {
          type: 'input',
          prop: 'name',
          label: '指标名称',
          placeholder: '请输入指标名称',
          maxlength: 100
        },
        {
          type: 'select',
          prop: 'indicatorType',
          label: '指标类型',
          placeholder: '请选择指标类型',
          options: [
            { value: 1, label: '计算指标' },
            { value: 2, label: '结果指标' }
          ]
        },
        {
          type: 'textarea',
          prop: 'remark',
          label: '备注',
          placeholder: '请输入备注信息',
          maxlength: 200,
          showWordLimit: true
        }
      ]
    };
  },
  created() {
    this.initData();
    this.setCacheArr("add");
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    strategyMatchObj() {
      return this.$store.state.strategyMatchManage.strategyMatchObj;
    },
    cacheArr() {
      return this.$store.state["layoutStore"].cacheArrName;
    },
    notCacheArr() {
      return this.$store.state["layoutStore"].notCacheArrName;
    }
  },
  methods: {
    //初始化数据
    async initData() {
      // await this.getDictList();
      this.initList();
    },
    async initList() {
      let res = await algoindicatorPage(this.initParam);
      if (!res) {
        return;
      }
      if (res.list && res.list.length > 0) {
        this.tableData = res.list;
        this.initParam.pageNum = res.pageNum;
        this.initParam.pageSize = res.pageSize;
        this.total = Number(res.total);
      } else {
        this.tableData = [];
        this.total = Number(res.total);
      }
    },
    handleTool(item) {
      if (item.type == "add") {
        this.handleAdd();
      }
    },
    // 处理表格操作按钮点击
    handleAction(actionData) {
      const { action, row } = actionData;
      switch (action) {
        case 'config':
          this.handleSourceConfig(row);
          break;
        case 'edit':
          this.handleUpdate(row);
          break;
        case 'version':
          this.handleVisionList(row);
          break;
        case 'view':
          this.handleViewConfig(row);
          break;
        case 'delete':
          this.handleDel(row);
          break;
        default:
          break;
      }
    },
    handleVisionList(row) {
      this.$store.commit("templateEngine/visionList", row);
      this.$router.push({
        name: "visionList",
        query: {
          indicatorId: row.indicatorId,
          name: row.name,
          bizCode: row.bizCode
        }
      });
    },
    handleUpdate(row) {
      this.handleEdit(row);
    },
    // 查看指标配置JSON
    handleViewConfig(row) {
      const url = `/indicator-config-view?indicatorId=${row.indicatorId}&bizCode=${encodeURIComponent(row.bizCode)}&name=${encodeURIComponent(row.name)}`;
      window.open(url, '_blank');
    },
    normalSearch(data) {
      this.initParam = data;
      this.initList();
    },
    normalResetQuery() {
      this.initParam.pageNum = 1;
      this.initParam.pageSize = 10;
      this.initParam.param = {
        indicatorType: "",
        name: ""
      };
      this.initList();
    },
    handleDel(row) {
      this.indicatorId = row.indicatorId;
      this.$refs.confirmDialog.show();
    },
    async confirmUpdate() {
      let res = await algoindicatorDel({ indicatorId: this.indicatorId });
      if (!res) {
        return;
      }
      this.initList();
      this.$refs.confirmDialog.hide();
    },
    handleView(row) {
      this.setCacheArr("del");
      this.handleRoute(row, "sourceMatchFieldConfigView");
    },
    handleSourceConfig(row) {
      this.handleRoute(row, "matchSource");
      this.$router.push({
        name: "matchSource",
        query: {
          indicatorId: row.indicatorId
        }
      });
    },
    handleIndicatorConfig(row) {
      this.handleRoute(row, "strategyMatchIndicatorList");
    },
    handleRoute(row, name) {
      this.setQueryParam(this.initParam, "strategyMatchParam");
      this.$store.commit("strategyMatchManage/setStrategyMatchObj", row);
      this.$router.push({
        name: name,
        query: {
          indicatorId: row.indicatorId
        }
      });
    },
    setCacheArr(type) {
      let arr = ["indexManage"];
      this._.each(arr, item => {
        console.log(item)
        this.$store.commit("layoutStore/setCacheArr", {
          status: type,
          routeName: item
        });
      });
    },
    // 获取字典/标签/分组
    async getDictList() {
      let res = await getDicItemList("sct.match.rule");
      this.setSearchFormTemp("matchRule", res);
      res = await getDicItemList("sct.source.config.status");
      this.setSearchFormTemp("remark", res);
      this.setSearchFormTemp("indicatorConfigStatus", res);
      res = await getDicItemList("gen.yesorno.num");
      this.setSearchFormTemp("status", res);
    },

    refreshData() {
      this.initList();
    },
    handleSizeChange(size) {
      this.initParam.pageSize = size;
      this.initList();
    },
    handleCurrentChange(num) {
      this.initParam.pageNum = num;
      this.initList();
    },
    // 新增指标
    handleAdd() {
      this.resetEditForm();
      this.editForm.isEdit = false;
      this.showEditDialog = true;
    },
    // 编辑指标
    handleEdit(row) {
      this.resetEditForm();
      this.editForm = {
        indicatorId: row.indicatorId,
        bizCode: row.bizCode,
        name: row.name,
        indicatorType: Number(row.indicatorType), // 确保类型为数字
        remark: row.remark || "",
        isEdit: true
      };
      this.showEditDialog = true;
    },
    // 重置表单
    resetEditForm() {
      this.editForm = {
        indicatorId: "",
        bizCode: "",
        name: "",
        indicatorType: "",
        remark: "",
        isEdit: false
      };
      // 使用nextTick确保DOM更新后再清除验证状态
      this.$nextTick(() => {
        if (this.$refs.editFormRef) {
          this.$refs.editFormRef.clearValidate();
        }
      });
    },
    // 对话框关闭时的处理
    handleDialogClose() {
      this.resetEditForm();
    },
    // 取消按钮处理
    handleCancel() {
      this.showEditDialog = false;
      this.resetEditForm();
    },
    // 保存指标
    async handleSaveIndicator(data) {
      try {
        this.saveLoading = true;
        
        const apiData = {
          bizCode: data.formData.bizCode,
          name: data.formData.name,
          indicatorType: data.formData.indicatorType,
          remark: data.formData.remark
        };
        
        if (data.isEdit) {
          apiData.indicatorId = data.formData.indicatorId;
        }
        
        // 调用后台API
        let res = data.isEdit ? 
          await algoindicatorUpdate(apiData) : 
          await algoindicatorAdd(apiData);
        
        this.$message.success(data.isEdit ? '更新成功' : '新增成功');
        this.showEditDialog = false;
        this.initList(); // 刷新列表
        
      } catch (error) {
        console.error('保存失败:', error);
      } finally {
        this.saveLoading = false;
      }
    }
  }
};
</script>

<style lang="less" scoped>
.index-manage-container {
  // 表格单元格样式 - 自定义插槽样式
  .code-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    .code-icon {
      width: 24px;
      height: 24px;
      background: rgba(215, 162, 86, 0.1);
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        color: #D7A256;
        font-size: 12px;
      }
    }

    .code-text {
      font-family: 'Courier New', monospace;
      font-weight: 500;
      color: #2c3e50;
      font-size: 13px;
    }
  }

  .name-cell {
    .name-text {
      font-weight: 500;
      color: #2c3e50;
    }
  }

  .type-cell {
    .calculate-tag {
      background: rgba(64, 158, 255, 0.1);
      color: #409eff;
      border: 1px solid rgba(64, 158, 255, 0.3);

      i {
        margin-right: 4px;
      }
    }

    .result-tag {
      background: rgba(103, 194, 58, 0.1);
      color: #67c23a;
      border: 1px solid rgba(103, 194, 58, 0.3);

      i {
        margin-right: 4px;
      }
    }
  }

  .remark-cell {
    .remark-text {
      color: #606266;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
    }

    .no-remark {
      color: #c0c4cc;
      font-style: italic;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;

      i {
        font-size: 12px;
      }
    }
  }

  .time-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    color: #606266;
    font-size: 13px;

    i {
      color: #D7A256;
      font-size: 14px;
    }
  }

  .user-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #606266;
    font-size: 13px;

    .user-avatar {
      width: 24px;
      height: 24px;
      background: rgba(215, 162, 86, 0.1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        color: #D7A256;
        font-size: 12px;
      }
    }
  }
}

// 全局样式
/deep/ .modern-tooltip {
  background: #2c3e50 !important;
  color: white !important;
  border-radius: 8px !important;
  padding: 8px 12px !important;
  font-size: 12px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/deep/ .el-loading-mask {
  background-color: rgba(251, 246, 238, 0.8) !important;
}
</style>
