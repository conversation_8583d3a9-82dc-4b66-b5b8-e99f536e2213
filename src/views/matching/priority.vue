<template>
  <div class="strategy-match-update">
    <el-breadcrumb separator-class="el-icon-arrow-right" class="dt-bread">
      <el-breadcrumb-item :to="{ name: 'matchinglist' }"
        >匹配数据源管理</el-breadcrumb-item
      >
      <el-breadcrumb-item
        :style="{ color: $store.state.layoutStore.themeObj.color }"
        >{{ getText }}</el-breadcrumb-item
      >
    </el-breadcrumb>
    <TableToolTemp :toolListProps="titleListPros"></TableToolTemp>
    <el-form
      :model="updateForm"
      label-width="160px"
      :rules="rules"
      ref="updateForm"
      label-position="left"
      class="pdl-20"
    >
      <el-form-item label="优先权名称" prop="priorityName">
        <el-input
          v-model="updateForm.priorityName"
          class="dt-input-width"
          placeholder="请输入30个字符以内不能包含特殊字符"
        ></el-input>
      </el-form-item>
      <el-form-item label="优先权规则" prop="priorityRule">
        <el-select
          v-model="updateForm.priorityRule"
          placeholder="请选择"
          class="dt-select"
        >
          <el-option
            v-for="(item, index) in commonData.priorityRule"
            :key="index"
            :label="item.dicItemName"
            :value="item.dicItemCode"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="匹配数据源字段" prop="matchSource">
        <el-select
          v-model="updateForm.matchSource"
          placeholder="请选择"
          class="dt-select"
        >
          <el-option
            v-for="(item, index) in commonData.matchSource"
            :key="index"
            :label="item.name"
            :value="item.fieldId"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="优先权描述" prop="description">
        <el-input type="textarea" v-model="updateForm.description"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submit">保存</el-button>
        <el-button
          @click="goBack"
          type="primary"
          plain
          :style="{ color: $store.state.layoutStore.themeObj.color }"
          >取消</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import { validate, validateAlls } from "@/config/validation";
import { getDicItemList } from "@/config/tool.js";
import {
  savePriority,
  getPriority,
  getMatchKeys,
} from "@/api/allocation/index.js";

export default {
  name: "matchingPriority",
  data() {
    return {
      titleListPros: {
        toolTitle: "",
      },
      rules: {
        priorityName: [
          {
            required: true,
            validator: validate,
            trigger: "blur",
            min: 1,
            max: 30,
            regax: [
              {
                message: "请输入30个字符以内不能包含特殊字符",
                ruleFormat: /^[a-zA-Z0-9\u4e00-\u9fa5]+$/i,
              },
            ],
          },
        ],
        priorityRule: [
          {
            required: true,
            min: 1,
            max: 100,
            validator: validate,
            trigger: "blur",
          },
        ],
      },
      updateForm: {
        fbId: "",
        priorityName: "",
        priorityRule: "",
        description: "",
        matchSource: "",
      },
      commonData: {
        priorityRule: [],
        matchSource: [],
      },
    };
  },
  components: {
    TableToolTemp,
  },
  computed: {
    getText() {
      return "匹配数据源优先权配置";
    },
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      this.titleListPros.toolTitle = this.getText;
      this.updateForm.fbId = this.$route.query.fbId;
      await this.getDictList();
      this.initData();
    },
    // 获取字典
    async getDictList() {
      this.commonData.priorityRule = await getDicItemList(
        "sct.source.priorityRule"
      );
      this.commonData.matchSource = await getMatchKeys();
    },
    async initData() {
      let res = await getPriority({
        fbId: this.$route.query.fbId,
      });
      if (!res) {
        return;
      }
      if (res && res.fbId) {
        this.updateForm = {
          fbId: res.fbId,
          priorityName: res.priorityName,
          priorityRule: res.priorityRule,
          description: res.description,
          matchSource: res.matchSource,
        };
      }
    },
    goBack() {
      this.$router.go(-1);
    },
    async submit() {
      if (!validateAlls(this.$refs.updateForm)) {
        return;
      }
      let param = {};
      let res;
      res = await savePriority(this.updateForm);
      if (!res) {
        return;
      }
      this.goBack();
    },
  },
};
</script>

<style lang="less">
.strategy-match-update {
  .table-tool {
    padding-bottom: 10px;
  }
  .draggable-wrap {
    display: inline-block;
  }
  .tags {
    margin-right: 6px;
  }
  .zidian-box {
    margin-bottom: 20px;
  }
}
</style>
