body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
input,
button,
textarea,
p,
blockquote,
th,
td,
a,
label {
    margin: 0;
    padding: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: normal
}

address,
caption,
cite,
code,
dfn,
em,
th,
var {
    font-style: normal;
    font-weight: normal;
}

a {
    color: inherit;
    text-decoration: none;
}

a:active {
    text-decoration: none;
}

a:hover {
    text-decoration: none;
}

img {
    border: none;
}

ol,
ul,
li {
    list-style: none;
}

table {
    border-collapse: collapse;
}

a {
    -webkit-tap-highlight-color: transparent;
    outline: none
}

body {
    font-size: 14px; // color:#666666;
    font-family: “Microsoft YaHei” ! important;
}

/****form******/

input,
textarea,
select {
    outline: none;
    display: inline-block;
    background: transparent;
    border: none;
    color: inherit;
    font-family: "Microsoft YaHei", Arial, Helvetica, FreeSans, "华文细黑",  "宋体", "Segoe UI", "Lucida Grande", sans-serif, FreeSans, Arimo;
    border-radius: 0;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

input[type=hidden],
input[hidden] {
    display: none !important;
}

input[type=submit] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

input[type=checkbox] {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

input[type=radio] {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

input[type=button] {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

input[type=text] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

input[type=number] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

input[type=password] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.fl {
    float: left
}

.fr {
    float: right
}

.clearfix:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}

.clearfix {
    *zoom: 1;
}

.dt-cursor-pointer {
    cursor: pointer;
}

.dt-disabled {
    pointer-events: none;
    cursor: default;
    opacity: 0.6;
}

.base-Attribute-list {
    .iconfont {
        font-size: 22px;
    }
    .pos-iconfont {
        position: relative;
        top: 3px;
    }
    .popup-text {
        text-align: center;
        font-size: 16px;
        padding: 20px 0 25px 0;
    }
}

.base-attribute-update {
    .container {
        overflow: auto;
        padding-left: 10px;
        .icondt8 {
            font-size: 20px;
            position: relative;
            top: 2px;
        }
    }
    .dt-form {
        overflow: auto;
        padding-right: 40px;
    }
    .el-breadcrumb__inner {
        cursor: pointer;
    }
}

.tdu {
    text-decoration: underline;
    cursor: pointer;
}

.popup-text {
    text-align: center;
    font-size: 16px;
    padding: 20px 0 25px 0;
}

.breadcrumb {
    color: #999;
    cursor: pointer;
}

.priorityField-tags {
    // padding: 10px;
    padding: 0 10px;
    text-align: center;
    background: #F5F5F5;
    margin-right: 6px;
    border-radius: 6px;
    color: #606266;
    cursor: pointer;
    display: inline-block;
}