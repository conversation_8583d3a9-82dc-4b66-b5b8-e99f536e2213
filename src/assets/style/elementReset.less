.el-loading-spinner {
  margin-top: -40px !important;
  .system-loading-gif {
    display: block;
    background: url(../imgs/loading.gif);
    background-size: 100%;
    width: 66px;
    height: 66px;
    margin: 0 auto;
  }
}

/*::-webkit-scrollbar-track-piece {width: 10px;background-color: #f0f0f0;}
::-webkit-scrollbar {width: 10px;height: 10px;background-color: #e40807;}
::-webkit-scrollbar-thumb {width: 10px;background-color: #666;}
::-webkit-scrollbar-thumb:hover {width: 10px;background-color: #000;}*/
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track-piece {
  background-color: rgba(0, 0, 0, 0.2);
  -webkit-border-radius: 6px;
}

::-webkit-scrollbar-thumb:vertical {
  height: 5px;
  background-color: rgba(175, 175, 175, 0.70);
  -webkit-border-radius: 6px;
}

::-webkit-scrollbar-thumb:horizontal {
  width: 5px;
  background-color: rgba(125, 125, 125, 0.7);
  -webkit-border-radius: 6px;
}


// 覆盖 element-ui 弹窗样式
.el-popover {
  padding: 0 !important;
}

.dt-dic-table{
  width: 600px !important;
  margin-top: -6px;
  border: 1px solid #eee !important;
  thead{
    color: #606266 !important;
  }
  .has-gutter{
     th{
      padding: 4px 0;
    }
  }
  

  .el-table--striped .el-table__body tr.el-table__row--striped td{
    background: #F5F7FA !important;
  }

  .has-gutter{
    tr{
      background: #FAFAFA !important;
    }
    th{
      background: #FAFAFA !important;
    }
  } 
  
  .el-table__row :hover{
    
  }
}



// 输入框宽度
.dt-input-width {
  width: 372px !important;
}

.dt-input-width-max {
  width: 400px !important;
}

// textarea 宽度
.dt-textarea-width {
  width: 426px !important;
}

.dt-condition-input{
  width: 350px !important;
  margin-left: 10px;
}

.dt-condition-select{
  width: 100px !important;
  margin-left: 10px;
}

.dt-condition-search-input{
  width: 200px !important;
}


.dt-date-type-select{
  width: 90px !important;
  margin-right: 10px;
}

.com-event-btn{
  padding: 11px 15px !important;
}

.el-textarea {
  .el-textarea__inner {
    font-family: Arial, Helvetica, sans-serif !important;
  }
}

.dt-popover-tips {
  padding: 8px;
  background: #000;
  opacity: 0.8;
  color: #fff;
}

.dt-popover-btn{
  margin-left: 10px !important;
  position: relative;
  top: 4px;
}

.dt-view-pointer{
  cursor: pointer;
}

// 内容区菜单
.dt-el-tabs {
  margin-left: -15px;
  margin-right: -15px;
}

.dt-fz14 {
  font-size: 14px;
}

.dt-fz15 {
  font-size: 15px;
}

.dt-fz16 {
  font-size: 16px;
}

.dt-fz18 {
  font-size: 18px;
}

.dt-fz22 {
  font-size: 22px;
}

//加粗
.dt-bl {
  font-weight: 600;
}

//文本
.text-c {
  text-align: center;
}

//字体颜色
.c-999 {
  color: #999;
}

.c-666 {
  color: #666;
}

.c-333 {
  color: #333;
}

//间距
.mr-5 {
  margin-right: 5px;
}

.mr-10 {
  margin-right: 10px;
}

.ml-10{
  margin-left: 10px;
}

.mr-20 {
  margin-right: 20px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: 20px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}

.pdl-10 {
  padding-left: 10px;
}

.ptb-20 {
  padding: 20px 0;
}

.pdl-20 {
  padding-left: 20px;
}

.pdb-20 {
  padding-bottom: 20px;
}

//布局
.df {
  display: flex;
}

.df-ac {
  display: flex;
  align-items: center;
}

.df-cc {
  display: flex;
  align-items: center;
  justify-content: center;
}

//表格
.dt-table.el-table {
  &::before {
    height: 0;
    width: 0;
  }

  thead {
    color: #333;
  }

  .el-table__row td {
    border: none
  }

  tr {
    th {
      font-weight: bold;
      .cell {
        padding-left: 14px;
      }

      background-color: #F9F9F9;

      &.is-leaf {
        border: none;
      }

    }
  }

}


.dt-bread {
  padding: 14px 0 14px 20px;
  border-bottom: 1px solid #eeeeee;

  .el-breadcrumb__inner.is-link {
    color: #999 !important;
    font-size: 14px;
    font-weight: 400 !important;
  }

  .el-breadcrumb__inner {
    color: inherit !important;
  }

  .trendsTitle {
    .el-breadcrumb__inner {
      color: #999 !important;
      font-size: 14px;
      font-weight: 400 !important;
      cursor: pointer;
    }
  }

}

.el-button {
  &.dt-btn {
    padding: 14px 66px;
    font-size: 18px;
  }

  &.dt-btn-large {
    padding: 12px 75px;
  }

  &.common {
    padding: 12px 35px;
  }

  &.el-button--primary.is-plain {
    background-color: #fff !important;
    &:hover {
    }
  }

  font-weight: 400 !important;
}

.cell-wrap {
  margin-left: -40px;
  margin-right: -40px;

  .cell-com {
    height: 50px;
    line-height: 50px;
    padding-left: 40px;
    padding-right: 40px;
  }

  .cell-item {
    color: #333;
    width: 30%;
    float: left;
    padding-right: 3%;

    div {
      display: inline-block;
    }

    .label {
      margin-right: 8px;
    }

    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .cell-bg {
    background: rgba(249, 249, 249, 1)
  }
}


.el-table .el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: transparent;
}

.dt-select {
  width: 372px;
}

.puoup-del {
  padding: 20px 0 25px 0;
}


.el-form-item {
  .el-form-item__label {
    position: relative;
    padding-left: 10px;

    &::before {
      position: absolute;
      top: 0;
      left: 0;
    }
  }
}

.form-block-item {
  display: block !important;
  text-align: center;
}


.title-tenantName {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  padding-left: 23px;
}

.el-tabs--border-card > .el-tabs__content {
  padding: 0;
}

.dt-tabs {
  &.el-tabs--border-card {
    box-shadow: none;
    border: none;

    .el-tabs__header {
      border: none;
      background-color: #f5f5f5;

      .el-tabs__item {
        border: none;
        padding: 0 !important;
        margin-right: 16px;
        background-color: #e7e7e7;
        border-top: none;
        height: auto;
        font-weight: bold;

        &.is-active {
          background-color: #fff;
        }

        .tab-label {
          padding: 0 20px;
          color: #999;
        }
      }
    }

    .el-tabs__content {
      padding: 0;
    }
  }

}

.icon-pop {
  .dt-tabs {
    &.el-tabs--border-card {
      .el-tabs__header {
        background-color: #f5f5f5;

        .el-tabs__item {
          margin-right: 0;

          .tab-label {
            color: #999;
            font-weight: normal;
          }
        }
      }
    }
  }
}

.log-com-title {
  // padding: 0 0 10px 88px;
  // padding-top: 10px;
  font-size: 14px;
}


.dt-subTable-title {
  padding-left: 20px;
  font-weight: 600;
  font-size: 16px;
  height: 48px;
  line-height: 48px;

  .title-icon {
    width: 10px;
    height: 10px;
    margin-right: 6px;
    display: inline-block;
    transform: rotate(45deg);
  }

  span {
    margin-right: 10px;
  }

  .iconfont {
    position: relative;
    font-size: 22px;
    top: 3px;
  }
}


.breadcrum {
  height: 35px;
  line-height: 35px !important;
  font-size: initial;
  padding-left: 20px;
  border-bottom: 1px solid #DCDFE6;

  .el-breadcrumb__inner.is-link {
    font-weight: initial;
  }
}


.el-date-editor--daterange.el-input, .el-date-editor--daterange.el-input__inner, .el-date-editor--timerange.el-input, .el-date-editor--timerange.el-input__inner{
  width: 372px !important;
}



.tab-tool.second-tab-tool {
  padding-left: 20px;
  .tab-title {
      i {
          width: 8px;
          height: 8px;
          transform: rotate(45deg);
          top: -3px;
          margin-right: 5px;
      }
      font-size: 16px;
  }
}

.popup-text {
  text-align: center;
  font-size: 16px;
  padding: 20px 0 25px 0;
}


.group-popover-wrap{
  padding: 20px 20px 12px 20px;
  .iconfont{
    font-size: 20px;
  }
  .group-btn{
    font-size: 12px;
    line-height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 192px;
    height: 32px;
    border-radius: 3px;
    border: 1px solid #4b5259;
    margin-bottom: 8px;
    font-weight: 400;
    cursor: pointer;
  }
}


.el-select .el-input.is-disabled .el-input__inner{
  color:#606266 !important;
}
.el-input.is-disabled .el-input__inner{
  color:#606266 !important;
}


.el-textarea.is-disabled .el-textarea__inner{
  color:#606266 !important;
}

// 保存按钮使用绿色主题
.el-button--primary {
  &.save-btn,
  &[title*="保存"],
  &[title*="Save"] {
    background-color: #67c23a !important;
    border-color: #67c23a !important;
    
    &:hover,
    &:focus {
      background-color: #85ce61 !important;
      border-color: #85ce61 !important;
    }
    
    &:active {
      background-color: #5daf34 !important;
      border-color: #5daf34 !important;
    }
  }
}

// 工具栏中的保存按钮
.toolbar-icon.primary {
  background: #67c23a !important;
  color: white !important;
  
  &:hover {
    background: #85ce61 !important;
    box-shadow: 0 4px 20px rgba(103, 194, 58, 0.5) !important;
  }
}