<template>
  <div class="wh100" id="app">
    <router-view />
  </div>
</template>
<script>
const version = require("element-ui/package.json").version; // 获取elment-ui版本
const ORIGINAL_THEME = "#D7A256"; // elment 默认主题
import { getParamString } from "@/utils/utils";
export default {
  data() {
    return {};
  },
  created() {
    this.init();
  },

  methods: {
    init() {
      let funcId = getParamString("funcId");
      let tenantId = getParamString("tenantId");
      let userName = getParamString("userName");
      let accessToken = getParamString("access_token");
      if (accessToken) {
        sessionStorage.setItem("LoginAccessToken", accessToken);
      }
      if (tenantId) {
        sessionStorage.setItem("tenantId", tenantId);
      }
      if (funcId) {
        sessionStorage.setItem("funcId", funcId);
      }

      this.$store.commit("layoutStore/setCurrentLoginUser", {
        accessToken,
        tenantId,
        funcId,
        userName,
      });

      this.$store.commit("layoutStore/setCacheArr", { status: "clear" });
      this.$store.commit("layoutStore/setQueryParam", {});
      this.$store.commit("clearDicData");
      //获取登录用户在该应用下权限位等信息
      this.$store.dispatch("getWebUserInfo");
    },
    handleTheme(color, themeObj) {
      if (window.dtHandleTheme) {
        window.dtHandleTheme(color, ORIGINAL_THEME, version);
      }
      this.$store.commit("layoutStore/setThemeObj", themeObj);
    },
  },
  mounted() {
    let sessionThemeObj = JSON.parse(sessionStorage.getItem("themeObj"));

    let themeObj = {
      color: getParamString("themeColor") || sessionThemeObj.color,
      tableBtnActiveColor:
        getParamString("themeColor") || sessionThemeObj.tableBtnActiveColor,
      navTagUnselectedColor:
        getParamString("navTagColor") || sessionThemeObj.navTagUnselectedColor,
      text: "",
    };

    if (themeObj.color) {
      sessionStorage.setItem("themeObj", JSON.stringify(themeObj));
    }

    if (themeObj.color) {
      setTimeout(() => {
        this.handleTheme(themeObj.color, themeObj);
      }, 700);
    }
    window.addEventListener("message", (e) => {
      if (e.data.theme) {
        //在此处标识是父系统嵌入的子系统，需要子系统隐藏左侧菜单栏，头部的Tas栏目，去掉主体内容的padding样式
        window.dtHandleTheme(e.data.theme.color, ORIGINAL_THEME, version);
        this.$store.commit("layoutStore/setThemeObj", e.data.theme);
      }
    });
  },
};
</script>

<style lang="less">
html,
head,
body {
  margin: 0;
  width: 100%;
  height: 100%;
}
body .el-table th.gutter {
  display: table-cell !important;
}
.wh100 {
  width: 100%;
  height: 100%;
}

@import "./assets/style/common";
@import "./assets/style/elementReset";

@media screen and (max-width: 1440px) {
  .form-block {
    .el-form-item__content {
      .el-input--medium {
        width: 180px !important;
      }
    }
  }
}
@media screen and (min-width: 1440px) {
  .form-block {
    .el-form-item__content {
      .el-input--medium {
        width: 215px !important;
      }
    }
  }
}
@media screen and (max-width: 1280px) {
  .form-block {
    .el-form-item__content {
      .el-input--medium {
        width: 160px !important;
      }
    }
  }
}
</style>
