package com.kbao.algo.client;


import com.github.pagehelper.PageInfo;
import com.kbao.algo.client.fallback.AlgoStrategyWebFallBackFactory;
import com.kbao.algo.dataSpace.bean.DataSpaceReqVo;
import com.kbao.algo.dataSpace.bean.DataSpaceRespVo;
import com.kbao.algo.source.bean.AlgoDataReqVo;
import com.kbao.algo.sourceField.bean.AlgoParamResVo;
import com.kbao.algo.strategy.bean.AlgoStrategyExecuteRespDTO;
import com.kbao.algo.strategy.bean.AlgoStrategyReqVo;
import com.kbao.algo.strategyBatch.bean.AlgoStrategyBatchReqVo;
import com.kbao.algo.strategyBatchNode.bean.AlgoStrategyBatchNodeRespDTO;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * xiaojy
 * @Description algo策略FEIGN CLIENT
 * @Date 2024年11月6日11:29:20
 */
@FeignClient(name = "kbc-sct-web", fallbackFactory = AlgoStrategyWebFallBackFactory.class)
public interface AlgoStrategyWebClient {

    /**
     * 创建数据空间
     * @param reqVo
     * @return
     */
    @PostMapping(value = "api/noauth/algoStrategy/dataSpace/create")
    Result<DataSpaceRespVo> createDataSpaceBatch(@RequestBody DataSpaceReqVo reqVo);

    /**
     * 执行策略
     * @param algoStrategyReqVo
     * @return
     */
    @PostMapping(value = "api/noauth/algoStrategy/execute")
    Result<AlgoStrategyExecuteRespDTO> execute(@RequestBody AlgoStrategyReqVo algoStrategyReqVo);

    /**
     * 获取策略可执行参数列表
     * @param algoStrategyReqVo
     * @return
     */
    @PostMapping(value = "api/noauth/algoStrategy/getStrategyParams")
    Result<List<AlgoParamResVo>> getStrategyParams(@RequestBody AlgoStrategyReqVo algoStrategyReqVo);

    /**
     * 获取数据源批次节点列表
     * @param batchCode
     * @return
     */
    @PostMapping(value = "api/noauth/algoStrategy/getSourceBatchNodes")
    Result<List<AlgoStrategyBatchNodeRespDTO>> getSourceBatchNodes(@RequestBody String batchCode);

    /**
     * 获取指标批次节点列表
     * @param batchCode
     * @return
     */
    @PostMapping(value = "api/noauth/algoStrategy/getIndicatorBatchNodes")
    Result<List<AlgoStrategyBatchNodeRespDTO>> getIndicatorBatchNodes(@RequestBody String batchCode);


    /**
     * 获取数据源节点批次明细数据列表
     * @param reqVo
     * @return
     */
    @PostMapping(value = "api/noauth/algoStrategy/getSourceBatchDetailPage")
    Result<PageInfo<Map>> getSourceBatchDetailPage(@RequestBody PageRequest<AlgoDataReqVo> reqVo);

    /**
     * 获取指标批次节点明细数据列表
     * @param reqVo
     * @return
     */
    @PostMapping(value = "api/noauth/algoStrategy/getIndicatorBatchDetailPage")
    Result<PageInfo<Map>> getIndicatorBatchDetailPage(@RequestBody PageRequest<AlgoDataReqVo> reqVo);

    /**
     * 根据策略批次编码和指标编码获取结果指标明细数据列表
     * @param reqVo
     * @return
     */
    @PostMapping(value = "api/noauth/algoStrategy/getResultIndicatorBatchDetailPage")
    Result<PageInfo<Map>> getResultIndicatorBatchDetailPage(@RequestBody PageRequest<AlgoStrategyBatchReqVo> reqVo);
}
