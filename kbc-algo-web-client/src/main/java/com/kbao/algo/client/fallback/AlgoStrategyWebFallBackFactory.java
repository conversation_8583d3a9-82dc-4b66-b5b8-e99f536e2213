package com.kbao.algo.client.fallback;

import com.github.pagehelper.PageInfo;
import com.kbao.algo.client.AlgoStrategyWebClient;
import com.kbao.algo.dataSpace.bean.DataSpaceReqVo;
import com.kbao.algo.dataSpace.bean.DataSpaceRespVo;
import com.kbao.algo.source.bean.AlgoDataReqVo;
import com.kbao.algo.sourceField.bean.AlgoParamResVo;
import com.kbao.algo.strategy.bean.AlgoStrategyExecuteRespDTO;
import com.kbao.algo.strategy.bean.AlgoStrategyReqVo;
import com.kbao.algo.strategyBatch.bean.AlgoStrategyBatchReqVo;
import com.kbao.algo.strategyBatchNode.bean.AlgoStrategyBatchNodeRespDTO;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description algo策略FEIGN CLIENT
 * @Date 2024年11月6日11:55:08
 */
@Slf4j
@Component
public class AlgoStrategyWebFallBackFactory implements FallbackFactory<AlgoStrategyWebClient> {
    @Override
    public AlgoStrategyWebClient create(Throwable throwable) {
        log.error(ExceptionUtils.getStackTrace(throwable));

        return new AlgoStrategyWebClient() {
            @Override
            public Result<DataSpaceRespVo> createDataSpaceBatch(DataSpaceReqVo reqVo) {
                return Result.failed(String.format("[%s]接口调用失败，请稍后重试！", "createDataSpaceBatch"));
            }
            @Override
            public Result<AlgoStrategyExecuteRespDTO> execute(AlgoStrategyReqVo algoStrategyReqVo) {
                return Result.failed(String.format("[%s]接口调用失败，请稍后重试！", "execute"));
            }
            @Override
            public Result<List<AlgoParamResVo>> getStrategyParams(AlgoStrategyReqVo algoStrategyReqVo) {
                return Result.failed(String.format("[%s]接口调用失败，请稍后重试！", "getStrategyParams"));
            }
            @Override
            public Result<List<AlgoStrategyBatchNodeRespDTO>> getSourceBatchNodes(String batchCode) {
                return Result.failed(String.format("[%s]接口调用失败，请稍后重试！", "getSourceBatchNodes"));
            }
            @Override
            public Result<List<AlgoStrategyBatchNodeRespDTO>> getIndicatorBatchNodes(String batchCode) {
                return Result.failed(String.format("[%s]接口调用失败，请稍后重试！", "getIndicatorBatchNodes"));
            }
            @Override
            public Result<PageInfo<Map>> getSourceBatchDetailPage(PageRequest<AlgoDataReqVo> reqVo) {
                return Result.failed(String.format("[%s]接口调用失败，请稍后重试！", "getSourceBatchDetailPage"));
            }
            @Override
            public Result<PageInfo<Map>> getIndicatorBatchDetailPage(PageRequest<AlgoDataReqVo> reqVo) {
                return Result.failed(String.format("[%s]接口调用失败，请稍后重试！", "getIndicatorBatchDetailPage"));
            }
            @Override
            public Result<PageInfo<Map>> getResultIndicatorBatchDetailPage(PageRequest<AlgoStrategyBatchReqVo> reqVo) {
                return Result.failed(String.format("[%s]接口调用失败，请稍后重试！", "getResultIndicatorBatchDetailPage"));
            }
        };
    }
}
