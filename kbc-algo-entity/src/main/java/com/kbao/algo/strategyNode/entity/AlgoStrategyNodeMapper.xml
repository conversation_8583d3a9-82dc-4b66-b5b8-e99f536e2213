<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.algo.strategyNode.dao.AlgoStrategyNodeMapper">

	<resultMap id="BaseResultMap" type="com.kbao.algo.strategyNode.entity.AlgoStrategyNode">
    	<id column="id" jdbcType="INTEGER"  property="id"  />
        <result property="strategyId" jdbcType="INTEGER"  column="strategy_id" />  
        <result property="nodeName" jdbcType="VARCHAR"  column="node_name" />  
        <result property="nodeType" jdbcType="VARCHAR"  column="node_type" />  
        <result property="nodeValue" jdbcType="VARCHAR"  column="node_value" />  
        <result property="parentId" jdbcType="INTEGER"  column="parent_id" />  
        <result property="nodeSort" jdbcType="INTEGER"  column="node_sort" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="updateId" jdbcType="VARCHAR"  column="update_id" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="createId" jdbcType="VARCHAR"  column="create_id" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
        <result property="isUpdate" jdbcType="VARCHAR"  column="is_update" />  
        <result property="positionTop" jdbcType="VARCHAR"  column="position_top" />  
        <result property="positionLeft" jdbcType="VARCHAR"  column="position_left" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		strategy_id,  
		node_name,  
		node_type,  
		node_value,  
		parent_id,  
		node_sort,  
		create_time,  
		update_id,  
		update_time,  
		create_id,  
		tenant_id,  
		is_update,  
		position_top,  
		position_left  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.strategy_id, 
		t.node_name, 
		t.node_type, 
		t.node_value, 
		t.parent_id, 
		t.node_sort, 
		t.create_time, 
		t.update_id, 
		t.update_time, 
		t.create_id, 
		t.tenant_id, 
		t.is_update, 
		t.position_top, 
		t.position_left 
	</sql>

	<sql id="Base_Condition">
		<where>
	    <if test="strategyId != null">
	   		and t.strategy_id = #{strategyId,jdbcType=INTEGER}  
	    </if>
	    <if test="nodeName != null and nodeName != ''">
	   		and t.node_name = #{nodeName,jdbcType=VARCHAR}  
	    </if>
	    <if test="nodeType != null and nodeType != ''">
	   		and t.node_type = #{nodeType,jdbcType=VARCHAR}  
	    </if>
	    <if test="nodeValue != null and nodeValue != ''">
	   		and t.node_value = #{nodeValue,jdbcType=VARCHAR}  
	    </if>
	    <if test="parentId != null">
	   		and t.parent_id = #{parentId,jdbcType=INTEGER}  
	    </if>
	    <if test="nodeSort != null">
	   		and t.node_sort = #{nodeSort,jdbcType=INTEGER}  
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="updateId != null and updateId != ''">
	   		and t.update_id = #{updateId,jdbcType=VARCHAR}  
	    </if>
	    <if test="updateTime != null">
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="createId != null and createId != ''">
	   		and t.create_id = #{createId,jdbcType=VARCHAR}  
	    </if>
	    <if test="tenantId != null and tenantId != ''">
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}  
	    </if>
	    <if test="isUpdate != null and isUpdate != ''">
	   		and t.is_update = #{isUpdate,jdbcType=VARCHAR}  
	    </if>
	    <if test="positionTop != null and positionTop != ''">
	   		and t.position_top = #{positionTop,jdbcType=VARCHAR}  
	    </if>
	    <if test="positionLeft != null and positionLeft != ''">
	   		and t.position_left = #{positionLeft,jdbcType=VARCHAR}  
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_algo_strategy_node t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_algo_strategy_node t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_algo_strategy_node
		where  id = #{id,jdbcType=INTEGER}
	</select>
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.algo.strategyNode.entity.AlgoStrategyNode">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_algo_strategy_node(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=INTEGER}, 
                 
                #{strategyId,jdbcType=INTEGER}, 
                 
                #{nodeName,jdbcType=VARCHAR}, 
                 
                #{nodeType,jdbcType=VARCHAR}, 
                 
                #{nodeValue,jdbcType=VARCHAR}, 
                 
                #{parentId,jdbcType=INTEGER}, 
                 
                #{nodeSort,jdbcType=INTEGER}, 
                 
                #{createTime,jdbcType=TIMESTAMP}, 
                 
                #{updateId,jdbcType=VARCHAR}, 
                 
                #{updateTime,jdbcType=TIMESTAMP}, 
                 
                #{createId,jdbcType=VARCHAR}, 
                 
                #{tenantId,jdbcType=VARCHAR}, 
                 
                #{isUpdate,jdbcType=VARCHAR}, 
                 
                #{positionTop,jdbcType=VARCHAR}, 
                 
                #{positionLeft,jdbcType=VARCHAR} 
               )
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.algo.strategyNode.entity.AlgoStrategyNode">
		<selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>
		insert into t_algo_strategy_node
		<trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="id != null ">  
	       		id,
	        </if>  
	        <if test="strategyId != null ">  
	       		strategy_id,
	        </if>  
	        <if test="nodeName != null ">  
	       		node_name,
	        </if>  
	        <if test="nodeType != null ">  
	       		node_type,
	        </if>  
	        <if test="nodeValue != null ">  
	       		node_value,
	        </if>  
	        <if test="parentId != null ">  
	       		parent_id,
	        </if>  
	        <if test="nodeSort != null ">  
	       		node_sort,
	        </if>  
	        <if test="createTime != null ">  
	       		create_time,
	        </if>  
	        <if test="updateId != null ">  
	       		update_id,
	        </if>  
	        <if test="updateTime != null ">  
	       		update_time,
	        </if>  
	        <if test="createId != null ">  
	       		create_id,
	        </if>  
	        <if test="tenantId != null ">  
	       		tenant_id,
	        </if>  
	        <if test="isUpdate != null ">  
	       		is_update,
	        </if>  
	        <if test="positionTop != null ">  
	       		position_top,
	        </if>  
	        <if test="positionLeft != null ">  
	       		position_left,
	        </if>  
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">  
            	#{id,jdbcType=INTEGER},
            </if>  
            <if test="strategyId != null">  
            	#{strategyId,jdbcType=INTEGER},
            </if>  
            <if test="nodeName != null">  
            	#{nodeName,jdbcType=VARCHAR},
            </if>  
            <if test="nodeType != null">  
            	#{nodeType,jdbcType=VARCHAR},
            </if>  
            <if test="nodeValue != null">  
            	#{nodeValue,jdbcType=VARCHAR},
            </if>  
            <if test="parentId != null">  
            	#{parentId,jdbcType=INTEGER},
            </if>  
            <if test="nodeSort != null">  
            	#{nodeSort,jdbcType=INTEGER},
            </if>  
            <if test="createTime != null">  
            	#{createTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="updateId != null">  
            	#{updateId,jdbcType=VARCHAR},
            </if>  
            <if test="updateTime != null">  
            	#{updateTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="createId != null">  
            	#{createId,jdbcType=VARCHAR},
            </if>  
            <if test="tenantId != null">  
            	#{tenantId,jdbcType=VARCHAR},
            </if>  
            <if test="isUpdate != null">  
            	#{isUpdate,jdbcType=VARCHAR},
            </if>  
            <if test="positionTop != null">  
            	#{positionTop,jdbcType=VARCHAR},
            </if>  
            <if test="positionLeft != null">  
            	#{positionLeft,jdbcType=VARCHAR},
            </if>  
		</trim>
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.algo.strategyNode.entity.AlgoStrategyNode">
		update t_algo_strategy_node
		<set>
	        <if test="strategyId != null ">  
	        	strategy_id = #{strategyId,jdbcType=INTEGER},  
	        </if>  
	        <if test="nodeName != null ">  
	        	node_name = #{nodeName,jdbcType=VARCHAR},  
	        </if>  
	        <if test="nodeType != null ">  
	        	node_type = #{nodeType,jdbcType=VARCHAR},  
	        </if>  
	        <if test="nodeValue != null ">  
	        	node_value = #{nodeValue,jdbcType=VARCHAR},  
	        </if>  
	        <if test="parentId != null ">  
	        	parent_id = #{parentId,jdbcType=INTEGER},  
	        </if>  
	        <if test="nodeSort != null ">  
	        	node_sort = #{nodeSort,jdbcType=INTEGER},  
	        </if>  
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="updateId != null ">  
	        	update_id = #{updateId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateTime != null ">  
	        	update_time = #{updateTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="createId != null ">  
	        	create_id = #{createId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="tenantId != null ">  
	        	tenant_id = #{tenantId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="isUpdate != null ">  
	        	is_update = #{isUpdate,jdbcType=VARCHAR},  
	        </if>  
	        <if test="positionTop != null ">  
	        	position_top = #{positionTop,jdbcType=VARCHAR},  
	        </if>  
	        <if test="positionLeft != null ">  
	        	position_left = #{positionLeft,jdbcType=VARCHAR},  
	        </if>  
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.algo.strategyNode.entity.AlgoStrategyNode">
		update t_algo_strategy_node
		set
           strategy_id = #{strategyId,jdbcType=INTEGER},
           node_name = #{nodeName,jdbcType=VARCHAR},
           node_type = #{nodeType,jdbcType=VARCHAR},
           node_value = #{nodeValue,jdbcType=VARCHAR},
           parent_id = #{parentId,jdbcType=INTEGER},
           node_sort = #{nodeSort,jdbcType=INTEGER},
           create_time = #{createTime,jdbcType=TIMESTAMP},
           update_id = #{updateId,jdbcType=VARCHAR},
           update_time = #{updateTime,jdbcType=TIMESTAMP},
           create_id = #{createId,jdbcType=VARCHAR},
           tenant_id = #{tenantId,jdbcType=VARCHAR},
           is_update = #{isUpdate,jdbcType=VARCHAR},
           position_top = #{positionTop,jdbcType=VARCHAR},
           position_left = #{positionLeft,jdbcType=VARCHAR}
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_algo_strategy_node(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
	        <choose>
	            <when test="item.strategyId != null">,#{item.strategyId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.nodeName != null">,#{item.nodeName}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.nodeType != null">,#{item.nodeType}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.nodeValue != null">,#{item.nodeValue}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.parentId != null">,#{item.parentId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.nodeSort != null">,#{item.nodeSort}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.isUpdate != null">,#{item.isUpdate}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.positionTop != null">,#{item.positionTop}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.positionLeft != null">,#{item.positionLeft}</when><otherwise>,default</otherwise>
	        </choose>
		)
		</foreach>
	</insert>

	<!-- 批量插入或更新 -->
	<update id="batchInsertOrUpdate" parameterType="java.util.List">
		insert into t_algo_strategy_node(
			<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
			<choose><when test="item.strategyId != null">,#{item.strategyId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.nodeName != null">,#{item.nodeName}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.nodeType != null">,#{item.nodeType}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.nodeValue != null">,#{item.nodeValue}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.parentId != null">,#{item.parentId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.nodeSort != null">,#{item.nodeSort}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.isUpdate != null">,#{item.isUpdate}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.positionTop != null">,#{item.positionTop}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.positionLeft != null">,#{item.positionLeft}</when><otherwise>,default</otherwise></choose>
		)
		</foreach>
		on duplicate key update 
		   strategy_id=values(strategy_id), 
		   node_name=values(node_name), 
		   node_type=values(node_type), 
		   node_value=values(node_value), 
		   parent_id=values(parent_id), 
		   node_sort=values(node_sort), 
		   create_time=values(create_time), 
		   update_id=values(update_id), 
		   update_time=values(update_time), 
		   create_id=values(create_id), 
		   tenant_id=values(tenant_id), 
		   is_update=values(is_update), 
		   position_top=values(position_top), 
		   position_left=values(position_left) 
	</update>
	
	<!-- 批量删除-->
	<delete id="batchDelete" parameterType="java.util.List">
		delete from t_algo_strategy_node where id in 
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")"> #{item}
		</foreach>
	</delete>
	
	<!-- 自定义查询 -->
	<!-- 根据策略id删除参数 -->
	<delete id="deleteByStrategyId" parameterType="java.lang.Integer">
		delete from t_algo_strategy_node
		where strategy_id = #{strategyId}
	</delete>

	<select id="selectByStrategyId" resultType="com.kbao.algo.strategyNode.entity.AlgoStrategyNode" parameterType="java.lang.Integer">
		select t.*, i.version from t_algo_strategy_node t
		         left join t_algo_indicator i on t.node_value = i.biz_code and t.node_type in ('2', '3') and i.is_deleted = '0'
		         where t.strategy_id = #{strategyId}
	</select>
</mapper>
