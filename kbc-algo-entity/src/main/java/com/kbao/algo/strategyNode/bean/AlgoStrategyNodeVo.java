package com.kbao.algo.strategyNode.bean;

import lombok.Data;

import java.util.List;

/**
 * 策略节点新增请求参数
 * @author: xia<PERSON><PERSON><PERSON><PERSON>
 * @time: 2024/10/28 13:51
 */
@Data
public class AlgoStrategyNodeVo {
    /**
     * 策略id
     */
    private Integer strategyId;

    /**
     * 节点id
     */
    private String nodeId;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 节点类型 1-数据源，2-计算指标，3-结果指标
     */
    private String nodeType;

    /**
     * 节点值 对应节点ID
     */
    private String nodeValue;

    /**
     * 父节点id
     */
    private Integer parentId;

    /**
     * 节点排序
     */
    private Integer nodeSort;

    /**
     * 是否更新数据 0-否 1-是
     */
    private String isUpdate;

    /**
     * 坐标（距离高点的高度）
     */
    private String positionTop;

    /**
     * 坐标（距离左侧的宽度）
     */
    private String positionLeft;

    private String version;

    /**
     * 子类
     */
    private List<AlgoStrategyNodeVo> children;
}
