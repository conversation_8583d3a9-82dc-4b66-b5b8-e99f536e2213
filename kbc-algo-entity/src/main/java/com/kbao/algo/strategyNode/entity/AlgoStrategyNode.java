package com.kbao.algo.strategyNode.entity;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* @Description 算法规则-策略执行节点表实体
* @Date 2024-12-06
*/
@Data
public class AlgoStrategyNode implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 自增主键
     */  
	private Integer id;

    /**
     * 对应表中strategy_id
     * 策略id
     */  
	private Integer strategyId;

    /**
     * 对应表中node_name
     * 节点名称
     */  
	private String nodeName;

    /**
     * 对应表中node_type
     * 节点类型 1-数据源，2-计算指标，3-结果指标
     */  
	private String nodeType;

    /**
     * 对应表中node_value
     * 节点值 对应节点code
     */  
	private String nodeValue;

    /**
     * 对应表中parent_id
     * 父节点id
     */  
	private Integer parentId;

    /**
     * 对应表中node_sort
     * 节点排序
     */  
	private Integer nodeSort;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

    /**
     * 对应表中update_id
     * 更新人
     */  
	private String updateId;

    /**
     * 对应表中update_time
     * 更新时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

    /**
     * 对应表中create_id
     * 创建人
     */  
	private String createId;

    /**
     * 对应表中tenant_id
     * 租户ID
     */  
	private String tenantId;

    /**
     * 对应表中is_update
     * 是否更新数据 0-否 1-是
     */  
	private String isUpdate;

    /**
     * 对应表中position_top
     * 坐标（距离高点的高度）
     */  
	private String positionTop;

    /**
     * 对应表中position_left
     * 坐标（距离左侧的宽度）
     */  
	private String positionLeft;

	private String version;
}   