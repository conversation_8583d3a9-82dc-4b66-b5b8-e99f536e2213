package com.kbao.algo.strategyNode.entity;

import com.alibaba.fastjson.JSONArray;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* @Description 算法规则-策略执行节点流程表实体
* @Date 2024-12-31
*/
@Data
@Document(value = "AlgoStrategyNodeFlow")
public class AlgoStrategyNodeFlow implements Serializable{
	private static final long serialVersionUID = 1L;

	@Id
	private String id;

    /**
     * 策略id
     */  
	private Integer strategyId;

	/**
	 * 策略code
	 */
	private String strategyCode;

	/**
	 * 节点流程列表
	 */
	private JSONArray nodeList;

	/**
	 * 对应表中create_id
	 * 创建人
	 */
	private String createId;

	/**
	* 创建时间
	*/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;

	/**
	 * 对应表中update_id
	 * 更新人
	 */
	private String updateId;

	/**
	* 更新时间
	*/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

	/**
	 * 租户id
	 */
	private String tenantId;

}   