package com.kbao.algo.source.bean;

import lombok.Data;

import java.util.List;

/**
 * client端请求参数, 尽量不要修改
 */
@Data
public class AlgoDataReqVo {
    private String bizCode;
    // 节点类型
    private String nodeType;
    private String tableName;
    // 策略批次id
    private Integer strategyBatchId;
    // 数据空间ID
    private Integer dataSpaceId;
    // 数据类型：1-当前数据，2-全量数据
    private String dataType;
    private String tenantId;
    private List<SearchParam> searchParamList;
}
