package com.kbao.algo.source.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlgoSourceTableDto {
    private String tableName;
    private List<Field> fieldList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Field {
        private String fieldName;
        private String fieldDesc;

    }
}
