<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.algo.source.dao.AlgoSourceMapper">

	<resultMap id="BaseResultMap" type="com.kbao.algo.source.entity.AlgoSource">
    	<id column="source_id" jdbcType="INTEGER"  property="sourceId"  />
        <result property="name" jdbcType="VARCHAR"  column="name" />  
        <result property="bizCode" jdbcType="VARCHAR"  column="biz_code" />  
        <result property="sourceType" jdbcType="VARCHAR"  column="source_type" />  
        <result property="sourceValue" jdbcType="VARCHAR"  column="source_value" />
		<result property="checkDataApi" jdbcType="VARCHAR"  column="check_data_api" />
		<result property="remark" jdbcType="VARCHAR"  column="remark" />
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="createId" jdbcType="VARCHAR"  column="create_id" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="updateId" jdbcType="VARCHAR"  column="update_id" />  
        <result property="isDeleted" jdbcType="TINYINT"  column="is_deleted" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
	</resultMap>

	<sql id="Base_Column_List">
		source_id,  
		name,  
		biz_code,  
		source_type,  
		source_value,
		check_data_api,
		remark,
		create_time,  
		create_id,  
		update_time,  
		update_id,  
		is_deleted,  
		tenant_id  
	</sql>

	<sql id="Alias_Column_List">
		t.source_id, 
		t.name, 
		t.biz_code, 
		t.source_type, 
		t.source_value,
		t.check_data_api,
		t.remark,
		t.create_time, 
		t.create_id, 
		t.update_time, 
		t.update_id, 
		t.is_deleted, 
		t.tenant_id 
	</sql>

	<sql id="Base_Condition">
		<where>
			t.is_deleted = 0
			<if test="name != null and name != ''">
				and t.name = #{name,jdbcType=VARCHAR}
			</if>
			<if test="bizCode != null and bizCode != ''">
				and t.biz_code = #{bizCode,jdbcType=VARCHAR}
			</if>
			<if test="sourceType != null and sourceType != ''">
				and t.source_type = #{sourceType,jdbcType=VARCHAR}
			</if>
			<if test="sourceValue != null and sourceValue != ''">
				and t.source_value = #{sourceValue,jdbcType=VARCHAR}
			</if>
			<if test="checkDataApi != null and checkDataApi != ''">
				and t.check_data_api = #{checkDataApi,jdbcType=VARCHAR}
			</if>
			<if test="remark != null and remark != ''">
				and t.remark = #{remark,jdbcType=VARCHAR}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime,jdbcType=TIMESTAMP}
			</if>
			<if test="createId != null and createId != ''">
				and t.create_id = #{createId,jdbcType=VARCHAR}
			</if>
			<if test="updateTime != null">
				and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
			</if>
			<if test="updateId != null and updateId != ''">
				and t.update_id = #{updateId,jdbcType=VARCHAR}
			</if>
			<if test="tenantId != null and tenantId != ''">
				and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
			</if>
			<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_algo_source t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_algo_source
		where  is_deleted = 0 and source_id = #{sourceId,jdbcType=INTEGER}
	</select>

	<!-- 根据主键删除 -->
    <update id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		update t_algo_source set is_deleted = 1
		where source_id = #{sourceId,jdbcType=INTEGER} and is_deleted = 0
	</update>
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.algo.source.entity.AlgoSource">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="sourceId">
		SELECT LAST_INSERT_ID() AS sourceId
		</selectKey>  -->
		insert into t_algo_source(
			<include refid="Base_Column_List" />
		)
		values(  
                #{sourceId,jdbcType=INTEGER}, 
                #{name,jdbcType=VARCHAR},
                #{bizCode,jdbcType=VARCHAR},
                #{sourceType,jdbcType=VARCHAR},
                #{sourceValue,jdbcType=VARCHAR},
				#{checkDataApi,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                now(),
                #{createId,jdbcType=VARCHAR},
				now(),
				#{createId,jdbcType=VARCHAR},
                0,
                #{tenantId,jdbcType=VARCHAR}
               )
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.algo.source.entity.AlgoSource">
		update t_algo_source
		<set>
	        <if test="name != null ">  
	        	name = #{name,jdbcType=VARCHAR},  
	        </if>
			<if test="bizCode != null ">
				biz_code = #{bizCode,jdbcType=VARCHAR},
			</if>
	        <if test="sourceType != null ">
	        	source_type = #{sourceType,jdbcType=VARCHAR},  
	        </if>
	        <if test="sourceValue != null ">  
	        	source_value = #{sourceValue,jdbcType=VARCHAR},  
	        </if>
			<if test="checkDataApi != null ">
				check_data_api = #{checkDataApi,jdbcType=VARCHAR},
			</if>
	        <if test="remark != null ">
	        	remark = #{remark,jdbcType=VARCHAR},  
	        </if>
			update_time = now(),
			<if test="updateId != null ">
	        	update_id = #{updateId,jdbcType=VARCHAR},  
	        </if>  
		</set>
		where source_id = #{sourceId,jdbcType=INTEGER}
	</update>

	<!-- 自定义查询 -->
	<resultMap id="sourceListMap" type="com.kbao.algo.source.bean.AlgoSourceResVo">
		<id column="source_id" jdbcType="INTEGER"  property="sourceId"  />
		<result property="name" jdbcType="VARCHAR"  column="name" />
		<result property="bizCode" jdbcType="VARCHAR"  column="biz_code" />
		<result property="sourceType" jdbcType="VARCHAR"  column="source_type" />
		<result property="remark" jdbcType="VARCHAR"  column="remark" />
		<result property="batchCount" jdbcType="INTEGER"  column="batch_count" />
		<result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />
		<collection property="fieldList" ofType="string">
				<result column="field_name" jdbcType="VARCHAR"/>
		</collection>
	</resultMap>

	<select id="getAlgoSourceList" resultMap="sourceListMap">
		select s.source_id, s.biz_code, s.name, s.source_type, s.remark, s.create_time, f.field_name,
		(select count(distinct b.data_space_id) from t_algo_strategy_batch_node bn join t_algo_strategy_batch b
		    on bn.batch_id = b.id where bn.node_type = '1' and bn.node_value = s.biz_code) as batch_count
		from t_algo_source s
		left join t_algo_source_field f on s.source_id = f.source_id and f.is_param = '1'
		where s.is_deleted = 0
		<if test="bizCode != null and bizCode != ''">
			and s.biz_code = #{bizCode,jdbcType=VARCHAR}
		</if>
		<if test="name != null and name != ''">
			and s.name like concat('%',#{name},'%')
		</if>
		order by s.update_time desc
	</select>

	<select id="getByCode" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from t_algo_source
		where  is_deleted = 0 and biz_code = #{bizCode,jdbcType=VARCHAR}
	</select>

	<select id="isExistSource" resultType="int">
		select count(1)
		from t_algo_source
		where is_deleted = 0 and biz_code = #{bizCode,jdbcType=VARCHAR}
		<if test="sourceId != null">
			and source_id != #{sourceId,jdbcType=INTEGER}
		</if>
	</select>

	<!--自动建表相关sql-->
	<select id="isExistSourceTable" resultType="int" useCache="false">
		select count(1) from information_schema.tables
		where table_name = #{tableName,jdbcType=VARCHAR}
	</select>

	<select id="isExistField" resultType="int">
		SELECT count(1) FROM INFORMATION_SCHEMA.COLUMNS
		WHERE TABLE_NAME = #{tableName,jdbcType=VARCHAR}
		  AND COLUMN_NAME = #{fieldName,jdbcType=VARCHAR}
	</select>

	<update id="ddlCreateTable">
		create table ${tableName} (
			id INT primary key auto_increment not null comment '主键',
			strategyBatchId int comment '策略批次ID',
			<foreach collection="fieldList" item="item">
				${item.fieldName} ${item.fieldType} comment #{item.name},
			</foreach>
			createTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
			tenant_id VARCHAR(10) not null comment '租户ID'
			<foreach collection="indexFields" item="field" open="," separator=",">
				index idx_field_${field}(${field})
			</foreach>
			,index idx_strategy_batch_id(strategyBatchId)
		) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4
	</update>

	<update id="ddlDropTable">
		drop table if exists ${tableName}
	</update>

	<insert id="insertSourceData">
		insert into ${tableName} (
		strategyBatchId,
		<foreach collection="fieldNames" item="name" separator=",">
			${name}
		</foreach>,
		createTime,tenant_id)
		values(
		#{strategyBatchId},
		<foreach collection="fieldValues" item="value" separator=",">
			#{value}
		</foreach>,
		now(), #{tenantId})
	</insert>

	<insert id="insertSourceStorageData">
		insert into ${storageTableName} (
		strategyBatchId,
		<foreach collection="fieldNames" item="name" separator=",">
			${name}
		</foreach>,
		createTime,tenant_id)
		select #{strategyBatchId},
		<foreach collection="fieldNames" item="name" separator=",">
			${name}
		</foreach>,
		createTime, tenant_id
		from ${tableName}
		where strategyBatchId = #{strategyBatchId}
	</insert>

	<select id="isExistSourceData" resultType="int">
		select count(1) from ${tableName}
	</select>

	<sql id="param_condition">
		<where>
			<foreach collection="paramList" item="param">
				<if test='param.paramType == "1"'>
					and ${param.fieldName} = #{param.value}
				</if>
				<if test='param.paramType == "2"'>
					and ${param.fieldName} in (
					<foreach collection="param.value" item="item" >
						#{item}
					</foreach>
					)
				</if>
				<if test='param.paramType == "3"'>
					and ${param.fieldName} >= #{param.value[0]}
					and ${param.fieldName} &lt; #{param.value[1]}
				</if>
			</foreach>
		</where>
	</sql>

	<select id="isExistSourceDataByParams" resultType="int">
		select count(1) from ${tableName}
		<include refid="param_condition"/>
	</select>

	<delete id="deleteSourceDataByParams">
		delete from ${tableName}
		<include refid="param_condition"/>
	</delete>

	<delete id="delSourceStorageData">
		delete from ${tableName} where strategyBatchId = #{strategyBatchId}
	</delete>

	<select id="getSourceDataList" resultType="map">
		select * from ${tableName}
		where strategyBatchId = #{strategyBatchId}
		<if test="searchParamList != null">
			<foreach collection="searchParamList" item="param">
		  		<if test='param.fieldName != null and param.fieldName != ""'>
					and ${param.fieldName} ${param.operator} #{param.value}
		  		</if>
			</foreach>
		</if>
	</select>

	<select id="getDataList" resultType="map">
		select * from ${tableName}
		<where>
			<if test="searchParamList != null">
				<foreach collection="searchParamList" item="param">
					<if test='param.fieldName != null and param.fieldName != ""'>
						and ${param.fieldName} ${param.operator} #{param.value}
					</if>
				</foreach>
			</if>
		</where>
	</select>

	<select id="statSourceData" resultType="map">
		select count(1) total
		<foreach collection="checkFields" item="field" open="," separator=",">
			sum(${field}) as ${field}
		</foreach>
		from ${tableName}
		where strategyBatchId = #{strategyBatchId}
	</select>

	<delete id="cleanSourceData">
		delete from ${tableName}
	</delete>
</mapper>
