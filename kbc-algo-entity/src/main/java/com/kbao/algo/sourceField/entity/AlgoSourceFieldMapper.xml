<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.algo.sourceField.dao.AlgoSourceFieldMapper">

	<resultMap id="BaseResultMap" type="com.kbao.algo.sourceField.entity.AlgoSourceField">
    	<id column="field_id" jdbcType="INTEGER"  property="fieldId"  />
        <result property="sourceId" jdbcType="INTEGER"  column="source_id" />  
        <result property="name" jdbcType="VARCHAR"  column="name" />  
        <result property="fieldName" jdbcType="VARCHAR"  column="field_name" />  
        <result property="fieldType" jdbcType="VARCHAR"  column="field_type" />
		<result property="fieldLength" jdbcType="VARCHAR"  column="field_length" />
        <result property="isIndex" jdbcType="VARCHAR"  column="is_index" />
		<result property="isParam" jdbcType="VARCHAR"  column="is_param" />
		<result property="paramType" jdbcType="VARCHAR"  column="param_type" />
		<result property="isCheck" jdbcType="VARCHAR"  column="is_check" />
		<result property="remark" jdbcType="VARCHAR"  column="remark" />
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="createId" jdbcType="VARCHAR"  column="create_id" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="updateId" jdbcType="VARCHAR"  column="update_id" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
	</resultMap>

	<sql id="Base_Column_List">
		field_id,  
		source_id,  
		name,  
		field_name,  
		field_type,
		field_length,
		is_index,
		is_param,
		param_type,
		is_check,
		remark,  
		create_time,  
		create_id,  
		update_time,  
		update_id,  
		tenant_id  
	</sql>

	<sql id="Alias_Column_List">
		t.field_id, 
		t.source_id, 
		t.name, 
		t.field_name, 
		t.field_type,
		t.field_length,
		t.is_index,
		t.is_param,
		t.param_type,
		t.is_check,
		t.remark, 
		t.create_time, 
		t.create_id, 
		t.update_time, 
		t.update_id, 
		t.tenant_id 
	</sql>

	<sql id="Base_Condition">
		<where>
			<if test="sourceId != null">
				and t.source_id = #{sourceId,jdbcType=INTEGER}
			</if>
			<if test="name != null and name != ''">
				and t.name = #{name,jdbcType=VARCHAR}
			</if>
			<if test="fieldName != null and fieldName != ''">
				and t.field_name = #{fieldName,jdbcType=VARCHAR}
			</if>
			<if test="fieldType != null and fieldType != ''">
				and t.field_type = #{fieldType,jdbcType=VARCHAR}
			</if>
			<if test="isIndex != null and isIndex != ''">
				and t.is_index = #{isIndex,jdbcType=VARCHAR}
			</if>
			<if test="isParam != null and isParam != ''">
				and t.is_param = #{isParam,jdbcType=VARCHAR}
			</if>
			<if test="paramType != null and paramType != ''">
				and t.param_type = #{paramType,jdbcType=VARCHAR}
			</if>
			<if test="isCheck != null and isCheck != ''">
				and t.is_check = #{isCheck,jdbcType=VARCHAR}
			</if>
			<if test="remark != null and remark != ''">
				and t.remark = #{remark,jdbcType=VARCHAR}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime,jdbcType=TIMESTAMP}
			</if>
			<if test="createId != null and createId != ''">
				and t.create_id = #{createId,jdbcType=VARCHAR}
			</if>
			<if test="updateTime != null">
				and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
			</if>
			<if test="updateId != null and updateId != ''">
				and t.update_id = #{updateId,jdbcType=VARCHAR}
			</if>
			<if test="tenantId != null and tenantId != ''">
				and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
			</if>
			<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_algo_source_field t
		<include refid="Base_Condition" />
		order by t.create_time asc
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_algo_source_field
		where  field_id = #{fieldId,jdbcType=INTEGER}
	</select>


	<!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		delete from t_algo_source_field
		where  field_id = #{fieldId,jdbcType=INTEGER}
	</delete>
	

	<!-- 插入所有字段 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="fieldId">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="fieldId">
		SELECT LAST_INSERT_ID() AS fieldId
		</selectKey>  -->
		insert into t_algo_source_field(
			<include refid="Base_Column_List" />
		)
		values(  
                #{fieldId,jdbcType=INTEGER}, 
                #{sourceId,jdbcType=INTEGER},
                #{name,jdbcType=VARCHAR},
                #{fieldName,jdbcType=VARCHAR},
                #{fieldType,jdbcType=VARCHAR},
		       	#{fieldLength,jdbcType=VARCHAR},
                #{isIndex,jdbcType=VARCHAR},
		       	#{isParam,jdbcType=VARCHAR},
		        #{paramType,jdbcType=VARCHAR},
		       	#{isCheck,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                now(),
                #{createId,jdbcType=VARCHAR},
                now(),
				#{createId,jdbcType=VARCHAR},
                #{tenantId,jdbcType=VARCHAR}
               )
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.algo.sourceField.entity.AlgoSourceField">
		update t_algo_source_field
		<set>
	        <if test="name != null ">
	        	name = #{name,jdbcType=VARCHAR},  
	        </if>  
	        <if test="fieldName != null ">  
	        	field_name = #{fieldName,jdbcType=VARCHAR},  
	        </if>  
	        <if test="fieldType != null ">  
	        	field_type = #{fieldType,jdbcType=VARCHAR},  
	        </if>
		    <if test="fieldLength != null ">
		    	field_length = #{fieldLength,jdbcType=VARCHAR},
		    </if>
	        <if test="isIndex != null ">  
	        	is_index = #{isIndex,jdbcType=VARCHAR},  
	        </if>
		    <if test="isParam != null ">
		    	is_param = #{isParam,jdbcType=VARCHAR},
		    </if>
		    <if test="paramType != null ">
		    	param_type = #{paramType,jdbcType=VARCHAR},
		    </if>
		    <if test="isCheck != null ">
		    	is_check = #{isCheck,jdbcType=VARCHAR},
		    </if>
	        <if test="remark != null ">  
	        	remark = #{remark,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateId != null ">
	        	update_id = #{updateId,jdbcType=VARCHAR},  
	        </if>
			update_time = now()
		</set>
		where field_id = #{fieldId,jdbcType=INTEGER}
	</update>

	<select id="count" resultType="int">
		select count(1) from t_algo_source_field
		where source_id = #{sourceId,jdbcType=INTEGER}
		<if test="fieldId != null">
		  	and field_id != #{fieldId,jdbcType=INTEGER}
		</if>
		<if test="fieldName != null and fieldName != ''">
			and field_name = #{fieldName,jdbcType=VARCHAR}
		</if>
	</select>

	<!-- 自定义查询 -->
	<select id="getSourceFields" resultType="com.kbao.algo.sourceField.bean.AlgoParamResVo">
		select f.field_name fieldName, f.name fieldDesc
		from t_algo_source_field f
		left join t_algo_source s on f.source_id = s.source_id
		where s.biz_code = #{bizCode,jdbcType=VARCHAR}
		order by f.create_time asc
	</select>

	<select id="getManySourceParams" resultType="com.kbao.algo.sourceField.bean.AlgoParamResVo">
		select f.field_name fieldName, f.name fieldDesc, f.param_type paramType
		from t_algo_source_field f
				 left join t_algo_source s on f.source_id = s.source_id
		where f.is_param = '1'
		and s.biz_code in
		<foreach collection="bizCodes" item="code" open="(" separator="," close=")">
			#{code,jdbcType=VARCHAR}
		</foreach>
		order by f.create_time asc
	</select>
</mapper>
