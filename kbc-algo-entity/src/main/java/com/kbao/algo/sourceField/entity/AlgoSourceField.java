package com.kbao.algo.sourceField.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR> jie
* @Description 实体
* @Date 2024-10-25
*/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlgoSourceField implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中field_id
     * 主键
     */  
	private Integer fieldId;

    /**
     * 对应表中source_id
     * 数据源ID
     */  
	private Integer sourceId;

    /**
     * 对应表中name
     * 字段名称
     */  
	private String name;

    /**
     * 对应表中field_name
     * 字段名
     */  
	private String fieldName;

    /**
     * 对应表中field_type
     * 字段类型 数据类型 string-字符串，int-整数，float-浮点数
     */  
	private String fieldType;

	/**
	* 对应表中field_length
	* 字段长度 10,2
	*/
	private String fieldLength;

    /**
     * 对应表中is_index
     * 是否是索引字段 0-否，1-是
     */  
	private String isIndex;

	private String isParam;

	private String paramType;

	private String isCheck;

    /**
     * 对应表中remark
     * 备注
     */  
	private String remark;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

    /**
     * 对应表中create_id
     * 创建人
     */  
	private String createId;

    /**
     * 对应表中update_time
     * 更新时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

    /**
     * 对应表中update_id
     * 更新人
     */  
	private String updateId;

    /**
     * 对应表中tenant_id
     * 租户ID
     */  
	private String tenantId;

}   