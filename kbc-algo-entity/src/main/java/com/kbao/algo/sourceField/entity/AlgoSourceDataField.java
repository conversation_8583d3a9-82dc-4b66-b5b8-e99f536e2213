package com.kbao.algo.sourceField.entity;

import com.kbao.algo.indicator.indicatorField.bean.DataField;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Data
@Document(value = "AlgoSourceDataField")
@AllArgsConstructor
public class AlgoSourceDataField {
    @Indexed(background = true)
    private Integer dataSpaceId;
    @Indexed(background = true)
    private String sourceCode;
    private List<DataField> fields;
}
