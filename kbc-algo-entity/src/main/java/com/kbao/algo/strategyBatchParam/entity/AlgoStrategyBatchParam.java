package com.kbao.algo.strategyBatchParam.entity;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* @Description 算法规则-策略执行批次参数实体
* @Date 2024-11-20
*/
@Data
public class AlgoStrategyBatchParam implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 主键
     */  
	private Integer id;

    /**
     * 对应表中batch_id
     * 批次ID
     */  
	private Integer batchId;

    /**
     * 对应表中field_name
     * 参数名
     */  
	private String fieldName;

    /**
     * 对应表中param_value
     * 参数值
     */  
	private String paramValue;

    /**
     * 对应表中create_id
     * 创建人 当前用户ID
     */  
	private String createId;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

    /**
     * 对应表中update_id
     * 更新人 默认为当前时间
     */  
	private String updateId;

    /**
     * 对应表中update_time
     * 更新时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

    /**
     * 对应表中tenant_id
     * 租户ID
     */  
	private String tenantId;

    /**
     * 对应表中param_type
     * 参数类型：1-单个，2-多个，3-范围
     */  
	private String paramType;

}   