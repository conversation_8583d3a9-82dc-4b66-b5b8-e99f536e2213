<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.algo.strategyBatchParam.dao.AlgoStrategyBatchParamMapper">

	<resultMap id="BaseResultMap" type="com.kbao.algo.strategyBatchParam.entity.AlgoStrategyBatchParam">
    	<id column="id" jdbcType="INTEGER"  property="id"  />
        <result property="batchId" jdbcType="INTEGER"  column="batch_id" />  
        <result property="fieldName" jdbcType="VARCHAR"  column="field_name" />  
        <result property="paramValue" jdbcType="VARCHAR"  column="param_value" />  
        <result property="createId" jdbcType="VARCHAR"  column="create_id" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="updateId" jdbcType="VARCHAR"  column="update_id" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
        <result property="paramType" jdbcType="CHAR"  column="param_type" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		batch_id,  
		field_name,  
		param_value,  
		create_id,  
		create_time,  
		update_id,  
		update_time,  
		tenant_id,  
		param_type  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.batch_id, 
		t.field_name, 
		t.param_value, 
		t.create_id, 
		t.create_time, 
		t.update_id, 
		t.update_time, 
		t.tenant_id, 
		t.param_type 
	</sql>

	<sql id="Base_Condition">
		<where>
	    <if test="batchId != null">
	   		and t.batch_id = #{batchId,jdbcType=INTEGER}  
	    </if>
	    <if test="fieldName != null and fieldName != ''">
	   		and t.field_name = #{fieldName,jdbcType=VARCHAR}  
	    </if>
	    <if test="paramValue != null and paramValue != ''">
	   		and t.param_value = #{paramValue,jdbcType=VARCHAR}  
	    </if>
	    <if test="createId != null and createId != ''">
	   		and t.create_id = #{createId,jdbcType=VARCHAR}  
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="updateId != null and updateId != ''">
	   		and t.update_id = #{updateId,jdbcType=VARCHAR}  
	    </if>
	    <if test="updateTime != null">
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="tenantId != null and tenantId != ''">
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}  
	    </if>
	    <if test="paramType != null">
	   		and t.param_type = #{paramType,jdbcType=CHAR}  
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_algo_strategy_batch_param t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_algo_strategy_batch_param t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_algo_strategy_batch_param
		where  id = #{id,jdbcType=INTEGER}
	</select>


	<!-- 根据主键删除 -->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		delete from t_algo_strategy_batch_param
		where id = #{id,jdbcType=INTEGER}
	</delete>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.algo.strategyBatchParam.entity.AlgoStrategyBatchParam">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_algo_strategy_batch_param(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=INTEGER}, 
                 
                #{batchId,jdbcType=INTEGER}, 
                 
                #{fieldName,jdbcType=VARCHAR}, 
                 
                #{paramValue,jdbcType=VARCHAR}, 
                 
                #{createId,jdbcType=VARCHAR}, 
                 
                #{createTime,jdbcType=TIMESTAMP}, 
                 
                #{updateId,jdbcType=VARCHAR}, 
                 
                #{updateTime,jdbcType=TIMESTAMP}, 
                 
                #{tenantId,jdbcType=VARCHAR}, 
                 
                #{paramType,jdbcType=CHAR} 
               )
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.algo.strategyBatchParam.entity.AlgoStrategyBatchParam">
		<selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>
		insert into t_algo_strategy_batch_param
		<trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="id != null ">  
	       		id,
	        </if>  
	        <if test="batchId != null ">  
	       		batch_id,
	        </if>  
	        <if test="fieldName != null ">  
	       		field_name,
	        </if>  
	        <if test="paramValue != null ">  
	       		param_value,
	        </if>  
	        <if test="createId != null ">  
	       		create_id,
	        </if>  
	        <if test="createTime != null ">  
	       		create_time,
	        </if>  
	        <if test="updateId != null ">  
	       		update_id,
	        </if>  
	        <if test="updateTime != null ">  
	       		update_time,
	        </if>  
	        <if test="tenantId != null ">  
	       		tenant_id,
	        </if>  
	        <if test="paramType != null ">  
	       		param_type,
	        </if>  
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">  
            	#{id,jdbcType=INTEGER},
            </if>  
            <if test="batchId != null">  
            	#{batchId,jdbcType=INTEGER},
            </if>  
            <if test="fieldName != null">  
            	#{fieldName,jdbcType=VARCHAR},
            </if>  
            <if test="paramValue != null">  
            	#{paramValue,jdbcType=VARCHAR},
            </if>  
            <if test="createId != null">  
            	#{createId,jdbcType=VARCHAR},
            </if>  
            <if test="createTime != null">  
            	#{createTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="updateId != null">  
            	#{updateId,jdbcType=VARCHAR},
            </if>  
            <if test="updateTime != null">  
            	#{updateTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="tenantId != null">  
            	#{tenantId,jdbcType=VARCHAR},
            </if>  
            <if test="paramType != null">  
            	#{paramType,jdbcType=CHAR},
            </if>  
		</trim>
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.algo.strategyBatchParam.entity.AlgoStrategyBatchParam">
		update t_algo_strategy_batch_param
		<set>
	        <if test="batchId != null ">  
	        	batch_id = #{batchId,jdbcType=INTEGER},  
	        </if>  
	        <if test="fieldName != null ">  
	        	field_name = #{fieldName,jdbcType=VARCHAR},  
	        </if>  
	        <if test="paramValue != null ">  
	        	param_value = #{paramValue,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createId != null ">  
	        	create_id = #{createId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="updateId != null ">  
	        	update_id = #{updateId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateTime != null ">  
	        	update_time = #{updateTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="tenantId != null ">  
	        	tenant_id = #{tenantId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="paramType != null ">  
	        	param_type = #{paramType,jdbcType=CHAR},  
	        </if>  
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.algo.strategyBatchParam.entity.AlgoStrategyBatchParam">
		update t_algo_strategy_batch_param
		set
           batch_id = #{batchId,jdbcType=INTEGER},
           field_name = #{fieldName,jdbcType=VARCHAR},
           param_value = #{paramValue,jdbcType=VARCHAR},
           create_id = #{createId,jdbcType=VARCHAR},
           create_time = #{createTime,jdbcType=TIMESTAMP},
           update_id = #{updateId,jdbcType=VARCHAR},
           update_time = #{updateTime,jdbcType=TIMESTAMP},
           tenant_id = #{tenantId,jdbcType=VARCHAR},
           param_type = #{paramType,jdbcType=CHAR}
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_algo_strategy_batch_param(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
	        <choose>
	            <when test="item.batchId != null">,#{item.batchId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.fieldName != null">,#{item.fieldName}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.paramValue != null">,#{item.paramValue}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.paramType != null">,#{item.paramType}</when><otherwise>,default</otherwise>
	        </choose>
		)
		</foreach>
	</insert>

	<!-- 批量插入或更新 -->
	<update id="batchInsertOrUpdate" parameterType="java.util.List">
		insert into t_algo_strategy_batch_param(
			<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
			<choose><when test="item.batchId != null">,#{item.batchId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.fieldName != null">,#{item.fieldName}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.paramValue != null">,#{item.paramValue}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.paramType != null">,#{item.paramType}</when><otherwise>,default</otherwise></choose>
		)
		</foreach>
		on duplicate key update 
		   batch_id=values(batch_id), 
		   field_name=values(field_name), 
		   param_value=values(param_value), 
		   create_id=values(create_id), 
		   create_time=values(create_time), 
		   update_id=values(update_id), 
		   update_time=values(update_time), 
		   tenant_id=values(tenant_id), 
		   param_type=values(param_type) 
	</update>
	
	<!-- 批量删除-->
	<delete id="batchDelete" parameterType="java.util.List">
		delete from t_algo_strategy_batch_param where id in 
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")"> #{item}
		</foreach>
	</delete>

	<!-- 自定义查询 -->
	<select id="selectByBatchId" resultType="com.kbao.algo.sourceField.bean.AlgoParamResVo" parameterType="java.lang.Integer">
		select
			t.field_name fieldName,
			t.param_value paramValue,
			t.param_type paramType
		from t_algo_strategy_batch_param t
		where t.batch_id = #{batchId}
	</select>
	<select id="selectBatchParamByBatchId" resultType="com.kbao.algo.strategyBatchParam.bean.AlgoStrategyBatchParamVo" parameterType="java.lang.Integer">
		select
			t.field_name fieldName,
			t.param_value fieldValue
		from t_algo_strategy_batch_param t
		where t.batch_id = #{batchId}
	</select>

	<delete id="delByDataSpaceId">
		delete from t_algo_strategy_batch_param
		where batch_id in (select id from t_algo_strategy_batch where data_space_id = #{dataSpaceId})
	</delete>

	<delete id="delByBatchIds">
		delete from t_algo_strategy_batch_param
		where batch_id in
		<foreach collection="batchIds" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

</mapper>
