package com.kbao.algo.strategyBatch.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 策略执行状态枚举
 * <AUTHOR>
 * @version V1.0
 * @date 2024年5月22日15:33:40
 * @description
 */
public enum StrategyBatchExecuteStatusEnum {

    UN_EXECUTED("0", "未执行"),
    IN_PROGRESS("1", "执行中"),
    EXECUTION_COMPLETED("2", "执行完成"),
    EXECUTION_FAILED("3", "执行失败");

    private String code;
    private String desc;

    StrategyBatchExecuteStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static final List<String> FINAL_STATE = Arrays.asList(UN_EXECUTED.getCode(), EXECUTION_FAILED.getCode());

    public static String  getDescByCode(String code){
        Map<String, String> dictMap = Arrays.stream(StrategyBatchExecuteStatusEnum.values()).collect(Collectors.toMap(StrategyBatchExecuteStatusEnum::getCode, StrategyBatchExecuteStatusEnum::getDesc));
        return dictMap.get(code) == null ? "" : dictMap.get(code);
    }

    public static String  getCodeByDesc(String desc){
        Map<String, String> dictMap = Arrays.stream(StrategyBatchExecuteStatusEnum.values()).collect(Collectors.toMap(StrategyBatchExecuteStatusEnum::getDesc, StrategyBatchExecuteStatusEnum::getCode));
        return dictMap.get(desc) == null ? "" : dictMap.get(desc);
    }
}
