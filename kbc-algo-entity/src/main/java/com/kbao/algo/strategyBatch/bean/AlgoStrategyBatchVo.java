package com.kbao.algo.strategyBatch.bean;

import com.kbao.algo.strategyBatchParam.bean.AlgoStrategyBatchParamVo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 策略批次返回对象
 * @author: xia<PERSON><PERSON><PERSON><PERSON>
 * @time: 2024/11/1 17:36
 */
@Data
public class AlgoStrategyBatchVo {

    private Integer id;

    private String bizCode;

    private String dataSpaceCode;

    private String executeStatus;

    private String name;

    private String remark;

    private Date executeTime;

    private Date finishTime;

    private String paramContent;

    private List<String> params;
}
