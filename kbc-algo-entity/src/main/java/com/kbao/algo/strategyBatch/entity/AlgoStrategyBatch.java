package com.kbao.algo.strategyBatch.entity;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* @Description 算法规则-策略执行批次实体
* @Date 2024-11-08
*/
@Data
public class AlgoStrategyBatch implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 主键
     */  
	private Integer id;

    /**
     * 对应表中data_space_id
     * 数据空间ID
     */  
	private Integer dataSpaceId;

    /**
     * 对应表中strategy_id
     * 策略ID
     */  
	private Integer strategyId;

    /**
     * 对应表中biz_code
     * 编码
     */  
	private String bizCode;

    /**
     * 对应表中remark
     * 备注
     */  
	private String remark;

    /**
     * 对应表中execute_id
     * 创建人
     */  
	private String executeId;

    /**
     * 对应表中execute_time
     * 执行时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date executeTime;

    /**
     * 对应表中finish_time
     * 完成时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date finishTime;

    /**
     * 对应表中execute_status
     * 执行状态 0-未执行 1-执行中 2-执行完成 3-执行失败
     */  
	private String executeStatus;

    /**
     * 对应表中tenant_id
     * 租户ID
     */  
	private String tenantId;

}   