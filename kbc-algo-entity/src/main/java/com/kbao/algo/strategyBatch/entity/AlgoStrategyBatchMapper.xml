<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.algo.strategyBatch.dao.AlgoStrategyBatchMapper">

	<resultMap id="BaseResultMap" type="com.kbao.algo.strategyBatch.entity.AlgoStrategyBatch">
    	<id column="id" jdbcType="INTEGER"  property="id"  />
        <result property="dataSpaceId" jdbcType="INTEGER"  column="data_space_id" />  
        <result property="strategyId" jdbcType="INTEGER"  column="strategy_id" />  
        <result property="bizCode" jdbcType="VARCHAR"  column="biz_code" />  
        <result property="remark" jdbcType="VARCHAR"  column="remark" />  
        <result property="executeId" jdbcType="VARCHAR"  column="execute_id" />  
        <result property="executeTime" jdbcType="TIMESTAMP"  column="execute_time" />  
        <result property="finishTime" jdbcType="TIMESTAMP"  column="finish_time" />  
        <result property="executeStatus" jdbcType="VARCHAR"  column="execute_status" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		data_space_id,  
		strategy_id,  
		biz_code,  
		remark,  
		execute_id,  
		execute_time,  
		finish_time,  
		execute_status,
		create_time,
		tenant_id  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.data_space_id, 
		t.strategy_id, 
		t.biz_code, 
		t.remark, 
		t.execute_id, 
		t.execute_time, 
		t.finish_time, 
		t.execute_status, 
		t.tenant_id 
	</sql>

	<sql id="Base_Condition">
		<where>
	    <if test="dataSpaceId != null">
	   		and t.data_space_id = #{dataSpaceId,jdbcType=INTEGER}  
	    </if>
	    <if test="strategyId != null">
	   		and t.strategy_id = #{strategyId,jdbcType=INTEGER}  
	    </if>
	    <if test="bizCode != null and bizCode != ''">
	   		and t.biz_code = #{bizCode,jdbcType=VARCHAR}  
	    </if>
	    <if test="remark != null and remark != ''">
	   		and t.remark = #{remark,jdbcType=VARCHAR}  
	    </if>
	    <if test="executeId != null and executeId != ''">
	   		and t.execute_id = #{executeId,jdbcType=VARCHAR}  
	    </if>
	    <if test="executeTime != null">
	   		and t.execute_time = #{executeTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="finishTime != null">
	   		and t.finish_time = #{finishTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="executeStatus != null and executeStatus != ''">
	   		and t.execute_status = #{executeStatus,jdbcType=VARCHAR}  
	    </if>
	    <if test="tenantId != null and tenantId != ''">
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}  
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_algo_strategy_batch t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_algo_strategy_batch t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_algo_strategy_batch
		where id = #{id,jdbcType=INTEGER}
	</select>
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.algo.strategyBatch.entity.AlgoStrategyBatch">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_algo_strategy_batch(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=INTEGER}, 
                 
                #{dataSpaceId,jdbcType=INTEGER}, 
                 
                #{strategyId,jdbcType=INTEGER}, 
                 
                #{bizCode,jdbcType=VARCHAR}, 
                 
                #{remark,jdbcType=VARCHAR}, 
                 
                #{executeId,jdbcType=VARCHAR}, 
                 
                #{executeTime,jdbcType=TIMESTAMP}, 
                 
                #{finishTime,jdbcType=TIMESTAMP}, 
                 
                #{executeStatus,jdbcType=VARCHAR},

		       	#{createTime,jdbcType=TIMESTAMP},
                 
                #{tenantId,jdbcType=VARCHAR} 
               )
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.algo.strategyBatch.entity.AlgoStrategyBatch">
		<selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>
		insert into t_algo_strategy_batch
		<trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="id != null ">  
	       		id,
	        </if>  
	        <if test="dataSpaceId != null ">  
	       		data_space_id,
	        </if>  
	        <if test="strategyId != null ">  
	       		strategy_id,
	        </if>  
	        <if test="bizCode != null ">  
	       		biz_code,
	        </if>  
	        <if test="remark != null ">  
	       		remark,
	        </if>  
	        <if test="executeId != null ">  
	       		execute_id,
	        </if>  
	        <if test="executeTime != null ">  
	       		execute_time,
	        </if>  
	        <if test="finishTime != null ">  
	       		finish_time,
	        </if>  
	        <if test="executeStatus != null ">  
	       		execute_status,
	        </if>
	        <if test="tenantId != null ">  
	       		tenant_id,
	        </if>
			create_time
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">  
            	#{id,jdbcType=INTEGER},
            </if>  
            <if test="dataSpaceId != null">  
            	#{dataSpaceId,jdbcType=INTEGER},
            </if>  
            <if test="strategyId != null">  
            	#{strategyId,jdbcType=INTEGER},
            </if>  
            <if test="bizCode != null">  
            	#{bizCode,jdbcType=VARCHAR},
            </if>  
            <if test="remark != null">  
            	#{remark,jdbcType=VARCHAR},
            </if>  
            <if test="executeId != null">  
            	#{executeId,jdbcType=VARCHAR},
            </if>  
            <if test="executeTime != null">  
            	#{executeTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="finishTime != null">  
            	#{finishTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="executeStatus != null">  
            	#{executeStatus,jdbcType=VARCHAR},
            </if>  
            <if test="tenantId != null">  
            	#{tenantId,jdbcType=VARCHAR},
            </if>
            now()
		</trim>
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.algo.strategyBatch.entity.AlgoStrategyBatch">
		update t_algo_strategy_batch
		<set>
	        <if test="dataSpaceId != null ">  
	        	data_space_id = #{dataSpaceId,jdbcType=INTEGER},  
	        </if>  
	        <if test="strategyId != null ">  
	        	strategy_id = #{strategyId,jdbcType=INTEGER},  
	        </if>  
	        <if test="bizCode != null ">  
	        	biz_code = #{bizCode,jdbcType=VARCHAR},  
	        </if>  
	        <if test="remark != null ">  
	        	remark = #{remark,jdbcType=VARCHAR},  
	        </if>  
	        <if test="executeId != null ">  
	        	execute_id = #{executeId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="executeTime != null ">  
	        	execute_time = #{executeTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="finishTime != null ">  
	        	finish_time = #{finishTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="executeStatus != null ">  
	        	execute_status = #{executeStatus,jdbcType=VARCHAR},  
	        </if>  
	        <if test="tenantId != null ">  
	        	tenant_id = #{tenantId,jdbcType=VARCHAR},  
	        </if>  
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.algo.strategyBatch.entity.AlgoStrategyBatch">
		update t_algo_strategy_batch
		set
           data_space_id = #{dataSpaceId,jdbcType=INTEGER},
           strategy_id = #{strategyId,jdbcType=INTEGER},
           biz_code = #{bizCode,jdbcType=VARCHAR},
           remark = #{remark,jdbcType=VARCHAR},
           execute_id = #{executeId,jdbcType=VARCHAR},
           execute_time = #{executeTime,jdbcType=TIMESTAMP},
           finish_time = #{finishTime,jdbcType=TIMESTAMP},
           execute_status = #{executeStatus,jdbcType=VARCHAR},
           tenant_id = #{tenantId,jdbcType=VARCHAR}
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_algo_strategy_batch(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
	        <choose>
	            <when test="item.dataSpaceId != null">,#{item.dataSpaceId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.strategyId != null">,#{item.strategyId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.bizCode != null">,#{item.bizCode}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.remark != null">,#{item.remark}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.executeId != null">,#{item.executeId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.executeTime != null">,#{item.executeTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.finishTime != null">,#{item.finishTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.executeStatus != null">,#{item.executeStatus}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise>
	        </choose>
		)
		</foreach>
	</insert>

	<!-- 批量插入或更新 -->
	<update id="batchInsertOrUpdate" parameterType="java.util.List">
		insert into t_algo_strategy_batch(
			<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
			<choose><when test="item.dataSpaceId != null">,#{item.dataSpaceId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.strategyId != null">,#{item.strategyId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.bizCode != null">,#{item.bizCode}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.remark != null">,#{item.remark}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.executeId != null">,#{item.executeId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.executeTime != null">,#{item.executeTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.finishTime != null">,#{item.finishTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.executeStatus != null">,#{item.executeStatus}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise></choose>
		)
		</foreach>
		on duplicate key update 
		   data_space_id=values(data_space_id), 
		   strategy_id=values(strategy_id), 
		   biz_code=values(biz_code), 
		   remark=values(remark), 
		   execute_id=values(execute_id), 
		   execute_time=values(execute_time), 
		   finish_time=values(finish_time), 
		   execute_status=values(execute_status), 
		   tenant_id=values(tenant_id) 
	</update>

	<!-- 自定义查询 -->
	<select id="getAlgoStrategyBatch" resultType="com.kbao.algo.strategyBatch.bean.AlgoStrategyBatchVo">
		select sb.id, ds.biz_code dataSpaceCode, sb.biz_code bizCode, s.name, sb.remark,
		       sb.execute_time executeTime, sb.finish_time finishTime, sb.execute_status executeStatus,
		group_concat(distinct concat(bp.field_name, ':', bp.param_value) separator ',') paramContent
		from t_algo_strategy_batch sb
		left join t_algo_strategy s on sb.strategy_id = s.strategy_id
		left join t_algo_data_space ds on sb.data_space_id = ds.id
		left join t_algo_strategy_batch_param bp on sb.id = bp.batch_id
		left join t_algo_strategy_batch_param bp2 on sb.id = bp2.batch_id
		<where>
			<if test="bizCode != null and bizCode != ''">
				and sb.biz_code = #{bizCode}
			</if>
			<if test="dataSpaceCode != null and dataSpaceCode != ''">
				and ds.biz_code = #{dataSpaceCode}
			</if>
			<if test="executeStatus != null and executeStatus != ''">
				and sb.execute_status = #{executeStatus}
			</if>
			<if test="paramValue != null and paramValue != ''">
				and bp2.param_value = #{paramValue}
			</if>
		</where>
		group by sb.id
		order by sb.data_space_id desc, sb.create_time desc
	</select>

	<select id="getAlgoStrategyBatchByCode" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from t_algo_strategy_batch
		where biz_code = #{bizCode,jdbcType=VARCHAR}
	</select>

	<select id="getStrategyBatchByIds" resultType="com.kbao.algo.strategyBatch.bean.AlgoStrategyBatchVo">
		select sb.id, sb.biz_code bizCode, group_concat(distinct concat(bp.field_name, ':', bp.param_value) separator ',') paramContent
		from t_algo_strategy_batch sb
		left join t_algo_strategy_batch_param bp on sb.id = bp.batch_id
		where sb.id in
		<foreach collection="batchIds" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		group by sb.id
	</select>

	<delete id="delByDataSpaceId">
		delete from t_algo_strategy_batch where data_space_id = #{dataSpaceId}
	</delete>

	<delete id="delByBatchIds">
		delete from t_algo_strategy_batch
		where id in
		<foreach collection="batchIds" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>
</mapper>
