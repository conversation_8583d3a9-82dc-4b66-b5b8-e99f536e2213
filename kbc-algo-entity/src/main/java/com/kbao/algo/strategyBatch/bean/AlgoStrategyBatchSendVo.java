package com.kbao.algo.strategyBatch.bean;

import com.kbao.algo.strategyBatchParam.bean.AlgoStrategyBatchParamVo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 策略批次mq通知对象信息
 * @author: xia<PERSON><PERSON><PERSON><PERSON>
 * @time: 2024/11/13 16:34
 */
@Data
public class AlgoStrategyBatchSendVo {

    private String strategyCode;

    private String batchCode;

    private List<AlgoStrategyBatchParamVo> paramList;

    private Date finishTime;

    private String executeStatus;
}
