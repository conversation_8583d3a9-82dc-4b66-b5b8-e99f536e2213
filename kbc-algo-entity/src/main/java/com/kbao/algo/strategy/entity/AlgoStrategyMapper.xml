<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.algo.strategy.dao.AlgoStrategyMapper">

	<resultMap id="BaseResultMap" type="com.kbao.algo.strategy.entity.AlgoStrategy">
    	<id column="strategy_id" jdbcType="INTEGER"  property="strategyId"  />
        <result property="name" jdbcType="VARCHAR"  column="name" />  
        <result property="bizCode" jdbcType="VARCHAR"  column="biz_code" />  
        <result property="remark" jdbcType="VARCHAR"  column="remark" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="createId" jdbcType="VARCHAR"  column="create_id" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="updateId" jdbcType="VARCHAR"  column="update_id" />  
        <result property="isDeleted" jdbcType="TINYINT"  column="is_deleted" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
	</resultMap>

	<sql id="Base_Column_List">
		strategy_id,  
		name,  
		biz_code,  
		remark,  
		create_time,  
		create_id,  
		update_time,  
		update_id,  
		is_deleted,  
		tenant_id  
	</sql>

	<sql id="Alias_Column_List">
		t.strategy_id, 
		t.name, 
		t.biz_code, 
		t.remark, 
		t.create_time, 
		t.create_id, 
		t.update_time, 
		t.update_id, 
		t.is_deleted, 
		t.tenant_id 
	</sql>

	<sql id="Base_Condition">
		<where>
			t.is_deleted = 0 
	    <if test="name != null and name != ''">
	   		and t.name = #{name,jdbcType=VARCHAR}  
	    </if>
	    <if test="bizCode != null and bizCode != ''">
	   		and t.biz_code = #{bizCode,jdbcType=VARCHAR}  
	    </if>
	    <if test="remark != null and remark != ''">
	   		and t.remark = #{remark,jdbcType=VARCHAR}  
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="createId != null and createId != ''">
	   		and t.create_id = #{createId,jdbcType=VARCHAR}  
	    </if>
	    <if test="updateTime != null">
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="updateId != null and updateId != ''">
	   		and t.update_id = #{updateId,jdbcType=VARCHAR}  
	    </if>
	    <if test="isDeleted != null">
	   		and t.is_deleted = #{isDeleted,jdbcType=TINYINT}  
	    </if>
	    <if test="tenantId != null and tenantId != ''">
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}  
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_algo_strategy t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_algo_strategy t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_algo_strategy
		where  is_deleted = 0 and strategy_id = #{strategyId,jdbcType=INTEGER}
	</select>


	<!-- 根据主键删除 -->
    <update id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		update t_algo_strategy set is_deleted = 1
		where strategy_id = #{strategyId,jdbcType=INTEGER} and is_deleted = 0
	</update>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.algo.strategy.entity.AlgoStrategy">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="strategyId">
		SELECT LAST_INSERT_ID() AS strategyId
		</selectKey>  -->
		insert into t_algo_strategy(
			<include refid="Base_Column_List" />
		)
		values(  
                #{strategyId,jdbcType=INTEGER}, 
                 
                #{name,jdbcType=VARCHAR}, 
                 
                #{bizCode,jdbcType=VARCHAR}, 
                 
                #{remark,jdbcType=VARCHAR}, 
                 
                #{createTime,jdbcType=TIMESTAMP}, 
                 
                #{createId,jdbcType=VARCHAR}, 
                 
                #{updateTime,jdbcType=TIMESTAMP}, 
                 
                #{updateId,jdbcType=VARCHAR}, 
                 
                #{isDeleted,jdbcType=TINYINT}, 
                 
                #{tenantId,jdbcType=VARCHAR} 
               )
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.algo.strategy.entity.AlgoStrategy">
		<selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="strategyId">
		SELECT LAST_INSERT_ID() AS strategyId
		</selectKey>
		insert into t_algo_strategy
		<trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="strategyId != null ">  
	       		strategy_id,
	        </if>  
	        <if test="name != null ">  
	       		name,
	        </if>  
	        <if test="bizCode != null ">  
	       		biz_code,
	        </if>  
	        <if test="remark != null ">  
	       		remark,
	        </if>  
	        <if test="createTime != null ">  
	       		create_time,
	        </if>  
	        <if test="createId != null ">  
	       		create_id,
	        </if>  
	        <if test="updateTime != null ">  
	       		update_time,
	        </if>  
	        <if test="updateId != null ">  
	       		update_id,
	        </if>  
	        <if test="isDeleted != null ">  
	       		is_deleted,
	        </if>  
	        <if test="tenantId != null ">  
	       		tenant_id,
	        </if>  
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="strategyId != null">  
            	#{strategyId,jdbcType=INTEGER},
            </if>  
            <if test="name != null">  
            	#{name,jdbcType=VARCHAR},
            </if>  
            <if test="bizCode != null">  
            	#{bizCode,jdbcType=VARCHAR},
            </if>  
            <if test="remark != null">  
            	#{remark,jdbcType=VARCHAR},
            </if>  
            <if test="createTime != null">  
            	#{createTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="createId != null">  
            	#{createId,jdbcType=VARCHAR},
            </if>  
            <if test="updateTime != null">  
            	#{updateTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="updateId != null">  
            	#{updateId,jdbcType=VARCHAR},
            </if>  
            <if test="isDeleted != null">  
            	#{isDeleted,jdbcType=TINYINT},
            </if>  
            <if test="tenantId != null">  
            	#{tenantId,jdbcType=VARCHAR},
            </if>  
		</trim>
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.algo.strategy.entity.AlgoStrategy">
		update t_algo_strategy
		<set>
	        <if test="name != null ">  
	        	name = #{name,jdbcType=VARCHAR},  
	        </if>  
	        <if test="bizCode != null ">  
	        	biz_code = #{bizCode,jdbcType=VARCHAR},  
	        </if>  
	        <if test="remark != null ">  
	        	remark = #{remark,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="createId != null ">  
	        	create_id = #{createId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateTime != null ">  
	        	update_time = #{updateTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="updateId != null ">  
	        	update_id = #{updateId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="isDeleted != null ">  
	        	is_deleted = #{isDeleted,jdbcType=TINYINT},  
	        </if>  
	        <if test="tenantId != null ">  
	        	tenant_id = #{tenantId,jdbcType=VARCHAR},  
	        </if>  
		</set>
		where strategy_id = #{strategyId,jdbcType=INTEGER}
	</update>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.algo.strategy.entity.AlgoStrategy">
		update t_algo_strategy
		set
           name = #{name,jdbcType=VARCHAR},
           biz_code = #{bizCode,jdbcType=VARCHAR},
           remark = #{remark,jdbcType=VARCHAR},
           create_time = #{createTime,jdbcType=TIMESTAMP},
           create_id = #{createId,jdbcType=VARCHAR},
           update_time = #{updateTime,jdbcType=TIMESTAMP},
           update_id = #{updateId,jdbcType=VARCHAR},
           is_deleted = #{isDeleted,jdbcType=TINYINT},
           tenant_id = #{tenantId,jdbcType=VARCHAR}
		where strategy_id = #{strategyId,jdbcType=INTEGER}
	</update>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_algo_strategy(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.strategyId}
	        <choose>
	            <when test="item.name != null">,#{item.name}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.bizCode != null">,#{item.bizCode}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.remark != null">,#{item.remark}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.isDeleted != null">,#{item.isDeleted}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise>
	        </choose>
		)
		</foreach>
	</insert>

	<!-- 批量插入或更新 -->
	<update id="batchInsertOrUpdate" parameterType="java.util.List">
		insert into t_algo_strategy(
			<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.strategyId}
			<choose><when test="item.name != null">,#{item.name}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.bizCode != null">,#{item.bizCode}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.remark != null">,#{item.remark}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.isDeleted != null">,#{item.isDeleted}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise></choose>
		)
		</foreach>
		on duplicate key update 
		   name=values(name), 
		   biz_code=values(biz_code), 
		   remark=values(remark), 
		   create_time=values(create_time), 
		   create_id=values(create_id), 
		   update_time=values(update_time), 
		   update_id=values(update_id), 
		   is_deleted=values(is_deleted), 
		   tenant_id=values(tenant_id) 
	</update>
	
	<!-- 批量删除-->
	<update id="batchDelete" parameterType="java.util.List">
	update t_algo_strategy set is_deleted = 1 where strategy_id in 
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")"> #{item}
		</foreach>
	and is_deleted = 0
	</update>
	
	<!-- 自定义查询 -->
	<!-- 根据code查询-->
	<select id="selectByCode" resultMap="BaseResultMap" parameterType="java.lang.String">
		select
		<include refid="Base_Column_List" />
		from t_algo_strategy
		where  is_deleted = 0 and biz_code = #{bizCode,jdbcType=VARCHAR}
	</select>
</mapper>
