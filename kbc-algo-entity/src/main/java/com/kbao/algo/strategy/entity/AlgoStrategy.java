package com.kbao.algo.strategy.entity;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* @Description 算法规则-策略实体
* @Date 2024-10-28
*/
@Data
public class AlgoStrategy implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中strategy_id
     * 主键
     */  
	private Integer strategyId;

    /**
     * 对应表中name
     * 名称
     */  
	private String name;

    /**
     * 对应表中biz_code
     * 编码
     */  
	private String bizCode;

    /**
     * 对应表中remark
     * 备注
     */  
	private String remark;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

    /**
     * 对应表中create_id
     * 创建人
     */  
	private String createId;

    /**
     * 对应表中update_time
     * 更新时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

    /**
     * 对应表中update_id
     * 更新人
     */  
	private String updateId;

    /**
     * 对应表中is_deleted
     * 是否删除 0-未删除 1-已删除
     */  
	private Integer isDeleted;

    /**
     * 对应表中tenant_id
     * 租户ID
     */  
	private String tenantId;

}   