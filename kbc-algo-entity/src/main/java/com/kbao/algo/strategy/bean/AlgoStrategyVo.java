package com.kbao.algo.strategy.bean;

import com.kbao.algo.strategyNode.bean.AlgoStrategyNodeVo;
import lombok.Data;

import java.util.List;

/**
 * 策略请求参数
 * @author: xia<PERSON><PERSON><PERSON><PERSON>
 * @time: 2024/10/28 13:40
 */
@Data
public class AlgoStrategyVo {
    /**
     * 策略id
     */
    private Integer strategyId;

    /**
     * 编码
     */
    private String bizCode;

    /**
     * 策略名称
     */
    private String name;

    /**
     * 策略备注
     */
    private String remark;

    /**
     * 策略节点列表
     */
    private List<AlgoStrategyNodeVo> nodeList;
}
