<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.algo.strategyBatchNode.dao.AlgoStrategyBatchNodeMapper">

	<resultMap id="BaseResultMap" type="com.kbao.algo.strategyBatchNode.entity.AlgoStrategyBatchNode">
    	<id column="id" jdbcType="INTEGER"  property="id"  />
        <result property="bizCode" jdbcType="VARCHAR"  column="biz_code" />  
        <result property="batchId" jdbcType="INTEGER"  column="batch_id" />  
        <result property="nodeName" jdbcType="VARCHAR"  column="node_name" />  
        <result property="nodeType" jdbcType="VARCHAR"  column="node_type" />  
        <result property="nodeValue" jdbcType="VARCHAR"  column="node_value" />  
        <result property="version" jdbcType="VARCHAR"  column="version" />  
        <result property="executeId" jdbcType="VARCHAR"  column="execute_id" />  
        <result property="executeTime" jdbcType="TIMESTAMP"  column="execute_time" />  
        <result property="finishTime" jdbcType="TIMESTAMP"  column="finish_time" />  
        <result property="executeStatus" jdbcType="VARCHAR"  column="execute_status" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
        <result property="failMessage" jdbcType="VARCHAR"  column="fail_message" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		biz_code,  
		batch_id,  
		node_name,  
		node_type,  
		node_value,  
		version,  
		execute_id,  
		execute_time,  
		finish_time,  
		execute_status,  
		tenant_id,  
		fail_message  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.biz_code, 
		t.batch_id, 
		t.node_name, 
		t.node_type, 
		t.node_value, 
		t.version, 
		t.execute_id, 
		t.execute_time, 
		t.finish_time, 
		t.execute_status, 
		t.tenant_id, 
		t.fail_message 
	</sql>

	<sql id="Base_Condition">
		<where>
	    <if test="bizCode != null and bizCode != ''">
	   		and t.biz_code = #{bizCode,jdbcType=VARCHAR}  
	    </if>
	    <if test="batchId != null">
	   		and t.batch_id = #{batchId,jdbcType=INTEGER}  
	    </if>
	    <if test="nodeName != null and nodeName != ''">
	   		and t.node_name = #{nodeName,jdbcType=VARCHAR}  
	    </if>
	    <if test="nodeType != null and nodeType != ''">
	   		and t.node_type = #{nodeType,jdbcType=VARCHAR}  
	    </if>
	    <if test="nodeValue != null and nodeValue != ''">
	   		and t.node_value = #{nodeValue,jdbcType=VARCHAR}  
	    </if>
	    <if test="executeId != null and executeId != ''">
	   		and t.execute_id = #{executeId,jdbcType=VARCHAR}  
	    </if>
	    <if test="executeTime != null">
	   		and t.execute_time = #{executeTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="finishTime != null">
	   		and t.finish_time = #{finishTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="executeStatus != null and executeStatus != ''">
	   		and t.execute_status = #{executeStatus,jdbcType=VARCHAR}  
	    </if>
	    <if test="tenantId != null and tenantId != ''">
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}  
	    </if>
	    <if test="failMessage != null and failMessage != ''">
	   		and t.fail_message = #{failMessage,jdbcType=VARCHAR}  
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_algo_strategy_batch_node t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_algo_strategy_batch_node t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_algo_strategy_batch_node
		where id = #{id,jdbcType=INTEGER}
	</select>
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.algo.strategyBatchNode.entity.AlgoStrategyBatchNode">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_algo_strategy_batch_node(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=INTEGER}, 
                 
                #{bizCode,jdbcType=VARCHAR}, 
                 
                #{batchId,jdbcType=INTEGER}, 
                 
                #{nodeName,jdbcType=VARCHAR}, 
                 
                #{nodeType,jdbcType=VARCHAR}, 
                 
                #{nodeValue,jdbcType=VARCHAR}, 
                 
                #{version,jdbcType=VARCHAR}, 
                 
                #{executeId,jdbcType=VARCHAR}, 
                 
                #{executeTime,jdbcType=TIMESTAMP}, 
                 
                #{finishTime,jdbcType=TIMESTAMP}, 
                 
                #{executeStatus,jdbcType=VARCHAR}, 
                 
                #{tenantId,jdbcType=VARCHAR}, 
                 
                #{failMessage,jdbcType=VARCHAR} 
               )
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.algo.strategyBatchNode.entity.AlgoStrategyBatchNode">
		<selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>
		insert into t_algo_strategy_batch_node
		<trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="id != null ">  
	       		id,
	        </if>  
	        <if test="bizCode != null ">  
	       		biz_code,
	        </if>  
	        <if test="batchId != null ">  
	       		batch_id,
	        </if>  
	        <if test="nodeName != null ">  
	       		node_name,
	        </if>  
	        <if test="nodeType != null ">  
	       		node_type,
	        </if>  
	        <if test="nodeValue != null ">  
	       		node_value,
	        </if>  
	        <if test="version != null ">  
	       		version,
	        </if>  
	        <if test="executeId != null ">  
	       		execute_id,
	        </if>  
	        <if test="executeTime != null ">  
	       		execute_time,
	        </if>  
	        <if test="finishTime != null ">  
	       		finish_time,
	        </if>  
	        <if test="executeStatus != null ">  
	       		execute_status,
	        </if>  
	        <if test="tenantId != null ">  
	       		tenant_id,
	        </if>  
	        <if test="failMessage != null ">  
	       		fail_message,
	        </if>  
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">  
            	#{id,jdbcType=INTEGER},
            </if>  
            <if test="bizCode != null">  
            	#{bizCode,jdbcType=VARCHAR},
            </if>  
            <if test="batchId != null">  
            	#{batchId,jdbcType=INTEGER},
            </if>  
            <if test="nodeName != null">  
            	#{nodeName,jdbcType=VARCHAR},
            </if>  
            <if test="nodeType != null">  
            	#{nodeType,jdbcType=VARCHAR},
            </if>  
            <if test="nodeValue != null">  
            	#{nodeValue,jdbcType=VARCHAR},
            </if>  
            <if test="version != null">  
            	#{version,jdbcType=VARCHAR},
            </if>  
            <if test="executeId != null">  
            	#{executeId,jdbcType=VARCHAR},
            </if>  
            <if test="executeTime != null">  
            	#{executeTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="finishTime != null">  
            	#{finishTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="executeStatus != null">  
            	#{executeStatus,jdbcType=VARCHAR},
            </if>  
            <if test="tenantId != null">  
            	#{tenantId,jdbcType=VARCHAR},
            </if>  
            <if test="failMessage != null">  
            	#{failMessage,jdbcType=VARCHAR},
            </if>  
		</trim>
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.algo.strategyBatchNode.entity.AlgoStrategyBatchNode">
		update t_algo_strategy_batch_node
		<set>
	        <if test="bizCode != null ">  
	        	biz_code = #{bizCode,jdbcType=VARCHAR},  
	        </if>  
	        <if test="batchId != null ">  
	        	batch_id = #{batchId,jdbcType=INTEGER},  
	        </if>  
	        <if test="nodeName != null ">  
	        	node_name = #{nodeName,jdbcType=VARCHAR},  
	        </if>  
	        <if test="nodeType != null ">  
	        	node_type = #{nodeType,jdbcType=VARCHAR},  
	        </if>  
	        <if test="nodeValue != null ">  
	        	node_value = #{nodeValue,jdbcType=VARCHAR},  
	        </if>  
	        <if test="version != null ">  
	        	version = #{version,jdbcType=VARCHAR},  
	        </if>  
	        <if test="executeId != null ">  
	        	execute_id = #{executeId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="executeTime != null ">  
	        	execute_time = #{executeTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="finishTime != null ">  
	        	finish_time = #{finishTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="executeStatus != null ">  
	        	execute_status = #{executeStatus,jdbcType=VARCHAR},  
	        </if>  
	        <if test="tenantId != null ">  
	        	tenant_id = #{tenantId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="failMessage != null ">  
	        	fail_message = #{failMessage,jdbcType=VARCHAR},  
	        </if>  
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.algo.strategyBatchNode.entity.AlgoStrategyBatchNode">
		update t_algo_strategy_batch_node
		set
           biz_code = #{bizCode,jdbcType=VARCHAR},
           batch_id = #{batchId,jdbcType=INTEGER},
           node_name = #{nodeName,jdbcType=VARCHAR},
           node_type = #{nodeType,jdbcType=VARCHAR},
           node_value = #{nodeValue,jdbcType=VARCHAR},
           version = #{version,jdbcType=VARCHAR},
           execute_id = #{executeId,jdbcType=VARCHAR},
           execute_time = #{executeTime,jdbcType=TIMESTAMP},
           finish_time = #{finishTime,jdbcType=TIMESTAMP},
           execute_status = #{executeStatus,jdbcType=VARCHAR},
           tenant_id = #{tenantId,jdbcType=VARCHAR},
           fail_message = #{failMessage,jdbcType=VARCHAR}
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_algo_strategy_batch_node(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
	        <choose>
	            <when test="item.bizCode != null">,#{item.bizCode}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.batchId != null">,#{item.batchId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.nodeName != null">,#{item.nodeName}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.nodeType != null">,#{item.nodeType}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.nodeValue != null">,#{item.nodeValue}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.version != null">,#{item.version}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.executeId != null">,#{item.executeId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.executeTime != null">,#{item.executeTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.finishTime != null">,#{item.finishTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.executeStatus != null">,#{item.executeStatus}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.failMessage != null">,#{item.failMessage}</when><otherwise>,default</otherwise>
	        </choose>
		)
		</foreach>
	</insert>

	<!-- 批量插入或更新 -->
	<update id="batchInsertOrUpdate" parameterType="java.util.List">
		insert into t_algo_strategy_batch_node(
			<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
			<choose><when test="item.bizCode != null">,#{item.bizCode}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.batchId != null">,#{item.batchId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.nodeName != null">,#{item.nodeName}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.nodeType != null">,#{item.nodeType}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.nodeValue != null">,#{item.nodeValue}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.version != null">,#{item.version}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.executeId != null">,#{item.executeId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.executeTime != null">,#{item.executeTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.finishTime != null">,#{item.finishTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.executeStatus != null">,#{item.executeStatus}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.failMessage != null">,#{item.failMessage}</when><otherwise>,default</otherwise></choose>
		)
		</foreach>
		on duplicate key update 
		   biz_code=values(biz_code), 
		   batch_id=values(batch_id), 
		   node_name=values(node_name), 
		   node_type=values(node_type), 
		   node_value=values(node_value), 
		   version=values(version), 
		   execute_id=values(execute_id), 
		   execute_time=values(execute_time), 
		   finish_time=values(finish_time), 
		   execute_status=values(execute_status), 
		   tenant_id=values(tenant_id), 
		   fail_message=values(fail_message)
	</update>

	<!-- 自定义查询 -->
	<!-- 更新所有字段 -->
	<update id="updateStatusByBatchId" parameterType="com.kbao.algo.strategyBatchNode.entity.AlgoStrategyBatchNode">
		update t_algo_strategy_batch_node
		<set>
			finish_time = #{finishTime,jdbcType=TIMESTAMP},
			execute_status = #{executeStatus,jdbcType=VARCHAR},
			fail_message = #{failMessage,jdbcType=VARCHAR}
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>

	<select id="getAlgoStrategyBatchNodes" resultType="com.kbao.algo.strategyBatchNode.bean.AlgoStrategyBatchNodeVo">
		select n.id batchNodeId, n.biz_code bizCode, n.execute_status executeStatus,
		n.node_type nodeType,n.node_name nodeName,n.node_value nodeValue,n.version version,
		n.execute_time executeTime,n.finish_time finishTime, n.fail_message failMessage
		from t_algo_strategy_batch_node n
		where n.tenant_id = #{tenantId}
		<if test="batchId != null and batchId != ''">
			and n.batch_id = #{batchId}
		</if>
		<if test="bizCode != null and bizCode != ''">
			and n.biz_code = #{bizCode}
		</if>
		<if test="nodeType != null and nodeType != ''">
			and n.node_type = #{nodeType}
		</if>
		<if test="nodeValue != null and nodeValue != ''">
			and n.node_value = #{nodeValue}
		</if>
	</select>
	<select id="selectByTypeAndValue" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from t_algo_strategy_batch_node
		where node_type = #{nodeType,jdbcType=VARCHAR} AND node_value = #{nodeValue,jdbcType=VARCHAR} AND batch_id = #{batchId,jdbcType=INTEGER}
	</select>
	<select id="getAlgoStrategyBatchNodeByCode" resultType="com.kbao.algo.strategyBatchNode.bean.AlgoStrategyBatchNodeRespDTO">
		select n.biz_code bizCode, n.node_type nodeType, n.node_name nodeName, n.execute_status executeStatus, n.version version
		from t_algo_strategy_batch_node n
		join t_algo_strategy_batch sb on n.batch_id = sb.id
		where sb.biz_code = #{batchCode}
		<choose>
			<when test="isSource == true">
				AND n.node_type = '1'
			</when>
			<otherwise>
				AND n.node_type != '1'
			</otherwise>
		</choose>
	</select>
	<select id="getBatchNodeDetail" resultType="com.kbao.algo.strategyBatchNode.bean.AlgoStrategyBatchNodeVo">
		select n.biz_code bizCode,(case when n.execute_status = '0' then '未执行' when n.execute_status = '1' then '执行中' when n.execute_status = '2' then '执行完成' when n.execute_status = '3' then '执行失败' end) executeStatus,
			   n.node_type nodeType,n.node_name nodeName,n.node_value nodeValue,n.version version,n.batch_id batchId,n.execute_time executeTime,n.finish_time finishTime
		from t_algo_strategy_batch_node n
		where n.biz_code = #{bizCode}
	</select>

	<update id="updateExecuteByBatchId" parameterType="com.kbao.algo.strategyBatchNode.entity.AlgoStrategyBatchNode">
		update t_algo_strategy_batch_node
		<set>
			execute_time = #{executeTime,jdbcType=TIMESTAMP},
			execute_status = #{executeStatus,jdbcType=VARCHAR}
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>

	<select id="getExecuteBatchNode" resultType="com.kbao.algo.strategyBatchNode.entity.AlgoStrategyBatchNode">
		select * from t_algo_strategy_batch_node
		where batch_id = #{batchId,jdbcType=INTEGER} and node_value = #{nodeValue,jdbcType=VARCHAR}
	</select>

	<select id="getByDataSpaceId" resultMap="BaseResultMap">
		select distinct node_type, node_value from t_algo_strategy_batch_node
		where batch_id in (select id from t_algo_strategy_batch where data_space_id = #{dataSpaceId})
	</select>

	<select id="getNodeByDataSpaceId" resultMap="BaseResultMap">
		select batch_id, node_type, node_value from t_algo_strategy_batch_node
		where batch_id in (select id from t_algo_strategy_batch where data_space_id = #{dataSpaceId})
	</select>

	<delete id="delByDataSpaceId">
		delete from t_algo_strategy_batch_node
		where batch_id in (select id from t_algo_strategy_batch where data_space_id = #{dataSpaceId})
	</delete>

	<delete id="delIndicatorNodeByBatchIds">
		delete from t_algo_strategy_batch_node
		where node_type in (2,3) and batch_id in
		<foreach collection="batchIds" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<select id="getStrategyBatchNodeById" resultType="com.kbao.algo.strategyBatchNode.bean.AlgoStrategyBatchNodeDTO">
		select b.data_space_id dataSpaceId, b.id strategyBatchId, bn.node_value nodeValue, iv.id versionId
		from t_algo_strategy_batch_node bn
		left join t_algo_strategy_batch b on b.id = bn.batch_id
		left join t_algo_indicator_version iv on bn.version = iv.version
		where bn.id = #{batchNodeId}
	</select>

</mapper>
