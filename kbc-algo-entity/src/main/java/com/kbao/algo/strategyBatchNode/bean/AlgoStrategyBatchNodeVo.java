package com.kbao.algo.strategyBatchNode.bean;

import com.kbao.algo.strategyBatchParam.bean.AlgoStrategyBatchParamVo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 策略批次节点综合参数
 * @author: xia<PERSON><PERSON><PERSON><PERSON>
 * @time: 2024/11/4 9:03
 */
@Data
public class AlgoStrategyBatchNodeVo {
    private Integer batchNodeId;

    private Integer batchId;

    private String bizCode;

    private String nodeType;

    private String nodeName;

    private String nodeValue;

    private String version;

    private String executeStatus;

    private Date executeTime;

    private Date finishTime;

    private String failMessage;

    private List<String> params;
}
