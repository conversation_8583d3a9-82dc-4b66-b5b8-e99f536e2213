package com.kbao.algo.strategyBatchNode.entity;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* @Description 算法规则-策略批次执行节点实体
* @Date 2024-11-13
*/
@Data
public class AlgoStrategyBatchNode implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 主键
     */  
	private Integer id;

    /**
     * 对应表中biz_code
     * 编码
     */  
	private String bizCode;

    /**
     * 对应表中batch_id
     * 批次ID
     */  
	private Integer batchId;

    /**
     * 对应表中node_name
     * 节点名称
     */  
	private String nodeName;

    /**
     * 对应表中node_type
     * 节点类型 1-数据源，2-计算指标，3-结果指标
     */  
	private String nodeType;

    /**
     * 对应表中node_value
     * 节点值 对应节点code
     */  
	private String nodeValue;

    /**
     * 对应表中version
     * 指标版本号
     */  
	private String version;

    /**
     * 对应表中execute_id
     * 创建人
     */  
	private String executeId;

    /**
     * 对应表中execute_time
     * 执行时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date executeTime;

    /**
     * 对应表中finish_time
     * 完成时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date finishTime;

    /**
     * 对应表中execute_status
     * 执行状态 1-执行中 2-执行完成 3-执行失败
     */  
	private String executeStatus;

    /**
     * 对应表中tenant_id
     * 租户ID
     */  
	private String tenantId;

    /**
     * 对应表中fail_message
     * 失败原因
     */  
	private String failMessage;

}   