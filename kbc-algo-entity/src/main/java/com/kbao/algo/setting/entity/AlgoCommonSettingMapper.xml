<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.algo.setting.dao.AlgoCommonSettingMapper">
    
    <resultMap id="BaseResultMap" type="com.kbao.algo.setting.entity.AlgoCommonSetting">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="value" property="value"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, code, value, create_by, create_time, update_by, update_time, tenant_id
    </sql>

    <select id="getByCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_algo_common_setting
        where code = #{code}
        limit 1
    </select>

    <insert id="insert" parameterType="com.kbao.algo.setting.entity.AlgoCommonSetting" useGeneratedKeys="true" keyProperty="id">
        insert into t_algo_common_setting
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">code,</if>
            <if test="value != null">value,</if>
            create_by, create_time, update_by, update_time,
            <if test="tenantId != null">tenant_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">#{code},</if>
            <if test="value != null">#{value},</if>
            #{createBy}, now(), #{createBy}, now(),
            <if test="tenantId != null">#{tenantId},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.algo.setting.entity.AlgoCommonSetting">
        update t_algo_common_setting
        <set>
            <if test="value != null">value = #{value},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </set>
        where code = #{code}
    </update>
</mapper> 