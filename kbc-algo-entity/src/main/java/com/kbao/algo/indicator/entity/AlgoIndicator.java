package com.kbao.algo.indicator.entity;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR> jie
* @Description 实体
* @Date 2024-10-25
*/
@Data
public class AlgoIndicator implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中indicator_id
     * 主键
     */  
	private Integer indicatorId;

    /**
     * 对应表中name
     * 名称
     */  
	private String name;

    /**
     * 对应表中biz_code
     * 编码
     */  
	private String bizCode;

    /**
     * 对应表中indicator_type
     * 指标类型 1-计算指标 2-结果指标 3-空间指标
     */  
	private String indicatorType;

    /**
     * 对应表中version
     * 指标版本号
     */  
	private String version;

    /**
     * 对应表中indicator_sort
     * 指标排序
     */  
	private Integer indicatorSort;

    /**
     * 对应表中remark
     * 名称
     */  
	private String remark;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

    /**
     * 对应表中create_id
     * 创建人
     */  
	private String createId;

    /**
     * 对应表中update_time
     * 更新时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

    /**
     * 对应表中update_id
     * 更新人
     */  
	private String updateId;

    /**
     * 对应表中is_deleted
     * 是否删除 0-未删除 1-已删除
     */  
	private Integer isDeleted;

    /**
     * 对应表中tenant_id
     * 租户ID
     */  
	private String tenantId;

	private JSONObject content;

	private List<JSONObject> nodeList;
}   