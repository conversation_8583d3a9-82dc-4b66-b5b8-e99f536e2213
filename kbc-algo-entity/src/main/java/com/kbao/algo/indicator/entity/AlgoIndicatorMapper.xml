<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.algo.indicator.indicator.dao.AlgoIndicatorMapper">

	<resultMap id="BaseResultMap" type="com.kbao.algo.indicator.entity.AlgoIndicator">
    	<id column="indicator_id" jdbcType="INTEGER"  property="indicatorId"  />
        <result property="name" jdbcType="VARCHAR"  column="name" />  
        <result property="bizCode" jdbcType="VARCHAR"  column="biz_code" />  
        <result property="indicatorType" jdbcType="VARCHAR"  column="indicator_type" />
        <result property="version" jdbcType="VARCHAR"  column="version" />
		<result property="indicatorSort" jdbcType="TINYINT"  column="indicator_sort" />
        <result property="remark" jdbcType="VARCHAR"  column="remark" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="createId" jdbcType="VARCHAR"  column="create_id" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="updateId" jdbcType="VARCHAR"  column="update_id" />  
        <result property="isDeleted" jdbcType="TINYINT"  column="is_deleted" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
	</resultMap>

	<sql id="Base_Column_List">
		indicator_id,  
		name,  
		biz_code,  
		indicator_type,  
		version,
		indicator_sort,  
		remark,  
		create_time,  
		create_id,  
		update_time,  
		update_id,  
		is_deleted,  
		tenant_id  
	</sql>

	<sql id="Alias_Column_List">
		t.indicator_id, 
		t.name, 
		t.biz_code, 
		t.indicator_type, 
		t.version,
		t.indicator_sort, 
		t.remark, 
		t.create_time, 
		t.create_id, 
		t.update_time, 
		t.update_id, 
		t.is_deleted, 
		t.tenant_id 
	</sql>

	<sql id="Base_Condition">
		<where>
			t.is_deleted = 0
	    <if test="name != null and name != ''">
	   		and instr(t.name, #{name,jdbcType=VARCHAR}) > 0
	    </if>
	    <if test="bizCode != null and bizCode != ''">
	   		and t.biz_code = #{bizCode,jdbcType=VARCHAR}
	    </if>
	    <if test="indicatorType != null and indicatorType != ''">
	   		and t.indicator_type = #{indicatorType,jdbcType=VARCHAR}
	    </if>
	    <if test="indicatorSort != null">
	   		and t.indicator_sort = #{indicatorSort,jdbcType=TINYINT}
	    </if>
	    <if test="remark != null and remark != ''">
	   		and t.remark = #{remark,jdbcType=VARCHAR}
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="createId != null and createId != ''">
	   		and t.create_id = #{createId,jdbcType=VARCHAR}
	    </if>
	    <if test="updateTime != null">
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="updateId != null and updateId != ''">
	   		and t.update_id = #{updateId,jdbcType=VARCHAR}
	    </if>
	    <if test="tenantId != null and tenantId != ''">
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_algo_indicator t
		<include refid="Base_Condition" />
		order by t.update_time desc
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_algo_indicator
		where  indicator_id = #{indicatorId,jdbcType=INTEGER}
	</select>


	<!-- 根据主键删除 -->
	<update id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		update t_algo_indicator set is_deleted = 1
		where indicator_id = #{indicatorId,jdbcType=INTEGER} and is_deleted = 0
	</update>


	<!-- 插入所有字段 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="indicatorId">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="indicatorId">
		SELECT LAST_INSERT_ID() AS indicatorId
		</selectKey>  -->
		insert into t_algo_indicator(
			<include refid="Base_Column_List" />
		)
		values(  
                #{indicatorId,jdbcType=INTEGER}, 
                #{name,jdbcType=VARCHAR},
                #{bizCode,jdbcType=VARCHAR},
                #{indicatorType,jdbcType=VARCHAR},
                #{version,jdbcType=VARCHAR},
                #{indicatorSort,jdbcType=TINYINT},
                #{remark,jdbcType=VARCHAR},
                now(),
                #{createId,jdbcType=VARCHAR},
                now(),
				#{createId,jdbcType=VARCHAR},
                0,
                #{tenantId,jdbcType=VARCHAR}
               )
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.algo.indicator.entity.AlgoIndicator">
		update t_algo_indicator
		<set>
	        <if test="name != null ">  
	        	name = #{name,jdbcType=VARCHAR},  
	        </if>
			<if test="bizCode != null ">
				biz_code = #{bizCode,jdbcType=VARCHAR},
			</if>
			<if test="indicatorType != null ">
	        	indicator_type = #{indicatorType,jdbcType=VARCHAR},  
	        </if>  
			<if test="version != null ">
	        	version = #{version,jdbcType=VARCHAR},  
	        </if>  
	        <if test="indicatorSort != null ">  
	        	indicator_sort = #{indicatorSort,jdbcType=TINYINT},  
	        </if>  
	        <if test="remark != null ">  
	        	remark = #{remark,jdbcType=VARCHAR},  
	        </if>
	        <if test="updateId != null ">  
	        	update_id = #{updateId,jdbcType=VARCHAR},  
	        </if>  
			update_time = now()
		</set>
		where indicator_id = #{indicatorId,jdbcType=INTEGER}
	</update>

	<!-- 自定义查询 -->
	<select id="getNodeList" resultType="com.kbao.algo.indicator.bean.AlgoNodeResVo">
		select * from (
		  select biz_code bizCode, name, '1' nodeType, create_time createTime
		  from t_algo_source where is_deleted = 0 and tenant_id = #{tenantId}
			union all
		  select biz_code bizCode, name,
			(case when indicator_type = '1' then '2' else '3' end) nodeType,
			create_time createTime
		  from t_algo_indicator where is_deleted = 0 and tenant_id = #{tenantId}
		) t order by nodeType, createTime desc
	</select>

	<select id="getByCode" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from t_algo_indicator
		where  is_deleted = 0 and biz_code = #{bizCode,jdbcType=VARCHAR}
	</select>

	<select id="isExistIndicator" resultType="int">
		select count(1)
		from t_algo_indicator
		where is_deleted = 0 and biz_code = #{bizCode,jdbcType=VARCHAR}
		<if test="indicatorId != null">
			and indicator_id != #{indicatorId,jdbcType=INTEGER}
		</if>
	</select>

	<update id="updateRemark">
		update t_algo_indicator set
		    remark = #{remark},
		    version = #{version},
		    update_time = now(),
		    update_id = #{updateId}
		where indicator_id = #{indicatorId}
	</update>

	<select id="isExistIndicatorData" resultType="int">
		select count(1) from ${tableName} where strategyBatchId = #{strategyBatchId}
	</select>

	<delete id="delIndicatorDataByBatchId">
		delete from ${tableName} where strategyBatchId = #{strategyBatchId};
	</delete>

	<delete id="dropProcedure">
		drop procedure if exists ${procedureName};
	</delete>

	<insert id="createProcedure">
		${content}
	</insert>

	<select id="callProcedure">
		call ${procedureName}();
	</select>

	<select id="getIndicatorData" resultType="java.util.Map">
		select * from (${selectSql}) t
		<where>
			<if test="searchParamList != null">
				<foreach collection="searchParamList" item="param">
					<if test='param.fieldName != null and param.fieldName != ""'>
						and ${param.fieldName} ${param.operator} #{param.value}
					</if>
				</foreach>
			</if>
		</where>
	</select>
</mapper>
