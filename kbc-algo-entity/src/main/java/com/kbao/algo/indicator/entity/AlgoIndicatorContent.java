package com.kbao.algo.indicator.entity;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

@Data
@Document(value = "AlgoIndicatorContent")
@AllArgsConstructor
public class AlgoIndicatorContent {
    @Id
    private Integer indicatorId;
    private JSONObject content;
    private Date createTime;
}
