package com.kbao.algo.indicator.indicatorField.entity;

import com.kbao.algo.indicator.indicatorField.bean.DataField;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Data
@Document(value = "AlgoIndicatorDataField")
@AllArgsConstructor
public class AlgoIndicatorDataField {
    @Indexed(background = true)
    private Integer dataSpaceId;
    @Indexed(background = true)
    private String indicatorCode;
    private List<DataField> fields;
}
