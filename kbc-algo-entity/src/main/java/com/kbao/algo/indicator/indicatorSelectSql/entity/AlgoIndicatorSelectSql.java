package com.kbao.algo.indicator.indicatorSelectSql.entity;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

@Data
@Document(value = "AlgoIndicatorSelectSql")
@AllArgsConstructor
public class AlgoIndicatorSelectSql {
    private Integer strategyBatchId;
    private String indicatorBizCode;
    private String selectSql;
    private Date createTime;
}
