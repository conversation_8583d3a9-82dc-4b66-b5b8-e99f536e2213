<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.algo.indicator.indicatorVersion.dao.AlgoIndicatorVersionMapper">

	<resultMap id="BaseResultMap" type="com.kbao.algo.indicator.indicatorVersion.entity.AlgoIndicatorVersion">
    	<id column="id" jdbcType="INTEGER"  property="id"  />
        <result property="indicatorId" jdbcType="INTEGER"  column="indicator_id" />  
        <result property="version" jdbcType="VARCHAR"  column="version" />  
        <result property="type" jdbcType="CHAR"  column="type" />  
        <result property="remark" jdbcType="VARCHAR"  column="remark" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="createBy" jdbcType="VARCHAR"  column="create_by" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="updateBy" jdbcType="VARCHAR"  column="update_by" />  
        <result property="isDelete" jdbcType="CHAR"  column="is_delete" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		indicator_id,  
		version,  
		type,  
		remark,  
		create_time,  
		create_by,  
		update_time,  
		update_by,  
		is_delete,  
		tenant_id  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.indicator_id, 
		t.version, 
		t.type, 
		t.remark, 
		t.create_time, 
		t.create_by, 
		t.update_time, 
		t.update_by, 
		t.is_delete, 
		t.tenant_id 
	</sql>

	<sql id="Base_Condition">
		<where>
			t.is_delete = '0'
	    <if test="indicatorId != null">
	   		and t.indicator_id = #{indicatorId,jdbcType=INTEGER}
	    </if>
	    <if test="version != null and version != ''">
	   		and t.version = #{version,jdbcType=VARCHAR}
	    </if>
	    <if test="type != null">
	   		and t.type = #{type,jdbcType=CHAR}
	    </if>
	    <if test="remark != null and remark != ''">
	   		and t.remark = #{remark,jdbcType=VARCHAR}
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="createBy != null and createBy != ''">
	   		and t.create_by = #{createBy,jdbcType=VARCHAR}
	    </if>
	    <if test="updateTime != null">
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="updateBy != null and updateBy != ''">
	   		and t.update_by = #{updateBy,jdbcType=VARCHAR}
	    </if>
	    <if test="tenantId != null and tenantId != ''">
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_algo_indicator_version t
		<include refid="Base_Condition" />
		order by t.type, t.create_time desc
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_algo_indicator_version
		where  id = #{id,jdbcType=INTEGER}
	</select>


	<!-- 根据主键删除 -->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		delete from t_algo_indicator_version
		where id = #{id,jdbcType=INTEGER}
	</delete>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert"  useGeneratedKeys="true" keyProperty="id">
		insert into t_algo_indicator_version(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=INTEGER}, 
                 
                #{indicatorId,jdbcType=INTEGER}, 
                 
                #{version,jdbcType=VARCHAR}, 
                 
                #{type,jdbcType=CHAR}, 
                 
                #{remark,jdbcType=VARCHAR}, 
                 
                now(),
                 
                #{createBy,jdbcType=VARCHAR}, 
                 
                now(),

				#{createBy,jdbcType=VARCHAR},
                 
                '0',
                 
                #{tenantId,jdbcType=VARCHAR} 
               )
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.algo.indicator.indicatorVersion.entity.AlgoIndicatorVersion">
		update t_algo_indicator_version
		<set>
	        <if test="indicatorId != null ">  
	        	indicator_id = #{indicatorId,jdbcType=INTEGER},  
	        </if>  
	        <if test="version != null ">  
	        	version = #{version,jdbcType=VARCHAR},  
	        </if>  
	        <if test="type != null ">  
	        	type = #{type,jdbcType=CHAR},  
	        </if>  
	        <if test="remark != null ">  
	        	remark = #{remark,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="createBy != null ">  
	        	create_by = #{createBy,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateTime != null ">  
	        	update_time = #{updateTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="updateBy != null ">  
	        	update_by = #{updateBy,jdbcType=VARCHAR},  
	        </if>  
	        <if test="isDelete != null ">  
	        	is_delete = #{isDelete,jdbcType=CHAR},  
	        </if>  
	        <if test="tenantId != null ">  
	        	tenant_id = #{tenantId,jdbcType=VARCHAR},  
	        </if>  
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>


	<!-- 自定义查询 -->
	<update id="toHistoryVersion">
		update t_algo_indicator_version set type = '3'
		where indicator_id = #{indicatorId,jdbcType=INTEGER} and type = '1'
	</update>

	<update id="delIndicatorDraft">
		update t_algo_indicator_version set is_delete = '1'
		where indicator_id = #{indicatorId,jdbcType=INTEGER} and type = '2'
	</update>

	<update id="updateRemark">
		update t_algo_indicator_version
		set remark = #{remark,jdbcType=VARCHAR},update_time = now(),
		update_by = #{updateBy,jdbcType=VARCHAR}
		where id = #{id,jdbcType=INTEGER}
	</update>

	<update id="delIndicatorVersion">
		update t_algo_indicator_version
		set is_delete = '1', update_time = now(), update_by = #{updateBy,jdbcType=VARCHAR}
		where id = #{id,jdbcType=INTEGER}
	</update>

	<select id="getByVersion" resultMap="BaseResultMap">
		select * from t_algo_indicator_version v
		         join t_algo_indicator i on v.indicator_id = i.indicator_id
		         where v.version = #{version,jdbcType=VARCHAR} and i.biz_code = #{indicatorCode,jdbcType=VARCHAR}
	</select>
</mapper>
