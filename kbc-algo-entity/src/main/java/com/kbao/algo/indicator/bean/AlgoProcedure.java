package com.kbao.algo.indicator.bean;

import com.kbao.algo.sourceField.bean.AlgoParamResVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlgoProcedure {
    private Integer dataSpaceId;
    private Integer strategyBatchId;
    private String indicatorBizCode;
    private Integer tableAliasIndex;
    private Integer tempTableIndex;
    private List<String> contents;
    private List<String> tempTables;
    private Map<String, AlgoParamResVo> params;

    public static AlgoProcedure getInstance(Integer dataSpaceId, Integer strategyBatchId, String indicatorBizCode, Map<String, AlgoParamResVo> paramMap) {
        return AlgoProcedure.builder().dataSpaceId(dataSpaceId).strategyBatchId(strategyBatchId).indicatorBizCode(indicatorBizCode)
                .tableAliasIndex(0).tempTableIndex(0)
                .contents(new ArrayList<>()).tempTables(new ArrayList<>())
                .params(paramMap).build();
    }
}
