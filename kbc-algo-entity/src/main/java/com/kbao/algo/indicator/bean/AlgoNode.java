package com.kbao.algo.indicator.bean;

import lombok.Data;

import java.util.Set;

@Data
public class AlgoNode {
    // 节点id
    private String nodeId;
    // 节点输出字段
    private Set<String> fields;
    // 节点对应表别名
    private String tableName;
    // 是否包含自定义字段
    private boolean hasFormula = false;
    // 输出字段sql
    private String outputSql;
    // 节点sql-存储过程使用
    private String nodeSql;
    // 查询sql-保存查询日志使用
    private String selectSql;
    private Set<String> indexFields;

}
