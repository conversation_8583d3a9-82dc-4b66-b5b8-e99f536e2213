package com.kbao.algo.indicator.bean;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlgoIndicatorConfigVo {
    private Integer indicatorId;
    @JsonInclude
    private List<JSONObject> nodeList;
    private JSONObject content;
    private String remark;
}
