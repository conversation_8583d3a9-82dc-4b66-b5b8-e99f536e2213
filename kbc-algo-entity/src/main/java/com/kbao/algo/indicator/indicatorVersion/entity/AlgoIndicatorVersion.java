package com.kbao.algo.indicator.indicatorVersion.entity;
import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

/**
* <AUTHOR> jie
* @Description 实体
* @Date 2025-05-30
*/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlgoIndicatorVersion implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 主键
     */  
	private Integer id;

    /**
     * 对应表中indicator_id
     * 指标ID
     */  
	private Integer indicatorId;

    /**
     * 对应表中version
     * 版本号
     */  
	private String version;

    /**
     * 对应表中type
     * 类型：1-当前应用，2-草稿，3-历史版本
     */  
	private String type;

    /**
     * 对应表中remark
     * 备注
     */  
	private String remark;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

	private String createBy;

	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private String updateTime;

	private String updateBy;

    /**
     * 对应表中is_delete
     * 是否删除，0-否，1-是
     */  
	private String isDelete;

    /**
     * 对应表中tenant_id
     * 租户ID
     */  
	private String tenantId;

}   