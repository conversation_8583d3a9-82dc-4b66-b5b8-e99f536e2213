package com.kbao.algo.indicator.indicatorVersion.entity;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

@Data
@Document(value = "AlgoIndicatorVersionContent")
@AllArgsConstructor
public class AlgoIndicatorVersionContent {
    @Id
    private Integer versionId;
    private JSONObject content;
    private Date createTime;
}
