<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.algo.indicator.indicatorField.dao.AlgoIndicatorFieldMapper">

	<resultMap id="BaseResultMap" type="com.kbao.algo.indicator.indicatorField.entity.AlgoIndicatorField">
    	<id column="field_id" jdbcType="INTEGER"  property="fieldId"  />
        <result property="indicatorId" jdbcType="INTEGER"  column="indicator_id" />  
        <result property="name" jdbcType="VARCHAR"  column="name" />  
        <result property="fieldName" jdbcType="VARCHAR"  column="field_name" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />
        <result property="createId" jdbcType="VARCHAR"  column="create_id" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="updateId" jdbcType="VARCHAR"  column="update_id" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
	</resultMap>

	<sql id="Base_Column_List">
		field_id,  
		indicator_id,  
		name,  
		field_name,  
		create_time,
		create_id,  
		update_time,  
		update_id,  
		tenant_id  
	</sql>

	<sql id="Alias_Column_List">
		t.field_id, 
		t.indicator_id, 
		t.name, 
		t.field_name, 
		t.create_time,
		t.create_id, 
		t.update_time, 
		t.update_id, 
		t.tenant_id 
	</sql>

	<sql id="Base_Condition">
		<where>
	    <if test="indicatorId != null">
	   		and t.indicator_id = #{indicatorId,jdbcType=INTEGER}
	    </if>
	    <if test="name != null and name != ''">
	   		and t.name = #{name,jdbcType=VARCHAR}
	    </if>
	    <if test="fieldName != null and fieldName != ''">
	   		and t.field_name = #{fieldName,jdbcType=VARCHAR}
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="createId != null and createId != ''">
	   		and t.create_id = #{createId,jdbcType=VARCHAR}
	    </if>
	    <if test="updateTime != null">
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="updateId != null and updateId != ''">
	   		and t.update_id = #{updateId,jdbcType=VARCHAR}
	    </if>
	    <if test="tenantId != null and tenantId != ''">
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_algo_indicator_field t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_algo_indicator_field
		where  field_id = #{fieldId,jdbcType=INTEGER}
	</select>

	<!-- 根据主键删除 -->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		delete from t_algo_indicator_field
		where field_id = #{fieldId,jdbcType=INTEGER}
	</delete>
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.algo.indicator.indicatorField.entity.AlgoIndicatorField">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="fieldId">
		SELECT LAST_INSERT_ID() AS fieldId
		</selectKey>  -->
		insert into t_algo_indicator_field(
			<include refid="Base_Column_List" />
		)
		values(  
                #{fieldId,jdbcType=INTEGER}, 
                 
                #{indicatorId,jdbcType=INTEGER}, 
                 
                #{name,jdbcType=VARCHAR}, 
                 
                #{fieldName,jdbcType=VARCHAR}, 
                 
                now(),
                 
                #{createId,jdbcType=VARCHAR},

				now(),

				#{createId,jdbcType=VARCHAR},
                 
                #{tenantId,jdbcType=VARCHAR} 
               )
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.algo.indicator.indicatorField.entity.AlgoIndicatorField">
		update t_algo_indicator_field
		<set>
	        <if test="indicatorId != null ">  
	        	indicator_id = #{indicatorId,jdbcType=INTEGER},  
	        </if>  
	        <if test="name != null ">  
	        	name = #{name,jdbcType=VARCHAR},  
	        </if>  
	        <if test="fieldName != null ">  
	        	field_name = #{fieldName,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createTime != null ">
	        	create_time = #{createTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="createId != null ">  
	        	create_id = #{createId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateTime != null ">  
	        	update_time = #{updateTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="updateId != null ">  
	        	update_id = #{updateId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="tenantId != null ">  
	        	tenant_id = #{tenantId,jdbcType=VARCHAR},  
	        </if>  
		</set>
		where field_id = #{fieldId,jdbcType=INTEGER}
	</update>

	<!-- 自定义查询 -->
	<delete id="delIndicatorField">
		delete from t_algo_indicator_field
		       where indicator_id = #{indicatorId,jdbcType=INTEGER}
	</delete>

	<select id="getIndicatorFields" resultType="com.kbao.algo.sourceField.bean.AlgoParamResVo">
		select f.field_name fieldName, f.name fieldDesc
		from t_algo_indicator_field f
		left join t_algo_indicator i on f.indicator_id = i.indicator_id
		where i.biz_code = #{bizCode,jdbcType=VARCHAR}
		and i.is_deleted = 0
	</select>
</mapper>
