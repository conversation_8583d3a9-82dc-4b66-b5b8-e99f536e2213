package com.kbao.algo.dataSpace.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR> jie
* @Description 实体
* @Date 2024-11-08
*/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlgoDataSpace implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 主键
     */  
	private Integer id;

    /**
     * 对应表中biz_code
     * 编码
     */  
	private String bizCode;

    /**
     * 对应表中name
     * 结算批次名称
     */  
	private String name;

    /**
     * 对应表中remark
     * 备注
     */  
	private String remark;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

    /**
     * 对应表中create_id
     * 创建人
     */  
	private String createId;

    /**
     * 对应表中update_time
     * 更新时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

    /**
     * 对应表中update_id
     * 更新人
     */  
	private String updateId;

        /**
     * 对应表中tenant_id
     * 租户ID
     */  
    private String tenantId;

    /**
     * 对应表中memory_limit
     * 内存限制(G)
     */
    private Double memoryLimit;
}      