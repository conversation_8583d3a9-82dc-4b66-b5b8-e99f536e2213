<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.algo.dataSpace.dao.AlgoDataSpaceMapper">

	<resultMap id="BaseResultMap" type="com.kbao.algo.dataSpace.entity.AlgoDataSpace">
    	<id column="id" jdbcType="INTEGER"  property="id"  />
        <result property="bizCode" jdbcType="VARCHAR"  column="biz_code" />  
        <result property="name" jdbcType="VARCHAR"  column="name" />  
        <result property="remark" jdbcType="VARCHAR"  column="remark" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="createId" jdbcType="VARCHAR"  column="create_id" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="updateId" jdbcType="VARCHAR"  column="update_id" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
        <result property="memoryLimit" jdbcType="DOUBLE" column="memory_limit" />
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		biz_code,  
		name,  
		remark,  
		create_time,  
		create_id,  
		update_time,  
		update_id,  
		tenant_id,  
        memory_limit
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.biz_code, 
		t.name, 
		t.remark, 
		t.create_time, 
		t.create_id, 
		t.update_time, 
		t.update_id, 
		t.tenant_id, 
        t.memory_limit
	</sql>

	<sql id="Base_Condition">
		<where>
	    <if test="bizCode != null and bizCode != ''">
	   		and t.biz_code = #{bizCode,jdbcType=VARCHAR}
	    </if>
	    <if test="name != null and name != ''">
	   		and t.name = #{name,jdbcType=VARCHAR}
	    </if>
	    <if test="remark != null and remark != ''">
	   		and t.remark = #{remark,jdbcType=VARCHAR}
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="createId != null and createId != ''">
	   		and t.create_id = #{createId,jdbcType=VARCHAR}
	    </if>
	    <if test="updateTime != null">
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="updateId != null and updateId != ''">
	   		and t.update_id = #{updateId,jdbcType=VARCHAR}
	    </if>
	    <if test="tenantId != null and tenantId != ''">
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_algo_data_space t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_algo_data_space
		where  id = #{id,jdbcType=INTEGER}
	</select>

	<!-- 根据主键删除 -->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		delete from t_algo_data_space
		where id = #{id,jdbcType=INTEGER}
	</delete>
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.algo.dataSpace.entity.AlgoDataSpace">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_algo_data_space(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=INTEGER}, 
                #{bizCode,jdbcType=VARCHAR},
                #{name,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                now(),
                #{createId,jdbcType=VARCHAR},
                now(),
				#{createId,jdbcType=VARCHAR},
                #{tenantId,jdbcType=VARCHAR},
                #{memoryLimit,jdbcType=DOUBLE}
               )
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.algo.dataSpace.entity.AlgoDataSpace">
		update t_algo_data_space
		<set>
	        <if test="name != null ">
	        	name = #{name,jdbcType=VARCHAR},  
	        </if>  
	        <if test="remark != null ">  
	        	remark = #{remark,jdbcType=VARCHAR},  
	        </if>
			<if test="memoryLimit != null ">
				memory_limit = #{memoryLimit,jdbcType=DOUBLE},
			</if>
	        update_id = #{updateId,jdbcType=VARCHAR},
	        update_time = now()
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 自定义查询 -->
	<select id="getByStrategyBatchId" resultMap="BaseResultMap">
		select t.* from t_algo_data_space t
		    join t_algo_strategy_batch y on t.id = y.data_space_id
		where y.id = #{strategyBatchId}
	</select>

	<select id="getByCode" resultMap="BaseResultMap">
		select t.* from t_algo_data_space t
		where t.biz_code = #{bizCode}
	</select>

	<select id="getMemoryUsage" resultType="java.lang.Double">
		select sum(truncate((data_length + index_length)/1024/1024/1024, 4)) from information_schema.tables
		where table_name like concat('t_algo_%_', #{dataSpaceId}) or table_name like concat('t_algo_%_', #{dataSpaceId}, '_storage')
	</select>

	<select id="getDataSpaceList" resultType="com.kbao.algo.dataSpace.bean.DataSpaceListRespVo">
		select * from (
			select ds.id, ds.biz_code dataSpaceCode, ds.name, ds.memory_limit memoryLimit, ds.remark, ds.create_time createTime,
			count(sb.id) as strategyTotal,
			sum(if(sb.execute_status = 0, 1, 0)) as strategyUnRunningNum,
			sum(if(sb.execute_status = 1, 1, 0)) as strategyRunningNum,
			sum(if(sb.execute_status = 2, 1, 0)) as strategyFinishedNum,
			sum(if(sb.execute_status = 3, 1, 0)) as strategyFailedNum,
			(select sum(truncate(data_length/1024/1024/1024, 4)) + sum(truncate(index_length/1024/1024/1024, 4))
			from information_schema.tables
			where (table_name like concat('t_algo_%_', ds.id) or table_name like concat('t_algo_%_', ds.id, '_storage'))) as memoryUsage
			from t_algo_data_space ds left join t_algo_strategy_batch sb on ds.id = sb.data_space_id
			<where>
				<if test="bizCode != null and bizCode != ''">
					and ds.biz_code = #{bizCode}
				</if>
			</where>
			group by ds.id
		) t order by createTime desc
	</select>

	<select id="getDataSpaceOverview" resultType="com.kbao.algo.dataSpace.bean.DataSpaceOverviewVo">
		select * from (
			select bn.node_value dataCode, bn.node_name dataName, bn.node_type dataType, s.name strategyName, count(bn.id) batchNum,
			(select truncate((data_length + index_length)/1024/1024, 4) from information_schema.tables
				where table_name LIKE CONCAT('t_algo_%_', bn.node_value, '_', b.data_space_id)) as memoryUsage,
			(select truncate((data_length + index_length)/1024/1024, 4) from information_schema.tables
				where table_name LIKE CONCAT('t_algo_%_', bn.node_value, '_', b.data_space_id, '_storage')) as storageMemoryUsage
			from t_algo_strategy_batch_node bn
			left join t_algo_strategy_batch b on bn.batch_id = b.id
			left join t_algo_strategy s on b.strategy_id = s.strategy_id
			where b.data_space_id = #{dataSpaceId}
			<if test="dataCode != null and dataCode != ''">
				and bn.node_value = #{dataCode}
			</if>
			<if test="dataType != null and dataType != ''">
				and bn.node_type = #{dataType}
			</if>
			<if test="dataName != null and dataName != ''">
				and bn.node_name like concat('%', #{dataName}, '%')
			</if>
			group by bn.node_value
		) t order by t.dataType, t.dataCode
	</select>
</mapper>
