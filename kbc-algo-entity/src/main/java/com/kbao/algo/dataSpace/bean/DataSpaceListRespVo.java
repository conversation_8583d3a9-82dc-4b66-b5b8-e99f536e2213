package com.kbao.algo.dataSpace.bean;

import lombok.Data;

import java.util.Date;

@Data
public class DataSpaceListRespVo {
    private Integer id;
    private String dataSpaceCode;
    private String name;
    private Double memoryLimit;
    private Integer strategyTotal;
    private Integer strategyUnRunningNum;
    private Integer strategyRunningNum;
    private Integer strategyFinishedNum;
    private Integer strategyFailedNum;
    private Double memoryUsage;
    private String remark;
    private Date createTime;
}
