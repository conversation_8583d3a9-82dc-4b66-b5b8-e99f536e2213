CREATE PROCEDURE ${procName}()
BEGIN
    -- 第1部分：所有DECLARE语句（必须放在最开头，遵循MySQL语法）
    DECLARE has_error BOOLEAN DEFAULT FALSE;
    DECLARE originalUniqueChecks TINYINT DEFAULT @@session.unique_checks;
    DECLARE originalFKChecks TINYINT DEFAULT @@session.foreign_key_checks;
    DECLARE tableExists INT DEFAULT 0;
    DECLARE lockName VARCHAR(64);
    DECLARE lockAcquired INT DEFAULT 0;
    DECLARE retryCount INT DEFAULT 0;
    DECLARE maxRetries INT DEFAULT 5;

    -- 第2部分：外层异常处理器（核心：先清理，再抛异常）
    DECLARE EXIT HANDLER FOR SQLEXCEPTION, SQLWARNING
    BEGIN
        -- 异常时执行清理逻辑（释放锁+重置会话+删临时表）
        -- 1. 释放锁
        IF lockAcquired = 1 THEN
            DO RELEASE_LOCK(lockName);
            SET lockAcquired = 0;
        END IF;
        -- 2. 重置会话参数（恢复初始设置）
        SET SESSION unique_checks = originalUniqueChecks;
        SET SESSION foreign_key_checks = originalFKChecks;
        -- 3. 删除临时表（Freemarker循环保留，与原逻辑一致）
        <#list tempTables as temp>
            DROP TEMPORARY TABLE IF EXISTS ${temp};
        </#list>
        -- 4. 抛出异常到Java代码（不吞噬异常）
        RESIGNAL;
    END;

    -- 第3部分：主逻辑块（核心业务逻辑）
    BEGIN
        -- 内层异常处理器：标记错误+回滚事务
        DECLARE EXIT HANDLER FOR SQLEXCEPTION, SQLWARNING
        BEGIN
            SET has_error = TRUE;  -- 标记错误状态
            ROLLBACK;              -- 错误时回滚，避免脏数据
        END;

        -- 1. 会话参数初始化（关闭外键/唯一键检查，提升执行效率）
        SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED;
        SET SESSION unique_checks = 0;
        SET SESSION foreign_key_checks = 0;

        -- 2. 创建临时表（保留原Freemarker循环，与业务逻辑一致）
        <#list contents as content>
            ${content};
        </#list>

        -- 3. 锁逻辑（避免并发创建表）
        SET lockName = CONCAT('db_lock_', REPLACE('${indicatorTableName}', '-', '_'), '_create');
        -- 检查表是否已存在
        SELECT COUNT(*) INTO tableExists FROM information_schema.tables
        WHERE table_schema = DATABASE() AND table_name = '${indicatorTableName}';
        -- 重试获取锁（最多5次，每次间隔0.5秒）
        WHILE tableExists = 0 AND lockAcquired = 0 AND retryCount < maxRetries DO
            SET lockAcquired = GET_LOCK(lockName, 0);  -- 0表示不阻塞，立即返回结果
            IF lockAcquired = 0 THEN
                DO SLEEP(0.5);  -- 未获取到锁，等待0.5秒重试
                SET retryCount = retryCount + 1;
            END IF;
        END WHILE;
        -- 再次检查表是否存在（防止重试期间其他线程已创建表）
        SELECT COUNT(*) INTO tableExists FROM information_schema.tables
        WHERE table_schema = DATABASE() AND table_name = '${indicatorTableName}';

        -- 4. 创建目标表（如果不存在）
        IF tableExists = 0 THEN
            -- 用PREPARE避免表名变量直接拼接的语法错误
            PREPARE stmt FROM 'CREATE TABLE ${indicatorTableName} LIKE ${tableName};';
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
            -- 新增必要字段和索引
            ALTER TABLE ${indicatorTableName}
                ADD strategyBatchId INT FIRST,
                ADD createTime DATETIME DEFAULT CURRENT_TIMESTAMP,
                ADD INDEX idx_strategyBatchId(strategyBatchId);
        END IF;

        -- 5. 将临时表数据写入到目标表
        INSERT INTO ${indicatorTableName} (
            strategyBatchId,
            <#list fields as field>
                ${field},
            </#list>
            createTime
        ) SELECT ${strategyBatchId},
            <#list fields as field>
                ${field},
            </#list>
        NOW()
        FROM ${tableName} t;

        -- 6. 正常执行时提交事务
        COMMIT;
    END;

    -- 第4部分：正常流程清理（无异常时执行，与异常清理逻辑一致）
    -- 1. 释放锁
    IF lockAcquired = 1 THEN
        DO RELEASE_LOCK(lockName);
        SET lockAcquired = 0;
    END IF;
    -- 2. 重置会话参数
    SET SESSION unique_checks = originalUniqueChecks;
    SET SESSION foreign_key_checks = originalFKChecks;
    -- 3. 删除临时表
    <#list tempTables as temp>
        DROP TEMPORARY TABLE IF EXISTS ${temp};
    </#list>

    -- 第5部分：兜底异常抛出（极端情况：内层未捕获的错误）
    IF has_error THEN
        SIGNAL SQLSTATE '45000'  -- 自定义SQL状态码（45000表示用户定义错误）
        SET MESSAGE_TEXT = '存储过程执行失败',
            MYSQL_ERRNO = 1001;  -- 自定义错误码，便于Java代码识别
    END IF;
END;