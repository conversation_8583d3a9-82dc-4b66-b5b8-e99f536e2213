CREATE PROCEDURE ${procName}()
BEGIN
    DECLARE tableExists INT DEFAULT 0;

    -- 创建临时表
    <#list contents as content>
        ${content};
    </#list>

    SELECT COUNT(*) INTO tableExists FROM information_schema.tables
    WHERE table_schema = DATABASE() AND table_name = '${indicatorTableName}';

    -- 如果表不存在则创建
    IF tableExists = 0 THEN
    -- 执行创建表的SQL语句
    PREPARE stmt FROM 'CREATE TABLE ${indicatorTableName} LIKE ${tableName};';
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    alter table ${indicatorTableName} add strategyBatchId INT first,
    add createTime DATETIME DEFAULT CURRENT_TIMESTAMP,
    add index idx_strategyBatchId(strategyBatchId);
    END IF;

    insert into ${indicatorTableName} select ${strategyBatchId},t.*, now() from ${tableName} t;

    -- 删除临时视图
    <#list tempTables as temp>
        drop temporary table if exists ${temp};
    </#list>
END;