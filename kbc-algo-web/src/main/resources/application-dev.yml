spring:
  datasource:
    # JDBC 配置(驱动类自动从url的mysql识别,数据源类型自动识别)
    url: *******************************************************************************************************************************************************************
    username: kbc_sct_algo_sta_rw
    password: B9T2pXbYi_4Z5wta
    driver-class-name:  com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    #连接池配置(通常来说，只需要修改initialSize、minIdle、maxActive
    # 配置获取连接等待超时的时间
    druid:
      max-active: 20
      min-idle: 5
      initial-size: 5
      max-wait: 10000

  redis:
    ################### redis 单机版 start ##########################
    host: ***********
    port: 6379
    password: ewOIJ*f7gUT^&63tiu3flk3o
    timeout: 6000
    database: 6
    lettuce:
      pool:
        max-active: 30 # 连接池最大连接数（使用负值表示没有限制）,如果赋值为-1，则表示不限制；如果pool已经分配了maxActive个jedis实例，则此时pool的状态为exhausted(耗尽)
        max-idle: 8   # 连接池中的最大空闲连接 ，默认值也是8
        max-wait: 100 # # 等待可用连接的最大时间，单位毫秒，默认值为-1，表示永不超时。如果超过等待时间，则直接抛出JedisConnectionException
        min-idle: 2    # 连接池中的最小空闲连接 ，默认值也是0
      shutdown-timeout: 100ms
    redisson:
      enable: true

  ################### redis 单机版 end ##########################

  ################## mongodb 配置 #################
  data:
    mongodb:
      uri: mongodb://${spring.data.mongodb.dbusername}:${spring.data.mongodb.dbpassword}@mongo-kbcs-test-lan.kbao123.com:27017/kbc_sct_algo_sta?authsource=kbc_sct_algo_sta
      dbusername: kbc_sct_algo_sta_rw
      dbpassword: ftqr8b5UOCpBiDZA

#  elasticsearch:
#    rest:
#      uris: http://10.78.8.116:9200
  elasticsearch:
    rest:
      uris: http://***********:9200,http://***********:9200,http://***********:9200
      username: <EMAIL>
      password: cWZz4Uvwy2dCqsi7IK3Y1npbAMa8mN5E

################## eureka 注册中心配置 #################
eureka:
  client:
    enabled: true # 是否开启向注册中心进行注册
    serviceUrl:
      defaultZone: https://kbc:<EMAIL>/eureka/
    registry-fetch-interval-seconds: 5
    instance-info-replication-interval-seconds: 10
  instance:
    prefer-ip-address: true
    instance-id: ${spring.application.name}:${spring.cloud.client.ip-address}:${spring.application.instance_id:${server.port}}
    lease-renewal-interval-in-seconds: 10
    lease-expiration-duration-in-seconds: 15  # 续约时间5， 主动下线检测心跳， 服务器默认90秒
    status-page-url: http://${spring.cloud.client.ip-address}:${server.port}/swagger-ui.html

ribbon:
  ServerListRefreshInterval: 5000    #刷新服务列表源的间隔时间
  ReadTimeout: 60000
  ConnectTimeout: 60000

logging:
  level:
    com.kbao: debug
    org.hibernate: info
    org.springframework: info
    org.hibernate.type.descriptor.sql.BasicBinder: trace
    org.hibernate.type.descriptor.sql.BasicExtractor: trace
    org.springframework.data.mongodb.core.MongoTemplate: debug
    com.alibaba.nacos.client: off


##feign参数优化
feign:
  client:
    config:
      default:
        ## 配合logging.level=trace debug用于开发调式日志
        loggerLevel: full


systemLog:
  elasticsearch:
    indexName: logstash-sta-kbcs-bsc-rest-api-system

thread:
  executor:
    algo:
      corePoolSize: 8
      maxPoolSize: 8
      keepAliveSeconds: 1000
      queueCapacity: 1000
      collectDataSemaphore: 5
      calcIndicatorSemaphore: 3

mq:
  enabled: true
  consumeThreadNums: 5
  messageCenterNameSrvAddr: http://onsaddr.cn-shanghai-finance-1.mq-internal.aliyuncs.com:8080
  messageCenterAccessKey: LTAI5tPztUUC6T5ehdY9aWSZ
  messageCenterSecretKey: ******************************
  messageCenterTopic: Topic-STA-KBC-ALGO-MSG
  messageCenterGroupId: GID-STA-KBC-ALGO-MSG
  messageCenterSendMsgTimeoutMillis: 3000
  #算法平台策略批次执行完成后发送MQ广播
  algoBatchSendBroadcastTag: ALGO-BATCH-SEND-BROADCAST-TAG