#端口配置
server:
  port: 7001   #固定端口
#  port: ${randomServerPort.value[7000,7005]}  #随机端口

#服务名称
spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: kbc-sct-algo-web
  cloud:
    nacos:
      config:
        # 配置中心地址
        server-addr: https://config-lan.kbao123.com
        # 账号
        username: kbc-sct-r
        # 密码
        password: oC%mxyAl!Xq3jnsl0O2YShCclZk!iJvN
        # 自动刷新配置
        #refresh-enabled: true
        # 配置文件格式
        file-extension: yml
        # 指定group 默认 DEFAULT_GROUP
        group: group-kbc-sct
        # 指定namespace id 默认public
        namespace: ${spring.profiles.active}
        # 自定义dataId，默认spring.application.name
        prefix: sta-kbcs-app-sct-algo-web-jar

jasypt:
  encryptor:
    # 秘钥
    newpassword: abc!@#123
    # 默认ENC()
    property:
      prefix: ENC(
      suffix: )
