spring:
  profiles:
    active: dev
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  main:
    allow-bean-definition-overriding: true
  # 国际化
  messages:
    basename: static/i18n/index,
      static/i18n/strategymatch,
      static/i18n/strategymatchindicator,
      static/i18n/sourcefield,
      static/i18n/strategyexecute,
      static/i18n/strategycalc
    encoding: UTF-8

  #指定freemarker的模板路径和模板的后缀
  freemarker:
    template-loader-path: classpath:/template/ftl/
    suffix: .ftl
    charset: UTF-8
    cache: false

############## mybatis 配置 ################33
mybatis:
  mapperLocations: classpath*:/com/kbao/algo/**/entity/*Mapper.xml

#开启json压缩
server:
  compression:
    enabled: true
    mime-types: application/json
    min-response-size: 20480

##健康检查
management:
  endpoints:
    web:
      exposure:
        include: "health"
  server:
    port: 7001
    servlet:
      context-path: /
    ssl:
      enabled: false
  endpoint:
    health:
      show-details: always

pagehelper:
  helper-dialect: mysql
  reasonable: false
  support-methods-arguments: true
  params: count=countSql
  offset-as-page-num: false

##feign参数优化
feign:
  client:
    config:
      default:
        # 连接其它项目服务超时时间
        connectTimeout: 5000
        # 读取其它项目服务超时时间
        readTimeout: 10000
jasypt:
  encryptor:
    # 秘钥
    newpassword: abc!@#123
    # 默认ENC()
    #property:
    # prefix: ENC(
    # suffix: )