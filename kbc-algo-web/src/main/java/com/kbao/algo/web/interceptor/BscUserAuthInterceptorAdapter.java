package com.kbao.algo.web.interceptor;

import com.kbao.algo.util.AlgoContext;
import com.kbao.commons.util.TokenUtil;
import com.kbao.kbcbsc.interceptor.UserAuthInterceptorAdapter;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Description BSC用户登录拦截器
 * @Date 2020-06-23
 */
@Component
public class BscUserAuthInterceptorAdapter extends UserAuthInterceptorAdapter {

    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if(!super.preHandle(request, response, handler)){
            return false;
        }
        AlgoContext.setTenantId(SysLoginUtils.getUser().getTenantId());
        return true;
    }

    /**
     * 在执行action里面的逻辑后返回视图之前执行
     */
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                           ModelAndView modelAndView) {
    }

    /**
     * 在action返回视图后执行
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
    }
}
