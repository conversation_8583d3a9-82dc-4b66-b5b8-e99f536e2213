package com.kbao.algo.web.interceptor;

import com.kbao.commons.exception.BusinessException;
import com.kbao.algo.util.AlgoContext;
import com.kbao.tool.util.EmptyUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class WebNoAuthInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String tenantid = request.getHeader("tenantid");
        if (EmptyUtils.isEmpty(tenantid)) {
            tenantid = request.getHeader("tenantId");
        }
        if (EmptyUtils.isEmpty(tenantid)) {
            tenantid = request.getParameter("tenantId");
        }
        if (EmptyUtils.isEmpty(tenantid)) {
            throw new BusinessException("租户ID不能为空");
        } else {
            AlgoContext.setTenantId(tenantid);
            return true;
        }
    }

    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
    }

    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
    }

}
