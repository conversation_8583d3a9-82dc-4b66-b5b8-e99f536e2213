package com.kbao.algo;

import com.kbao.kbcbsc.log.annotation.EnableLogging;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@EnableFeignClients(basePackages = "com.kbao")
@Configuration
@EnableLogging
@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = "com.kbao")
@EnableTransactionManagement
@EnableScheduling
@EnableAsync
@EnableEncryptableProperties
public class KbcAlgoWebApplication {

    public static void main(String[] args) {
        SpringApplication.run(KbcAlgoWebApplication.class, args);
    }

}
