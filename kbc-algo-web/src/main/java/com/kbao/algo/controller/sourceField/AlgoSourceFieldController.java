package com.kbao.algo.controller.sourceField;

import com.kbao.algo.sourceField.bean.AlgoParamResVo;
import com.kbao.algo.sourceField.bean.AlgoSourceFieldReqVo;
import com.kbao.algo.sourceField.entity.AlgoSourceField;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.algo.sourceField.service.AlgoSourceFieldService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;

import java.util.List;

/**
 * <AUTHOR> jie
 * @Description 管理
 * @Date 2024-10-25
*/
@RestController
@RequestMapping("/web/algoSourceField")
public class AlgoSourceFieldController extends BaseController {

	@Autowired
	private AlgoSourceFieldService algoSourceFieldService;

	@PostMapping("/list")
	@LogAnnotation(module = "管理", action = "查询", desc = "查询列表")
	public Result<List<AlgoSourceField>> list(@RequestBody AlgoSourceFieldReqVo reqVo) {
		List<AlgoSourceField> list = algoSourceFieldService.list(reqVo);
		return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/add")
	@LogAnnotation(module = "管理", action = "新增", desc = "新增")
	public Result add(@RequestBody AlgoSourceField algoSourceField){
		algoSourceFieldService.addSourceField(algoSourceField);
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/update")
	@LogAnnotation(module = "管理", action = "修改", desc = "修改")
	public Result update(@RequestBody AlgoSourceField algoSourceField){
		algoSourceFieldService.updateSourceField(algoSourceField);
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/delete")
	@LogAnnotation(module = "管理", action = "删除", desc = "删除")
	public Result delete(@RequestBody AlgoSourceFieldReqVo reqVo) {
		algoSourceFieldService.delSourceField(reqVo.getFieldId());
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/fields")
	@LogAnnotation(module = "管理", action = "查询", desc = "查询列表字段")
	public Result<List<AlgoParamResVo>> getSourceFields(@RequestBody AlgoSourceFieldReqVo reqVo) {
		List<AlgoParamResVo> list = algoSourceFieldService.getSourceFields(reqVo);
		return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/manySourceParams")
	@LogAnnotation(module = "管理", action = "查询", desc = "查询列表字段")
	public Result<List<AlgoParamResVo>> getManySourceParams(@RequestBody AlgoSourceFieldReqVo reqVo) {
		List<AlgoParamResVo> list = algoSourceFieldService.getManySourceParams(reqVo.getSourceCodes());
		return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
	}
}