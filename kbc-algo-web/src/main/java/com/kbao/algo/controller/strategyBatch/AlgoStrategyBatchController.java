package com.kbao.algo.controller.strategyBatch;

import com.github.pagehelper.PageInfo;
import com.kbao.algo.dataSpace.bean.DataSpaceReqVo;
import com.kbao.algo.strategyBatch.bean.AlgoStrategyBatchPageReqVo;
import com.kbao.algo.strategyBatch.bean.AlgoStrategyBatchVo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.algo.strategyBatch.service.AlgoStrategyBatchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 算法规则-策略执行批次管理
 * @Date 2024-10-29
*/
@RestController
@RequestMapping("/web/algoStrategyBatch")
public class AlgoStrategyBatchController extends BaseController {

	@Autowired
	private AlgoStrategyBatchService algoStrategyBatchService;

	@PostMapping("/page")
	@LogAnnotation(module = "算法规则-策略执行批次管理", action = "查询", desc = "分页查询列表")
	public Result<PageInfo<AlgoStrategyBatchVo>> page(@RequestBody RequestObjectPage<AlgoStrategyBatchPageReqVo> page) {
		List<AlgoStrategyBatchVo> list = algoStrategyBatchService.getAlgoStrategyBatch(page);
		return Result.succeed(new PageInfo<>(list), ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/clean")
	@LogAnnotation(module = "算法规则-策略执行批次管理", action = "删除", desc = "清理执行批次数据")
	public Result<PageInfo<AlgoStrategyBatchVo>> cleanIndicatorBatch(@RequestBody DataSpaceReqVo reqVo) {
		algoStrategyBatchService.cleanIndicatorBatch(reqVo.getDataSpaceId());
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}
}