package com.kbao.algo.controller.strategy;

import com.github.pagehelper.PageInfo;
import com.kbao.algo.strategy.bean.AlgoStrategyExecuteRespDTO;
import com.kbao.algo.strategy.bean.AlgoStrategyReqVo;
import com.kbao.algo.strategy.bean.AlgoStrategyVo;
import com.kbao.algo.strategy.entity.AlgoStrategy;
import com.kbao.algo.strategyBatch.entity.AlgoStrategyBatch;
import com.kbao.algo.strategyNode.entity.AlgoStrategyNodeFlow;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.algo.strategy.service.AlgoStrategyService;
import com.kbao.algo.strategyNode.service.AlgoStrategyNodeFlowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description 算法规则-策略管理
 * @Date 2024-10-28
*/
@RestController
@RequestMapping("/web/algoStrategy")
public class AlgoStrategyController extends BaseController {

	@Autowired
	private AlgoStrategyService algoStrategyService;

	@Autowired
	private AlgoStrategyNodeFlowService algoStrategyNodeFlowService;

	@PostMapping("/page")
	@LogAnnotation(module = "算法规则-策略管理", action = "查询", desc = "分页查询列表")
	public Result<PageInfo<AlgoStrategy>> page(@RequestBody RequestObjectPage<AlgoStrategy> page) {
		page.setSort("create_time DESC");
		PageInfo<AlgoStrategy> algoStrategyPage = algoStrategyService.page(page);
		Result<PageInfo<AlgoStrategy>> result = Result.succeed(algoStrategyPage,ResultStatusEnum.SUCCESS.getMsg());
		return result;
	}

	@PostMapping("/add")
	@LogAnnotation(module = "算法规则-策略管理", action = "新增", desc = "新增算法规则-策略")
	public Result add(@RequestBody AlgoStrategyVo algoStrategyAddReq){
		algoStrategyService.insert(algoStrategyAddReq);
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/update")
	@LogAnnotation(module = "算法规则-策略管理", action = "更新", desc = "更新算法规则-策略")
	public Result update(@RequestBody AlgoStrategyVo algoStrategyUpdateReq){
		algoStrategyService.update(algoStrategyUpdateReq);
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/config")
	@LogAnnotation(module = "算法规则-策略管理", action = "配置", desc = "保存算法规则-策略配置")
	public Result config(@RequestBody AlgoStrategyVo algoStrategyUpdateReq){
		algoStrategyService.config(algoStrategyUpdateReq);
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}
	@PostMapping("/find")
	@LogAnnotation(module = "算法规则-策略管理", action = "查询", desc = "查询算法规则-策略")
	public Result<AlgoStrategy> find(@RequestBody AlgoStrategyVo algoStrategyReq){
		AlgoStrategy algoStrategy = algoStrategyService.find(algoStrategyReq.getStrategyId());
		return Result.succeed(algoStrategy,ResultStatusEnum.SUCCESS.getMsg());
	}
	@PostMapping("/config/find")
	@LogAnnotation(module = "算法规则-策略管理", action = "查询", desc = "查询算法规则-策略配置")
	public Result<AlgoStrategyVo> configFind(@RequestBody AlgoStrategyVo algoStrategyReq){
		AlgoStrategyVo algoStrategyVo = algoStrategyService.configFind(algoStrategyReq.getStrategyId());
		return Result.succeed(algoStrategyVo,ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/delete")
	@LogAnnotation(module = "算法规则-策略管理", action = "删除", desc = "删除算法规则-策略")
	public Result delete(@RequestBody AlgoStrategy algoStrategy) {
	    algoStrategyService.deleteById(algoStrategy.getStrategyId());
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/execute")
	@LogAnnotation(module = "算法规则-策略管理", action = "执行", desc = "算法规则-策略执行")
	public Result<AlgoStrategyExecuteRespDTO> execute(@RequestBody AlgoStrategyReqVo algoStrategyReqVo){
		AlgoStrategyExecuteRespDTO res = algoStrategyService.execute(algoStrategyReqVo);
		return Result.succeed(res, ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/retry")
	@LogAnnotation(module = "算法规则-策略管理", action = "重试", desc = "重新手动执行当前策略")
	public Result retryStrategyBatch(@RequestBody AlgoStrategyBatch algoStrategyBatch) {
		algoStrategyService.retryStrategyBatch(algoStrategyBatch.getId());
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/node/flow/config/save")
	@LogAnnotation(module = "算法规则-策略管理", action = "配置", desc = "保存算法规则-流程策略节点配置")
	public Result<AlgoStrategyVo> nodeFlowConfig(@RequestBody AlgoStrategyNodeFlow algoStrategyNodeFlow){
		AlgoStrategyVo algoStrategyVo = algoStrategyNodeFlowService.saveFlow(algoStrategyNodeFlow);
		return Result.succeed(algoStrategyVo, ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/node/flow/config/find")
	@LogAnnotation(module = "算法规则-策略管理", action = "配置", desc = "查询算法规则-流程策略节点配置")
	public Result<AlgoStrategyNodeFlow> nodeFlowConfigFind(@RequestBody AlgoStrategyNodeFlow algoStrategyNodeFlow){
		AlgoStrategyNodeFlow algoStrategyNodeFlowObj = algoStrategyNodeFlowService.nodeFlowConfigFind(algoStrategyNodeFlow.getStrategyId());
		return Result.succeed(algoStrategyNodeFlowObj, ResultStatusEnum.SUCCESS.getMsg());
	}
}