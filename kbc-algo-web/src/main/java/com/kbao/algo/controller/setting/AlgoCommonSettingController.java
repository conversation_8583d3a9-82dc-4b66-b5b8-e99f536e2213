package com.kbao.algo.controller.setting;

import com.kbao.algo.setting.entity.AlgoCommonSetting;
import com.kbao.algo.setting.service.AlgoCommonSettingService;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 通用配置Controller
 */
@RestController
@RequestMapping("/web/algoCommonSetting")
public class AlgoCommonSettingController extends BaseController {

    @Autowired
    private AlgoCommonSettingService algoCommonSettingService;

    /**
     * 获取配置
     */
    @PostMapping("/get")
    public Result<String> get(@RequestBody AlgoCommonSetting setting) {
        AlgoCommonSetting result = algoCommonSettingService.getByCode(setting.getCode());
        return Result.succeed(result.getValue(), ResultStatusEnum.SUCCESS.getMsg());
    }

    /**
     * 保存配置
     */
    @PostMapping("/save")
    public Result<Void> save(@RequestBody AlgoCommonSetting setting) {
        algoCommonSettingService.save(setting);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
} 