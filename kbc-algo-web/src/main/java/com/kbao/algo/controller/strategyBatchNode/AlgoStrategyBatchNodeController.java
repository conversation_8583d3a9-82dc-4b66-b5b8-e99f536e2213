package com.kbao.algo.controller.strategyBatchNode;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import com.kbao.algo.indicator.bean.AlgoIndicatorConfigVo;
import com.kbao.algo.redis.RedisLockUtils;
import com.kbao.algo.source.bean.AlgoDataReqVo;
import com.kbao.algo.strategyBatchNode.bean.AlgoStrategyBatchNodeVo;
import com.kbao.algo.strategyBatchNode.bean.StrategyBatchNodeDataReqVo;
import com.kbao.algo.strategyBatchNode.entity.AlgoStrategyBatchNode;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.algo.source.service.AlgoSourceService;
import com.kbao.algo.strategyBatchNode.service.AlgoStrategyBatchNodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 算法规则-策略执行批次节点管理
 * @Date 2024-10-29
*/
@RestController
@RequestMapping("/web/algoStrategyBatchNode")
public class AlgoStrategyBatchNodeController extends BaseController {

	@Autowired
	private AlgoStrategyBatchNodeService algoStrategyBatchNodeService;

	@Autowired
	private AlgoSourceService algoSourceService;

	@Autowired
	private RedisLockUtils redisLockUtils;

	@PostMapping("/list")
	@LogAnnotation(module = "算法规则-策略执行批次节点管理", action = "查询", desc = "查询列表")
	public Result<List<AlgoStrategyBatchNodeVo>> list(@RequestBody AlgoStrategyBatchNode algoStrategyBatchNode) {
		List<AlgoStrategyBatchNodeVo> list = algoStrategyBatchNodeService.getAlgoStrategyBatchNodes(algoStrategyBatchNode);
		return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/detail/page")
	@LogAnnotation(module = "算法规则-策略执行批次节点管理", action = "查询", desc = "分页查询节点批次明细列表")
	public Result<PageInfo<Map>> getDetailPage(@RequestBody PageRequest<AlgoDataReqVo> reqVo) {
		PageInfo<Map> sourceDataList = algoSourceService.getSourceDataList(reqVo);
		return Result.succeed(sourceDataList, ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/retry")
	@LogAnnotation(module = "算法规则-策略执行批次节点管理", action = "重试", desc = "重新手动执行当前策略执行批次节点")
	public Result retryStrategyBatchNode(@RequestBody AlgoStrategyBatchNode algoStrategyBatchNode) {
		AlgoStrategyBatchNodeVo batchNodeDetail = algoStrategyBatchNodeService.getBatchNodeDetail(algoStrategyBatchNode.getBizCode());
		Boolean lock = redisLockUtils.isLock(batchNodeDetail.getBizCode(), batchNodeDetail.getBizCode(), 3600);
		if (!lock) {
			throw new BusinessException("当前策略批次节点正在重试执行中，请稍后再试");
		}
		algoStrategyBatchNodeService.retryStrategyBatchNode(batchNodeDetail, BscUserUtils.getUser().getUser().getTenantId());
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/indicatorConfig")
	@LogAnnotation(module = "算法规则-策略执行批次节点管理", action = "查询", desc = "查询本次执行节点指标配置")
	public Result<AlgoIndicatorConfigVo> getIndicatorVersionConfig(@RequestBody AlgoStrategyBatchNodeVo reqVo) {
		AlgoIndicatorConfigVo indicatorVersionConfig = algoStrategyBatchNodeService.getIndicatorVersionConfig(reqVo.getBatchNodeId());
		return Result.succeed(indicatorVersionConfig, ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/getIndicatorNodeFields")
	@LogAnnotation(module = "算法规则-策略执行批次节点管理", action = "查询", desc = "查询指标节点输出字段")
	public Result<JSONArray> getIndicatorNodeFields(@RequestBody StrategyBatchNodeDataReqVo reqVo) {
		JSONArray nodeFields = algoStrategyBatchNodeService.getIndicatorNodeFields(reqVo);
		return Result.succeed(nodeFields, ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/getIndicatorNodeData")
	@LogAnnotation(module = "算法规则-策略执行批次节点管理", action = "查询", desc = "查询指标节点数据")
	public Result<PageInfo<Map>> getIndicatorNodeData(@RequestBody PageRequest<StrategyBatchNodeDataReqVo> pageRequest) {
		PageInfo<Map> indicatorNodeData = algoStrategyBatchNodeService.getIndicatorNodeData(pageRequest);
		return Result.succeed(indicatorNodeData, ResultStatusEnum.SUCCESS.getMsg());
	}
}