package com.kbao.algo.controller.source;

import com.alibaba.fastjson.JSONObject;
import com.kbao.algo.indicator.indicatorField.bean.DataField;
import com.kbao.algo.source.bean.AlgoDataReqVo;
import com.kbao.algo.source.bean.AlgoSourceResVo;
import com.kbao.algo.source.bean.SourceDelReqVo;
import com.kbao.algo.source.entity.AlgoSource;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.algo.source.bean.AlgoSourceReqVo;
import com.kbao.algo.source.service.AlgoDataCollectService;
import com.kbao.algo.source.service.AlgoSourceService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageInfo;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> jie
 * @Description 管理
 * @Date 2024-10-25
*/
@RestController
@RequestMapping("/web/algoSource")
public class AlgoSourceController extends BaseController {

	@Autowired
	private AlgoSourceService algoSourceService;
	@Autowired
	private AlgoDataCollectService algoDataCollectService;

	@PostMapping("/list")
	@LogAnnotation(module = "管理", action = "查询", desc = "查询列表")
	public Result<PageInfo<AlgoSourceResVo>> list(@RequestBody PageRequest<AlgoSourceReqVo> reqVo) {
		PageInfo<AlgoSourceResVo> list = algoSourceService.sourcePage(reqVo);
		return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/save")
	@LogAnnotation(module = "管理", action = "新增/修改", desc = "新增/修改")
	public Result save(@RequestBody AlgoSource algoSource){
		algoSourceService.save(algoSource);
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/getById")
	@LogAnnotation(module = "管理", action = "查询", desc = "查询详情")
	public Result<AlgoSource> getById(@RequestBody AlgoSourceReqVo reqVo){
		AlgoSource algoSource = algoSourceService.selectByPrimaryKey(reqVo.getSourceId());
		return Result.succeed(algoSource, ResultStatusEnum.SUCCESS.getMsg());
	}


	@PostMapping("/delete")
	@LogAnnotation(module = "管理", action = "删除", desc = "删除")
	public Result delete(@RequestBody AlgoSourceReqVo reqVo) {
		algoSourceService.delete(reqVo.getSourceId());
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/data/fields")
	@LogAnnotation(module = "管理", action = "查询", desc = "查询列表")
	public Result<List<DataField>> getDataFields(@RequestBody AlgoDataReqVo reqVo) {
		List<DataField> list = algoSourceService.getDataFields(reqVo);
		return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/data/export")
	@LogAnnotation(module = "管理", action = "查询", desc = "导出")
	public void exportSourceData(@RequestBody AlgoDataReqVo reqVo) {
		algoSourceService.exportSourceData(reqVo, response);
	}

	@PostMapping("/ddlDropTable")
	@LogAnnotation(module = "管理", action = "删除", desc = "删除")
	public Result ddlDropTable(@RequestBody SourceDelReqVo reqVo) {
		algoSourceService.ddlDropTable(reqVo);
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/collectData")
	@LogAnnotation(module = "管理", action = "收集数据源", desc = "收集数据源")
	public Result collectData(@RequestBody JSONObject jsonObject) throws Exception {
		Integer strategyBatchId = jsonObject.getInteger("strategyBatchId");
		String sourceBizCode = jsonObject.getString("sourceBizCode");
		String isUpdate = jsonObject.getString("isUpdate");
		algoDataCollectService.collectData(strategyBatchId, sourceBizCode, isUpdate, new ArrayList<>());
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}
}