package com.kbao.algo.controller.dataSpace;

import com.github.pagehelper.PageInfo;
import com.kbao.algo.dataSpace.bean.DataSpaceListRespVo;
import com.kbao.algo.dataSpace.bean.DataSpaceOverviewVo;
import com.kbao.algo.dataSpace.bean.DataSpaceReqVo;
import com.kbao.algo.dataSpace.entity.AlgoDataSpace;
import com.kbao.algo.dataSpace.service.AlgoDataSpaceService;
import com.kbao.algo.indicator.entity.AlgoIndicator;
import com.kbao.algo.indicator.indicatorField.bean.DataField;
import com.kbao.algo.source.bean.AlgoDataReqVo;
import com.kbao.algo.source.service.AlgoSourceService;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 数据空间Controller
 */
@RestController
@RequestMapping("/web/algoDataSpace")
public class AlgoDataSpaceController extends BaseController {

    @Autowired
    private AlgoDataSpaceService algoDataSpaceService;
    @Autowired
    private AlgoSourceService algoSourceService;

    /**
     * 获取数据空间列表
     */
    @PostMapping("/list")
    @LogAnnotation(module = "数据空间", action = "查看", desc = "查询列表")
    public Result<PageInfo<DataSpaceListRespVo>> list(@RequestBody PageRequest<AlgoDataSpace> pageRequest) {
        PageInfo<DataSpaceListRespVo> list = algoDataSpaceService.getDataSpaceList(pageRequest);
        return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/overview")
    @LogAnnotation(module = "数据空间", action = "查看", desc = "查询数据概览")
    public Result<PageInfo<DataSpaceOverviewVo>> overview(@RequestBody PageRequest<DataSpaceOverviewVo> pageRequest) {
        PageInfo<DataSpaceOverviewVo> overview = algoDataSpaceService.getDataSpaceOverview(pageRequest);
        return Result.succeed(overview, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/fields")
    @LogAnnotation(module = "管理", action = "查询", desc = "查询数据空间字段列表")
    public Result<List<DataField>> getDataFields(@RequestBody AlgoDataReqVo reqVo) {
        List<DataField> list = algoSourceService.getSpaceDataFields(reqVo);
        return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/data")
    @LogAnnotation(module = "管理", action = "查询", desc = "查询数据列表")
    public Result<PageInfo<Map>> getDataFields(@RequestBody PageRequest<AlgoDataReqVo> pageRequest) {
        PageInfo<Map> list = algoSourceService.getSpaceDataList(pageRequest);
        return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/data/export")
    @LogAnnotation(module = "管理", action = "查询", desc = "导出")
    public void exportSourceData(@RequestBody AlgoDataReqVo reqVo) {
        algoSourceService.exportSpaceData(reqVo, response);
    }

    @PostMapping("/create")
    @LogAnnotation(module = "数据空间", action = "新增", desc = "创建数据空间")
    public Result<PageInfo<DataSpaceListRespVo>> create(@RequestBody DataSpaceReqVo reqVo) {
        algoDataSpaceService.create(reqVo);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/update")
    @LogAnnotation(module = "数据空间", action = "更新", desc = "更新数据空间")
    public Result<PageInfo<DataSpaceListRespVo>> update(@RequestBody AlgoDataSpace reqVo) {
        algoDataSpaceService.update(reqVo);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    /**
     * 删除数据空间
     */
    @PostMapping("/delete")
    @LogAnnotation(module = "数据空间", action = "删除", desc = "删除数据空间")
    public Result<PageInfo<AlgoIndicator>> delDataSpace(@RequestBody AlgoDataSpace dataSpace) {
        algoDataSpaceService.delDataSpace(dataSpace.getId());
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
}
