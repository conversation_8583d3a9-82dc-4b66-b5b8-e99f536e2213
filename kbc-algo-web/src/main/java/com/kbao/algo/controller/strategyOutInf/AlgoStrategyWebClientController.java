package com.kbao.algo.controller.strategyOutInf;

import com.github.pagehelper.PageInfo;
import com.kbao.algo.client.AlgoStrategyWebClient;
import com.kbao.algo.dataSpace.bean.DataSpaceReqVo;
import com.kbao.algo.dataSpace.bean.DataSpaceRespVo;
import com.kbao.algo.dataSpace.service.AlgoDataSpaceService;
import com.kbao.algo.enums.AlgoNodeTypeEnum;
import com.kbao.algo.source.bean.AlgoDataReqVo;
import com.kbao.algo.sourceField.bean.AlgoParamResVo;
import com.kbao.algo.strategy.bean.AlgoStrategyExecuteRespDTO;
import com.kbao.algo.strategy.bean.AlgoStrategyReqVo;
import com.kbao.algo.strategyBatch.bean.AlgoStrategyBatchReqVo;
import com.kbao.algo.strategyBatchNode.bean.AlgoStrategyBatchNodeRespDTO;
import com.kbao.algo.strategyBatchNode.bean.AlgoStrategyBatchNodeVo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.algo.source.service.AlgoSourceService;
import com.kbao.algo.strategy.service.AlgoStrategyService;
import com.kbao.algo.strategyBatchNode.service.AlgoStrategyBatchNodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 策略对外接口
 * @author: xiaojiayao
 * @time: 2024/11/6 13:52
 */
@RestController
@RequestMapping("/api/noauth/algoStrategy")
public class AlgoStrategyWebClientController extends BaseController implements AlgoStrategyWebClient {

    @Autowired
    private AlgoStrategyService algoStrategyService;

    @Autowired
    private AlgoStrategyBatchNodeService algoStrategyBatchNodeService;

    @Autowired
    private AlgoSourceService algoSourceService;

    @Autowired
    private AlgoDataSpaceService algoDataSpaceService;

    @PostMapping("/dataSpace/create")
    public Result<DataSpaceRespVo> createDataSpaceBatch(@RequestBody DataSpaceReqVo reqVo) {
        String bizCode = algoDataSpaceService.create(reqVo);
        DataSpaceRespVo res = new DataSpaceRespVo();
        res.setBizCode(bizCode);
        return Result.succeed(res, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/execute")
    public Result<AlgoStrategyExecuteRespDTO> execute(@RequestBody AlgoStrategyReqVo algoStrategyReqVo) {
        AlgoStrategyExecuteRespDTO res = algoStrategyService.execute(algoStrategyReqVo);
        return Result.succeed(res, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/getStrategyParams")
    public Result<List<AlgoParamResVo>> getStrategyParams(@RequestBody AlgoStrategyReqVo algoStrategyReqVo) {
        List<AlgoParamResVo> algoStrategyParamVos = algoStrategyService.getStrategyParams(algoStrategyReqVo.getStrategyCode());
        return Result.succeed(algoStrategyParamVos, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/getSourceBatchNodes")
    public Result<List<AlgoStrategyBatchNodeRespDTO>> getSourceBatchNodes(@RequestBody String batchCode) {
        List<AlgoStrategyBatchNodeRespDTO> res = algoStrategyBatchNodeService.getSourceBatchNodes(batchCode);
        return Result.succeed(res, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/getIndicatorBatchNodes")
    public Result<List<AlgoStrategyBatchNodeRespDTO>> getIndicatorBatchNodes(@RequestBody String batchCode) {
        List<AlgoStrategyBatchNodeRespDTO> res = algoStrategyBatchNodeService.getIndicatorBatchNodes(batchCode);
        return Result.succeed(res, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/getSourceBatchDetailPage")
    public Result<PageInfo<Map>> getSourceBatchDetailPage(@RequestBody PageRequest<AlgoDataReqVo> reqVo) {
        AlgoStrategyBatchNodeVo batchNodeDetail = algoStrategyBatchNodeService.getBatchNodeDetail(reqVo.getParam().getBizCode());
        reqVo.getParam().setBizCode(batchNodeDetail.getNodeValue());
        reqVo.getParam().setStrategyBatchId(batchNodeDetail.getBatchId());
        reqVo.getParam().setNodeType(batchNodeDetail.getNodeType());
        PageInfo<Map> sourceDataList = algoSourceService.getSourceDataList(reqVo);
        return Result.succeed(sourceDataList, ResultStatusEnum.SUCCESS.getMsg());
    }
    @PostMapping("/getIndicatorBatchDetailPage")
    public Result<PageInfo<Map>> getIndicatorBatchDetailPage(@RequestBody PageRequest<AlgoDataReqVo> reqVo) {
        reqVo.getParam().setNodeType(AlgoNodeTypeEnum.CALC_INDICATOR.getCode());
        AlgoStrategyBatchNodeVo batchNodeDetail = algoStrategyBatchNodeService.getBatchNodeDetail(reqVo.getParam().getBizCode());
        reqVo.getParam().setBizCode(batchNodeDetail.getNodeValue());
        reqVo.getParam().setStrategyBatchId(batchNodeDetail.getBatchId());
        reqVo.getParam().setNodeType(batchNodeDetail.getNodeType());
        PageInfo<Map> sourceDataList = algoSourceService.getSourceDataList(reqVo);
        return Result.succeed(sourceDataList, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/getResultIndicatorBatchDetailPage")
    public Result<PageInfo<Map>> getResultIndicatorBatchDetailPage(@RequestBody PageRequest<AlgoStrategyBatchReqVo> reqVo) {
        PageInfo<Map> sourceDataList = algoStrategyService.getResultIndicatorBatchDetailPage(reqVo);
        return Result.succeed(sourceDataList, ResultStatusEnum.SUCCESS.getMsg());
    }
}
