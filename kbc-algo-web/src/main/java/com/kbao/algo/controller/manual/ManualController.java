package com.kbao.algo.controller.manual;

import com.github.pagehelper.PageInfo;
import com.kbao.algo.indicator.entity.AlgoIndicator;
import com.kbao.algo.redis.RedissonUtil;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/web/manual")
public class ManualController {
    @Autowired
    private RedissonUtil redissonUtil;

    @PostMapping("/semaphoreInit")
    @LogAnnotation(module = "手动调用", action = "初始化", desc = "初始化系统信号量")
    public Result<PageInfo<AlgoIndicator>> semaphoreInit() {
        redissonUtil.init();
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
}
