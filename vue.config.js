module.exports = {
  publicPath: "/sct-algo",
  lintOnSave: false,
  devServer: {
    host: "0.0.0.0", // 允许外部ip访问
    port: 9091, // 端口
    https: false, // 启用https
    hot: true,
    overlay: {
      warnings: false,
      errors: false
    },
    proxy: {
      //
      "/mock": {
        target: "https://kbc-sta.kbao123.com/gateway/kbc-bsc",
        changeOrigin: true, // 允许websockets跨域
        pathRewrite: {
          "^/mock": ""
        }
      },
      // "/dataMock": {
      //   target: "https://kbc-sta.kbao123.com/gateway/kbc-sct-algo",
      //   changeOrigin: true, // 允许websockets跨域
      //   pathRewrite: {
      //     "^/dataMock": ""
      //   }
      // },
      "/dataMock": {
        target: "http://127.0.0.1:7001",
        changeOrigin: true, // 允许websockets跨域
        pathRewrite: {
          "^/dataMock": ""
        }
      },
      "/gateway/kbc-cbs-ism/web/gpagreementproduct/agreementSelectProduct": {
        changeOrigin: true,
        target: "http://127.0.0.1:8888",
        pathRewrite: path => path.replace(/^\/gateway\/kbc-cbs-ism/, "")
      },
      "/gateway/kbc-cbs-ism/": {
        changeOrigin: true,
        // target: "http://127.0.0.1:8888",
        target: "https://kbc-sta.kbao123.com/gateway/kbc-cbs-ism",
        pathRewrite: path => path.replace(/^\/gateway\/kbc-cbs-ism/, "")
      }
      // "/dataMock": {
      //   target: "http://10.176.18.17:7022/v1",
      //   changeOrigin: true, // 允许websockets跨域
      //   pathRewrite: {
      //     "^/dataMock": ""
      //   }
      // }
    }
  },
  chainWebpack: config => {
    // 移除 prefetch 插件
    config.plugins.delete("prefetch");
    // 移除 preload 插件
    config.plugins.delete("preload");
    // if(process.env.NODE_ENV === "dev"){
    //   config.plugin('webpack-bundle-analyzer')
    //           .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin)
    // }
  },
  configureWebpack: {
    optimization: {
      splitChunks: {
        chunks: "all",
        maxInitialRequests: Infinity,
        minSize: 300000, // 依赖包超过300000bit将被单独打包
        automaticNameDelimiter: "-",
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name(module) {
              const packageName = module.context.match(
                /[\\/]node_modules[\\/](.*?)([\\/]|$)/
              )[1];
              return `chunk.${packageName.replace("@", "")}`;
            },
            priority: 10
          }
        }
      }
    }
  }
};
