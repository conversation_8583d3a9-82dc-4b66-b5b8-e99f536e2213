<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>kbc-sct-algo</artifactId>
        <groupId>com.kbao</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <groupId>com.kbao</groupId>
    <artifactId>kbc-algo-service</artifactId>

    <properties>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <!-- 公共实体配置 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>common-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-algo-common</artifactId>
        </dependency>

        <!-- db配置 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>db-spring-boot-starter</artifactId>
        </dependency>

        <!-- redis 配置 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>redis-spring-boot-starter</artifactId>
        </dependency>

        <!-- 日志中心 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>log-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-algo-entity</artifactId>
        </dependency>

        <!-- 云服核心 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-cbs-ism-web-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-uoc-web-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-ucs-web-client</artifactId>
        </dependency>

        <!-- 基础平台web 接口服务包 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-bsc-web-client</artifactId>
        </dependency>

        <!-- 基础平台api 接口服务包 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-bsc-api-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-ums-web-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.26-incubating</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
