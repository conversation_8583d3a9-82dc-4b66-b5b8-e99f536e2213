package com.kbao.algo.indicator.indicatorVersion.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kbao.algo.enums.AlgoIndicatorVersionTypeEnum;
import com.kbao.algo.indicator.AlgoIndicatorHelper;
import com.kbao.algo.indicator.bean.AlgoIndicatorConfigVo;
import com.kbao.algo.indicator.indicatorVersion.dao.AlgoIndicatorVersionMapper;
import com.kbao.algo.indicator.indicatorVersion.entity.AlgoIndicatorVersion;
import com.kbao.algo.indicator.indicatorVersion.entity.AlgoIndicatorVersionContent;
import com.kbao.algo.util.AlgoContext;
import com.kbao.commons.exception.BusinessException;
import com.kbao.tool.util.IDUtils;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
* <AUTHOR> jie
* @Description Service类
* @Date 2025-05-30
*/
@Service
public class AlgoIndicatorVersionService extends BaseSQLServiceImpl<AlgoIndicatorVersion, Integer, AlgoIndicatorVersionMapper> {
    @Autowired
    private MongoTemplate mongoTemplate;

    @Transactional(rollbackFor = Exception.class)
    public void saveHistoryVersion(Integer indicatorId, JSONObject content, String remark, String version) {
        mapper.toHistoryVersion(indicatorId);
        AlgoIndicatorVersion versionEntity = AlgoIndicatorVersion.builder()
                .indicatorId(indicatorId)
                .type(AlgoIndicatorVersionTypeEnum.CURRENT.getCode())
                .version(version)
                .createBy(SysLoginUtils.getUserId())
                .remark(remark)
                .tenantId(AlgoContext.getTenantId())
                .build();
        mapper.insert(versionEntity);
        AlgoIndicatorVersionContent versionContent = new AlgoIndicatorVersionContent(versionEntity.getId(), content, new Date());
        mongoTemplate.save(versionContent);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveDraft(AlgoIndicatorConfigVo configVo) {
        mapper.delIndicatorDraft(configVo.getIndicatorId());
        AlgoIndicatorVersion version = AlgoIndicatorVersion.builder()
                .indicatorId(configVo.getIndicatorId())
                .type(AlgoIndicatorVersionTypeEnum.DRAFT.getCode())
                .version(AlgoIndicatorHelper.getVersionNum())
                .createBy(SysLoginUtils.getUserId())
                .tenantId(AlgoContext.getTenantId())
                .build();
        mapper.insert(version);

        JSONObject content = new JSONObject();
        content.put("nodeList", configVo.getNodeList());
        AlgoIndicatorVersionContent versionContent = new AlgoIndicatorVersionContent(version.getId(), content, new Date());
        mongoTemplate.save(versionContent);
    }

    public AlgoIndicatorConfigVo getVersionConfig(Integer versionId) {
        AlgoIndicatorVersion version = mapper.selectByPrimaryKey(versionId);
        return getConfigContent(version);
    }

    public AlgoIndicatorConfigVo getConfigByVersion(String indicatorCode, String version) {
        AlgoIndicatorVersion indicatorVersion = mapper.getByVersion(indicatorCode, version);
        return getConfigContent(indicatorVersion);
    }

    public JSONObject getVersionJSONConfig(Integer versionId) {
        AlgoIndicatorVersion version = mapper.selectByPrimaryKey(versionId);
        if (AlgoIndicatorVersionTypeEnum.DRAFT.getCode().equals(version.getType())) {
            throw new BusinessException("草稿不能查看配置信息");
        }
        AlgoIndicatorVersionContent versionContent = mongoTemplate.findById(version.getId(), AlgoIndicatorVersionContent.class);
        if (versionContent == null) {
            return null;
        }
        return versionContent.getContent();
    }

    public AlgoIndicatorConfigVo getConfigContent(AlgoIndicatorVersion version) {
        AlgoIndicatorConfigVo configVo = new AlgoIndicatorConfigVo();
        AlgoIndicatorVersionContent versionContent = mongoTemplate.findById(version.getId(), AlgoIndicatorVersionContent.class);
        if (versionContent == null) {
            return null;
        }
        List<JSONObject> nodeList;
        JSONObject content = versionContent.getContent();
        if (AlgoIndicatorVersionTypeEnum.DRAFT.getCode().equals(version.getType())) {
            JSONArray nodes = content.getJSONArray("nodeList");
            nodeList = new ArrayList<>(nodes.size());
            for (int i = 0; i < nodes.size(); i++) {
                nodeList.add(nodes.getJSONObject(i));
            }
        } else {
            nodeList = AlgoIndicatorHelper.indicatorTreeToNodes(content);
        }
        configVo.setIndicatorId(version.getIndicatorId());
        configVo.setNodeList(nodeList);
        return configVo;
    }

    public JSONObject getNodeContent(Integer versionId, String indicatorNodeId) {
        AlgoIndicatorVersionContent versionContent = mongoTemplate.findById(versionId, AlgoIndicatorVersionContent.class);
        if (versionContent == null) {
            return new JSONObject();
        }
        JSONObject content = versionContent.getContent();
        if (indicatorNodeId.equals(content.getString("nodeId"))) {
            content.put("isTop", 1);
            return content;
        }
        return AlgoIndicatorHelper.findTreeNode(content, indicatorNodeId);
    }

    public void updateRemark(AlgoIndicatorVersion version) {
        mapper.updateRemark(version.getId(), version.getRemark(), SysLoginUtils.getUserId());
    }

    public void delIndicatorVersion(Integer versionId) {
        mapper.delIndicatorVersion(versionId, SysLoginUtils.getUserId());
    }
}
