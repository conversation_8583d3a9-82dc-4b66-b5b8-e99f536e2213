CREATE PROCEDURE proc_IND000041_0()
BEGIN
    -- 创建临时视图
        drop temporary table if exists t_algo_temp_IND000041_b0_0;
        create temporary table t_algo_temp_IND000041_b0_0 as select t1.agentcode, (ifnull(t1.sixMonthV, 0)) sixMonthV, (ifnull(t1.lastAuarterlyVValue, 0)) lastAuarterlyVValue, (ifnull(t1.auarterlyVValue, 0)) auarterlyVValue, (ifnull(t1.sofficeFirstMonthV, 0)) sofficeFirstMonthV, (ifnull(t1.sofficeSecondMonthV, 0)) sofficeSecondMonthV, (ifnull(t1.sofficeFirstMonthBackV, 0)) sofficeFirstMonthBackV, (ifnull(t1.sofficeSecondMonthBackV, 0)) sofficeSecondMonthBackV, (ifnull(t1.soffice<PERSON>onthNum, 0)) sofficeMonthNum  from (select * , sum(if(t0.wageno >= get_months_ago('@param(wageno)', 5) and t0.wageno < '@param(wageno)', t0.agentMonthV, 0)) as sixMonthV, sum(if(right('@param(wageno)', 2) in ('04', '07', '10', '01') and t0.wageno >= get_months_ago('@param(wageno)', 3) and t0.wageno < '@param(wageno)', t0.agentMonthV, 0)) as lastAuarterlyVValue, sum(if(right('@param(wageno)', 2) in ('03', '06', '09', '12') and t0.wageno >= get_months_ago('@param(wageno)', 2) and t0.wageno < '@param(wageno)', t0.agentMonthV, 0)) as auarterlyVValue, sum(if(t0.isAgentcyS = '是' and right('@param(wageno)', 2) in ('03', '06', '09', '12') and t0.wageno = get_months_ago('@param(wageno)', 2), t0.agentMonthV, 0)) as sofficeFirstMonthV, sum(if(t0.isAgentcyS = '是' and right('@param(wageno)', 2) in ('03', '06', '09', '12') and t0.wageno = get_months_ago('@param(wageno)', 1), t0.agentMonthV, 0)) as sofficeSecondMonthV, sum(if(t0.isAgentcyS = '是' and right('@param(wageno)', 2) in ('03', '06', '09', '12') and t0.wageno = get_months_ago('@param(wageno)', 2), t0.sOfficeMonthBackV, 0)) as sofficeFirstMonthBackV, sum(if(t0.isAgentcyS = '是' and right('@param(wageno)', 2) in ('03', '06', '09', '12') and t0.wageno = get_months_ago('@param(wageno)', 1), t0.sOfficeMonthBackV, 0)) as sofficeSecondMonthBackV, sum(if(t0.isAgentcyS = '是' and right('@param(wageno)', 2) in ('03', '06', '09', '12') and t0.wageno >= get_months_ago('@param(wageno)', 2) and t0.wageno < '@param(wageno)', 1, 0)) + 1 as sofficeMonthNum from (select agentcode, wageno, agentMonthV, sOfficeMonthV, sOfficeMonthBackV, isAgentcyS  from t_algo_source_D00017_0) t0 group by t0.agentcode) t1;
        create index idx_agentcode on t_algo_temp_IND000041_b0_0(agentcode);

    DECLARE tableExists INT DEFAULT 0;

    SELECT COUNT(*) INTO tableExists FROM information_schema.tables
    WHERE table_schema = DATABASE() AND table_name = 't_algo_indicator_IND000041_0';

    -- 如果表不存在则创建
    IF tableExists = 0 THEN
        -- 执行创建表的SQL语句
        PREPARE stmt FROM 'CREATE TABLE t_algo_indicator_IND000041_0 LIKE t_algo_temp_IND000041_b0_0;';
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        alter table t_algo_indicator_IND000041_0 add strategyBatchId INT first,
                                          add createTime DATETIME DEFAULT CURRENT_TIMESTAMP,
                                          add index idx_strategyBatchId(strategyBatchId);
    END IF;

    insert into t_algo_indicator_IND000041_0 select 0,t.*, now() from t_algo_temp_IND000041_b0_0 t;

    -- 删除临时视图
        drop view if exists t_algo_temp_IND000041_b0_0;
END;