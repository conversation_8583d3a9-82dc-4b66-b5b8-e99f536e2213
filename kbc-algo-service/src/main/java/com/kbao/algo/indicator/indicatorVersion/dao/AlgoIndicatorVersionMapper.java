package com.kbao.algo.indicator.indicatorVersion.dao;

import com.kbao.algo.indicator.indicatorVersion.entity.AlgoIndicatorVersion;
import com.kbao.kbcbsc.dao.sql.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR> jie
* @Description Dao类
* @Date 2025-05-30
*/
public interface AlgoIndicatorVersionMapper extends BaseMapper<AlgoIndicatorVersion, Integer>{

    void toHistoryVersion(@Param("indicatorId") Integer indicatorId);

    void delIndicatorDraft(@Param("indicatorId") Integer indicatorId);

    void updateRemark(@Param("id") Integer id, @Param("remark") String remark, @Param("updateBy") String updateBy);

    void delIndicatorVersion(@Param("id") Integer id, @Param("updateBy") String updateBy);

    AlgoIndicatorVersion getByVersion(@Param("indicatorCode") String indicatorCode, @Param("version") String version);
}