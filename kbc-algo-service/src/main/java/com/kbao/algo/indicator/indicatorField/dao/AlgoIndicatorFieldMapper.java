package com.kbao.algo.indicator.indicatorField.dao;

import com.kbao.algo.indicator.indicatorField.entity.AlgoIndicatorField;
import com.kbao.algo.sourceField.bean.AlgoParamResVo;
import com.kbao.kbcbsc.dao.sql.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR> jie
* @Description Dao类
* @Date 2024-11-01
*/
public interface AlgoIndicatorFieldMapper  extends BaseMapper<AlgoIndicatorField, Integer>{

    void delIndicatorField(@Param("indicatorId") Integer indicatorId);

    List<AlgoParamResVo> getIndicatorFields(@Param("bizCode") String bizCode);
}