package com.kbao.algo.indicator.indicator.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.algo.dataSpace.service.AlgoDataSpaceService;
import com.kbao.algo.enums.AlgoNodeTypeSubEnum;
import com.kbao.algo.enums.AlgoParamTypeEnum;
import com.kbao.algo.indicator.AlgoIndicatorHelper;
import com.kbao.algo.indicator.bean.AlgoNode;
import com.kbao.algo.indicator.bean.AlgoProcedure;
import com.kbao.algo.indicator.indicatorSelectSql.service.AlgoIndicatorSelectSqlService;
import com.kbao.algo.source.bean.AlgoSourceTableDto;
import com.kbao.algo.sourceField.bean.AlgoParamResVo;
import com.kbao.algo.strategyBatchNode.bean.AlgoStrategyBatchNodeDTO;
import com.kbao.algo.strategyBatchNode.bean.StrategyBatchNodeDataReqVo;
import com.kbao.algo.strategyBatchParam.service.AlgoStrategyBatchParamService;
import com.kbao.algo.util.AlgoConstants;
import com.kbao.commons.exception.BusinessException;
import com.kbao.algo.source.service.AlgoSourceService;
import com.kbao.commons.web.PageRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import freemarker.template.Configuration;
import freemarker.template.Template;

import java.io.StringWriter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Service
public class AlgoProcedureService {
    @Autowired
    private AlgoSourceService algoSourceService;
    @Autowired
    private AlgoIndicatorService algoIndicatorService;
    @Autowired
    private AlgoIndicatorSelectSqlService algoIndicatorSelectSqlService;
    @Autowired
    private Configuration configuration;
    @Autowired
    private AlgoStrategyBatchParamService algoStrategyBatchParamService;
    private final ThreadLocal<AlgoProcedure> proceThreadLocal = new ThreadLocal<>();

    public String buildProcedure(Integer dataSpaceId, Integer strategyBatchId, String indicatorBizCode, JSONObject content, Map<String, AlgoParamResVo> paramsMap) {
        String procedureName = AlgoConstants.getProcedureName(indicatorBizCode, strategyBatchId);
        AlgoProcedure algoProcedure = this.setAlgoProcedure(dataSpaceId, strategyBatchId, indicatorBizCode, paramsMap);
        AlgoNode algoNode = buildProcedureItem(content, null, 0);
        List<String> contents = algoProcedure.getContents();
        List<String> tempTables = algoProcedure.getTempTables();
        String indicatorTableName = AlgoConstants.getIndicatorTableName(indicatorBizCode, dataSpaceId);

        StringWriter writer = new StringWriter();
        Map<String, Object> dataModel = new HashMap<>();
        // freemarker中数字
        dataModel.put("strategyBatchId", strategyBatchId.toString());
        dataModel.put("procName", procedureName);
        dataModel.put("contents", contents);
        dataModel.put("tableName", algoNode.getTableName());
        dataModel.put("indicatorTableName", indicatorTableName);
        dataModel.put("tempTables", tempTables);
        dataModel.put("fields", algoNode.getFields());
        Template template;
        try {
            template = configuration.getTemplate("create_proc.ftl");
            template.process(dataModel, writer);
        } catch (Exception e) {
            throw new BusinessException("生成存储过程语句失败", e);
        }
        algoIndicatorService.dropProcedure(procedureName);
        algoIndicatorService.createProcedure(procedureName, writer.toString());
        algoIndicatorSelectSqlService.save(strategyBatchId, indicatorBizCode, algoNode.getSelectSql());
        return procedureName;
    }

    public PageInfo<Map> getIndicatorNodeData(PageRequest<StrategyBatchNodeDataReqVo> pageRequest, AlgoStrategyBatchNodeDTO batchNode, JSONObject content) {
        StrategyBatchNodeDataReqVo param = pageRequest.getParam();
        if ("1".equals(content.getString("isTop"))) {
            param.setBizCode(batchNode.getNodeValue());
            param.setDataSpaceId(batchNode.getDataSpaceId());
            param.setStrategyBatchId(batchNode.getStrategyBatchId());
            return algoSourceService.getIndicatorDataList(pageRequest);
        }
        // 构建查询语句
        Map<String, AlgoParamResVo> paramMap = algoStrategyBatchParamService.getParamMap(batchNode.getStrategyBatchId());
        this.setAlgoProcedure(batchNode.getDataSpaceId(), batchNode.getStrategyBatchId(), batchNode.getNodeValue(), paramMap);
        AlgoNode algoNode = buildProcedureItem(content, null, 1);
        // 查询数据
        param.setSelectSql(algoNode.getSelectSql());
        PageHelper.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());
        List<Map> data = algoIndicatorService.getIndicatorData(param);
        return PageInfo.of(data);
    }

    public AlgoNode buildProcedureItem(JSONObject node, AlgoNode prevNode, int depth) {
        AlgoNodeTypeSubEnum nodeTypeSub = AlgoNodeTypeSubEnum.getByValue(node.getString("nodeTypeSub"));
        JSONArray children = node.getJSONArray("children");
        if (AlgoNodeTypeSubEnum.CONNECT.getValue().equals(nodeTypeSub.getValue())) {
            if (children.size() < 2) {
                throw new BusinessException("连接节点必须有两个及以上子节点");
            }
            List<AlgoNode> childNodes = new ArrayList<>();
            for (int i = 0; i < children.size(); i++) {
                //处理数据连接下面的多个子节点
                AlgoNode algoNode = buildProcedureItem(children.getJSONObject(i), prevNode, depth + 1);
                childNodes.add(algoNode);
            }
            return connectNodeHandler(node, childNodes);
        } else if (CollectionUtils.isNotEmpty(children)) {
            if (children.size() > 1) {
                throw new BusinessException("非连接节点只能有一个子节点");
            }
            //处理子节点
            prevNode = buildProcedureItem(children.getJSONObject(0), prevNode, depth + 1);
        }
        switch (nodeTypeSub) {
            case GROUP:
                return groupNodeHandler(node, prevNode);
            case FILTER:
                return filterNodeHandler(node, prevNode);
            case CASE:
                return caseNodeHandler(node, prevNode);
            case SOURCE:
            case INDICATOR:
                if (depth == 0) {
                    throw new BusinessException("源节点不能在最后一级");
                }
                return inputNodeHandler(node);
            default:
                throw new BusinessException("未知节点类型：" + nodeTypeSub);
        }
    }

    /**
     * 处理连接节点
     * @param node 节点
     * @param children 子节点
     * @return
     */
    public AlgoNode connectNodeHandler(JSONObject node, List<AlgoNode> children) {
        String nodeName = node.getString("nodeName");
        Map<String, String> aliasMap = new HashMap<>();
        Map<String, AlgoNode> childMap = new HashMap<>();
        for (AlgoNode child : children) {
            aliasMap.put(child.getNodeId(), getNextAlias());
            childMap.put(child.getNodeId(), child);
            String tableName = child.getTableName();
            // 如果子节点没有创建临时表，则创建临时表
            if (StringUtils.isEmpty(tableName)) {
                String tempTableName = getNextTableName();
                String procedureContent = "create temporary table " + tempTableName + " as " + child.getNodeSql();
                child.setTableName(tempTableName);
                child.setNodeSql("select * from " + tempTableName);
                this.addProcContent(procedureContent, child);
            }
        }
        Set<String> usedNodeIds = new HashSet<>();
        JSONObject nodeConfig = node.getJSONObject("nodeConfig");
        JSONArray connectConditions = AlgoIndicatorHelper.getDefaultAndCustom(nodeName, nodeConfig, "connectCondition",  "custom", "连接条件不能为空");
        StringBuilder connectSql = new StringBuilder();
        StringBuilder connectSelectSql = new StringBuilder();
        for (int i = 0; i < connectConditions.size(); i++) {
            JSONObject condition = connectConditions.getJSONObject(i);
            String fromNodeId = condition.getString("fromNodeId");
            String toNodeId = condition.getString("toNodeId");
            String fromTableAlias = aliasMap.get(fromNodeId);
            String toTableAlias = aliasMap.get(toNodeId);
            String formula = condition.getString("formula");
            if (i == 0) {
                usedNodeIds.add(fromNodeId);
                usedNodeIds.add(toNodeId);
                connectSql.append(childMap.get(fromNodeId).getTableName()).append(" ").append(fromTableAlias).append(" ");
                connectSelectSql.append("(").append(childMap.get(fromNodeId).getSelectSql()).append(") ").append(fromTableAlias).append(" ");
            } else {
                if (usedNodeIds.add(fromNodeId) || !usedNodeIds.add(toNodeId)) {
                    throw new BusinessException(nodeName + "：连接条件不正确，请检查");
                }
            }
            connectSql.append(condition.getString("connectType")).append(" ").append(childMap.get(toNodeId).getTableName()).append(" ")
                    .append(toTableAlias).append(" on ");
            connectSelectSql.append(condition.getString("connectType")).append(" (").append(childMap.get(toNodeId).getSelectSql()).append(") ")
                    .append(toTableAlias).append(" on ");
            if (StringUtils.isNotEmpty(formula)) {
                formula = this.replaceConnectFields(formula, AlgoConstants.FIELD_REGEX, aliasMap, childMap);
                connectSql.append(formula).append("\n");
                connectSelectSql.append(formula).append("\n");
            } else {
                connectSql.append(fromTableAlias).append(".").append(condition.getString("fromFieldName"))
                        .append(" = ").append(toTableAlias).append(".").append(condition.getString("toFieldName")).append("\n");
                connectSelectSql.append(fromTableAlias).append(".").append(condition.getString("fromFieldName"))
                        .append(" = ").append(toTableAlias).append(".").append(condition.getString("toFieldName")).append("\n");
            }
        }
        AlgoNode algoNode = this.getConnectOutputFieldSql(node.getString("nodeId"), nodeName, node.getJSONObject("output"), childMap, aliasMap);
        String tempTableName = getNextTableName();
        algoNode.setTableName(tempTableName);
        String createTablePrefix = getCreateTablePrefix();
        String procedureContent = createTablePrefix + tempTableName + " as select " +
                algoNode.getOutputSql() + " from " + connectSql;
        algoNode.setNodeSql("select * from " + tempTableName);
        algoNode.setSelectSql("select " + algoNode.getOutputSql() + " from " + connectSelectSql);
        this.addProcContent(procedureContent, algoNode);
        return algoNode;
    }

    /**
     * 处理聚合节点
     */
    public AlgoNode groupNodeHandler(JSONObject node, AlgoNode prevNode) {
        String nodeName = node.getString("nodeName");
        Set<String> inputFields = new HashSet<>(prevNode.getFields());
        String groupTableAlias = getNextAlias(); // group by 子查询别名

        JSONObject nodeConfig = node.getJSONObject("nodeConfig");
        JSONArray groupFields = nodeConfig.getJSONArray("groupFields");
        Set<String> groupFieldSet = new HashSet<>();
        for (int i = 0; i < nodeConfig.getJSONArray("groupFields").size(); i++) {
            String fieldName = groupFields.getJSONObject(i).getString("fieldName");
            groupFieldSet.add(fieldName);
        }
        Set<String> usedFields = new HashSet<>(groupFieldSet);
        JSONObject groupJson = nodeConfig.getJSONObject("group");
        JSONArray groupList = AlgoIndicatorHelper.getDefaultAndCustom(nodeName, groupJson, "default",  "custom", null);
        StringBuilder groupFieldSql = new StringBuilder();
        if (CollectionUtils.isNotEmpty(groupList)) {
            for (int i = 0; i < groupList.size(); i++) {
                JSONObject group = groupList.getJSONObject(i);
                String formula = group.getString("formula");
                String fieldName = group.getString("fieldName");
                if (!inputFields.add(fieldName)) {
                    throw new BusinessException(nodeName + "：分组聚合条件字段" + fieldName + "与上级字段冲突");
                }
                if (StringUtils.isEmpty(formula)) {
                    String aggrFieldName = group.getString("aggrFieldName");
                    usedFields.add(aggrFieldName);
                    groupFieldSql.append(", ").append(group.getString("function")).append("(").append(groupTableAlias).append(".").append(aggrFieldName).append(") as ").append(fieldName);
                } else {
                    formula = this.groupParamHandler(formula);
                    formula = this.replaceFields(formula, AlgoConstants.FIELD_REGEX, groupTableAlias, usedFields);
                    groupFieldSql.append(", ").append(formula).append(" as ").append(group.getString("fieldName"));
                }
            }
        }
        usedFields.removeAll(inputFields);
        if (!usedFields.isEmpty()) {
            throw new BusinessException(nodeName + "：分组聚合条件字段" + String.join(",", usedFields) + "不存在");
        }
        String groupBySql = "";
        if (CollectionUtils.isNotEmpty(groupFieldSet)) {
            groupBySql = " group by " + groupFieldSet.stream().map(s -> groupTableAlias + "." + s).collect(Collectors.joining(","));
        }
        String outputTableAlias = getNextAlias();
        String tempTableName = getNextTableName();
        AlgoNode algoNode = this.getOutputFieldSql(node.getString("nodeId"), nodeName, outputTableAlias, node.getJSONObject("output"), inputFields);

        String createTablePrefix = getCreateTablePrefix();
        String procedureContent = createTablePrefix + tempTableName + " as select " +
                algoNode.getOutputSql() + " from (select * "+ groupFieldSql +" from (" + prevNode.getNodeSql() + ") " + groupTableAlias + groupBySql + ") " + outputTableAlias;

        algoNode.setTableName(tempTableName);
        algoNode.setNodeSql("select * from " + tempTableName);
        algoNode.setSelectSql("select " + algoNode.getOutputSql() + " from (select * "+ groupFieldSql
                + " from (" + prevNode.getSelectSql() + ") " + groupTableAlias + groupBySql + ") " + outputTableAlias);
        this.addProcContent(procedureContent, algoNode);
        return algoNode;
    }

    public String groupParamHandler(String formula) {
        String paramName = this.getMatchParam(formula, AlgoConstants.PARAM_REGEX);
        if (paramName == null) {
            return formula;
        }
        if (proceThreadLocal.get().getDataSpaceId() == 0) {
            formula = formula.replace("@param("+ paramName +")", "'@param("+ paramName +")'");
        } else {
            AlgoParamResVo param = this.getParams(paramName);
            formula = formula.replace("@param("+ paramName +")", param.getParamValue().toString());
        }
        return formula;
    }

    /**
     * 处理数据过滤节点
     */
    public AlgoNode filterNodeHandler(JSONObject node, AlgoNode prevNode) {
        String nodeName = node.getString("nodeName");
        Set<String> inputFields = prevNode.getFields();
        String tableAlias = getNextAlias();
        JSONObject filter = node.getJSONObject("nodeConfig").getJSONObject("filter");
        JSONArray conditions = AlgoIndicatorHelper.getDefaultAndCustom(nodeName, filter, "default",  "custom", null);
        Set<String> whereUsedFields = new HashSet<>();
        StringBuilder whereSql = new StringBuilder(" where 1=1");
        for (int i = 0; i < conditions.size(); i++) {
            JSONObject condition = conditions.getJSONObject(i);
            String formula = condition.getString("formula");
            if (StringUtils.isEmpty(formula)) {
                String left = condition.getString("left");
                whereUsedFields.add(left);
                this.buildWhereCondition(whereSql, tableAlias, condition);
            } else {
                formula = this.replaceFields(formula, AlgoConstants.FIELD_REGEX, tableAlias, whereUsedFields);
                whereSql.append(" and ").append(formula);
            }
        }
        whereUsedFields.removeAll(inputFields);
        if (!whereUsedFields.isEmpty()) {
            throw new BusinessException(nodeName + "：数据过滤使用字段" + String.join(",", whereUsedFields) + "不存在");
        }
        String tempTableName = getNextTableName();
        AlgoNode algoNode = this.getOutputFieldSql(node.getString("nodeId"), nodeName, tableAlias, node.getJSONObject("output"), inputFields);
        String createTablePrefix = getCreateTablePrefix();
        String procedureContent = createTablePrefix + tempTableName + " as select " +
                algoNode.getOutputSql() + " from (" + prevNode.getNodeSql() + ") " + tableAlias + whereSql;
        algoNode.setTableName(tempTableName);
        algoNode.setNodeSql("select * from " + tempTableName);
        algoNode.setSelectSql("select " + algoNode.getOutputSql() + " from (" + prevNode.getSelectSql() + ") " + tableAlias + whereSql);
        this.addProcContent(procedureContent, algoNode);
        return algoNode;
    }

    public void buildWhereCondition(StringBuilder whereSql, String tableAlias, JSONObject condition) {
        AlgoProcedure procedure = proceThreadLocal.get();
        String left = condition.getString("left");
        String right = condition.getString("right");
        String paramName = this.getMatchParam(right, AlgoConstants.PARAM_REGEX);
        if (paramName == null || procedure.getDataSpaceId() == 0) {
            if (paramName != null) {
                right = "'" + right + "'";
            }
            whereSql.append(" and ").append(tableAlias).append(".").append(left).append(" ").append(condition.getString("operator"))
                    .append(" ").append(right);
            return;
        }
        AlgoParamResVo param = this.getParams(paramName);
        if (param == null) {
            return;
        }
        if (AlgoParamTypeEnum.MULTIPLE.getValue().equals(param.getParamType())) {
            whereSql.append(" and ").append(tableAlias).append(".").append(left).append(" in (");
            JSONArray paramValue = (JSONArray) param.getParamValue();
            for (Object o : paramValue) {
                whereSql.append("'").append(o).append("',");
            }
            whereSql.deleteCharAt(whereSql.length() - 1).append(")");
        } else {
            whereSql.append(" and ").append(tableAlias).append(".").append(left).append(" ").append(condition.getString("operator"))
                    .append(" '").append(param.getParamValue()).append("'");
        }
    }



    /**
     * 处理条件选择节点
     */
    public AlgoNode caseNodeHandler(JSONObject node, AlgoNode prevNode) {
        JSONArray handle = node.getJSONObject("nodeConfig").getJSONArray("handle");
        String nodeName = node.getString("nodeName");
        Set<String> caseInputFields = prevNode.getFields();

        String caseTableAlias = getNextAlias(); // case when 子查询别名
        List<String> caseSqlList = new ArrayList<>();
        Set<String> inputFields = new HashSet<>(caseInputFields);
        Set<String> caseUsedFields = new HashSet<>();
        for (int i = 0; i < handle.size(); i++) {
            JSONObject item = handle.getJSONObject(i);
            StringBuilder caseSql = new StringBuilder("(case");

            String fieldName = item.getString("fieldName");
            if (!inputFields.add(fieldName)) {
                throw new BusinessException(nodeName + "：条件组字段" + fieldName + "与上级字段冲突");
            }
            JSONArray conditions = AlgoIndicatorHelper.getDefaultAndCustom(nodeName, item, "default",  "custom", "条件组字段不能为空");
            for (int j = 0; j < conditions.size(); j++) {
                JSONObject condition = conditions.getJSONObject(j);
                String formula = condition.getString("formula");
                String caseValue = condition.getString("caseValue");
                caseValue = this.replaceFields(caseValue, AlgoConstants.FIELD_REGEX, caseTableAlias, caseUsedFields);
                if (StringUtils.isEmpty(formula)) {
                    String left = condition.getString("left");
                    caseUsedFields.add(left);
                    String right = condition.getString("right");
                    String operator = condition.getString("operator");
                    caseSql.append(" when ").append(caseTableAlias).append(".").append(left).append(" ").append(operator)
                            .append(" ").append(right).append(" then ").append(caseValue);
                } else {
                    if ("else".equals(formula.trim())) {
                        caseSql.append(" else ").append(caseValue);
                    } else {
                        formula = this.replaceFields(formula, AlgoConstants.FIELD_REGEX, caseTableAlias, caseUsedFields);
                        caseSql.append(" when ").append(formula).append(" then ").append(caseValue);
                    }
                }
            }
            caseSql.append(" end ) ").append(fieldName);
            caseSqlList.add(caseSql.toString());
        }
        caseUsedFields.removeAll(caseInputFields);
        if (!caseUsedFields.isEmpty()) {
            throw new BusinessException(nodeName + "：条件组使用字段" + String.join(",", caseUsedFields) + "不存在");
        }
        String outputTableAlias = getNextAlias();
        JSONObject output = node.getJSONObject("output");
        AlgoNode algoNode = this.getOutputFieldSql(node.getString("nodeId"), nodeName, outputTableAlias, output, inputFields);

        String nextTableName = getNextTableName();
        algoNode.setTableName(nextTableName);
        String procedureContent = "create temporary table " + nextTableName + " as select " + algoNode.getOutputSql() + " from (select *," +
                String.join(", ", caseSqlList) +
                " from " + "(" + prevNode.getNodeSql() + ") " + caseTableAlias + ") " + outputTableAlias;
        this.addProcContent(procedureContent, algoNode);
        algoNode.setNodeSql("select * from " + nextTableName);
        algoNode.setSelectSql("select " + algoNode.getOutputSql() + " from (select *," +
                String.join(", ", caseSqlList) +
                " from " + "(" + prevNode.getSelectSql() + ") " + caseTableAlias + ") " + outputTableAlias);
        return algoNode;
    }

    /**
     * 处理输入节点
     */
    public AlgoNode inputNodeHandler(JSONObject node) {
        String nodeTypeSub = node.getString("nodeTypeSub");
        JSONObject nodeConfig = node.getJSONObject("nodeConfig");
        String nodeName = node.getString("nodeName");
        String bizCode = nodeConfig.getString("bizCode");

        AlgoProcedure algoProcedure = proceThreadLocal.get();
        Integer dataSpaceId = algoProcedure.getDataSpaceId();
        AlgoSourceTableDto table = null;
        String filterSql = "";
        if (AlgoNodeTypeSubEnum.SOURCE.getValue().equals(nodeTypeSub)) {
            table = algoSourceService.getSourceTable(dataSpaceId, bizCode);
        } else if (AlgoNodeTypeSubEnum.INDICATOR.getValue().equals(nodeTypeSub)) {
            table = algoIndicatorService.getIndicatorTable(dataSpaceId, bizCode);
            filterSql = " where strategyBatchId = " + algoProcedure.getStrategyBatchId();
        }
        if (table == null) {
            throw new BusinessException(nodeName + "：输入节点数据有误");
        }
        Set<String> inputFields = table.getFieldList().stream().map(AlgoSourceTableDto.Field::getFieldName).collect(Collectors.toSet());

        JSONObject output = node.getJSONObject("output");
        AlgoNode algoNode = this.getOutputFieldSql(node.getString("nodeId"), nodeName, "", output, inputFields);
        String select = "select " + algoNode.getOutputSql() + " from " +
                table.getTableName() + filterSql;
        // 指标表和带公式查询语句，需要先生成中间视图
        if (!algoNode.isHasFormula() && !AlgoNodeTypeSubEnum.INDICATOR.getValue().equals(nodeTypeSub)) {
            algoNode.setTableName(table.getTableName());
        }
        algoNode.setNodeSql(select);
        algoNode.setSelectSql(select);
        return algoNode;
    }


    /**
     * 处理输出字段
     * @param nodeId 节点ID
     * @param nodeName 节点名称
     * @param tableAlias 表别名
     * @param output 输出字段配置
     * @param inputFields 输入字段
     * @return
     */
    public AlgoNode getOutputFieldSql(String nodeId, String nodeName, String tableAlias, JSONObject output, Set<String> inputFields) {
        tableAlias = getTableAlias(tableAlias);
        AlgoNode algoNode = new AlgoNode();
        Set<String> usedFields = new HashSet<>();
        Set<String> outputFields = new HashSet<>();
        Set<String> indexFields = new HashSet<>();

        JSONObject fields = output.getJSONObject("fields");
        JSONArray fieldList = AlgoIndicatorHelper.getDefaultAndCustom(nodeName, fields, "default",  "custom", "输出字段不能为空");
        StringBuilder outputFieldSql = new StringBuilder();
        for (int i = 0; i < fieldList.size(); i++) {
            JSONObject field = fieldList.getJSONObject(i);
            if ("1".equals(field.getString("isIndex"))) {
                indexFields.add(field.getString("fieldName"));
            }
            String fieldName = field.getString("fieldName");
            String formula = field.getString("formula");
            outputFields.add(fieldName);
            if (formula != null) {
                algoNode.setHasFormula(true);
                formula = this.replaceFields(formula, AlgoConstants.FIELD_REGEX, tableAlias, usedFields);
                outputFieldSql.append("(").append(formula).append(") ").append(fieldName).append(", ");
            } else {
                usedFields.add(fieldName);
                outputFieldSql.append(tableAlias).append(fieldName).append(", ");
            }
        }
        outputFieldSql.deleteCharAt(outputFieldSql.length() - 2);
        inputFields.forEach(usedFields::remove);
        if (!usedFields.isEmpty()) {
            throw new BusinessException(nodeName +"：输出字段" + String.join(",", usedFields) + "不存在");
        }
        algoNode.setFields(outputFields);
        algoNode.setOutputSql(outputFieldSql.toString());
        algoNode.setIndexFields(indexFields);
        algoNode.setNodeId(nodeId);
        return algoNode;
    }

    public AlgoNode getConnectOutputFieldSql(String nodeId, String nodeName, JSONObject output, Map<String, AlgoNode> children, Map<String, String> aliasMap) {
        AlgoNode algoNode = new AlgoNode();
        Set<String> outputFields = new HashSet<>();
        Set<String> indexFields = new HashSet<>();
        JSONObject fields = output.getJSONObject("fields");
        JSONArray fieldList = AlgoIndicatorHelper.getDefaultAndCustom(nodeName, fields, "default",  "custom", "输出字段不能为空");
        StringBuilder outputFieldSql = new StringBuilder();
        for (int i = 0; i < fieldList.size(); i++) {
            JSONObject field = fieldList.getJSONObject(i);
            String fieldName = field.getString("fieldName");
            String formula = field.getString("formula");
            if ("1".equals(field.getString("isIndex"))) {
                indexFields.add(field.getString("fieldName"));
            }
            outputFields.add(fieldName);
            if (formula != null) {
                algoNode.setHasFormula(true);
                formula = this.replaceConnectFields(formula, AlgoConstants.FIELD_REGEX, aliasMap, children);
                outputFieldSql.append("(").append(formula).append(") ").append(fieldName).append(", ");
            } else {
                String fieldNodeId = field.getString("fieldNodeId");
                String tableAlias = aliasMap.get(fieldNodeId) + ".";
                Set<String> inputFields = children.get(fieldNodeId).getFields();
                if (!inputFields.contains(fieldName)) {
                    throw new BusinessException(nodeName +"：输出字段" + fieldName + "不存在");
                }
                outputFieldSql.append(tableAlias).append(fieldName).append(", ");
            }
        }
        outputFieldSql.deleteCharAt(outputFieldSql.length() - 2);
        algoNode.setFields(outputFields);
        algoNode.setOutputSql(outputFieldSql.toString());
        algoNode.setIndexFields(indexFields);
        algoNode.setNodeId(nodeId);
        return algoNode;
    }



    public String replaceConnectFields(String input, String regex, Map<String, String> aliasMap, Map<String, AlgoNode> children) {
        // 创建Pattern对象
        Pattern pattern = Pattern.compile(regex);

        // 创建Matcher对象
        Matcher matcher = pattern.matcher(input);

        // 使用StringBuffer和appendReplacement/appendTail方法进行替换
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            // 获取匹配到的字符串（即#之间的部分）
            String matchedString = matcher.group(1);
            String[] split = matchedString.split("\\.");
            String tableAlias = aliasMap.get(split[0]);
            if (!children.get(split[0]).getFields().contains(split[1])) {
                throw new BusinessException("输出字段" + matchedString + "不存在");
            }
            // 替换为t.字符串的形式
            matcher.appendReplacement(sb, tableAlias + "." + split[1]);
        }
        // 添加最后一次匹配后的剩余部分
        matcher.appendTail(sb);

        // 返回替换后的字符串
        return sb.toString();
    }

    public String replaceFields(String input, String regex, String alias, Set<String> fields) {
        alias = getTableAlias(alias);
        // 创建Pattern对象
        Pattern pattern = Pattern.compile(regex);

        // 创建Matcher对象
        Matcher matcher = pattern.matcher(input);

        // 使用StringBuffer和appendReplacement/appendTail方法进行替换
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            // 获取匹配到的字符串（即#之间的部分）
            String matchedString = matcher.group(1);
            fields.add(matchedString);
            // 替换为t.字符串的形式
            matcher.appendReplacement(sb, alias + matchedString);
        }
        // 添加最后一次匹配后的剩余部分
        matcher.appendTail(sb);

        // 返回替换后的字符串
        return sb.toString();
    }

    public String getMatchParam(String str, String regex) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    public String getTableAlias(String alias) {
        if (StringUtils.isNotEmpty(alias) && !alias.endsWith(".")) {
            return alias + ".";
        }
        return alias;
    }

    public String getNextAlias() {
        AlgoProcedure algoProcedure = proceThreadLocal.get();
        Integer index = algoProcedure.getTableAliasIndex();
        algoProcedure.setTableAliasIndex(index + 1);
        return "t" + index;
    }

    public AlgoParamResVo getParams(String paramName){
        AlgoProcedure algoProcedure = proceThreadLocal.get();
        Map<String, AlgoParamResVo> params = algoProcedure.getParams();
        return params.get(paramName);
    }

    public String getCreateTablePrefix() {
        return "create temporary table ";
    }

    public String getNextTableName() {
        AlgoProcedure p = proceThreadLocal.get();
        Integer index = p.getTempTableIndex();
        p.setTempTableIndex(index + 1);
        return "t_algo_temp_" + p.getIndicatorBizCode() + "_b" + p.getStrategyBatchId() + "_" + index;
    }

    public void addProcContent(String content, AlgoNode node) {
        AlgoProcedure algoProcedure = proceThreadLocal.get();
        List<String> contents = algoProcedure.getContents();
        contents.add("drop temporary table if exists " + node.getTableName());
        contents.add(content);

        if (node.getTableName() != null) {
            algoProcedure.getTempTables().add(node.getTableName());
            if (CollectionUtils.isNotEmpty(node.getFields())) {
                node.getIndexFields().forEach(field -> {
                    String createIndexSql = "create index idx_" + field +" on " + node.getTableName()+ "(" + field + ")";
                    contents.add(createIndexSql);
                });
            }
        }
    }

    public AlgoProcedure setAlgoProcedure(Integer dataSpaceId, Integer strategyBatchId, String indicatorBizCode, Map<String, AlgoParamResVo> paramMap) {
        AlgoProcedure algoProcedure = AlgoProcedure.getInstance(dataSpaceId, strategyBatchId, indicatorBizCode, paramMap);
        proceThreadLocal.set(algoProcedure);
        return algoProcedure;
    }
}
