package com.kbao.algo.indicator;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.exception.BusinessException;
import com.kbao.tool.util.IDUtils;
import io.swagger.models.auth.In;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class AlgoIndicatorHelper {

    public static JSONObject indicatorNodesToTree(List<JSONObject> nodes) {
        Map<String, JSONObject> nodeMap = nodes.stream()
                .collect(Collectors.toMap(node -> node.getString("nodeId"), Function.identity(), (a, b) -> a));

        nodes.forEach(node -> {
            JSONArray childList = node.getJSONArray("childIds");
            if (CollectionUtils.isEmpty(childList)) {
                return;
            }
            JSONArray children = new JSONArray();
            for (int i = 0; i < childList.size(); i++) {
                String childId = childList.getString(i);
                JSONObject childNode = nodeMap.remove(childId);
                if (childNode == null) {
                    throw new BusinessException("节点"+ childId +"不存在或已被使用");
                }
                children.add(childNode);
            }
            node.put("children", children);
        });
        Collection<JSONObject> mainNodes = nodeMap.values();
        if (CollectionUtils.isEmpty(mainNodes) || mainNodes.size() > 1) {
            throw new BusinessException("存在多个输出节点或者闭环节点");
        }
        return mainNodes.iterator().next();
    }

    public static List<JSONObject> indicatorTreeToNodes(JSONObject tree) {
        List<JSONObject> nodes = new ArrayList<>();
        nodes.add(tree);
        JSONArray childList = tree.getJSONArray("children");
        tree.remove("children");
        if (CollectionUtils.isEmpty(childList)) {
            return nodes;
        }
        List<String> childIds = new ArrayList<>();
        for (int i = 0; i < childList.size(); i++) {
            JSONObject children = childList.getJSONObject(i);
            childIds.add(children.getString("nodeId"));
            nodes.addAll(indicatorTreeToNodes(children));
        }
        tree.put("childIds", childIds);
        return nodes;
    }

    public static JSONObject findTreeNode(JSONObject tree, String indicatorNodeId) {
        if (tree.getString("nodeId").equals(indicatorNodeId)) {
            return tree;
        }
        JSONArray childList = tree.getJSONArray("children");
        if (CollectionUtils.isEmpty(childList)) {
            return null;
        }
        for (int i = 0; i < childList.size(); i++) {
            JSONObject children = childList.getJSONObject(i);
            JSONObject item = findTreeNode(children, indicatorNodeId);
            if (item != null) {
                return item;
            }
        }
        return null;
    }

    public static JSONArray getDefaultAndCustom(String nodeName, JSONObject fields, String defaultItem, String customItem, String msg) {
        JSONArray fieldList = new JSONArray();
        if (fields == null) {
            return fieldList;
        }
        JSONArray defaultArray = fields.getJSONArray(defaultItem);
        if (CollectionUtils.isNotEmpty(defaultArray)) {
            fieldList.addAll(defaultArray);
        }
        JSONArray custom = fields.getJSONArray(customItem);
        if (CollectionUtils.isNotEmpty(custom)) {
            fieldList.addAll(custom);
        }
        if (StringUtils.isNotEmpty(nodeName) && fieldList.isEmpty() && StringUtils.isNotEmpty(msg)) {
            throw new BusinessException(nodeName + "：" + msg);
        }
        return fieldList;
    }

    public static JSONArray getOutputFields(JSONObject content) {
        // 保存输出字段
        JSONObject output = content.getJSONObject("output");
        JSONObject fields = output.getJSONObject("fields");
        return getDefaultAndCustom(null, fields, "default", "custom", null);
    }

    public static String getVersionNum() {
        return IDUtils.generateBizId("V").replace("-", "");
    }
}
