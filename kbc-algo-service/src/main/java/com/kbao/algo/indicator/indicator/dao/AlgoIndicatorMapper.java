package com.kbao.algo.indicator.indicator.dao;

import com.kbao.algo.indicator.bean.AlgoNodeResVo;
import com.kbao.algo.indicator.entity.AlgoIndicator;
import com.kbao.algo.strategyBatchNode.bean.StrategyBatchNodeDataReqVo;
import com.kbao.kbcbsc.dao.sql.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR> jie
* @Description Dao类
* @Date 2024-10-25
*/
public interface AlgoIndicatorMapper  extends BaseMapper<AlgoIndicator, Integer>{

    List<AlgoNodeResVo> getNodeList(@Param("tenantId") String tenantId);

    AlgoIndicator getByCode(@Param("bizCode") String bizCode);

    int isExistIndicator(@Param("bizCode") String bizCode, @Param("indicatorId") Integer indicatorId);

    int updateRemark(@Param("remark") String remark,
                     @Param("version") String version,
                     @Param("updateId") String updateId,
                     @Param("indicatorId") Integer indicatorId);

    int isExistIndicatorData(@Param("strategyBatchId") Integer strategyBatchId, @Param("tableName") String tableName);

    void delIndicatorDataByBatchId(@Param("strategyBatchId") Integer strategyBatchId, @Param("tableName") String tableName);

    void dropProcedure(@Param("procedureName") String procedureName);
    void createProcedure(@Param("procedureName") String procedureName, @Param("content") String content);

    void callProcedure(@Param("procedureName") String procedureName);

    List<Map> getIndicatorData(StrategyBatchNodeDataReqVo param);
}