package com.kbao.algo.indicator.indicator.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.kbao.algo.dataSpace.entity.AlgoDataSpace;
import com.kbao.algo.dataSpace.service.AlgoDataSpaceService;
import com.kbao.algo.indicator.AlgoIndicatorHelper;
import com.kbao.algo.indicator.bean.AlgoIndicatorConfigVo;
import com.kbao.algo.indicator.bean.AlgoNodeResVo;
import com.kbao.algo.indicator.bean.ProcedureDelVo;
import com.kbao.algo.indicator.indicator.dao.AlgoIndicatorMapper;
import com.kbao.algo.indicator.entity.AlgoIndicator;
import com.kbao.algo.indicator.entity.AlgoIndicatorContent;
import com.kbao.algo.indicator.indicatorField.bean.DataField;
import com.kbao.algo.indicator.indicatorField.entity.AlgoIndicatorField;
import com.kbao.algo.indicator.indicatorVersion.service.AlgoIndicatorVersionService;
import com.kbao.algo.series.service.SeriesDataService;
import com.kbao.algo.source.bean.AlgoSourceTableDto;
import com.kbao.algo.sourceField.bean.AlgoParamResVo;
import com.kbao.algo.strategyBatchNode.bean.StrategyBatchNodeDataReqVo;
import com.kbao.algo.util.AlgoConstants;
import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.SysLoginUtils;
import com.kbao.algo.indicator.bean.AlgoIndicatorReqVo;
import com.kbao.algo.indicator.indicatorField.service.AlgoIndicatorFieldService;
import com.kbao.algo.source.service.AlgoSourceService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR> jie
* @Description Service类
* @Date 2024-10-25
*/
@Service
public class AlgoIndicatorService extends BaseSQLServiceImpl<AlgoIndicator, Integer, AlgoIndicatorMapper> {
    @Autowired
    private SeriesDataService seriesDataService;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private AlgoIndicatorFieldService algoIndicatorFieldService;
    @Autowired
    private AlgoProcedureService algoProcedureService;
    @Autowired
    private AlgoSourceService algoSourceService;
    @Autowired
    private AlgoDataSpaceService algoDataSpaceService;
    @Autowired
    private AlgoIndicatorVersionService algoIndicatorVersionService;

    public PageInfo<AlgoIndicator> indicatorPage(RequestObjectPage<AlgoIndicatorReqVo> reqVo) {
        AlgoIndicatorReqVo param = reqVo.getParam();
        param.setTenantId(SysLoginUtils.getUser().getTenantId());
        return this.page(reqVo);
    }

    public void save(AlgoIndicator indicator) {
        //编码校验
        if(EmptyUtils.isEmpty(indicator.getBizCode())){
            throw new BusinessException("指标编码不能为空");
        }
        int count = mapper.isExistIndicator(indicator.getBizCode(), indicator.getIndicatorId());
        if(count > 0){
            throw new BusinessException("该指标编码已存在");
        }
        if (indicator.getIndicatorId() == null) {
            indicator.setCreateId(BscUserUtils.getUserId());
            indicator.setTenantId(SysLoginUtils.getUser().getTenantId());
            this.insert(indicator);
        } else {
            indicator.setUpdateId(BscUserUtils.getUserId());
            this.updateByPrimaryKeySelective(indicator);
        }
    }

    public JSONObject checkConfig(AlgoIndicatorConfigVo configVo) {
        JSONObject content = configVo.getContent();
        if (EmptyUtils.isEmpty(content)) {
            content = AlgoIndicatorHelper.indicatorNodesToTree(configVo.getNodeList());
        }
        AlgoIndicator indicator = this.selectByPrimaryKey(configVo.getIndicatorId());
        // 检查构建存储过程是否会报错
        try {
            algoProcedureService.buildProcedure(0,0, indicator.getBizCode(), content, new HashMap<>());
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("生成存储过程失败", e);
            throw new BusinessException("生成存储过程失败，请检查配置");
        }
        return content;
    }

    public void config(AlgoIndicatorConfigVo configVo) {
        JSONObject content = checkConfig(configVo);
        // 保存输出字段
        JSONArray outputFields = AlgoIndicatorHelper.getOutputFields(content);
        algoIndicatorFieldService.saveIndicatorField(configVo.getIndicatorId(), outputFields);
        // 保存配置内容
        AlgoIndicatorContent indicatorContent = new AlgoIndicatorContent(configVo.getIndicatorId(), content, new Date());
        mongoTemplate.save(indicatorContent);
        // 更新备注信息
        String version = AlgoIndicatorHelper.getVersionNum();
        mapper.updateRemark(configVo.getRemark(), version, BscUserUtils.getUserId(), configVo.getIndicatorId());
        // 保存版本数据
        algoIndicatorVersionService.saveHistoryVersion(configVo.getIndicatorId(), content, configVo.getRemark(), version);
    }

    public AlgoIndicatorConfigVo getConfig(Integer id) {
        AlgoIndicatorConfigVo configVo = new AlgoIndicatorConfigVo();
        AlgoIndicator indicator = this.selectByPrimaryKey(id);
        AlgoIndicatorContent content = mongoTemplate.findById(id, AlgoIndicatorContent.class);
        if (content != null) {
            List<JSONObject> nodes = AlgoIndicatorHelper.indicatorTreeToNodes(content.getContent());
            configVo.setNodeList(nodes);
        }
        configVo.setIndicatorId(indicator.getIndicatorId());
        return configVo;
    }

    public JSONObject getJSONConfigById(Integer id) {
        AlgoIndicatorContent content = mongoTemplate.findById(id, AlgoIndicatorContent.class);
        if (content == null) {
            return null;
        }
        return content.getContent();
    }

    public AlgoIndicator getByCode(String indicatorBizCode) {
        AlgoIndicator indicator = mapper.getByCode(indicatorBizCode);
        AlgoIndicatorContent content = mongoTemplate.findById(indicator.getIndicatorId(), AlgoIndicatorContent.class);
        if (content != null) {
            indicator.setContent(content.getContent());
        }
        return indicator;
    }

    public AlgoSourceTableDto getIndicatorTable(Integer dataSpaceId, String bizCode) {
        AlgoIndicator indicator = mapper.getByCode(bizCode);
        List<AlgoIndicatorField> fields = algoIndicatorFieldService.listBySourceId(indicator.getIndicatorId());
        if (CollectionUtils.isEmpty(fields)) {
            throw new BusinessException("无输出属性");
        }
        String tableName = AlgoConstants.getIndicatorTableName(indicator.getBizCode(), dataSpaceId);
        List<AlgoSourceTableDto.Field> fieldList = fields.stream().map(item -> new AlgoSourceTableDto.Field(item.getFieldName(), item.getName())).collect(Collectors.toList());
        return new AlgoSourceTableDto(tableName, fieldList);
    }

    public void executeCalcIndicator(Integer strategyBatchId, String indicatorBizCode, String isUpdate, List<AlgoParamResVo> params) {
        AlgoIndicator indicator = this.getByCode(indicatorBizCode);
        AlgoDataSpace dataSpace = algoDataSpaceService.getByStrategyBatchId(strategyBatchId);
        String indicatorTableName = AlgoConstants.getIndicatorTableName(indicatorBizCode, dataSpace.getId());
        boolean isExists = this.isExistIndicatorData(strategyBatchId, indicatorTableName);
        if (isExists) {
            if ("0".equals(isUpdate)) {
                return;
            }
            mapper.delIndicatorDataByBatchId(strategyBatchId, indicatorTableName);
        }
        List<AlgoParamResVo> indicatorFields = algoIndicatorFieldService.getIndicatorFields(indicatorBizCode);
        if (CollectionUtils.isEmpty(indicatorFields)) {
            throw new RuntimeException(indicatorBizCode+"：指标项未配置输出字段，请检查配置");
        }
        List<DataField> dataFields = algoIndicatorFieldService.getIndicatorDataFields(dataSpace.getId(), indicatorBizCode);
        if (dataFields != null) {
            Set<String> oldFields = dataFields.stream().map(DataField::getFieldName).collect(Collectors.toSet());
            Set<String> indFields = indicatorFields.stream().map(AlgoParamResVo::getFieldName).collect(Collectors.toSet());
            if (!oldFields.equals(indFields)) {
                throw new RuntimeException(indicatorBizCode+"：该数据空间指标字段发生变更，与历史批次指标字段不一致");
            }
        }
        Map<String, AlgoParamResVo> paramMap = params.stream().collect(Collectors.toMap(AlgoParamResVo::getFieldName, Function.identity()));
        String procedureName = algoProcedureService.buildProcedure(dataSpace.getId(), strategyBatchId, indicator.getBizCode(), indicator.getContent(), paramMap);
        try {
            mapper.callProcedure(procedureName);
            mapper.dropProcedure(procedureName);
        } catch (Exception e) {
            logger.error("执行存储过程失败, procedureName:{}", procedureName);
            throw e;
        }
        if (dataFields == null) {
            algoIndicatorFieldService.saveIndicatorDataFields(dataSpace.getId(), indicatorBizCode, indicatorFields);
        }
    }

    public boolean isExistIndicatorData(Integer strategyBatchId, String tableName) {
        if (!algoSourceService.isExistSourceTable(tableName)
                || !algoSourceService.isExistField(tableName, AlgoConstants.STRATEGY_BATCH_ID_FIELD)) {
            return false;
        }
        return mapper.isExistIndicatorData(strategyBatchId, tableName) > 0;
    }

    public void dropProcedure(String procedureName) {
        mapper.dropProcedure(procedureName);
    }

    public void createProcedure(String procedureName, String content) {
        mapper.createProcedure(procedureName, content);
    }

    public List<AlgoNodeResVo> getNodeList() {
        return mapper.getNodeList(SysLoginUtils.getUser().getTenantId());
    }

    public void dropIndicatorTable(String indicatorBizCode, Integer dataSpaceId) {
        String tableName = AlgoConstants.getIndicatorTableName(indicatorBizCode, dataSpaceId);
        algoSourceService.dropTable(tableName);
        //删除指标数据字段
        algoIndicatorFieldService.delIndicatorDataFields(dataSpaceId, indicatorBizCode);
    }

    public void dropIndicatorProcedure(ProcedureDelVo vo) {
        for (Integer strategyBatchId : vo.getStrategyBatchIds()) {
            String procedureName = AlgoConstants.getProcedureName(vo.getIndicatorBizCode(), strategyBatchId);
            this.dropProcedure(procedureName);
        }
    }

    public List<Map> getIndicatorData(StrategyBatchNodeDataReqVo param) {
        return mapper.getIndicatorData(param);
    }

    public String getNextId() {
        return seriesDataService.getNextId("algo_indicator", "IND", 6);
    }
}
