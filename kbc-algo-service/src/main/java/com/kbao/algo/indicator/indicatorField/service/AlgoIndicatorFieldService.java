package com.kbao.algo.indicator.indicatorField.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kbao.algo.indicator.indicatorField.bean.DataField;
import com.kbao.algo.indicator.indicatorField.entity.AlgoIndicatorDataField;
import com.kbao.algo.indicator.indicatorField.entity.AlgoIndicatorField;
import com.kbao.algo.sourceField.bean.AlgoParamResVo;
import com.kbao.algo.util.AlgoContext;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.tool.util.SysLoginUtils;
import com.kbao.algo.indicator.indicatorField.dao.AlgoIndicatorFieldMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR> jie
* @Description Service类
* @Date 2024-11-01
*/
@Service
public class AlgoIndicatorFieldService extends BaseSQLServiceImpl<AlgoIndicatorField, Integer, AlgoIndicatorFieldMapper> {
    @Autowired
    private MongoTemplate mongoTemplate;

    public List<AlgoIndicatorField> listBySourceId(Integer indicatorId) {
        Map<String, Object> param = new HashMap<>();
        param.put("indicatorId", indicatorId);
        param.put("tenantId", AlgoContext.getTenantId());
        return this.selectByParam(param);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveIndicatorField(Integer indicatorId, JSONArray outputFields) {
        String createId = BscUserUtils.getUserId();
        String tenantId = SysLoginUtils.getUser().getTenantId();

        List<AlgoIndicatorField> list = new ArrayList<>();
        for (int i = 0; i < outputFields.size(); i++) {
            JSONObject field = outputFields.getJSONObject(i);
            AlgoIndicatorField indicatorField = AlgoIndicatorField.builder()
                    .indicatorId(indicatorId).name(field.getString("fieldDesc"))
                    .fieldName(field.getString("fieldName"))
                    .createId(createId).tenantId(tenantId).build();
            list.add(indicatorField);
        }
        mapper.delIndicatorField(indicatorId);
        this.batchInsert(list);
    }

    public List<AlgoParamResVo> getIndicatorFields(String bizCode) {
        return mapper.getIndicatorFields(bizCode);
    }

    public void saveIndicatorDataFields(Integer dataSpaceId, String indicatorBizCode, List<AlgoParamResVo> indicatorFields) {
        List<DataField> fields = indicatorFields.stream().map(item -> new DataField(item.getFieldName(), item.getFieldDesc(), null))
                .collect(Collectors.toList());
        AlgoIndicatorDataField dataField = new AlgoIndicatorDataField(dataSpaceId, indicatorBizCode, fields);
        mongoTemplate.save(dataField);
    }

    public List<DataField> getIndicatorDataFields(Integer dataSpaceId, String indicatorBizCode) {
        Query query = new Query(Criteria.where("dataSpaceId").is(dataSpaceId)
                .and("indicatorCode").is(indicatorBizCode));
        AlgoIndicatorDataField dataField = mongoTemplate.findOne(query, AlgoIndicatorDataField.class);
        if (dataField == null) {
            return null;
        }
        return dataField.getFields();
    }

    public void delIndicatorDataFields(Integer dataSpaceId, String indicatorBizCode) {
        Query query = new Query(Criteria.where("dataSpaceId").is(dataSpaceId)
                .and("indicatorCode").is(indicatorBizCode));
        mongoTemplate.remove(query, AlgoIndicatorDataField.class);
    }
}
