package com.kbao.algo.indicator.indicatorSelectSql.service;

import com.kbao.algo.indicator.indicatorSelectSql.entity.AlgoIndicatorSelectSql;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class AlgoIndicatorSelectSqlService {
    @Autowired
    private MongoTemplate mongoTemplate;

    public void save(Integer strategyBatchId, String indicatorBizCode, String selectSql) {
        AlgoIndicatorSelectSql sql = new AlgoIndicatorSelectSql(strategyBatchId, indicatorBizCode, selectSql, new Date());
        mongoTemplate.save(sql);
    }
}
