package com.kbao.algo.strategyNode.service;

import com.kbao.algo.strategy.bean.AlgoStrategyVo;
import com.kbao.algo.strategyNode.bean.AlgoStrategyNodeVo;
import com.kbao.algo.strategyNode.dao.AlgoStrategyNodeMapper;
import com.kbao.algo.strategyNode.entity.AlgoStrategyNode;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.tool.util.EmptyUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @Description 算法规则-策略执行节点Service类
* @Date 2024-10-28
*/
@Service
public class AlgoStrategyNodeService extends BaseSQLServiceImpl<AlgoStrategyNode, Integer, AlgoStrategyNodeMapper> {


    /**
     * 插入算法策略节点
     *
     * @param nodeList 算法策略节点列表
     * @param strategyId 策略ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void insert(List<AlgoStrategyNodeVo> nodeList, Integer strategyId) {
        //先删除在新增
        this.mapper.deleteByStrategyId(strategyId);
        if(EmptyUtils.isNotEmpty(nodeList)){
            String currentUserId = BscUserUtils.getUserId();
            String tenantId = BscUserUtils.getUser().getUser().getTenantId();
            for(AlgoStrategyNodeVo node: nodeList){
                AlgoStrategyNode algoStrategyNodeParent = new AlgoStrategyNode();
                BeanUtils.copyProperties(node, algoStrategyNodeParent, "nodeType","nodeId");
                algoStrategyNodeParent.setStrategyId(strategyId);
                algoStrategyNodeParent.setNodeSort(nodeList.indexOf(node));
                algoStrategyNodeParent.setCreateTime(new Date());
                algoStrategyNodeParent.setCreateId(currentUserId);
                algoStrategyNodeParent.setUpdateTime(new Date());
                algoStrategyNodeParent.setUpdateId(currentUserId);
                algoStrategyNodeParent.setTenantId(tenantId);
                this.mapper.insertSelective(algoStrategyNodeParent);
                //保存子类数据,最多只支持2级,故不需要递归处理
                if(EmptyUtils.isNotEmpty(node.getChildren())){
                    for(AlgoStrategyNodeVo child: node.getChildren()){
                        AlgoStrategyNode algoStrategyNodeChild = new AlgoStrategyNode();
                        BeanUtils.copyProperties(child, algoStrategyNodeChild, "nodeId");
                        algoStrategyNodeChild.setStrategyId(strategyId);
                        algoStrategyNodeChild.setNodeSort(node.getChildren().indexOf(child));
                        algoStrategyNodeChild.setCreateTime(new Date());
                        algoStrategyNodeChild.setCreateId(currentUserId);
                        algoStrategyNodeChild.setUpdateTime(new Date());
                        algoStrategyNodeChild.setUpdateId(currentUserId);
                        algoStrategyNodeChild.setTenantId(tenantId);
                        algoStrategyNodeChild.setParentId(algoStrategyNodeParent.getId());
                        this.mapper.insertSelective(algoStrategyNodeChild);
                    }
                }
            }
        }
    }

    /**
     * 根据策略ID查询策略节点信息列表
     *
     * @param strategyId 策略ID
     * @return 返回策略节点信息列表的VO对象
     */
    public List<AlgoStrategyNodeVo> selectByStrategyId(Integer strategyId) {
        List<AlgoStrategyNode> algoStrategyNodes = mapper.selectByStrategyId(strategyId);
        //获取父类数据，并且排序
        List<AlgoStrategyNode> parentList = new ArrayList<>();
        for (AlgoStrategyNode algoStrategyNode : algoStrategyNodes) {
            if (algoStrategyNode.getParentId() == null) {
                parentList.add(algoStrategyNode);
            }
        }
        parentList.sort(Comparator.comparingInt(AlgoStrategyNode::getNodeSort));
        //将父类数据转换为VO对象
        List<AlgoStrategyNodeVo> result = new ArrayList<>();
        for (AlgoStrategyNode parent : parentList) {
            AlgoStrategyNodeVo vo = new AlgoStrategyNodeVo();
            BeanUtils.copyProperties(parent, vo, "strategyId");
            vo.setNodeId(parent.getId().toString());
            //获取子类数据，并且按排序
            List<AlgoStrategyNodeVo> childList = new ArrayList<>();
            for (AlgoStrategyNode child : algoStrategyNodes) {
                if (child.getParentId() != null && child.getParentId().equals(parent.getId())) {
                    AlgoStrategyNodeVo childVo = new AlgoStrategyNodeVo();
                    BeanUtils.copyProperties(child, childVo, "parentId", "strategyId");
                    childVo.setNodeId(child.getId().toString());
                    childList.add(childVo);
                }
            }
            childList.sort(Comparator.comparingInt(AlgoStrategyNodeVo::getNodeSort));
            vo.setChildren(childList);
            result.add(vo);
        }
        return result;
    }
    /**
     * 根据策略ID查询策略节点信息列表
     *
     * @param strategyId 策略ID
     * @return 返回策略节点信息列表
     */
    public List<AlgoStrategyNode> list(Integer strategyId) {
        List<AlgoStrategyNodeVo> algoStrategyNodes = this.selectByStrategyId(strategyId);
        List<AlgoStrategyNode> result = new ArrayList<>();
        for(AlgoStrategyNodeVo algoStrategyNodeVoMain : algoStrategyNodes){
            AlgoStrategyNode algoStrategyNodeMain = new AlgoStrategyNode();
            BeanUtils.copyProperties(algoStrategyNodeVoMain, algoStrategyNodeMain);
            result.add(algoStrategyNodeMain);
            for (AlgoStrategyNodeVo algoStrategyNodeVoChild : algoStrategyNodeVoMain.getChildren()) {
                AlgoStrategyNode algoStrategyNodeChild = new AlgoStrategyNode();
                BeanUtils.copyProperties(algoStrategyNodeVoChild, algoStrategyNodeChild);
                result.add(algoStrategyNodeChild);
            }
        }
        return result;
    }

    /**
     * 插入算法策略节点
     *
     * @param algoStrategyVo 算法策略节点列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void insert(AlgoStrategyVo algoStrategyVo) {
        //先删除在新增
        this.mapper.deleteByStrategyId(algoStrategyVo.getStrategyId());
        if(EmptyUtils.isNotEmpty(algoStrategyVo.getNodeList())){
            String currentUserId = BscUserUtils.getUserId();
            String tenantId = BscUserUtils.getUser().getUser().getTenantId();
            for(AlgoStrategyNodeVo node: algoStrategyVo.getNodeList()){
                AlgoStrategyNode algoStrategyNodeParent = new AlgoStrategyNode();
                BeanUtils.copyProperties(node, algoStrategyNodeParent, "nodeId");
                algoStrategyNodeParent.setStrategyId(algoStrategyVo.getStrategyId());
                algoStrategyNodeParent.setCreateTime(new Date());
                algoStrategyNodeParent.setCreateId(currentUserId);
                algoStrategyNodeParent.setUpdateTime(new Date());
                algoStrategyNodeParent.setUpdateId(currentUserId);
                algoStrategyNodeParent.setTenantId(tenantId);
                this.mapper.insertSelective(algoStrategyNodeParent);
                //保存子类数据,最多只支持2级,故不需要递归处理
                if(EmptyUtils.isNotEmpty(node.getChildren())){
                    for(AlgoStrategyNodeVo child: node.getChildren()){
                        AlgoStrategyNode algoStrategyNodeChild = new AlgoStrategyNode();
                        BeanUtils.copyProperties(child, algoStrategyNodeChild, "nodeId");
                        algoStrategyNodeChild.setStrategyId(algoStrategyVo.getStrategyId());
                        algoStrategyNodeChild.setCreateTime(new Date());
                        algoStrategyNodeChild.setCreateId(currentUserId);
                        algoStrategyNodeChild.setUpdateTime(new Date());
                        algoStrategyNodeChild.setUpdateId(currentUserId);
                        algoStrategyNodeChild.setTenantId(tenantId);
                        algoStrategyNodeChild.setParentId(algoStrategyNodeParent.getId());
                        this.mapper.insertSelective(algoStrategyNodeChild);
                    }
                }
            }
        }
    }
}
