package com.kbao.algo.strategyNode.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kbao.algo.strategy.bean.AlgoStrategyVo;
import com.kbao.algo.strategy.entity.AlgoStrategy;
import com.kbao.algo.strategyNode.bean.AlgoStrategyNodeVo;
import com.kbao.algo.strategyNode.dao.AlgoStrategyNodeFlowDao;
import com.kbao.algo.strategyNode.entity.AlgoStrategyNodeFlow;
import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.algo.strategy.service.AlgoStrategyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 策略节点流程业务处理
 * @author: xiaojiayao
 * @time: 2024/12/31 17:19
 */
@Service
public class AlgoStrategyNodeFlowService extends BaseMongoServiceImpl<AlgoStrategyNodeFlow, String, AlgoStrategyNodeFlowDao> {

    @Autowired
    private AlgoStrategyService algoStrategyService;

    @Autowired
    private AlgoStrategyNodeService algoStrategyNodeService;

    public AlgoStrategyVo saveFlow(AlgoStrategyNodeFlow algoStrategyNodeFlow) {
        //校验策略节点
        checkStrategyNode(algoStrategyNodeFlow);
        //处理流程数据，将其变成简单树形结构
        List<AlgoStrategyNodeVo> algoStrategyNodeVos = this.flowStructureConverter(algoStrategyNodeFlow.getNodeList());
        //查询策略信息
        AlgoStrategy algoStrategy = algoStrategyService.getMapper().selectByPrimaryKey(algoStrategyNodeFlow.getStrategyId());
        //根据策略id查询节点流程数据
        Query existQuery = new Query();
        existQuery.addCriteria(Criteria.where("strategyId").is(algoStrategyNodeFlow.getStrategyId()));
        AlgoStrategyNodeFlow exist = this.findOne(existQuery);
        String currentUserId = BscUserUtils.getUserId();
        String tenantId = BscUserUtils.getUser().getUser().getTenantId();
        if(EmptyUtils.isNotEmpty(exist)){
            Query updateQuery = new Query(Criteria.where("_id").is(exist.getId()));
            Update update = new Update();
            update.set("strategyCode", algoStrategy.getBizCode());
            update.set("nodeList", algoStrategyNodeFlow.getNodeList());
            update.set("updateId", currentUserId);
            update.set("updateTime", new Date());
            this.update(updateQuery, update);
        } else {
            algoStrategyNodeFlow.setStrategyCode(algoStrategy.getBizCode());
            algoStrategyNodeFlow.setCreateId(currentUserId);
            algoStrategyNodeFlow.setCreateTime(new Date());
            algoStrategyNodeFlow.setTenantId(tenantId);
            this.save(algoStrategyNodeFlow);
        }
        AlgoStrategyVo algoStrategyVo = new AlgoStrategyVo();
        algoStrategyVo.setStrategyId(algoStrategyNodeFlow.getStrategyId());
        algoStrategyVo.setNodeList(algoStrategyNodeVos);
        algoStrategyNodeService.insert(algoStrategyVo);
        return algoStrategyVo;
    }

    /**
     * 检查算法策略节点流配置的正确性。
     *
     * @param algoStrategyNodeFlow 算法策略节点流配置对象
     * @throws BusinessException 当策略节点配置不符合要求时抛出此异常
     */
    public void checkStrategyNode(AlgoStrategyNodeFlow algoStrategyNodeFlow) {
        //策略节点校验
        if (EmptyUtils.isEmpty(algoStrategyNodeFlow.getNodeList())) {
            throw new BusinessException("请连接必要节点，策略节点数据不能为空");
        }
        //校验策略节点是否连接完整
        JSONArray nodeList = algoStrategyNodeFlow.getNodeList();
        Boolean isStartNodeFlag = false;
        Boolean isEndNodeFlag = false;
        String startNodeId = null;
        for (int i = 0; i < nodeList.size(); i++) {
            JSONObject node = nodeList.getJSONObject(i);
            if ("开始".equals(node.getString("nodeName"))) {
                isStartNodeFlag = true;
                startNodeId = node.getString("nodeId");
            }
            if ("结束".equals(node.getString("nodeName"))) {
                isEndNodeFlag = true;
            }
        }
        if (!isStartNodeFlag) {
            throw new BusinessException("请连接必要节点，未找到开始节点");
        }
        if (!isEndNodeFlag) {
            throw new BusinessException("请连接必要节点，未找到结束节点");
        }
        //校验节点是否连接完整
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = null;
        try {
            rootNode = objectMapper.readTree(JSONObject.toJSONString(algoStrategyNodeFlow));
            JsonNode jsonNodeList = rootNode.path("nodeList");
            Map<String, List<String>> connections = new HashMap<>();
            Set<String> nodeIds = new HashSet<>();
            // 构建连接图
            for (JsonNode node : jsonNodeList) {
                String nodeId = node.path("nodeId").asText();
                nodeIds.add(nodeId);
                JsonNode transferRules = node.path("transferRuleList");

                for (JsonNode rule : transferRules) {
                    String nextNodeId = rule.path("nextNodeId").asText();
                    connections.computeIfAbsent(nodeId, k -> new ArrayList<>()).add(nextNodeId);
                }
            }
            // 检查连接完整性
            boolean isComplete = checkCompleteness(connections, nodeIds, startNodeId);
            if (!isComplete) {
                throw new BusinessException("请连接必要节点，策略节点未完全连接");
            }
        } catch (JsonProcessingException e) {
            throw new BusinessException("策略节点数据格式不正确");
        }
        //校验父节点只能是自定义节点、子节点里面不能有自定义节点
    }

    private static boolean checkCompleteness(Map<String, List<String>> connections, Set<String> nodeIds, String startNodeId) {
        Set<String> visited = new HashSet<>();
        Queue<String> queue = new LinkedList<>();
        queue.add(startNodeId);

        while (!queue.isEmpty()) {
            String currentNode = queue.poll();
            if (!visited.contains(currentNode)) {
                visited.add(currentNode);
                List<String> nextNodes = connections.get(currentNode);
                if (nextNodes != null) {
                    queue.addAll(nextNodes);
                }
            }
        }

        // 确保所有节点均被访问（如果期望的是所有节点都能到达结束节点）
        return visited.equals(nodeIds);
    }
    /**
     * 根据策略ID查找算法策略节点流配置
     *
     * @param strategyId 策略ID
     * @return 根据策略ID找到的算法策略节点流配置对象，如果未找到则返回null
     */
    public AlgoStrategyNodeFlow nodeFlowConfigFind(Integer strategyId){
        Query query = new Query();
        query.addCriteria(Criteria.where("strategyId").is(strategyId));
        AlgoStrategyNodeFlow algoStrategyNodeFlow = this.findOne(query);
        return algoStrategyNodeFlow;
    }

    /**
     * 将 JSONArray 类型的节点列表转换为包含 AlgoStrategyNodeVo 对象的 List。
     *
     * @param nodeList JSONArray 类型的节点列表，每个节点为一个 JSONObject 对象。
     * @return 包含转换后的 AlgoStrategyNodeVo 对象的 List。
     */
    private List<AlgoStrategyNodeVo> flowStructureConverter(JSONArray nodeList){
        String startNodeId = null;
        String endNodeId = null;
        // 建立 nodeId 到节点的映射
        Map<String, JSONObject> nodeMap = new HashMap<>();
        for (int i = 0; i < nodeList.size(); i++) {
            JSONObject node = nodeList.getJSONObject(i);
            nodeMap.put(node.getString("nodeId"), node);
            // 初始化 isParent 为 0
            node.put("isParent", 0);
            if ("开始".equals(node.getString("nodeName"))) {
                startNodeId = node.getString("nodeId");
            }
            if ("结束".equals(node.getString("nodeName"))) {
                endNodeId = node.getString("nodeId");
            }
        }

        // 查找开始节点
        JSONObject startNode = nodeMap.get(startNodeId);
        JSONObject endNode = nodeMap.get(endNodeId);

        // 执行标记父节点的操作
        if (startNode != null) {
            markParentNodes(startNode, nodeMap, endNode);
        }

        List<JSONObject> parentsList = new ArrayList<>();

        // 填充节点映射并识别父节点
        for (int i = 0; i < nodeList.size(); i++) {
            JSONObject node = nodeList.getJSONObject(i);
            if ("1".equals(node.getString("isParent"))) {
                // 收集父节点到列表中
                parentsList.add(node);
            }
        }
        // 从“开始”节点开始，赋值 nodeSort
        int sortIndex = 1;
        assignNodeSort(startNodeId, sortIndex, nodeMap);

        List<AlgoStrategyNodeVo> converterNodeList = new ArrayList<>();
        // 处理每个父节点以构建 JSON 结构
        for (JSONObject parentNode : parentsList) {
            // 过滤开始和结束节点
            if (parentNode.getString("nodeId").equals(startNodeId) || parentNode.getString("nodeId").equals(endNodeId)) {
                continue;
            }
            JSONObject newNode = createNodeWithChildren(parentNode, nodeMap);
            AlgoStrategyNodeVo algoStrategyNodeVo = newNode.toJavaObject(AlgoStrategyNodeVo.class);
            converterNodeList.add(algoStrategyNodeVo);
        }
        return converterNodeList;
    }

    /**
     * 标记所有从给定结束节点到起始节点的父节点。
     *
     * @param node      当前处理的节点对象
     * @param nodeMap   包含所有节点及其对应ID的映射
     * @param endNode   结束节点对象，用于标识路径的终点
     * @return 如果在递归过程中找到了结束节点或其父节点，则返回true；否则返回false
     */
    // 递归遍历节点并标记父节点
    private static boolean markParentNodes(JSONObject node, Map<String, JSONObject> nodeMap, JSONObject endNode) {
        // 检查当前节点是否已标记为父节点
        if (node.getInteger("isParent") == 1) {
            return false; // 如果已经标记为父节点，直接返回
        }

        // 如果当前节点是结束节点，直接返回
        if (node.equals(endNode)) {
            node.put("isParent", 1); // 标记为父节点
            return true;
        }

        // 查找当前节点的转移规则
        boolean foundEnding = false;
        if (node.getJSONArray("transferRuleList") != null) {
            JSONArray transferRules = node.getJSONArray("transferRuleList");
            for (int i = 0; i < transferRules.size(); i++) {
                JSONObject transferRule = transferRules.getJSONObject(i);
                String nextNodeId = transferRule.getString("nextNodeId");

                // 查找下一个节点
                if (nodeMap.containsKey(nextNodeId)) {
                    JSONObject nextNode = nodeMap.get(nextNodeId);
                    // 递归标记下一个节点
                    foundEnding |= markParentNodes(nextNode, nodeMap, endNode);
                }
            }
        }

        // 如果下一级节点中有任何一个标记为父节点，则当前节点也标记为父节点
        if (foundEnding) {
            node.put("isParent", 1);
        }
        return foundEnding;
    }

    /**
     * 递归为节点分配 nodeSort 值，子节点不参与排序。
     *
     * @param nodeId       当前节点的ID
     * @param currentSortIndex 当前排序索引
     * @param nodeMap      包含所有节点的映射，键为节点ID，值为节点对象
     * @return 下一个排序值
     */
    // 递归为节点分配 nodeSort 值，子节点不参与排序
    private static int assignNodeSort(String nodeId, int currentSortIndex, Map<String, JSONObject> nodeMap) {
        JSONObject currentNode = nodeMap.get(nodeId);
        if ("1".equals(currentNode.getString("isParent"))) {
            //开始和结束节点不参与排序
            if (!"开始".equals(currentNode.getString("nodeName")) && !"结束".equals(currentNode.getString("nodeName"))) {
                currentNode.put("nodeSort", currentSortIndex++); // 设置当前节点的排序值
            }
            // 如果有转移规则，处理每一个规则
            if (currentNode.getJSONArray("transferRuleList") != null) {
                JSONArray transferRules = currentNode.getJSONArray("transferRuleList");
                for (int i = 0; i < transferRules.size(); i++) {
                    JSONObject transferRule = transferRules.getJSONObject(i);
                    String nextNodeId = transferRule.getString("nextNodeId");
                    currentSortIndex = assignNodeSort(nextNodeId, currentSortIndex, nodeMap);
                }
            }
        }
        return currentSortIndex; // 返回下一个排序值
    }

    /**
     * 根据给定的节点和节点映射，创建包含子节点的新节点JSON对象。
     *
     * @param node 给定的节点JSON对象，包含其属性及可能的子节点引用。
     * @param nodeMap 节点ID到节点JSON对象的映射，用于查找子节点。
     * @return 包含给定节点及其所有子节点的新JSON对象。
     */
    // 根据 transferRuleList 创建新节点及其子节点
    private static JSONObject createNodeWithChildren(JSONObject node, Map<String, JSONObject> nodeMap) {
        // 创建新的节点 JSON 对象
        JSONObject newNode = new JSONObject();
        newNode.put("nodeId", node.getString("nodeId"));
        newNode.put("nodeName", node.getString("nodeName"));
        if(EmptyUtils.isNotEmpty(node.getString("nodeValue"))){
            newNode.put("nodeValue", node.getString("nodeValue"));
        }
        if(EmptyUtils.isNotEmpty(node.getString("nodeType")) && !Arrays.asList("-1","-2","5").contains(node.getString("nodeType"))){
            newNode.put("nodeType", node.getString("nodeType"));
        }
        newNode.put("isUpdate", node.getString("isUpdate"));
        newNode.put("positionTop", node.getString("positionTop"));
        newNode.put("positionLeft", node.getString("positionLeft"));
        newNode.put("nodeSort", node.getInteger("nodeSort")); // 添加 nodeSort 字段

        // 准备保存子节点的数组
        JSONArray children = new JSONArray();

        // 处理转移规则以查找子节点
        if (node.getJSONArray("transferRuleList") != null) {
            JSONArray transferRules = node.getJSONArray("transferRuleList");
            for (int i = 0; i < transferRules.size(); i++) {
                JSONObject transferRule = transferRules.getJSONObject(i);
                String childNodeId = transferRule.getString("nextNodeId");

                // 如果子节点在节点映射中，则添加它
                if (nodeMap.containsKey(childNodeId)) {
                    JSONObject childNode = nodeMap.get(childNodeId);
                    int sortIndex = 1;
                    // 确保它确实是一个子节点 (必须性parent为 "0")
                    if ("0".equals(childNode.getString("isParent"))) {
                        if ("5".equals(childNode.getString("nodeType"))) {
                            throw new BusinessException("子节点不能是自定义节点");
                        }
                        // 添加子节点进 children 数组
                        JSONObject cloneChildNode = childNode.clone().fluentRemove("isParent");
                        cloneChildNode.put("nodeSort", sortIndex); // 设置当前节点的排序值
                        children.add(cloneChildNode); // 注意，不进行 nodeSort 赋值

                        // 递归获取所有子节点的子节点并添加
                        addAllChildren(children, cloneChildNode, nodeMap, sortIndex);
                    }
                }
            }
        }

        // 如果有子节点，则添加到新节点中
        if (!children.isEmpty()) {
            newNode.put("children", children);
        }
        return newNode;
    }

    /**
     * 递归添加所有子节点的子节点
     *
     * @param children 用于存储所有子节点的 JSONArray
     * @param parentNode 当前节点对应的 JSONObject
     * @param nodeMap 包含所有节点信息的 Map，键为 nodeId，值为对应的 JSONObject
     * @param sortIndex 当前节点的排序索引，用于设置子节点的排序值
     */
    // 递归添加所有子节点的子节点
    private static void addAllChildren(JSONArray children, JSONObject parentNode, Map<String, JSONObject> nodeMap, int sortIndex) {
        if (parentNode.getJSONArray("transferRuleList") != null) {
            sortIndex++;
            JSONArray transferRules = parentNode.getJSONArray("transferRuleList");
            for (int i = 0; i < transferRules.size(); i++) {
                JSONObject transferRule = transferRules.getJSONObject(i);
                String nextNodeId = transferRule.getString("nextNodeId");

                if (nodeMap.containsKey(nextNodeId)) {
                    JSONObject childNode = nodeMap.get(nextNodeId);

                    // 确保是子节点 (isParent 为 "0")
                    if ("0".equals(childNode.getString("isParent"))) {
                        if ("5".equals(childNode.getString("nodeType"))) {
                            throw new BusinessException("子节点不能是自定义节点");
                        }
                        JSONObject cloneChildNode = childNode.clone().fluentRemove("isParent");
                        cloneChildNode.put("nodeSort", sortIndex); // 设置当前节点的排序值
                        children.add(cloneChildNode); // 添加子节点

                        // 递归添加子节点的子节点
                        addAllChildren(children, childNode, nodeMap, sortIndex);
                    }
                }
            }
        }
    }
}
