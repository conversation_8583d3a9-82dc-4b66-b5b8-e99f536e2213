package com.kbao.algo.strategyNode.dao;

import com.kbao.algo.strategyNode.entity.AlgoStrategyNode;
import com.kbao.kbcbsc.dao.sql.BaseMapper;

import java.util.List;

/**
* <AUTHOR>
* @Description 算法规则-策略执行节点Dao类
* @Date 2024-10-28
*/
public interface AlgoStrategyNodeMapper  extends BaseMapper<AlgoStrategyNode, Integer>{
    /**
     * 根据策略id删除参数
     */
    void deleteByStrategyId(Integer strategyId);
    /**
     * 根据策略id查询节点列表
     */
    List<AlgoStrategyNode> selectByStrategyId(Integer strategyId);
}