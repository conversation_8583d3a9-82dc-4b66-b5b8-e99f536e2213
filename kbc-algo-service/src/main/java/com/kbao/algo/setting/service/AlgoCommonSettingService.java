package com.kbao.algo.setting.service;

import com.kbao.algo.setting.dao.AlgoCommonSettingMapper;
import com.kbao.algo.setting.entity.AlgoCommonSetting;
import com.kbao.algo.util.AlgoContext;
import com.kbao.algo.util.SettingConstants;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 通用配置Service
 */
@Service
public class AlgoCommonSettingService extends BaseSQLServiceImpl<AlgoCommonSetting, Long, AlgoCommonSettingMapper> {

    /**
     * 根据编码获取配置
     */
    public AlgoCommonSetting getByCode(String code) {
        return mapper.getByCode(code);
    }

    /**
     * 保存配置
     */
    public void save(AlgoCommonSetting setting) {
        AlgoCommonSetting existSetting = getByCode(setting.getCode());
        if (existSetting == null) {
            setting.setCreateBy(SysLoginUtils.getUserId());
            setting.setTenantId(AlgoContext.getTenantId());
            mapper.insert(setting);
        } else {
            setting.setUpdateBy(SysLoginUtils.getUserId());
            mapper.updateByPrimaryKeySelective(setting);
        }
    }

    /**
     * 获取数据空间内存限制
     */
    public double getDataSpaceMemoryLimit() {
        AlgoCommonSetting setting = getByCode(SettingConstants.DATA_SPACE_MEMORY_LIMIT);
        if (setting == null) {
            return 6.0; // 默认6G
        }
        try {
            return Double.parseDouble(setting.getValue());
        } catch (NumberFormatException e) {
            return 6.0;
        }
    }
} 