package com.kbao.algo.series.dao;

import cn.hutool.core.lang.Assert;
import com.kbao.algo.series.entity.SeriesData;
import com.kbao.kbcbsc.dao.nosql.BaseMongoDaoImpl;
import com.kbao.tool.util.NullProcessUtil;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 主键策略Dao类
 * @Date 2021-08-16
 */
@Repository
public class SeriesDataDao extends BaseMongoDaoImpl<SeriesData, String> {
	
	/**
	 * 查询序列名是否已存在
	 * @param seriesName
	 * @return
	 */
	public boolean isExistSeriesName(String seriesName) {
		Query query = new Query(Criteria.where("seriesName").is(seriesName));
		SeriesData bean = this.mongoTemplate.findOne(query, SeriesData.class);
		if (Objects.isNull(bean)) {
			return Boolean.FALSE;
		}
		return Boolean.TRUE;
	}

	/**
	 * 自增序列值
	 * @param seriesName
	 * @return
	 */
	public synchronized String getNextId(String seriesName) {
		Query query = new Query(Criteria.where("seriesName").is(seriesName));
		Update update = new Update();
		update.inc("incVal",1);
		FindAndModifyOptions options = new FindAndModifyOptions();
		options.upsert(true);
		options.returnNew(true);
		SeriesData inc = this.mongoTemplate.findAndModify(query, update, SeriesData.class);
		Assert.isTrue(inc != null, "请在序列管理中创建该序列名");
		return formatId(inc);
	}

	private String formatId(SeriesData inc) {
		StringBuffer idStr = new StringBuffer();
		if (Objects.nonNull(idStr)) {
			idStr.append(NullProcessUtil.nvlToString(inc.getPreFix(),""));
			int addNums = inc.getComplementNum() - inc.getIncVal().toString().length();
			if (addNums > 0) {
				for(int i = 0; i < addNums; i++) {
					idStr.append("0");
				}
			}
			idStr.append(inc.getIncVal());
		}
		return idStr.toString();
	}

}
