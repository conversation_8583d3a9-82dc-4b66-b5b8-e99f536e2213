package com.kbao.algo.series.service;

import com.kbao.algo.series.dao.SeriesDataDao;
import com.kbao.algo.series.entity.SeriesData;
import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Description 主键策略Service类
 * @Date 2021-08-16
 */
@Service
public class SeriesDataService extends BaseMongoServiceImpl<SeriesData, String, SeriesDataDao> {


	/**
	 * 查询序列名是否已存在
	 * @param seriesName 序列名
	 * @return 是否存在
	 */
	@Transactional(readOnly = true)
	public boolean isExistSeriesName(String seriesName) {
		return this.dao.isExistSeriesName(seriesName);
	}
	
	/**
	 * 自增序列值
	 * @param seriesName
	 * @return ID字符串
	 */
	@Transactional(rollbackFor = Exception.class)
	public String getNextId(String seriesName, String preFix, Integer complementNum) {
		if(!isExistSeriesName(seriesName)) {
			SeriesData mo = new SeriesData();
			mo.setComplementNum(complementNum);
			mo.setSeriesName(seriesName);
			mo.setPreFix(preFix);
			mo.setIncVal(0L);
			this.dao.save(mo);
		}
		return this.dao.getNextId(seriesName);
	}

}
