package com.kbao.algo.sourceField.dao;

import com.kbao.algo.sourceField.bean.AlgoParamResVo;
import com.kbao.algo.sourceField.entity.AlgoSourceField;
import com.kbao.kbcbsc.dao.sql.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR> jie
* @Description Dao类
* @Date 2024-10-25
*/
public interface AlgoSourceFieldMapper extends BaseMapper<AlgoSourceField, Integer>{

    int count(@Param("sourceId") Integer sourceId, @Param("fieldName") String fieldName,
              @Param("fieldId") Integer fieldId);

    List<AlgoParamResVo> getSourceFields(@Param("bizCode") String bizCode);

    List<AlgoParamResVo> getManySourceParams(@Param("bizCodes") List<String> bizCodes);

}
