package com.kbao.algo.sourceField.service;

import com.alibaba.fastjson.JSONObject;
import com.kbao.algo.enums.AlgoFieldTypeEnum;
import com.kbao.algo.enums.AlgoNodeTypeEnum;
import com.kbao.algo.sourceField.bean.AlgoParamResVo;
import com.kbao.algo.sourceField.bean.AlgoSourceFieldReqVo;
import com.kbao.algo.sourceField.dao.AlgoSourceFieldMapper;
import com.kbao.algo.sourceField.entity.AlgoSourceField;
import com.kbao.algo.util.AlgoContext;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.tool.util.SysLoginUtils;
import com.kbao.algo.indicator.indicatorField.service.AlgoIndicatorFieldService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
* <AUTHOR> jie
* @Description Service类
* @Date 2024-10-25
*/
@Slf4j
@Service
public class AlgoSourceFieldService extends BaseSQLServiceImpl<AlgoSourceField, Integer, AlgoSourceFieldMapper> {
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private AlgoIndicatorFieldService algoIndicatorFieldService;

    @Transactional(rollbackFor = Exception.class)
    public void addSourceField(AlgoSourceField field) {
        // 校验字段
        this.checkField(field);
        if (isExistField(field.getSourceId(), field.getFieldName(), null)) {
            throw new RuntimeException("属性名已存在");
        }
        field.setCreateId(BscUserUtils.getUserId());
        field.setTenantId(SysLoginUtils.getUser().getTenantId());
        mapper.insert(field);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateSourceField(AlgoSourceField field) {
        this.checkField(field);
        if (isExistField(field.getSourceId(), field.getFieldName(), field.getFieldId())) {
            throw new RuntimeException("属性名已存在");
        }
        field.setUpdateId(BscUserUtils.getUserId());
        this.mapper.updateByPrimaryKeySelective(field);
    }

    private void checkField(AlgoSourceField field) {
        AlgoFieldTypeEnum.buildFieldType(field.getFieldType(), field.getFieldLength());
        if ("1".equals(field.getIsCheck())
                && !(AlgoFieldTypeEnum.INT.getCode().equals(field.getFieldType())
                || AlgoFieldTypeEnum.DOUBLE.getCode().equals(field.getFieldType()))) {
            throw new RuntimeException("必须是数字类型才能设置为核对项");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void delSourceField(Integer fieldId) {
        mapper.deleteByPrimaryKey(fieldId);
    }

    public List<AlgoSourceField> list(AlgoSourceFieldReqVo reqVo) {
        Map<String, Object> param = new HashMap<>();
        param.put("sourceId", reqVo.getSourceId());
        param.put("name", reqVo.getName());
        param.put("fieldName", reqVo.getFieldName());
        param.put("tenantId", SysLoginUtils.getUser().getTenantId());
        return this.selectByParam(param);
    }

    public List<AlgoParamResVo> getSourceFields(AlgoSourceFieldReqVo reqVo) {
        if (AlgoNodeTypeEnum.SOURCE.getCode().equals(reqVo.getNodeType())) {
            return mapper.getSourceFields(reqVo.getSourceCode());
        } else {
            return algoIndicatorFieldService.getIndicatorFields(reqVo.getSourceCode());
        }
    }

    public List<AlgoParamResVo> getManySourceParams(List<String> bizCodes) {
        if (CollectionUtils.isEmpty(bizCodes)) {
            return Collections.emptyList();
        }
        List<AlgoParamResVo> params = mapper.getManySourceParams(bizCodes);
        Map<String, AlgoParamResVo> paramMap = new HashMap<>();
        params.forEach(param -> {
            AlgoParamResVo old = paramMap.get(param.getFieldName());
            if (old == null) {
                paramMap.put(param.getFieldName(), param);
            } else if (!old.getParamType().equals(param.getParamType())) {
                throw new RuntimeException("数据源之间参数冲突");
            }
        });
        return new ArrayList<>(paramMap.values());
    }

    public List<AlgoSourceField> listBySourceId(Integer sourceId) {
        Map<String, Object> param = new HashMap<>();
        param.put("sourceId", sourceId);
        param.put("tenantId", AlgoContext.getTenantId());
        log.info("查询数据源属性列表参数:{}", JSONObject.toJSONString(param));
        return this.selectByParam(param);
    }

    public boolean isExistField(Integer sourceId, String fieldName, Integer fieldId) {
        return mapper.count(sourceId, fieldName, fieldId) > 0;
    }


}
