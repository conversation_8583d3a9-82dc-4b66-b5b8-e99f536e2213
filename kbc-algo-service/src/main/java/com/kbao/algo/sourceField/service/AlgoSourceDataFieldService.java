package com.kbao.algo.sourceField.service;

import com.kbao.algo.indicator.indicatorField.bean.DataField;
import com.kbao.algo.sourceField.entity.AlgoSourceDataField;
import com.kbao.algo.sourceField.entity.AlgoSourceField;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class AlgoSourceDataFieldService {
    @Autowired
    private MongoTemplate mongoTemplate;
    public void saveSourceDataFields(Integer dataSpaceId, String sourceCode, List<AlgoSourceField> sourceFields) {
        List<DataField> fields = sourceFields.stream().map(item -> new DataField(item.getFieldName(), item.getName(), item.getFieldType()))
                .collect(Collectors.toList());

        AlgoSourceDataField sourceDataField = new AlgoSourceDataField(dataSpaceId, sourceCode, fields);
        this.delSourceDataField(dataSpaceId, sourceCode);
        mongoTemplate.save(sourceDataField);
    }

    public void checkSourceFields(Integer dataSpaceId, String sourceCode, List<AlgoSourceField> fields) {
        List<DataField> list = this.getSourceDataFields(dataSpaceId, sourceCode);
        if (CollectionUtils.isEmpty(list)) {
            throw new RuntimeException("该批次源表未保存字段");
        }
        Set<String> fieldSet = fields.stream().map(field -> field.getFieldName() + "#" + field.getFieldType()).collect(Collectors.toSet());
        Set<String> dbSet = list.stream().map(field -> field.getFieldName() + "#" + field.getFieldType()).collect(Collectors.toSet());
        // 当前源表字段删除，不影响计算
        fieldSet.removeAll(dbSet);
        if (!fieldSet.isEmpty()) {
            throw new RuntimeException("当前数据空间源表字段发生变更，与历史抽取批次数据不一致");
        }
    }

    public void delSourceDataField(Integer dataSpaceId, String sourceCode) {
        Query query = new Query(Criteria.where("dataSpaceId").is(dataSpaceId)
                .and("sourceCode").is(sourceCode));
        mongoTemplate.remove(query, AlgoSourceDataField.class);
    }

    public List<DataField> getSourceDataFields(Integer dataSpaceId, String sourceCode) {
        Query query = new Query(Criteria.where("dataSpaceId").is(dataSpaceId)
                .and("sourceCode").is(sourceCode));
        AlgoSourceDataField dataField = mongoTemplate.findOne(query, AlgoSourceDataField.class);
        if (dataField == null) {
            return null;
        }
        return dataField.getFields();
    }
}
