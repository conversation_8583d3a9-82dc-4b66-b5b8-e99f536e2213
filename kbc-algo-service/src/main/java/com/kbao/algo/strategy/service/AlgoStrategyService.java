package com.kbao.algo.strategy.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.kbao.algo.dataSpace.entity.AlgoDataSpace;
import com.kbao.algo.dataSpace.service.AlgoDataSpaceService;
import com.kbao.algo.enums.AlgoNodeTypeEnum;
import com.kbao.algo.redis.RedisLockUtils;
import com.kbao.algo.series.service.SeriesDataService;
import com.kbao.algo.source.bean.AlgoDataReqVo;
import com.kbao.algo.sourceField.bean.AlgoParamResVo;
import com.kbao.algo.strategy.bean.AlgoStrategyExecuteRespDTO;
import com.kbao.algo.strategy.bean.AlgoStrategyReqVo;
import com.kbao.algo.strategy.bean.AlgoStrategyVo;
import com.kbao.algo.strategy.entity.AlgoStrategy;
import com.kbao.algo.strategyBatch.bean.AlgoStrategyBatchReqVo;
import com.kbao.algo.strategyBatch.entity.AlgoStrategyBatch;
import com.kbao.algo.strategyBatch.enums.StrategyBatchExecuteStatusEnum;
import com.kbao.algo.strategyBatchNode.bean.AlgoStrategyBatchNodeVo;
import com.kbao.algo.strategyBatchNode.entity.AlgoStrategyBatchNode;
import com.kbao.algo.strategyNode.bean.AlgoStrategyNodeVo;
import com.kbao.algo.strategyNode.entity.AlgoStrategyNode;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.algo.source.service.AlgoSourceService;
import com.kbao.algo.sourceField.service.AlgoSourceFieldService;
import com.kbao.algo.strategy.dao.AlgoStrategyMapper;
import com.kbao.algo.strategyBatch.service.AlgoStrategyBatchService;
import com.kbao.algo.strategyBatchNode.service.AlgoStrategyBatchNodeService;
import com.kbao.algo.strategyBatchParam.service.AlgoStrategyBatchParamService;
import com.kbao.algo.strategyNode.service.AlgoStrategyNodeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @Description 算法规则-策略Service类
* @Date 2024-10-28
*/
@Service
@Slf4j
public class AlgoStrategyService extends BaseSQLServiceImpl<AlgoStrategy, Integer, AlgoStrategyMapper> {
    @Autowired
    private SeriesDataService seriesDataService;

    @Autowired
    private AlgoStrategyNodeService algoStrategyNodeService;

    @Autowired
    private AlgoStrategyBatchService algoStrategyBatchService;

    @Autowired
    private AlgoStrategyBatchNodeService algoStrategyBatchNodeService;

    @Autowired
    private AlgoStrategyBatchParamService algoStrategyBatchParamService;

    @Autowired
    private AlgoDataSpaceService algoDataSpaceService;

    @Autowired
    private AlgoSourceFieldService algoSourceFieldService;

    @Autowired
    private RedisLockUtils redisLockUtils;

    @Autowired
    private AlgoSourceService algoSourceService;
    /**
     * 插入算法策略
     *
     * @param algoStrategyReq 算法策略请求对象，包含策略ID、策略名称等信息
     * @throws Exception 如果插入过程中出现异常，则抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    public void insert(AlgoStrategyVo algoStrategyReq) {
        String currentUserId = BscUserUtils.getUserId();
        String tenantId = BscUserUtils.getUser().getUser().getTenantId();
        //策略编码校验（策略编码唯一）
        Boolean checkStrategy = checkStrategy(algoStrategyReq.getBizCode());
        if(checkStrategy){
            throw new BusinessException("该策略编码已存在");
        }
        //保存策略
        AlgoStrategy algoStrategy = new AlgoStrategy();
        BeanUtils.copyProperties(algoStrategyReq, algoStrategy);
        algoStrategy.setCreateTime(new Date());
        algoStrategy.setCreateId(currentUserId);
        algoStrategy.setUpdateTime(new Date());
        algoStrategy.setUpdateId(currentUserId);
        algoStrategy.setIsDeleted(0);
        algoStrategy.setTenantId(tenantId);
        this.mapper.insertSelective(algoStrategy);
    }
    /**
     * 算法策略配置
     *
     * @param algoStrategyReq 算法策略请求对象，包含策略ID、策略名称等信息
     * @throws Exception 如果配置过程中出现异常，则抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    public void config(AlgoStrategyVo algoStrategyReq) {
        if(EmptyUtils.isEmpty(algoStrategyReq.getNodeList())){
            throw new BusinessException("请连接必要节点，策略节点数据不能为空");
        }
        AlgoStrategy algoStrategy = this.mapper.selectByPrimaryKey(algoStrategyReq.getStrategyId());
        algoStrategy.setUpdateTime(new Date());
        algoStrategy.setUpdateId(BscUserUtils.getUserId());
        this.mapper.updateByPrimaryKeySelective(algoStrategy);
        //保存策略节点数据
        algoStrategyNodeService.insert(algoStrategyReq.getNodeList(), algoStrategyReq.getStrategyId());
    }
    /**
     * 更新算法策略
     *
     * @param algoStrategyReq 算法策略请求对象，包含策略ID等信息
     * @throws Exception 如果在更新过程中出现异常，则抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(AlgoStrategyVo algoStrategyReq) {
        String currentUserId = BscUserUtils.getUserId();
        //策略编码校验（策略编码唯一）
        AlgoStrategy selectByCode = this.mapper.selectByCode(algoStrategyReq.getBizCode());
        if(EmptyUtils.isNotEmpty(selectByCode) && !algoStrategyReq.getStrategyId().equals(selectByCode.getStrategyId())){
            throw new BusinessException("该策略编码已存在");
        }
        AlgoStrategy algoStrategy = this.mapper.selectByPrimaryKey(algoStrategyReq.getStrategyId());
        BeanUtils.copyProperties(algoStrategyReq, algoStrategy);
        algoStrategy.setUpdateTime(new Date());
        algoStrategy.setUpdateId(currentUserId);
        this.mapper.updateByPrimaryKeySelective(algoStrategy);
    }

    /**
     * 根据策略ID删除算法策略及其关联节点数据
     *
     * @param strategyId 策略ID
     * @throws Exception 如果删除过程中出现异常，则抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Integer strategyId) {
        //如果该策略已经生成了批次，则不允许删除
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("strategyId", strategyId);
        paramMap.put("tenantId", BscUserUtils.getUser().getUser().getTenantId());
        List<AlgoStrategyBatch> algoStrategyBatches = algoStrategyBatchService.getMapper().selectAll(paramMap);
        if(algoStrategyBatches.size() > 0){
            throw new BusinessException("该策略已生成批次，不允许删除");
        }
        this.mapper.deleteByPrimaryKey(strategyId);
        algoStrategyNodeService.getMapper().deleteByStrategyId(strategyId);
    }
    /**
     * 根据策略ID查询算法策略信息
     *
     * @param strategyId 策略ID
     * @return 算法策略视图对象
     */
    public AlgoStrategy find(Integer strategyId) {
        return this.mapper.selectByPrimaryKey(strategyId);
    }

    /**
     * 根据策略ID查询算法策略配置信息
     *
     * @param strategyId 策略ID
     * @return 算法策略视图对象，包含策略节点列表
     */
    public AlgoStrategyVo configFind(Integer strategyId) {
        AlgoStrategyVo algoStrategyVo = new AlgoStrategyVo();
        AlgoStrategy algoStrategy = this.mapper.selectByPrimaryKey(strategyId);
        BeanUtils.copyProperties(algoStrategy, algoStrategyVo);
        //查询策略节点列表
        List<AlgoStrategyNodeVo> algoStrategyNodeVos = algoStrategyNodeService.selectByStrategyId(strategyId);
        algoStrategyVo.setNodeList(algoStrategyNodeVos);
        return algoStrategyVo;
    }

    /**
     * 执行算法策略
     *
     * @param algoStrategyReqVo 算法策略请求对象
     * @return 算法策略执行响应对象
     * @throws Exception 如果执行过程中发生异常，则抛出异常
     */
    public AlgoStrategyExecuteRespDTO execute(AlgoStrategyReqVo algoStrategyReqVo){
        String dataSpaceBatchCode = algoStrategyReqVo.getDataSpaceBatchCode();
        //校验数据空间编码不能为空
        if(EmptyUtils.isEmpty(dataSpaceBatchCode)) {
            throw new BusinessException("数据空间编码不能为空");
        }
        //校验数据空间编码是否存在
        AlgoDataSpace algoDataSpace = algoDataSpaceService.getByCode(dataSpaceBatchCode);
        if(EmptyUtils.isEmpty(algoDataSpace)){
            throw new BusinessException("数据空间未生成，请先生成数据空间");
        }
        //校验数据空间内存
        algoDataSpaceService.isOverMemoryLimit(algoDataSpace.getId());
        //获取策略配置
        String strategyCode = algoStrategyReqVo.getStrategyCode();
        AlgoStrategy algoStrategy = this.mapper.selectByCode(strategyCode);
        if(EmptyUtils.isEmpty(algoStrategy)) {
            throw new BusinessException("根据策略编码【" + strategyCode + "】未查询到数据");
        }
        String tenantId = algoStrategy.getTenantId();
        //校验策略参数是否满足条件
        JSONObject reqParam = algoStrategyReqVo.getReqParam();
        if(EmptyUtils.isEmpty(reqParam)){
            throw new BusinessException("策略请求入参不能为空");
        }
        //获取策略节点列表
        List<AlgoStrategyNode> algoStrategyNodes = algoStrategyNodeService.list(algoStrategy.getStrategyId());
        if(EmptyUtils.isEmpty(algoStrategyNodes)){
            throw new BusinessException("策略执行节点未配置，请先配置策略执行节点");
        }
        //获取参数数据列表
        List<AlgoParamResVo> newAlgoParamResVos = new ArrayList<>();
        for (String key : reqParam.keySet()) {
            Object value = reqParam.get(key);
            AlgoParamResVo algoParamResVo = new AlgoParamResVo();
            algoParamResVo.setFieldName(key);
            if (value instanceof String) {
                algoParamResVo.setParamType("1");
                algoParamResVo.setParamValue(value);
            } else {
                algoParamResVo.setParamType("2");
                algoParamResVo.setParamValue(JSONArray.parse(value.toString()));
            }
            newAlgoParamResVos.add(algoParamResVo);
        }
        String operatorId = algoStrategyReqVo.getOperatorId();
        if(EmptyUtils.isEmpty(algoStrategyReqVo.getOperatorId())) {
            operatorId = "sys";
        }
        //生成策略批次
        AlgoStrategyBatch algoStrategyBatch = algoStrategyBatchService.insert(algoStrategy, operatorId, tenantId, algoDataSpace.getId());
        redisLockUtils.lock(algoStrategyBatch.getBizCode(), 1, 3600);
        //生成策略批次节点
        algoStrategyBatchNodeService.insert(algoStrategyBatch, algoStrategyNodes, operatorId, tenantId);
        //保存策略批次参数
        algoStrategyBatchParamService.insert(algoStrategyBatch, newAlgoParamResVos, operatorId, tenantId);
        //按顺序执行策略节点（异步调用）
        algoStrategyBatchService.executeStrategyBatch(algoStrategyBatch, algoStrategyNodes, newAlgoParamResVos, strategyCode, tenantId);
        AlgoStrategyExecuteRespDTO res = new AlgoStrategyExecuteRespDTO();
        res.setBizCode(algoStrategyBatch.getBizCode());
        return res;
    }

    /**
     * 重试执行算法策略批次
     *
     * @param strategyBatchId 算法策略批次ID
     * @throws Exception 如果执行过程中发生异常，则抛出异常
     */
    public void retryStrategyBatch(Integer strategyBatchId){
        //获取当前批次节点信息
        AlgoStrategyBatch algoStrategyBatch = algoStrategyBatchService.getMapper().selectByPrimaryKey(strategyBatchId);
        Boolean lock = redisLockUtils.isLock(algoStrategyBatch.getBizCode(), 1, 3600);
        if (!lock) {
            throw new BusinessException("当前策略批次正在重试执行中，请稍后再试");
        }
        try {
            AlgoStrategy algoStrategy = this.mapper.selectByPrimaryKey(algoStrategyBatch.getStrategyId());
            //重置策略批次节点状态为执行中
            algoStrategyBatch.setExecuteTime(new Date());
            algoStrategyBatch.setExecuteStatus(StrategyBatchExecuteStatusEnum.IN_PROGRESS.getCode());
            algoStrategyBatchService.getMapper().updateByPrimaryKeySelective(algoStrategyBatch);
            //获取策略节点列表
            List<AlgoStrategyNode> algoStrategyNodes = algoStrategyNodeService.list(algoStrategyBatch.getStrategyId());
            //查询策略批次节点信息，并根据节点是否成功给是否更新字段赋值
            algoStrategyBatchNodeService.handleBatchNode(algoStrategyNodes, strategyBatchId);
            //获取策略批次参数
            List<AlgoParamResVo> algoStrategyBatchParams = algoStrategyBatchParamService.getMapper().selectByBatchId(strategyBatchId);
            for(AlgoParamResVo algoStrategyBatchParamVo : algoStrategyBatchParams){
              if(!"1".equals(algoStrategyBatchParamVo.getParamType())){
                  algoStrategyBatchParamVo.setParamValue(JSONArray.parse(algoStrategyBatchParamVo.getParamValue().toString()));
              }
            }
            //按顺序执行策略节点（异步调用）
            String tenantId = BscUserUtils.getUser().getUser().getTenantId();
            algoStrategyBatchService.executeStrategyBatch(algoStrategyBatch, algoStrategyNodes, algoStrategyBatchParams, algoStrategy.getBizCode(), tenantId);
        } catch (Exception e) {
            log.error("策略批次执行失败:{}", ExceptionUtils.getStackTrace(e));
        }
    }

    /**
     * 获取策略参数
     *
     * @param strategyCode 策略编码
     * @return 策略参数列表
     */
    public List<AlgoParamResVo> getStrategyParams(String strategyCode){
        AlgoStrategy algoStrategy = this.mapper.selectByCode(strategyCode);
        //获取策略节点列表
        List<AlgoStrategyNode> algoStrategyNodes = algoStrategyNodeService.list(algoStrategy.getStrategyId());
        List<String> nodeValueCodes = algoStrategyNodes.stream().filter(node -> node.getNodeType().equals("1")).map(AlgoStrategyNode::getNodeValue).collect(Collectors.toList());
        //获取参数数据列表
        List<AlgoParamResVo> algoParamResVos = algoSourceFieldService.getManySourceParams(nodeValueCodes);
        return algoParamResVos;
    }

    public Boolean checkStrategy(String strategyCode){
        AlgoStrategy algoStrategy = this.mapper.selectByCode(strategyCode);
        if(EmptyUtils.isNotEmpty(algoStrategy)){
            return true;
        }
        return false;
    }

    public PageInfo<Map> getResultIndicatorBatchDetailPage(PageRequest<AlgoStrategyBatchReqVo> reqVo){
        String batchCode = reqVo.getParam().getBatchCode();
        String indicatorCode = reqVo.getParam().getIndicatorCode();
        AlgoStrategyBatch algoStrategyBatchByCode = algoStrategyBatchService.getMapper().getAlgoStrategyBatchByCode(batchCode);
        if(EmptyUtils.isNotEmpty(algoStrategyBatchByCode)){
            AlgoStrategyBatchNode vo = new AlgoStrategyBatchNode();
            vo.setBatchId(algoStrategyBatchByCode.getId());
            vo.setTenantId(algoStrategyBatchByCode.getTenantId());
            vo.setNodeType(AlgoNodeTypeEnum.RESULT_INDICATOR.getCode());
            if(EmptyUtils.isNotEmpty(indicatorCode)){
                vo.setNodeValue(indicatorCode);
            }
            //获取策略批次结果指标节点
            List<AlgoStrategyBatchNodeVo> strategyBatchNodeVos = algoStrategyBatchNodeService.getMapper().getAlgoStrategyBatchNodes(vo);
            if(EmptyUtils.isNotEmpty(strategyBatchNodeVos)){
                AlgoStrategyBatchNodeVo batchNodeDetail;
                if(strategyBatchNodeVos.size() > 1){
                   //取结果指标最后一个对象
                   batchNodeDetail = strategyBatchNodeVos.get(strategyBatchNodeVos.size()-1);
                } else {
                    batchNodeDetail = strategyBatchNodeVos.get(0);
                }
                PageRequest<AlgoDataReqVo> pageReqVo = new PageRequest<>();
                AlgoDataReqVo algoDataReqVo = new AlgoDataReqVo();
                algoDataReqVo.setBizCode(batchNodeDetail.getNodeValue());
                algoDataReqVo.setStrategyBatchId(batchNodeDetail.getBatchId());
                algoDataReqVo.setNodeType(batchNodeDetail.getNodeType());
                algoDataReqVo.setStrategyBatchId(algoStrategyBatchByCode.getId());
                pageReqVo.setPageNum(reqVo.getPageNum());
                pageReqVo.setPageSize(reqVo.getPageSize());
                pageReqVo.setParam(algoDataReqVo);
                PageInfo<Map> sourceDataList = algoSourceService.getSourceDataList(pageReqVo);
                return sourceDataList;
            }
        }
        return new PageInfo<>();
    }
}
