package com.kbao.algo.strategyBatchNode.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.kbao.algo.indicator.AlgoIndicatorHelper;
import com.kbao.algo.indicator.bean.AlgoIndicatorConfigVo;
import com.kbao.algo.indicator.indicator.service.AlgoIndicatorService;
import com.kbao.algo.indicator.indicator.service.AlgoProcedureService;
import com.kbao.algo.indicator.indicatorVersion.service.AlgoIndicatorVersionService;
import com.kbao.algo.redis.RedisLockUtils;
import com.kbao.algo.series.service.SeriesDataService;
import com.kbao.algo.sourceField.bean.AlgoParamResVo;
import com.kbao.algo.strategyBatch.entity.AlgoStrategyBatch;
import com.kbao.algo.strategyBatch.enums.StrategyBatchExecuteStatusEnum;
import com.kbao.algo.strategyBatchNode.bean.AlgoStrategyBatchNodeDTO;
import com.kbao.algo.strategyBatchNode.bean.StrategyBatchNodeDataReqVo;
import com.kbao.algo.strategyBatchNode.bean.AlgoStrategyBatchNodeRespDTO;
import com.kbao.algo.strategyBatchNode.bean.AlgoStrategyBatchNodeVo;
import com.kbao.algo.strategyBatchNode.dao.AlgoStrategyBatchNodeMapper;
import com.kbao.algo.strategyBatchNode.entity.AlgoStrategyBatchNode;
import com.kbao.algo.strategyBatchParam.bean.AlgoStrategyBatchParamVo;
import com.kbao.algo.strategyNode.entity.AlgoStrategyNode;
import com.kbao.algo.util.AlgoContext;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.algo.source.service.AlgoDataCollectService;
import com.kbao.algo.strategyBatch.service.AlgoStrategyBatchService;
import com.kbao.algo.strategyBatchParam.service.AlgoStrategyBatchParamService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @Description 算法规则-策略执行批次节点Service类
* @Date 2024-10-29
*/
@Slf4j
@Service
public class AlgoStrategyBatchNodeService extends BaseSQLServiceImpl<AlgoStrategyBatchNode, Integer, AlgoStrategyBatchNodeMapper> {
    @Autowired
    private SeriesDataService seriesDataService;

    @Autowired
    private AlgoDataCollectService algoDataCollectService;

    @Autowired
    private AlgoStrategyBatchParamService algoStrategyBatchParamService;

    @Autowired
    private AlgoIndicatorService algoIndicatorService;

    @Autowired
    private RedisLockUtils redisLockUtils;

    @Autowired
    private AlgoStrategyBatchService algoStrategyBatchService;

    @Autowired
    private AlgoIndicatorVersionService algoIndicatorVersionService;

    @Autowired
    private AlgoProcedureService algoProcedureService;

    /**
     * 获取算法策略批次节点列表
     *
     * @param algoStrategyBatchNode 请求对象查询参数
     * @return 算法策略批次节点列表
     */
    public List<AlgoStrategyBatchNodeVo> getAlgoStrategyBatchNodes(AlgoStrategyBatchNode algoStrategyBatchNode){
        algoStrategyBatchNode.setTenantId(BscUserUtils.getUser().getUser().getTenantId());
        List<AlgoStrategyBatchNodeVo> nodeList = mapper.getAlgoStrategyBatchNodes(algoStrategyBatchNode);
        if (EmptyUtils.isEmpty(nodeList)) {
            return Collections.emptyList();
        }
        List<AlgoStrategyBatchParamVo> paramList = algoStrategyBatchParamService.getParamList(algoStrategyBatchNode.getBatchId());
        if (EmptyUtils.isNotEmpty(paramList)) {
            List<String> params = paramList.stream().map(o -> o.getFieldName() + ":" + o.getFieldValue()).collect(Collectors.toList());
            nodeList.forEach(o -> o.setParams(params));
        }
        return nodeList;
    }

    /**
     * 插入算法策略批次节点
     *
     * @param algoStrategyBatch 算法策略批次对象
     * @param algoStrategyNodes 算法策略节点列表
     * @param operatorId 操作人ID
     * @param tenantId 租户ID
     * @throws Exception 当插入过程中发生错误时抛出异常
     */
    public void insert(AlgoStrategyBatch algoStrategyBatch, List<AlgoStrategyNode> algoStrategyNodes, String operatorId, String tenantId) {
        List<AlgoStrategyBatchNode> algoStrategyBatchNodes = new ArrayList<>();
        for (AlgoStrategyNode algoStrategyNode : algoStrategyNodes) {
            if(EmptyUtils.isNotEmpty(algoStrategyNode.getNodeValue())){
                AlgoStrategyBatchNode algoStrategyBatchNode = new AlgoStrategyBatchNode();
                algoStrategyBatchNode.setBizCode(getNextId());
                algoStrategyBatchNode.setBatchId(algoStrategyBatch.getId());
                algoStrategyBatchNode.setNodeName(algoStrategyNode.getNodeName());
                algoStrategyBatchNode.setNodeType(algoStrategyNode.getNodeType());
                algoStrategyBatchNode.setNodeValue(algoStrategyNode.getNodeValue());
                algoStrategyBatchNode.setVersion(algoStrategyNode.getVersion());
                algoStrategyBatchNode.setExecuteId(operatorId);
                algoStrategyBatchNode.setExecuteTime(new Date());
                algoStrategyBatchNode.setExecuteStatus(StrategyBatchExecuteStatusEnum.UN_EXECUTED.getCode());
                algoStrategyBatchNode.setTenantId(tenantId);
                algoStrategyBatchNodes.add(algoStrategyBatchNode);
            }
        }
        if(EmptyUtils.isNotEmpty(algoStrategyBatchNodes)){
            this.batchInsert(algoStrategyBatchNodes);
        }
    }

    public String getNextId() {
        return seriesDataService.getNextId("algo_strategy_batch_node", "BS", 8);
    }

    /**
     * 根据批次ID更新算法策略批次节点的状态
     *
     * @param batchId    批次ID
     * @param nodeValue  节点值
     * @param nodeType   节点类型
     * @param tenantId   租户ID
     * @param status     状态
     * @param message    错误信息
     */
    public void updateStatusByBatchId(Integer batchId, String nodeValue, String nodeType, String tenantId, String status, String message, Date executeTime) {
        AlgoStrategyBatchNode algoStrategyBatchNode = new AlgoStrategyBatchNode();
        algoStrategyBatchNode.setBatchId(batchId);
        algoStrategyBatchNode.setNodeValue(nodeValue);
        algoStrategyBatchNode.setNodeType(nodeType);
        algoStrategyBatchNode.setFinishTime(new Date());
        algoStrategyBatchNode.setExecuteStatus(status);
        algoStrategyBatchNode.setTenantId(tenantId);
        if(EmptyUtils.isNotEmpty(executeTime)){
            algoStrategyBatchNode.setExecuteTime(executeTime);
        }
        algoStrategyBatchNode.setFailMessage(message);
        AlgoStrategyBatchNode executeBatchNode = this.mapper.getExecuteBatchNode(algoStrategyBatchNode);
        algoStrategyBatchNode.setId(executeBatchNode.getId());
        this.mapper.updateStatusByBatchId(algoStrategyBatchNode);
    }

    /**
     * 处理算法策略节点
     *
     * @param algoStrategyNodes 算法策略节点列表
     * @param batchId           批次ID
     */
    public void handleBatchNode(List<AlgoStrategyNode> algoStrategyNodes, Integer batchId) {
        Iterator<AlgoStrategyNode> iterator = algoStrategyNodes.iterator();
        while (iterator.hasNext()) {
            AlgoStrategyNode algoStrategyNode = iterator.next();
            if(EmptyUtils.isNotEmpty(algoStrategyNode.getNodeValue())){
                AlgoStrategyBatchNode algoStrategyBatchNode = this.mapper.selectByTypeAndValue(algoStrategyNode.getNodeType(), algoStrategyNode.getNodeValue(), batchId);
                if(EmptyUtils.isNotEmpty(algoStrategyBatchNode) && StrategyBatchExecuteStatusEnum.FINAL_STATE.contains(algoStrategyBatchNode.getExecuteStatus())){
                    algoStrategyNode.setIsUpdate("1");
                }
                if(EmptyUtils.isEmpty(algoStrategyBatchNode)){
                    //过滤策略批次节点里面不存在的节点
                    iterator.remove();
                }
            }
        }
    }

    /**
     * 根据批次编码获取数据源批次节点列表
     *
     * @param batchCode 批次编码
     * @return 数据源批次节点列表
     */
    public List<AlgoStrategyBatchNodeRespDTO> getSourceBatchNodes(String batchCode) {
        return this.mapper.getAlgoStrategyBatchNodeByCode(batchCode, true);
    }

    /**
     * 根据批次编码获取指标批次节点列表
     *
     * @param batchCode 批次代码
     * @return 指标批次节点列表
     */
    public List<AlgoStrategyBatchNodeRespDTO> getIndicatorBatchNodes(String batchCode) {
        return this.mapper.getAlgoStrategyBatchNodeByCode(batchCode, false);
    }

    /**
     * 根据批次编码获取批次节点详情
     *
     * @param bizCode 业务代码
     * @return 批次节点详情对象
     */
    public AlgoStrategyBatchNodeVo getBatchNodeDetail(String bizCode) {
        return this.mapper.getBatchNodeDetail(bizCode);
    }

    /**
     * 异步重试算法策略批次节点
     *
     * @param batchNodeDetail 批次节点详情对象
     * @param tenantId        租户ID
     */
    @Async("algoCalcThreadPool")
    public void retryStrategyBatchNode(AlgoStrategyBatchNodeVo batchNodeDetail, String tenantId){
        Integer strategyBatchId = batchNodeDetail.getBatchId();
        AlgoContext.setTenantId(tenantId);
        String sourceId = batchNodeDetail.getNodeValue();
        String isUpdate = "1";
        String nodeType = batchNodeDetail.getNodeType();
        //重试的时候重置策略执行时间
        AlgoStrategyBatch algoStrategyBatch = new AlgoStrategyBatch();
        algoStrategyBatch.setId(strategyBatchId);
        algoStrategyBatch.setExecuteTime(new Date());
        algoStrategyBatch.setExecuteStatus(StrategyBatchExecuteStatusEnum.IN_PROGRESS.getCode());
        algoStrategyBatchService.getMapper().updateByPrimaryKeySelective(algoStrategyBatch);
        this.updateExecuteByBatchId(tenantId, sourceId, nodeType, strategyBatchId, StrategyBatchExecuteStatusEnum.IN_PROGRESS.getCode());
        //获取执行参数
        List<AlgoParamResVo> algoStrategyBatchParams = algoStrategyBatchParamService.getMapper().selectByBatchId(strategyBatchId);
        for(AlgoParamResVo algoStrategyBatchParamVo : algoStrategyBatchParams){
            if(!"1".equals(algoStrategyBatchParamVo.getParamType())){
                algoStrategyBatchParamVo.setParamValue(JSONArray.parse(algoStrategyBatchParamVo.getParamValue().toString()));
            }
        }
        try {
            //数据源节点执行
            if("1".equals(nodeType)){
                algoDataCollectService.collectData(strategyBatchId, sourceId, isUpdate, algoStrategyBatchParams);
            } else {
                //指标节点执行
                algoIndicatorService.executeCalcIndicator(strategyBatchId, sourceId, isUpdate, algoStrategyBatchParams);
            }

            //更新策略批次节点状态
            this.updateStatusByBatchId(strategyBatchId, sourceId, nodeType, tenantId, StrategyBatchExecuteStatusEnum.EXECUTION_COMPLETED.getCode(), null, null);
        } catch (Exception e) {
            log.error("策略批次执行失败:{}", ExceptionUtils.getStackTrace(e));
            //截取错误信息，如果超过200个字符，则截取前200个字符
            String message = e.getMessage().length() > 200 ? e.getMessage().substring(0, 200) : e.getMessage();
            //如果执行失败，更新当前节点状态
            this.updateStatusByBatchId(strategyBatchId, sourceId, nodeType, tenantId, StrategyBatchExecuteStatusEnum.EXECUTION_FAILED.getCode(), message, null);
        } finally {
            //释放锁
            String lockKey = redisLockUtils.setLockKey(batchNodeDetail.getBizCode());
            redisLockUtils.unlock(lockKey, batchNodeDetail.getBizCode());
        }
        //检查所有节点是否都执行成功，如果都成功，则更新策略批次状态为执行完成
        Map<String, Object> map = new HashMap<>();
        map.put("batchId", strategyBatchId);
        map.put("tenantId", tenantId);
        List<AlgoStrategyBatchNode> algoStrategyBatchNodes = this.mapper.selectAll(map);
        if(EmptyUtils.isNotEmpty(algoStrategyBatchNodes)){
            Boolean isAllSuccess = true;
            for(AlgoStrategyBatchNode batchNode : algoStrategyBatchNodes){
                if(!StrategyBatchExecuteStatusEnum.EXECUTION_COMPLETED.equals(batchNode.getExecuteStatus())){
                    isAllSuccess = false;
                }
            }
            if(isAllSuccess){
                algoStrategyBatchService.updateStatusByBatchId(strategyBatchId, StrategyBatchExecuteStatusEnum.EXECUTION_COMPLETED.getCode());
            } else {
                algoStrategyBatchService.updateStatusByBatchId(strategyBatchId, StrategyBatchExecuteStatusEnum.EXECUTION_FAILED.getCode());
            }
        }
    }

    public void updateExecuteByBatchId(String tenantId, String sourceId, String nodeType, Integer strategyBatchId, String status) {
        AlgoStrategyBatchNode algoStrategyBatchNode = new AlgoStrategyBatchNode();
        algoStrategyBatchNode.setNodeValue(sourceId);
        algoStrategyBatchNode.setNodeType(nodeType);
        algoStrategyBatchNode.setBatchId(strategyBatchId);
        algoStrategyBatchNode.setTenantId(tenantId);
        algoStrategyBatchNode.setExecuteTime(new Date());
        algoStrategyBatchNode.setExecuteStatus(status);
        AlgoStrategyBatchNode executeBatchNode = this.mapper.getExecuteBatchNode(algoStrategyBatchNode);
        algoStrategyBatchNode.setId(executeBatchNode.getId());
        this.mapper.updateExecuteByBatchId(algoStrategyBatchNode);
    }

    public List<AlgoStrategyBatchNode> getByDataSpaceId(Integer batchId) {
        return mapper.getByDataSpaceId(batchId);
    }

    public List<AlgoStrategyBatchNode> getNodeByDataSpaceId(Integer dataSpaceId) {
        return mapper.getNodeByDataSpaceId(dataSpaceId);
    }

    public void delByDataSpaceId(Integer dataSpaceId) {
        mapper.delByDataSpaceId(dataSpaceId);
    }

    public void delIndicatorNodeByBatchIds(Set<Integer> batchIds) {
        mapper.delIndicatorNodeByBatchIds(batchIds);
    }

    public AlgoIndicatorConfigVo getIndicatorVersionConfig(Integer batchNodeId) {
        AlgoStrategyBatchNode node = mapper.selectByPrimaryKey(batchNodeId);
        return algoIndicatorVersionService.getConfigByVersion(node.getNodeValue(), node.getVersion());
    }

    public JSONArray getIndicatorNodeFields(StrategyBatchNodeDataReqVo reqVo) {
        AlgoStrategyBatchNodeDTO batchNode = mapper.getStrategyBatchNodeById(reqVo.getBatchNodeId());
        JSONObject nodeContent = algoIndicatorVersionService.getNodeContent(batchNode.getVersionId(), reqVo.getIndicatorNodeId());
        return AlgoIndicatorHelper.getOutputFields(nodeContent);
    }

    public PageInfo<Map> getIndicatorNodeData(PageRequest<StrategyBatchNodeDataReqVo> pageRequest) {
        StrategyBatchNodeDataReqVo param = pageRequest.getParam();
        AlgoStrategyBatchNodeDTO batchNode = mapper.getStrategyBatchNodeById(param.getBatchNodeId());
        JSONObject nodeContent = algoIndicatorVersionService.getNodeContent(batchNode.getVersionId(), param.getIndicatorNodeId());
        return algoProcedureService.getIndicatorNodeData(pageRequest, batchNode, nodeContent);
    }
}
