package com.kbao.algo.strategyBatchNode.dao;

import com.kbao.algo.strategyBatchNode.bean.AlgoStrategyBatchNodeDTO;
import com.kbao.algo.strategyBatchNode.bean.AlgoStrategyBatchNodeRespDTO;
import com.kbao.algo.strategyBatchNode.bean.AlgoStrategyBatchNodeVo;
import com.kbao.algo.strategyBatchNode.entity.AlgoStrategyBatchNode;
import com.kbao.kbcbsc.dao.sql.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
* <AUTHOR>
* @Description 算法规则-策略执行批次节点Dao类
* @Date 2024-10-29
*/
public interface AlgoStrategyBatchNodeMapper extends BaseMapper<AlgoStrategyBatchNode, Integer>{
	/**
	 * 更新批次节点完成状态
	 */
	int updateStatusByBatchId(AlgoStrategyBatchNode algoStrategyBatchNode);

	/**
	 * 更新批次节点执行状态
	 */
	int updateExecuteByBatchId(AlgoStrategyBatchNode algoStrategyBatchNode);

	/**
	 * 查询批次节点
	 */
	AlgoStrategyBatchNode getExecuteBatchNode(AlgoStrategyBatchNode algoStrategyBatchNode);

	/**
	 * 查询批次节点列表
	 */
	List<AlgoStrategyBatchNodeVo> getAlgoStrategyBatchNodes(AlgoStrategyBatchNode vo);

	/**
	 * 根据批次编码查询批次节点列表
	 */
	List<AlgoStrategyBatchNodeRespDTO> getAlgoStrategyBatchNodeByCode(@Param("batchCode") String batchCode, @Param("isSource") Boolean isSource);

	/**
	 * 根据批次id和节点类型查询批次节点
	 */
	AlgoStrategyBatchNode selectByTypeAndValue(@Param("nodeType") String nodeType, @Param("nodeValue") String nodeValue,@Param("batchId") int batchId);

	/**
	 *  根据批次编码查询批次节点
	 */
	AlgoStrategyBatchNodeVo getBatchNodeDetail(@Param("bizCode") String bizCode);

	List<AlgoStrategyBatchNode> getByDataSpaceId(@Param("dataSpaceId") Integer dataSpaceId);

	List<AlgoStrategyBatchNode> getNodeByDataSpaceId(@Param("dataSpaceId") Integer dataSpaceId);

	void delByDataSpaceId(@Param("dataSpaceId") Integer dataSpaceId);

	void delIndicatorNodeByBatchIds(@Param("batchIds") Set<Integer> batchIds);

	AlgoStrategyBatchNodeDTO getStrategyBatchNodeById(@Param("batchNodeId") Integer batchNodeId);
}