package com.kbao.algo.strategyBatch.dao;

import com.kbao.algo.strategyBatch.bean.AlgoStrategyBatchPageReqVo;
import com.kbao.algo.strategyBatch.bean.AlgoStrategyBatchVo;
import com.kbao.algo.strategyBatch.entity.AlgoStrategyBatch;
import com.kbao.kbcbsc.dao.sql.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
* <AUTHOR>
* @Description 算法规则-策略执行批次Dao类
* @Date 2024-10-29
*/
public interface AlgoStrategyBatchMapper  extends BaseMapper<AlgoStrategyBatch, Integer>{

    /**
     * 查询算法规则-策略执行批次列表
     */
    List<AlgoStrategyBatchVo> getAlgoStrategyBatch(AlgoStrategyBatchPageReqVo vo);


    /**
     * 根据策略批次编码查询算法规则-策略执行批次
     */
    AlgoStrategyBatch getAlgoStrategyBatchByCode(@Param("bizCode") String bizCode);

    List<AlgoStrategyBatchVo> getStrategyBatchByIds(@Param("batchIds") Set<Integer> batchIds);

    void delByDataSpaceId(@Param("dataSpaceId") Integer dataSpaceId);

    void delByBatchIds(@Param("batchIds") Set<Integer> batchIds);
}