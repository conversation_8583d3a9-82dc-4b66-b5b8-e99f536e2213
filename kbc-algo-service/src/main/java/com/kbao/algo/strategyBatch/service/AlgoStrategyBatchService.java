package com.kbao.algo.strategyBatch.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.kbao.algo.dataSpace.service.AlgoDataSpaceService;
import com.kbao.algo.enums.AlgoNodeTypeEnum;
import com.kbao.algo.indicator.indicator.service.AlgoIndicatorService;
import com.kbao.algo.mq.bean.MqPushMessageVO;
import com.kbao.algo.mq.service.MqProducerService;
import com.kbao.algo.redis.RedisLockUtils;
import com.kbao.algo.redis.RedissonUtil;
import com.kbao.algo.series.service.SeriesDataService;
import com.kbao.algo.sourceField.bean.AlgoParamResVo;
import com.kbao.algo.strategy.entity.AlgoStrategy;
import com.kbao.algo.strategyBatch.bean.AlgoStrategyBatchPageReqVo;
import com.kbao.algo.strategyBatch.bean.AlgoStrategyBatchVo;
import com.kbao.algo.strategyBatch.dao.AlgoStrategyBatchMapper;
import com.kbao.algo.strategyBatch.entity.AlgoStrategyBatch;
import com.kbao.algo.strategyBatch.enums.StrategyBatchExecuteStatusEnum;
import com.kbao.algo.strategyBatchNode.entity.AlgoStrategyBatchNode;
import com.kbao.algo.strategyBatchParam.bean.AlgoStrategyBatchParamVo;
import com.kbao.algo.strategyNode.entity.AlgoStrategyNode;
import com.kbao.algo.util.AlgoConstants;
import com.kbao.algo.util.AlgoContext;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.redis.util.RedisUtil;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.algo.source.service.AlgoDataCollectService;
import com.kbao.algo.source.service.AlgoSourceService;
import com.kbao.algo.strategyBatch.bean.AlgoStrategyBatchSendVo;
import com.kbao.algo.strategyBatchNode.service.AlgoStrategyBatchNodeService;
import com.kbao.algo.strategyBatchParam.service.AlgoStrategyBatchParamService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RSemaphore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
* <AUTHOR>
* @Description 算法规则-策略执行批次Service类
* @Date 2024-10-29
*/
@Service
@Slf4j
public class AlgoStrategyBatchService extends BaseSQLServiceImpl<AlgoStrategyBatch, Integer, AlgoStrategyBatchMapper> {
    @Autowired
    private SeriesDataService seriesDataService;

    @Autowired
    private AlgoDataCollectService algoDataCollectService;

    @Lazy
    @Autowired
    private AlgoStrategyBatchNodeService algoStrategyBatchNodeService;

    @Autowired
    private AlgoStrategyBatchParamService algoStrategyBatchParamService;

    @Autowired
    private AlgoIndicatorService algoIndicatorService;

    @Autowired
    private AlgoSourceService algoSourceService;

    @Autowired
    private MqProducerService mqProducerService;

    @Autowired
    private ThreadPoolTaskExecutor algoCalcThreadPool;
    @Autowired
    private RedisLockUtils redisLockUtils;
    @Autowired
    private RedissonUtil redissonUtil;
    @Autowired
    private AlgoDataSpaceService algoDataSpaceService;

    /**
     * 获取算法策略批次列表
     *
     * @param page 分页请求对象，包含分页信息和查询参数
     * @return 算法策略批次列表
     */
    public List<AlgoStrategyBatchVo> getAlgoStrategyBatch(RequestObjectPage<AlgoStrategyBatchPageReqVo> page){
        PageHelper.startPage(page.getPageNum(), page.getPageSize());
        List<AlgoStrategyBatchVo> strategyBatchVos = mapper.getAlgoStrategyBatch(page.getParam());
        if (EmptyUtils.isEmpty(strategyBatchVos)) {
            return strategyBatchVos;
        }
        for(AlgoStrategyBatchVo vo : strategyBatchVos){
            String paramContent = vo.getParamContent();
            if (EmptyUtils.isNotEmpty(paramContent)) {
                String[] split = paramContent.split(",");
                vo.setParams(Arrays.asList(split));
            }
        }
        return strategyBatchVos;
    }

    public List<AlgoStrategyBatchVo> getAlgoStrategyBatch(Set<Integer> batchIds){
        return mapper.getStrategyBatchByIds(batchIds);
    }

    /**
     * 插入算法策略批次
     *
     * @param algoStrategy       算法策略对象
     * @param operatorId         操作人ID
     * @param tenantId           租户ID
     * @param settlementBatchId  结算批次ID
     * @return 插入的算法策略批次对象
     * @throws Exception 如果插入过程中发生异常，则抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    public AlgoStrategyBatch insert(AlgoStrategy algoStrategy, String operatorId, String tenantId, Integer settlementBatchId) {
        AlgoStrategyBatch algoStrategyBatch = new AlgoStrategyBatch();
        algoStrategyBatch.setBizCode(getNextId());
        algoStrategyBatch.setDataSpaceId(settlementBatchId);
        algoStrategyBatch.setStrategyId(algoStrategy.getStrategyId());
        algoStrategyBatch.setRemark(algoStrategy.getRemark());
        algoStrategyBatch.setExecuteStatus(StrategyBatchExecuteStatusEnum.UN_EXECUTED.getCode());
        algoStrategyBatch.setExecuteId(operatorId);
        algoStrategyBatch.setTenantId(tenantId);
        this.mapper.insertSelective(algoStrategyBatch);
        return algoStrategyBatch;
    }

    /**
     * 根据批次ID更新算法策略批次的状态
     *
     * @param batchId 批次ID
     * @param status  状态值
     * @return 更新后的算法策略批次对象
     * @throws Exception 如果更新过程中发生异常，则抛出异常
     */
    public AlgoStrategyBatch updateStatusByBatchId(Integer batchId, String status) {
        AlgoStrategyBatch algoStrategyBatch = new AlgoStrategyBatch();
        algoStrategyBatch.setId(batchId);
        algoStrategyBatch.setFinishTime(new Date());
        algoStrategyBatch.setExecuteStatus(status);
        this.mapper.updateByPrimaryKeySelective(algoStrategyBatch);
        return algoStrategyBatch;
    }

    public AlgoStrategyBatch updateExecuteStatus(Integer batchId) {
        AlgoStrategyBatch algoStrategyBatch = new AlgoStrategyBatch();
        algoStrategyBatch.setId(batchId);
        algoStrategyBatch.setExecuteTime(new Date());
        algoStrategyBatch.setExecuteStatus(StrategyBatchExecuteStatusEnum.IN_PROGRESS.getCode());
        this.mapper.updateByPrimaryKeySelective(algoStrategyBatch);
        return algoStrategyBatch;
    }

    public String getNextId() {
        return seriesDataService.getNextId("algo_strategy_batch", "BAT", 8);
    }

    /**
     * 异步执行算法策略批次流程
     *
     * @param algoStrategyBatch   算法策略批次对象
     * @param algoStrategyNodes   算法策略节点列表
     * @param algoParamResVos     请求参数映射表
     * @param tenantId            租户ID
     */
    @Async("algoCalcThreadPool")
    public void executeStrategyBatch(AlgoStrategyBatch algoStrategyBatch, List<AlgoStrategyNode> algoStrategyNodes, List<AlgoParamResVo> algoParamResVos, String strategyCode, String tenantId){
        try {
            AlgoContext.setTenantId(tenantId);
            log.info("线程池当前执行线程数:{}，等待数:{}", algoCalcThreadPool.getActiveCount(), algoCalcThreadPool.getThreadPoolExecutor().getQueue().size());
            AlgoStrategyBatch algoStrategyBatchUpdated = doExecuteStrategyBatch(algoStrategyBatch, algoStrategyNodes, algoParamResVos, tenantId);
            algoStrategyBatchUpdated.setBizCode(algoStrategyBatch.getBizCode());
            //发送MQ广播通知
            sendMQBroadcast(algoStrategyBatchUpdated, strategyCode);
        } catch (Exception e) {
            log.error("策略批次执行失败:{}", ExceptionUtils.getStackTrace(e));
        } finally {
            // 释放锁
            String lockKey = redisLockUtils.setLockKey(algoStrategyBatch.getBizCode());
            redisLockUtils.unlock(lockKey, algoStrategyBatch.getBizCode());
        }
    }

    public AlgoStrategyBatch doExecuteStrategyBatch(AlgoStrategyBatch algoStrategyBatch, List<AlgoStrategyNode> algoStrategyNodes, List<AlgoParamResVo> algoParamResVos, String tenantId) throws Exception {
        boolean isSuccess = true;
        Integer strategyBatchId = algoStrategyBatch.getId();
        RSemaphore semaphore = null;
        Boolean lastIsSource = null;
        String sourceId = null, nodeType = null, isUpdate;
        int sleepTime = new Random().nextInt(3000);
        Thread.sleep(sleepTime);
        try {
            for(AlgoStrategyNode algoStrategyNode : algoStrategyNodes) {
                sourceId = algoStrategyNode.getNodeValue();
                if (EmptyUtils.isEmpty(sourceId)) {
                    continue;
                }
                isUpdate = algoStrategyNode.getIsUpdate();
                nodeType = algoStrategyNode.getNodeType();
                Boolean isSource = AlgoNodeTypeEnum.SOURCE.getCode().equals(nodeType);
                String lockKey = isSource ? AlgoConstants.LOCK_ALGO_SOURCE : AlgoConstants.LOCK_ALGO_INDICATOR;
                if (!isSource.equals(lastIsSource)) {
                    // 不同操作需要释放许可，并获取对应信号量许可
                    redissonUtil.releaseSemaphore(semaphore, 1);
                    semaphore = redissonUtil.getSemaphore(lockKey);
                    semaphore.acquire(1);
                }
                if (lastIsSource == null) {
                    // 首次拿到许可之后，更新策略执行状态
                    this.updateExecuteStatus(algoStrategyBatch.getId());
                    //校验数据空间内存
                    algoDataSpaceService.isOverMemoryLimit(algoStrategyBatch.getDataSpaceId());
                }
                log.info("lock:{}, thread:{}, availablePermits:{}", lockKey, Thread.currentThread().getName(), semaphore.availablePermits());
                algoStrategyBatchNodeService.updateExecuteByBatchId(tenantId, sourceId, nodeType, strategyBatchId, StrategyBatchExecuteStatusEnum.IN_PROGRESS.getCode());
                // 执行策略节点
                this.executeStrategyBatchNode(strategyBatchId, sourceId, isUpdate, algoParamResVos, isSource, 0);
                //更新策略批次节点状态
                algoStrategyBatchNodeService.updateStatusByBatchId(strategyBatchId, sourceId, nodeType, tenantId, StrategyBatchExecuteStatusEnum.EXECUTION_COMPLETED.getCode(), null, null);
                lastIsSource = isSource;
            }
        } catch (Exception e) {
            log.error("策略批次执行失败:{}", ExceptionUtils.getStackTrace(e));
            isSuccess = false;
            //截取错误信息，如果超过200个字符，则截取前200个字符
            String message = e.getMessage().length() > 200 ? e.getMessage().substring(0, 200) : e.getMessage();
            //如果执行失败，更新当前节点状态
            algoStrategyBatchNodeService.updateStatusByBatchId(strategyBatchId, sourceId, nodeType, tenantId, StrategyBatchExecuteStatusEnum.EXECUTION_FAILED.getCode(),message,null);
        } finally {
            redissonUtil.releaseSemaphore(semaphore, 1);
        }
        String executeStatus = isSuccess ? StrategyBatchExecuteStatusEnum.EXECUTION_COMPLETED.getCode() : StrategyBatchExecuteStatusEnum.EXECUTION_FAILED.getCode();
        return this.updateStatusByBatchId(algoStrategyBatch.getId(), executeStatus);
    }

    public void executeStrategyBatchNode(Integer strategyBatchId, String sourceId, String isUpdate, List<AlgoParamResVo> algoParamResVos, Boolean isSource, int tryCount) throws Exception {
        try {
            if (isSource) {
                // 抽取数据源
                algoDataCollectService.collectData(strategyBatchId, sourceId, isUpdate, algoParamResVos);
            } else {
                // 计算指标
                algoIndicatorService.executeCalcIndicator(strategyBatchId, sourceId, isUpdate, algoParamResVos);
            }
        } catch (Exception e) {
            log.error("策略批次执行失败 strategyBatchId:{}, sourceId:{}, error:{}", strategyBatchId, sourceId, ExceptionUtils.getStackTrace(e));
            if (tryCount < 0) {
                // 先不重试
                Thread.sleep(2000);
                executeStrategyBatchNode(strategyBatchId, sourceId, isUpdate, algoParamResVos, isSource, tryCount + 1);
            } else {
                throw e;
            }
        }
    }

    private void sendMQBroadcast(AlgoStrategyBatch algoStrategyBatch, String strategyCode) {
        List<AlgoStrategyBatchParamVo> paramList = algoStrategyBatchParamService.getParamList(algoStrategyBatch.getId());
        AlgoStrategyBatchSendVo algoStrategyBatchSendVo = new AlgoStrategyBatchSendVo();
        algoStrategyBatchSendVo.setStrategyCode(strategyCode);
        algoStrategyBatchSendVo.setParamList(paramList);
        algoStrategyBatchSendVo.setBatchCode(algoStrategyBatch.getBizCode());
        algoStrategyBatchSendVo.setExecuteStatus(algoStrategyBatch.getExecuteStatus());
        algoStrategyBatchSendVo.setFinishTime(algoStrategyBatch.getFinishTime());
        MqPushMessageVO mqPushMessageVO = new MqPushMessageVO();
        mqPushMessageVO.setBizId(algoStrategyBatch.getBizCode());
        mqPushMessageVO.setMessageBody(JSONObject.toJSONString(algoStrategyBatchSendVo));
        mqProducerService.algoBatchSendBroadcast(mqPushMessageVO);
    }

    public void cleanStrategyBatch(Integer dataSpaceId) {
        Set<String> sourceCodes = new HashSet<>();
        Set<String> indicatorCodes = new HashSet<>();
        List<AlgoStrategyBatchNode> nodeList = algoStrategyBatchNodeService.getByDataSpaceId(dataSpaceId);
        if (EmptyUtils.isEmpty(nodeList)) {
            return;
        }
        for (AlgoStrategyBatchNode node : nodeList) {
            if (AlgoNodeTypeEnum.SOURCE.getCode().equals(node.getNodeType())) {
                sourceCodes.add(node.getNodeValue());
            } else {
                indicatorCodes.add(node.getNodeValue());
            }
        }
        //删除数据源表数据
        sourceCodes.forEach(code -> algoSourceService.dropSourceTable(code, dataSpaceId));
        //删除指标表数据
        indicatorCodes.forEach(code -> {
            algoIndicatorService.dropIndicatorTable(code, dataSpaceId);
        });
        //删除批次节点表数据
        algoStrategyBatchNodeService.delByDataSpaceId(dataSpaceId);
        //删除批次参数表数据
        algoStrategyBatchParamService.delByDataSpaceId(dataSpaceId);
        //删除批次表数据
        mapper.delByDataSpaceId(dataSpaceId);
    }


    public void cleanIndicatorBatch(Integer dataSpaceId) {
        //纯计算指标批次
        Set<Integer> indicatorBatchIds = new HashSet<>();
        Set<Integer> sourceBatchIds = new HashSet<>();
        Set<String> indicatorCodes = new HashSet<>();
        List<AlgoStrategyBatchNode> nodeList = algoStrategyBatchNodeService.getNodeByDataSpaceId(dataSpaceId);
        if (EmptyUtils.isEmpty(nodeList)) {
            return;
        }
        for (AlgoStrategyBatchNode node : nodeList) {
            if (AlgoNodeTypeEnum.SOURCE.getCode().equals(node.getNodeType())) {
                sourceBatchIds.add(node.getBatchId());
            } else {
                indicatorCodes.add(node.getNodeValue());
                indicatorBatchIds.add(node.getBatchId());
            }
        }
        if (EmptyUtils.isEmpty(indicatorCodes)) {
            return;
        }
        //删除指标表数据
        indicatorCodes.forEach(code -> {
            algoIndicatorService.dropIndicatorTable(code, dataSpaceId);
        });
        //删除指标批次节点数据
        algoStrategyBatchNodeService.delIndicatorNodeByBatchIds(indicatorBatchIds);
        indicatorBatchIds.removeAll(sourceBatchIds);
        if (EmptyUtils.isNotEmpty(indicatorBatchIds)) {
            //删除指标批次参数数据
            algoStrategyBatchParamService.delByBatchIds(indicatorBatchIds);
            //删除指标批次数据
            mapper.delByBatchIds(indicatorBatchIds);
        }
    }
}
