package com.kbao.algo.dataSpace.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.algo.dataSpace.bean.DataSpaceListRespVo;
import com.kbao.algo.dataSpace.bean.DataSpaceOverviewVo;
import com.kbao.algo.dataSpace.bean.DataSpaceReqVo;
import com.kbao.algo.dataSpace.dao.AlgoDataSpaceMapper;
import com.kbao.algo.dataSpace.entity.AlgoDataSpace;
import com.kbao.algo.series.service.SeriesDataService;
import com.kbao.algo.strategyBatch.service.AlgoStrategyBatchService;
import com.kbao.algo.util.AlgoContext;
import com.kbao.algo.setting.service.AlgoCommonSettingService;
import com.kbao.algo.util.StringUtil;
import com.kbao.commons.web.PageRequest;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.SysLoginUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
* <AUTHOR> jie
* @Description Service类
* @Date 2024-11-08
*/
@Service
public class AlgoDataSpaceService extends BaseSQLServiceImpl<AlgoDataSpace, Integer, AlgoDataSpaceMapper> {
    @Autowired
    private SeriesDataService seriesDataService;
    @Autowired
    private @Lazy AlgoStrategyBatchService algoStrategyBatchService;
    @Autowired
    private AlgoCommonSettingService algoCommonSettingService;

    public String create(DataSpaceReqVo reqVo) {
        String bizCode = getNextId();
        String createId = SysLoginUtils.getUserId();
        AlgoDataSpace dataSpace = AlgoDataSpace.builder().name(reqVo.getName())
                .bizCode(bizCode).remark(reqVo.getRemark())
                .createId(createId == null ? "sys" : createId)
                .createTime(DateUtils.getCurrentDate())
                .tenantId(AlgoContext.getTenantId()).build();
        dataSpace.setMemoryLimit(algoCommonSettingService.getDataSpaceMemoryLimit());
        mapper.insert(dataSpace);
        return bizCode;
    }

    public void update(AlgoDataSpace dataSpace) {
        dataSpace.setUpdateId(SysLoginUtils.getUserId());
        mapper.updateByPrimaryKeySelective(dataSpace);
    }

    public AlgoDataSpace getByStrategyBatchId(Integer id) {
        return mapper.getByStrategyBatchId(id);
    }

    public AlgoDataSpace getByCode(String code) {
        return mapper.getByCode(code);
    }

    public void isOverMemoryLimit(Integer dataSpaceId) {
        AlgoDataSpace algoDataSpace = mapper.selectByPrimaryKey(dataSpaceId);
        Double memoryUsage = mapper.getMemoryUsage(dataSpaceId);
        if (memoryUsage != null && memoryUsage >= algoDataSpace.getMemoryLimit()) {
            throw new RuntimeException("数据空间内存超出上限");
        }
    }

    public void delDataSpace(Integer dataSpaceId) {
        algoStrategyBatchService.cleanStrategyBatch(dataSpaceId);
        mapper.deleteByPrimaryKey(dataSpaceId);
    }

    public String getNextId() {
        return seriesDataService.getNextId("algo_data_space", "DS", 6);
    }

    /**
     * 获取数据空间列表
     */
    public PageInfo<DataSpaceListRespVo> getDataSpaceList(PageRequest<AlgoDataSpace> pageRequest) {
        PageHelper.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());
        List<DataSpaceListRespVo> list = mapper.getDataSpaceList(pageRequest.getParam());
        return new PageInfo<>(list);
    }

    /**
     * 获取数据空间概览，使用实际COUNT查询获取准确数量
     */
    public PageInfo<DataSpaceOverviewVo> getDataSpaceOverview(PageRequest<DataSpaceOverviewVo> pageRequest) {
        PageHelper.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());
        List<DataSpaceOverviewVo> list = mapper.getDataSpaceOverview(pageRequest.getParam());
        return new PageInfo<>(list);
    }
}
