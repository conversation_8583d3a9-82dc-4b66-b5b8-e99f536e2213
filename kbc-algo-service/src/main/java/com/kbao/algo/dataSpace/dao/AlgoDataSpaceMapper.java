package com.kbao.algo.dataSpace.dao;

import com.kbao.algo.dataSpace.bean.DataSpaceListRespVo;
import com.kbao.algo.dataSpace.bean.DataSpaceOverviewVo;
import com.kbao.algo.dataSpace.entity.AlgoDataSpace;
import com.kbao.kbcbsc.dao.sql.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据空间Mapper
 */
public interface AlgoDataSpaceMapper extends BaseMapper<AlgoDataSpace, Integer> {

    /**
     * 获取数据空间列表
     */
    List<DataSpaceListRespVo> getDataSpaceList(AlgoDataSpace dataSpace);

    List<DataSpaceOverviewVo> getDataSpaceOverview(DataSpaceOverviewVo vo);

    /**
     * 根据编码获取数据空间
     */
    AlgoDataSpace getByCode(@Param("bizCode") String bizCode);

    Double getMemoryUsage(@Param("dataSpaceId") Integer dataSpaceId);

    /**
     * 根据策略批次ID获取数据空间
     */
    AlgoDataSpace getByStrategyBatchId(@Param("strategyBatchId") Integer id);
}