package com.kbao.algo.source.dao;

import com.kbao.algo.source.bean.AlgoDataReqVo;
import com.kbao.algo.source.bean.AlgoSourceResVo;
import com.kbao.algo.source.entity.AlgoSource;
import com.kbao.algo.sourceField.entity.AlgoSourceField;
import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.algo.source.bean.AlgoSourceReqVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
* <AUTHOR> jie
* @Description Dao类
* @Date 2024-10-25
*/
public interface AlgoSourceMapper  extends BaseMapper<AlgoSource, Integer>{

    List<AlgoSourceResVo> getAlgoSourceList(AlgoSourceReqVo reqVo);

    AlgoSource getByCode(@Param("bizCode") String bizCode);

    int isExistSource(@Param("bizCode") String bizCode, @Param("sourceId") Integer sourceId);

    int isExistSourceTable(@Param("tableName") String tableName);

    int isExistField(@Param("tableName") String tableName, @Param("fieldName") String fieldName);

    int ddlCreateTable(@Param("tableName") String tableName,
                       @Param("fieldList") List<AlgoSourceField> fieldList,
                       @Param("indexFields") List<String> indexFields);

    int ddlCreateStorageTable(@Param("tableName") String tableName,
                       @Param("fieldList") List<AlgoSourceField> fieldList,
                       @Param("indexFields") List<String> indexFields);

    int ddlDropTable(@Param("tableName") String tableName);

    int insertSourceData(@Param("strategyBatchId") Integer strategyBatchId,
                        @Param("tableName") String tableName,
                        @Param("fieldNames") List<String> fieldNames,
                        @Param("fieldValues") List<Object> fieldValues,
                        @Param("tenantId") String tenantId);

    int insertSourceStorageData(@Param("strategyBatchId") Integer strategyBatchId,
                                @Param("storageTableName") String storageTableName,
                                @Param("tableName") String tableName,
                                @Param("fieldNames") List<String> fieldNames);

    int isExistSourceData(@Param("tableName") String tableName);

    List<Map> getSourceDataList(AlgoDataReqVo req);

    List<Map> getDataList(AlgoDataReqVo reqVo);

    Map<String, Object> statSourceData(@Param("strategyBatchId") Integer strategyBatchId,
                                       @Param("tableName") String tableName,
                                       @Param("checkFields") Set<String> checkFields);

    int isExistSourceDataByParams(@Param("tableName") String tableName, @Param("paramList") List<Map<String, Object>> params);

    int deleteSourceDataByParams(@Param("tableName") String tableName, @Param("paramList") List<Map<String, Object>> params);

    int delSourceStorageData(@Param("tableName") String tableName, @Param("strategyBatchId") Integer strategyBatchId);
}