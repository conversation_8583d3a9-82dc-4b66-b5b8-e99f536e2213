package com.kbao.algo.source.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.algo.dataSpace.entity.AlgoDataSpace;
import com.kbao.algo.dataSpace.service.AlgoDataSpaceService;
import com.kbao.algo.enums.AlgoFieldTypeEnum;
import com.kbao.algo.enums.AlgoNodeTypeEnum;
import com.kbao.algo.indicator.indicatorField.bean.DataField;
import com.kbao.algo.series.service.SeriesDataService;
import com.kbao.algo.source.bean.*;
import com.kbao.algo.source.entity.AlgoSource;
import com.kbao.algo.sourceField.entity.AlgoSourceField;
import com.kbao.algo.strategyBatch.bean.AlgoStrategyBatchVo;
import com.kbao.algo.strategyBatch.service.AlgoStrategyBatchService;
import com.kbao.algo.strategyBatchNode.bean.StrategyBatchNodeDataReqVo;
import com.kbao.algo.strategyBatchParam.service.AlgoStrategyBatchParamService;
import com.kbao.algo.util.AlgoConstants;
import com.kbao.algo.util.EasyExcelUtil;
import com.kbao.algo.util.excelHalper.CustomSqlDateConverter;
import com.kbao.algo.util.excelHalper.CustomTimeStampConverter;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.SysLoginUtils;
import com.kbao.algo.indicator.indicatorField.service.AlgoIndicatorFieldService;
import com.kbao.algo.source.dao.AlgoSourceMapper;
import com.kbao.algo.sourceField.service.AlgoSourceDataFieldService;
import com.kbao.algo.sourceField.service.AlgoSourceFieldService;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR> jie
* @Description Service类
* @Date 2024-10-25
*/
@Slf4j
@Service
public class AlgoSourceService extends BaseSQLServiceImpl<AlgoSource, Integer, AlgoSourceMapper> {
    @Autowired
    private SeriesDataService seriesDataService;
    @Autowired
    private AlgoSourceFieldService algoSourceFieldService;
    @Autowired
    private AlgoDataSpaceService algoDataSpaceService;
    @Autowired
    private AlgoSourceDataFieldService algoSourceDataFieldService;
    @Autowired
    private AlgoIndicatorFieldService algoIndicatorFieldService;
    @Lazy
    @Autowired
    private AlgoStrategyBatchService algoStrategyBatchService;

    @Transactional(rollbackFor = Exception.class)
    public void save(AlgoSource algoSource) {
        if(EmptyUtils.isEmpty(algoSource.getBizCode())){
            throw new BusinessException("数据源编码不能为空");
        }
        int count = mapper.isExistSource(algoSource.getBizCode(), algoSource.getSourceId());
        if(count > 0){
            throw new BusinessException("该数据源编码已存在");
        }
        if (algoSource.getSourceId() == null) {
            algoSource.setCreateId(BscUserUtils.getUserId());
            algoSource.setTenantId(SysLoginUtils.getUser().getTenantId());
            this.insert(algoSource);
        } else {
            algoSource.setUpdateId(BscUserUtils.getUserId());
            this.updateByPrimaryKeySelective(algoSource);
        }
    }

    public PageInfo<AlgoSourceResVo> sourcePage(PageRequest<AlgoSourceReqVo> reqVo) {
        AlgoSourceReqVo param = reqVo.getParam();
        param.setTenantId(SysLoginUtils.getUser().getTenantId());
        PageHelper.startPage(reqVo.getPageNum(), reqVo.getPageSize());
        List<AlgoSourceResVo> list = mapper.getAlgoSourceList(param);
        return new PageInfo<>(list);
    }

    public PageInfo<Map> getSourceDataList(PageRequest<AlgoDataReqVo> reqVo) {
        AlgoDataReqVo param = reqVo.getParam();
        AlgoDataSpace dataSpace = algoDataSpaceService.getByStrategyBatchId(param.getStrategyBatchId());
        String tableName;
        if (AlgoNodeTypeEnum.SOURCE.getCode().equals(param.getNodeType())) {
            tableName = AlgoConstants.getSourceStorageTableName(AlgoConstants.getSourceTableName(param.getBizCode(), dataSpace.getId()));
        } else {
            tableName = AlgoConstants.getIndicatorTableName(param.getBizCode(), dataSpace.getId());
        }
        if (!isExistSourceTable(tableName)) {
            return new PageInfo<>();
        }
        param.setTableName(tableName);
        PageHelper.startPage(reqVo.getPageNum(), reqVo.getPageSize());
        List<Map> list = mapper.getSourceDataList(param);
        return new PageInfo<>(list);
    }

    public PageInfo<Map> getIndicatorDataList(PageRequest<StrategyBatchNodeDataReqVo> reqVo) {
        StrategyBatchNodeDataReqVo param = reqVo.getParam();
        param.setTableName(AlgoConstants.getIndicatorTableName(param.getBizCode(), param.getDataSpaceId()));
        if (!isExistSourceTable(param.getTableName())) {
            return new PageInfo<>();
        }
        PageHelper.startPage(reqVo.getPageNum(), reqVo.getPageSize());
        List<Map> list = mapper.getSourceDataList(param);
        return new PageInfo<>(list);
    }

    public PageInfo<Map> getSpaceDataList(PageRequest<AlgoDataReqVo> reqVo) {
        AlgoDataReqVo param = reqVo.getParam();
        String tableName;
        if (AlgoNodeTypeEnum.SOURCE.getCode().equals(param.getNodeType())) {
            tableName = AlgoConstants.getSourceTableName(param.getBizCode(), param.getDataSpaceId());
            if (!"1".equals(param.getDataType())) {
                tableName = AlgoConstants.getSourceStorageTableName(tableName);
            }
        } else {
            tableName = AlgoConstants.getIndicatorTableName(param.getBizCode(), param.getDataSpaceId());
        }
        if (!isExistSourceTable(tableName)) {
            return new PageInfo<>();
        }
        param.setTableName(tableName);
        PageHelper.startPage(reqVo.getPageNum(), reqVo.getPageSize());
        List<Map> list = mapper.getDataList(param);
        if (CollectionUtils.isNotEmpty(list)) {
            Set<Integer> strategyBatchIds = list.stream().map(item -> (Integer) item.get("strategyBatchId")).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(strategyBatchIds)) {
                List<AlgoStrategyBatchVo> algoStrategyBatch = algoStrategyBatchService.getAlgoStrategyBatch(strategyBatchIds);
                Map<Integer, AlgoStrategyBatchVo> batchCodeMap = algoStrategyBatch.stream()
                        .collect(Collectors.toMap(AlgoStrategyBatchVo::getId, Function.identity(), (key1, key2) -> key2));
                for (Map item : list) {
                    Integer strategyBatchId = MapUtils.getInteger(item, "strategyBatchId");
                    AlgoStrategyBatchVo algoStrategyBatchVo = batchCodeMap.get(strategyBatchId);
                    if (algoStrategyBatchVo != null) {
                        item.put("strategyBatchCode", algoStrategyBatchVo.getBizCode());
                        item.put("strategyBatchParams", algoStrategyBatchVo.getParamContent());
                    }
                }
            }
        }
        return new PageInfo<>(list);
    }

    public List<DataField> getDataFields(AlgoDataReqVo reqVo) {
        AlgoDataSpace dataSpace = algoDataSpaceService.getByStrategyBatchId(reqVo.getStrategyBatchId());
        if (AlgoNodeTypeEnum.SOURCE.getCode().equals(reqVo.getNodeType())) {
            return algoSourceDataFieldService.getSourceDataFields(dataSpace.getId(), reqVo.getBizCode());
        } else {
            return algoIndicatorFieldService.getIndicatorDataFields(dataSpace.getId(), reqVo.getBizCode());
        }
    }

    public List<DataField> getSpaceDataFields(AlgoDataReqVo reqVo) {
        List<DataField> dataFields;
        if (AlgoNodeTypeEnum.SOURCE.getCode().equals(reqVo.getNodeType())) {
            dataFields = algoSourceDataFieldService.getSourceDataFields(reqVo.getDataSpaceId(), reqVo.getBizCode());
        } else {
            dataFields = algoIndicatorFieldService.getIndicatorDataFields(reqVo.getDataSpaceId(), reqVo.getBizCode());
        }
        DataField batchCode = new DataField("strategyBatchCode", "批次编码", "");
        dataFields.add(0, batchCode);
        DataField batchParams = new DataField("strategyBatchParams", "执行参数", "");
        dataFields.add(1, batchParams);
        DataField createTime = new DataField("createTime", "创建时间", "");
        dataFields.add(createTime);
        return dataFields;
    }

    public AlgoSource getByCode(String bizCode) {
        return mapper.getByCode(bizCode);
    }

    public AlgoSourceTableDto getSourceTable(Integer dataSpaceId, String bizCode) {
        AlgoSource algoSource = this.getByCode(bizCode);
        List<AlgoSourceField> fields = algoSourceFieldService.listBySourceId(algoSource.getSourceId());
        if (CollectionUtils.isEmpty(fields)) {
            throw new BusinessException("数据源未配置字段");
        }
        String tableName = AlgoConstants.getSourceTableName(algoSource.getBizCode(), dataSpaceId);
        List<AlgoSourceTableDto.Field> fieldList = fields.stream().map(item -> new AlgoSourceTableDto.Field(item.getFieldName(), item.getName())).collect(Collectors.toList());
        return new AlgoSourceTableDto(tableName, fieldList);
    }

    public boolean isExistSourceTable(String tableName) {
        return mapper.isExistSourceTable(tableName) > 0;
    }

    public boolean isExistField(String tableName, String fieldName) {
        return mapper.isExistField(tableName, fieldName) > 0;
    }

    public boolean isExistSourceDataByParams(String tableName, List<Map<String, Object>> params) {
        return mapper.isExistSourceDataByParams(tableName, params) > 0;
    }

    public void deleteSourceDataByParams(Integer strategyBatchId, String tableName, List<Map<String, Object>> params) {
        int num = mapper.deleteSourceDataByParams(tableName, params);
        String storageTableName = AlgoConstants.getSourceStorageTableName(tableName);
        int storageNum = mapper.delSourceStorageData(storageTableName, strategyBatchId);
        logger.info("删除表数据 {}：{}条, {}：{}条", tableName, num, storageTableName, storageNum);
    }

    public void dropSourceTable(String bizCode, Integer dataSpaceId) {
        String tableName = AlgoConstants.getSourceTableName(bizCode, dataSpaceId);
        String storageTableName = AlgoConstants.getSourceStorageTableName(tableName);
        this.dropTable(tableName);
        this.dropTable(storageTableName);
        algoSourceDataFieldService.delSourceDataField(dataSpaceId, bizCode);
    }

    public void ddlDropTable(SourceDelReqVo reqVo) {
        this.dropSourceTable(reqVo.getSourceBizCode(), reqVo.getDataSpaceId());
    }

    public void dropTable(String tableName) {
        logger.info("删除表：{}, 执行用户：{}", tableName, BscUserUtils.getUserId());
        mapper.ddlDropTable(tableName);
    }

    @Transactional(rollbackFor = Exception.class)
    public void ddlCreateTable(Integer dataSpaceId, String sourceCode, String tableName, List<AlgoSourceField> fieldList) {
        if (CollectionUtils.isEmpty(fieldList)) {
            throw new BusinessException("数据源未配置字段");
        }
        algoSourceDataFieldService.saveSourceDataFields(dataSpaceId, sourceCode, fieldList);
        List<String> indexFields = new ArrayList<>();
        fieldList.forEach(item -> {
            String fieldType = AlgoFieldTypeEnum.buildFieldType(item.getFieldType(), item.getFieldLength());
            item.setFieldType(fieldType);
            if ("1".equals(item.getIsIndex())) {
                indexFields.add(item.getFieldName());
            }
        });
        this.dropTable(tableName);
        mapper.ddlCreateTable(tableName, fieldList, indexFields);
        // 创建数据存储表
        String storageTableName = AlgoConstants.getSourceStorageTableName(tableName);
        this.dropTable(storageTableName);
        mapper.ddlCreateTable(storageTableName, fieldList, indexFields);
    }

    public void insertSourceStorageData(Integer strategyBatchId,String tableName,List<String> fieldNames) {
        String storageTableName = AlgoConstants.getSourceStorageTableName(tableName);
        mapper.insertSourceStorageData(strategyBatchId, storageTableName, tableName, fieldNames);
    }

    public Map<String, Object> statSourceData(Integer strategyBatchId, String tableName, Set<String> checkFields) {
        return mapper.statSourceData(strategyBatchId, tableName, checkFields);
    }

    public void exportSourceData(AlgoDataReqVo reqVo, HttpServletResponse response) {
        List<DataField> fieldList = this.getDataFields(reqVo);
        Assert.notEmpty(fieldList, "表头字段不能为空");
        //遍历field 得到字段名称  name得到表头
        List<List<String>> headNameList = new LinkedList<>();
        List<String> headFieldList = new LinkedList<>();
        for (DataField field : fieldList) {
            List<String> temp = new LinkedList<>();
            temp.add(field.getFieldDesc());
            headNameList.add(temp);
            headFieldList.add(field.getFieldName());
        }
        int pageNum = 1, pageSize = 5000;
        PageRequest<AlgoDataReqVo> reqVoPage = new PageRequest<>();
        reqVoPage.setParam(reqVo);
        reqVoPage.setPageSize(pageSize);
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            ExcelWriter excelWriter = EasyExcel.write(outputStream)
                    .registerConverter(new CustomSqlDateConverter()).registerConverter(new CustomTimeStampConverter()).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0)
                    .head(headNameList)
                    .registerWriteHandler(EasyExcelUtil.formatExcel())
                    .registerWriteHandler(new EasyExcelUtil.ExcelWidthStyleStrategy())
                    .build();
            while (true) {
                List<List<Object>> dataList = new LinkedList<>();
                reqVoPage.setPageNum(pageNum++);
                PageInfo<Map> pageInfo = this.getSourceDataList(reqVoPage);
                List<Map> mapList = pageInfo.getList();
                for (Map map : mapList) {
                    List<Object> temp = new LinkedList<>();
                    for (String field : headFieldList) {
                        temp.add(map.get(field));
                    }
                    dataList.add(temp);
                }
                excelWriter.write(dataList, writeSheet);
                if (pageInfo.getSize() < pageSize || pageInfo.getTotal() <= reqVoPage.getPageNum() * pageSize) {
                    break;
                }
            }
            EasyExcelUtil.packageResponse("策略批次数据明细", response);
            excelWriter.finish();
            outputStream.flush();
        } catch (Exception e) {
            logger.error("导出数据失败", e);
        }
    }

    public void exportSpaceData(AlgoDataReqVo reqVo, HttpServletResponse response) {
        List<DataField> fieldList = this.getSpaceDataFields(reqVo);
        Assert.notEmpty(fieldList, "表头字段不能为空");
        //遍历field 得到字段名称  name得到表头
        List<List<String>> headNameList = new LinkedList<>();
        List<String> headFieldList = new LinkedList<>();
        for (DataField field : fieldList) {
            List<String> temp = new LinkedList<>();
            temp.add(field.getFieldDesc());
            headNameList.add(temp);
            headFieldList.add(field.getFieldName());
        }
        int pageNum = 1, pageSize = 5000;
        PageRequest<AlgoDataReqVo> reqVoPage = new PageRequest<>();
        reqVoPage.setParam(reqVo);
        reqVoPage.setPageSize(pageSize);
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            ExcelWriter excelWriter = EasyExcel.write(outputStream)
                    .registerConverter(new CustomSqlDateConverter()).registerConverter(new CustomTimeStampConverter()).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0)
                    .head(headNameList)
                    .registerWriteHandler(EasyExcelUtil.formatExcel())
                    .registerWriteHandler(new EasyExcelUtil.ExcelWidthStyleStrategy())
                    .build();
            while (pageNum <= 10) {
                List<List<Object>> dataList = new LinkedList<>();
                reqVoPage.setPageNum(pageNum++);
                PageInfo<Map> pageInfo = this.getSpaceDataList(reqVoPage);
                List<Map> mapList = pageInfo.getList();
                for (Map map : mapList) {
                    List<Object> temp = new LinkedList<>();
                    for (String field : headFieldList) {
                        temp.add(map.get(field));
                    }
                    dataList.add(temp);
                }
                excelWriter.write(dataList, writeSheet);
                if (pageInfo.getSize() < pageSize || pageInfo.getTotal() <= reqVoPage.getPageNum() * pageSize) {
                    break;
                }
            }
            EasyExcelUtil.packageResponse("数据空间数据明细", response);
            excelWriter.finish();
            outputStream.flush();
        } catch (Exception e) {
            logger.error("导出数据失败", e);
        }
    }

    public String getNextId() {
        return seriesDataService.getNextId("algo_source", "D", 5);
    }
}
