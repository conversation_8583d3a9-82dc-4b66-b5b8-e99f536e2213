package com.kbao.algo.source.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kbao.algo.dataSpace.entity.AlgoDataSpace;
import com.kbao.algo.dataSpace.service.AlgoDataSpaceService;
import com.kbao.algo.enums.AlgoSourceTypeEnum;
import com.kbao.algo.redis.RedissonUtil;
import com.kbao.algo.source.dao.AlgoSourceMapper;
import com.kbao.algo.source.entity.AlgoSource;
import com.kbao.algo.sourceField.bean.AlgoParamResVo;
import com.kbao.algo.sourceField.entity.AlgoSourceField;
import com.kbao.algo.util.*;
import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcbsc.model.RequestPage;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.algo.sourceField.service.AlgoSourceDataFieldService;
import com.kbao.algo.sourceField.service.AlgoSourceFieldService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AlgoDataCollectService {
    @Autowired
    private AlgoSourceService algoSourceService;
    @Autowired
    private AlgoSourceFieldService algoSourceFieldService;
    @Autowired
    private AlgoDataSpaceService algoDataSpaceService;
    @Autowired
    private AlgoSourceDataFieldService algoSourceDataFieldService;
    @Autowired
    private SqlSessionFactory sqlSessionFactory;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private RedissonUtil redissonUtil;

    /**
     * 抽取数据
     * @param strategyBatchId 策略批次ID
     * @param sourceBizCode 数据源编码
     * @param isUpdate 是否更新
     * @param params 参数
     */
    public void collectData(Integer strategyBatchId, String sourceBizCode, String isUpdate, List<AlgoParamResVo> params) {
        AlgoDataSpace dataSpace = algoDataSpaceService.getByStrategyBatchId(strategyBatchId);
        if (dataSpace == null) {
            throw new BusinessException("数据空间不存在");
        }
        AlgoSource algoSource = algoSourceService.getByCode(sourceBizCode);
        List<AlgoSourceField> fields = algoSourceFieldService.listBySourceId(algoSource.getSourceId());
        if (CollectionUtils.isEmpty(fields)) {
            throw new BusinessException("数据源" + algoSource.getBizCode() + "未配置字段");
        }
        Map<String, Object> paramsMap = new HashMap<>();
        // 校验参数
        Map<String, AlgoParamResVo> allParamMap = params.stream().collect(Collectors.toMap(AlgoParamResVo::getFieldName, Function.identity()));
        Set<AlgoSourceField> paramsList = fields.stream().filter(f -> "1".equals(f.getIsParam())).collect(Collectors.toSet());
        List<Map<String, Object>> executeParamsList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(paramsList)) {
            for (AlgoSourceField param : paramsList) {
                AlgoParamResVo resVo = allParamMap.get(param.getFieldName());
                if (EmptyUtils.isNotEmpty(resVo)) {
                    Map<String, Object> item = new HashMap<>();
                    item.put("paramType", param.getParamType());
                    item.put("fieldName", param.getFieldName());
                    item.put("value", resVo.getParamValue());
                    executeParamsList.add(item);
                    paramsMap.put(param.getFieldName(), resVo.getParamValue());
                }
            }
        }
        String tableName = AlgoConstants.getSourceTableName(algoSource.getBizCode(), dataSpace.getId());
        boolean isExistTable = this.createTable(dataSpace.getId(), sourceBizCode, tableName, fields);
        if (isExistTable) {
            // 同一结算批次多次执行，校验数据源字段
            algoSourceDataFieldService.checkSourceFields(dataSpace.getId(), sourceBizCode, fields);
            boolean isExistSourceData = algoSourceService.isExistSourceDataByParams(tableName, executeParamsList);
            if (isExistSourceData) {
                if ("0".equals(isUpdate)) {
                    return;
                }
                algoSourceService.deleteSourceDataByParams(strategyBatchId, tableName, executeParamsList);
            }
        }
        List<String> fieldList = fields.stream().map(AlgoSourceField::getFieldName).collect(Collectors.toList());
        int total = doCollectData(strategyBatchId, tableName, algoSource, fieldList, paramsMap);
        // 核对数据
        Set<String> checkFields = fields.stream().filter(f -> "1".equals(f.getIsCheck()))
                .map(AlgoSourceField::getFieldName).collect(Collectors.toSet());
        checkSourceData(strategyBatchId, tableName, total, algoSource, checkFields, paramsMap);
        algoSourceService.insertSourceStorageData(strategyBatchId, tableName, fieldList);
    }

    public boolean createTable(Integer dataSpaceId, String sourceBizCode, String tableName, List<AlgoSourceField> fields) {
        boolean isExistTable = algoSourceService.isExistSourceTable(tableName);
        if (!isExistTable) {
            RLock lock = redissonUtil.getLock(AlgoConstants.LOCK_ALGO_CREATE_TABLE + tableName);
            try {
                lock.lock();
                isExistTable = algoSourceService.isExistSourceTable(tableName);
                if (!isExistTable) {
                    algoSourceService.ddlCreateTable(dataSpaceId, sourceBizCode, tableName, fields);
                }
            } finally {
                lock.unlock();
            }
        }
        return isExistTable;
    }

    public void checkSourceData(Integer strategyBatchId, String tableName, Integer total, AlgoSource algoSource
            , Set<String> checkFields, Map<String, Object> paramsMap) {
        Map<String, Object> statData = algoSourceService.statSourceData(strategyBatchId, tableName, checkFields);
        Object dataTotal = statData.remove("total");
        if (!Objects.equals(total, NumberUtil.toInt(dataTotal))) {
            throw new BusinessException("数据源" + algoSource.getBizCode() + "抽取数据异常, 请求总数:" + total + ", 实际入库数:" + dataTotal);
        }
        if (CollectionUtils.isEmpty(checkFields)) {
            return;
        }
        JSONObject data = queryCheckData(algoSource, paramsMap);
        if (data == null) {
            throw new BusinessException("数据源" + algoSource.getBizCode() + "核对接口无数据");
        }
        for (String field : checkFields) {
            String v1 = StringUtil.removeTrailingZeros(MapUtils.getString(data, field + "Sum"));
            String v2 = StringUtil.removeTrailingZeros(MapUtils.getString(statData, field));
            if (!Objects.equals(v1, v2)) {
                throw new BusinessException("数据源" + algoSource.getBizCode() + "核对异常, 字段:" + field + ", 请求数:" + v1 + ", 实际入库数:" + v2);
            }
        }
    }

    public int doCollectData(Integer strategyBatchId, String tableName, AlgoSource algoSource, List<String> fieldList, Map<String, Object> paramsMap) {
        String sourceValue = algoSource.getSourceValue();
        int pageSize = 500;
        Integer total = 0;
        RequestPage requestPage = new RequestPage();
        requestPage.setPageNum(1);
        requestPage.setPageSize(pageSize);
        requestPage.setParam(paramsMap);
        while (true) {
            log.info("请求数据源url:{}, params:{}", sourceValue, JSONObject.toJSONString(requestPage));
            JSONObject result;
            if (AlgoSourceTypeEnum.REST.getCode().equals(algoSource.getSourceType())) {
                result = HttpClientUtil.postJson(sourceValue, JSONObject.toJSONString(requestPage));
            } else {
                result = invokeFeignPageMethod(sourceValue, requestPage);
            }
            if (result == null) {
                break;
            }
            JSONArray list;
            JSONObject datas = result.getJSONObject("datas");
            if (datas == null || CollectionUtils.isEmpty((list = datas.getJSONArray("list")))) {
                log.info("请求数据源url:{}, params:{}, result:{}", sourceValue, JSONObject.toJSONString(requestPage), result.toJSONString());
                break;
            }
            total = datas.getInteger("total");
            log.info("请求数据源url:{}, params:{}, 返回数据总数:{}", sourceValue, JSONObject.toJSONString(requestPage), list.size());
            batchSaveSourceData(strategyBatchId, tableName, fieldList, list, AlgoContext.getTenantId());
            if (list.size() < pageSize || total <= requestPage.getPageNum() * pageSize) {
                break;
            }
            requestPage.setPageNum(requestPage.getPageNum() + 1);
        }
        return total;
    }

    public void batchSaveSourceData(Integer strategyBatchId, String tableName, List<String> fieldList, JSONArray list, String tenantId) {
        try (SqlSession session = sqlSessionFactory.openSession(ExecutorType.BATCH)) {
            AlgoSourceMapper sessionMapper = session.getMapper(AlgoSourceMapper.class);
            for (int i = 0; i < list.size(); i++) {
                JSONObject item = list.getJSONObject(i);
                List<Object> fieldValues = new ArrayList<>(fieldList.size());
                for (String field : fieldList) {
                    fieldValues.add(item.get(field));
                }
                sessionMapper.insertSourceData(strategyBatchId, tableName, fieldList, fieldValues, tenantId);
            }
            session.commit();
        }
    }

    public JSONObject invokeFeignPageMethod(String sourceValue, RequestPage params) {
        int lastIndex = sourceValue.lastIndexOf(".");
        if (lastIndex < 0) {
            throw new BusinessException("数据来源参数错误");
        }
        String className = sourceValue.substring(0, lastIndex);
        String methodName = sourceValue.substring(lastIndex + 1);
        try {
            Object clientBean = applicationContext.getBean(className);
            Method method = clientBean.getClass().getMethod(methodName, RequestPage.class);
            FeignUtils.baseFeignHeaders();
            Object invoke = method.invoke(clientBean, params);
            return JSONObject.parseObject(JSONObject.toJSONString(invoke));
        } catch (Exception e) {
            log.error("调用数据源方法失败", e);
            throw new BusinessException("调用数据源方法失败");
        }
    }

    public JSONObject queryCheckData(AlgoSource algoSource, Map<String, Object> paramsMap) {
        String checkDataApi = algoSource.getCheckDataApi();
        log.info("请求数据源url:{}, params:{}", checkDataApi, JSONObject.toJSONString(paramsMap));
        JSONObject result;
        if (AlgoSourceTypeEnum.REST.getCode().equals(algoSource.getSourceType())) {
            result = HttpClientUtil.postJson(checkDataApi, JSONObject.toJSONString(paramsMap));
        } else {
            result = invokeFeignMethod(checkDataApi, paramsMap);
        }
        log.info("核对数据源返回结果:{}", result);
        if (result == null) {
            return null;
        }
        return result.getJSONObject("datas");
    }

    public JSONObject invokeFeignMethod(String sourceValue, Map<String, Object> paramsMap) {
        int lastIndex = sourceValue.lastIndexOf(".");
        if (lastIndex < 0) {
            throw new BusinessException("数据来源参数错误");
        }
        String className = sourceValue.substring(0, lastIndex);
        String methodName = sourceValue.substring(lastIndex + 1);
        try {
            Object clientBean = applicationContext.getBean(className);
            Method[] methods = clientBean.getClass().getDeclaredMethods();
            Method method = Arrays.stream(methods).filter(m -> m.getName().equals(methodName) && m.getParameterCount() == 1).findFirst().get();
            Class<?> parameterType = method.getParameterTypes()[0];
            Object param = parameterType.getDeclaredConstructor().newInstance();
            for (Map.Entry<String, Object> entry : paramsMap.entrySet()) {
                Field declaredField = parameterType.getDeclaredField(entry.getKey());
                if (declaredField == null) {
                    throw new BusinessException("数据源核对接口参数与数据源抽取参数不一致，缺失参数："+ entry.getKey());
                }
                declaredField.setAccessible(true);
                declaredField.set(param, entry.getValue());
            }
            FeignUtils.baseFeignHeaders();
            Object invoke = method.invoke(clientBean, param);
            return JSONObject.parseObject(JSONObject.toJSONString(invoke));
        } catch (Exception e) {
            log.error("调用数据源核对方法失败", e);
            throw new BusinessException("调用数据源核对方法失败");
        }
    }
}
