package com.kbao.algo.mq.config;

import com.aliyun.openservices.ons.api.PropertyKeyConst;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.Properties;

/**
 * @ClassName HandleMqConfig
 * @description: MQ配置
 * @author: Sui<PERSON>inYang
 * @create: 2024-04-11 14:40
 **/
@Slf4j
@Component
@PropertySource(value = "classpath:/application.yml")
@ConfigurationProperties(prefix = "mq")
@Data
public class HandleMqConfig {

    private String messageCenterNameSrvAddr;

    private String messageCenterAccessKey;

    private String messageCenterSecretKey;

    private String messageCenterTopic;

    private String messageCenterGroupId;

    private String messageCenterSendMsgTimeoutMillis;

    private String consumeThreadNums;

    private String algoBatchSendBroadcastTag;

    public Properties getMqProperties() {
        Properties properties = new Properties();
        properties.put(PropertyKeyConst.GROUP_ID, this.getMessageCenterGroupId());
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, this.messageCenterNameSrvAddr);
        properties.setProperty(PropertyKeyConst.AccessKey, this.messageCenterAccessKey);
        properties.setProperty(PropertyKeyConst.SecretKey, this.messageCenterSecretKey);
        //设置发送超时时间，单位毫秒
        properties.setProperty(PropertyKeyConst.SendMsgTimeoutMillis, this.messageCenterSendMsgTimeoutMillis);
        return properties;
    }
}
