package com.kbao.algo.mq.service;

import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.Producer;
import com.aliyun.openservices.ons.api.SendResult;
import com.aliyun.openservices.ons.api.exception.ONSClientException;
import com.kbao.algo.mq.bean.MqPushMessageVO;
import com.kbao.algo.mq.config.HandleMqConfig;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * @ClassName SctToBrPushService
 * @description: MQ生产者推送消息服务
 * @author: SuiXinYang
 * @create: 2024-04-17 14:16
 **/
@Slf4j
@Service
public class MqProducerService {

    @Autowired(required = false)
    @Qualifier("mqProducer")
    private Producer producer;

    @Autowired
    private HandleMqConfig handleMqConfig;


    /*
     * @Description: 算法平台策略批次执行完成后发送MQ广播
     * <AUTHOR>
     * @Date 2024/11/13 14:52
     **/
    public void algoBatchSendBroadcast(MqPushMessageVO mqPushMessageVO){
        try{
            if (StringUtils.isBlank(mqPushMessageVO.getMessageBody())) {
                log.error("算法平台策略批次执行完成后发送MQ广播消息为空，topic={} tag={}", handleMqConfig.getMessageCenterTopic(), handleMqConfig.getAlgoBatchSendBroadcastTag());
                return;
            }
            Message msg = new Message(handleMqConfig.getMessageCenterTopic(),
                    handleMqConfig.getAlgoBatchSendBroadcastTag(),
                    mqPushMessageVO.getMessageBody().getBytes()
            );
            SendResult sendResult = producer.send(msg);
            if (EmptyUtils.isNotEmpty(sendResult)) {
                log.info("算法平台策略批次执行完成后发送MQ广播成功,messageId:{},topic:{},msg:{}",sendResult.getMessageId(),sendResult.getTopic(),mqPushMessageVO.getMessageBody());
            } else {
                log.error("算法平台策略批次执行完成后发送MQ广播失败!");
            }
        } catch (Exception e) {
            log.error("算法平台策略批次执行完成后发送MQ广播失败，失败原因:{}",e.getMessage());
        }
    }
}
