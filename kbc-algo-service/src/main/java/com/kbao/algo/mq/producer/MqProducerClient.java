package com.kbao.algo.mq.producer;

import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.kbao.algo.mq.config.HandleMqConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName MqProducer
 * @description: mq推送生产者
 * @author: SuiXinYang
 * @create: 2024-04-17 14:08
 **/
@Configuration
@ConditionalOnProperty(value = "mq.enabled", havingValue = "true")
public class MqProducerClient {

    @Autowired
    private HandleMqConfig handleMqConfig;

    @Bean(name = "mqProducer",initMethod = "start",destroyMethod = "shutdown")
    public ProducerBean buildProducer() {
        ProducerBean producer = new ProducerBean();
        producer.setProperties(handleMqConfig.getMqProperties());
        return producer;
    }
}
