package com.kbao.algo.strategyBatchParam.service;

import com.kbao.algo.sourceField.bean.AlgoParamResVo;
import com.kbao.algo.strategyBatch.entity.AlgoStrategyBatch;
import com.kbao.algo.strategyBatchParam.bean.AlgoStrategyBatchParamVo;
import com.kbao.algo.strategyBatchParam.entity.AlgoStrategyBatchParam;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.algo.strategyBatchParam.dao.AlgoStrategyBatchParamMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @Description 算法规则-策略执行批次参数Service类
* @Date 2024-10-29
*/
@Service
public class AlgoStrategyBatchParamService extends BaseSQLServiceImpl<AlgoStrategyBatchParam, Integer, AlgoStrategyBatchParamMapper> {

    /**
     * 插入算法策略批次参数
     *
     * @param algoStrategyBatch 算法策略批次对象
     * @param algoParamResVos 字段值列表
     * @param operatorId 操作人ID
     * @param tenantId 租户ID
     */
    public void insert(AlgoStrategyBatch algoStrategyBatch, List<AlgoParamResVo> algoParamResVos, String operatorId, String tenantId) {
        List<AlgoStrategyBatchParam> algoStrategyBatchParams = new java.util.ArrayList<>();
        for(AlgoParamResVo algoReqParam : algoParamResVos) {
            AlgoStrategyBatchParam algoStrategyBatchParam = new AlgoStrategyBatchParam();
            algoStrategyBatchParam.setBatchId(algoStrategyBatch.getId());
            algoStrategyBatchParam.setFieldName(algoReqParam.getFieldName());
            algoStrategyBatchParam.setParamValue(algoReqParam.getParamValue().toString());
            algoStrategyBatchParam.setParamType(algoReqParam.getParamType());
            algoStrategyBatchParam.setUpdateId(operatorId);
            algoStrategyBatchParam.setUpdateTime(new Date());
            algoStrategyBatchParam.setCreateId(operatorId);
            algoStrategyBatchParam.setCreateTime(new Date());
            algoStrategyBatchParam.setTenantId(tenantId);
            algoStrategyBatchParams.add(algoStrategyBatchParam);
        }
        if(EmptyUtils.isNotEmpty(algoStrategyBatchParams)){
            this.batchInsert(algoStrategyBatchParams);
        }
    }
    /**
     * 根据算法策略批次ID获取参数列表
     *
     * @param batchId 算法策略批次ID
     * @return 算法策略批次参数列表
     */
    public List<AlgoStrategyBatchParamVo> getParamList(Integer batchId) {
        return this.mapper.selectBatchParamByBatchId(batchId);
    }

    public Map<String, AlgoParamResVo> getParamMap(Integer batchId) {
        List<AlgoParamResVo> params = this.mapper.selectByBatchId(batchId);
        return params.stream().collect(Collectors.toMap(AlgoParamResVo::getFieldName, a -> a));
    }

    public void delByDataSpaceId(Integer dataSpaceId) {
        this.mapper.delByDataSpaceId(dataSpaceId);
    }

    public void delByBatchIds(Set<Integer> batchIds) {
        this.mapper.delByBatchIds(batchIds);
    }
}
