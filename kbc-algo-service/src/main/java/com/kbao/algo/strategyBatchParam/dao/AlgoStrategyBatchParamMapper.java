package com.kbao.algo.strategyBatchParam.dao;

import com.kbao.algo.sourceField.bean.AlgoParamResVo;
import com.kbao.algo.strategyBatchParam.bean.AlgoStrategyBatchParamVo;
import com.kbao.algo.strategyBatchParam.entity.AlgoStrategyBatchParam;
import com.kbao.kbcbsc.dao.sql.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
* <AUTHOR>
* @Description 算法规则-策略执行批次参数Dao类
* @Date 2024-10-29
*/
public interface AlgoStrategyBatchParamMapper  extends BaseMapper<AlgoStrategyBatchParam, Integer>{
    /**
     * 根据批次ID查询批次参数列表
     */
    List<AlgoParamResVo> selectByBatchId(Integer batchId);

    /**
     * 根据批次ID查询批次基础参数列表
     */
    List<AlgoStrategyBatchParamVo> selectBatchParamByBatchId(Integer batchId);

    void delByDataSpaceId(Integer dataSpaceId);

    void delByBatchIds(@Param("batchIds") Set<Integer> batchIds);

}