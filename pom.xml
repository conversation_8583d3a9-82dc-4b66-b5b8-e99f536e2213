<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>kbc-common</artifactId>
    <groupId>com.kbao</groupId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.kbao</groupId>
  <artifactId>kbc-sct-algo</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <name>kbc-sct-algo</name>
  <packaging>pom</packaging>
  <description>快保云服-规则引擎</description>

  <properties>
    <java.version>1.8</java.version>
    <core.version>1.0.0-SNAPSHOT</core.version>
    <jasypt.version>1.14</jasypt.version>
    <hutool.version>5.0.7</hutool.version>
    <fastjson.version>1.2.83</fastjson.version>
    <disruptor.version>3.4.1</disruptor.version>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
    <spring-boot.version>2.1.12.RELEASE</spring-boot.version>
    <spring-platform.version>Cairo-SR3</spring-platform.version>
    <spring.social.version>1.1.6.RELEASE</spring.social.version>
    <commons-collections4.version>4.1</commons-collections4.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <hibernate-validator.verion>6.1.5.Final</hibernate-validator.verion>
    <flowable.version>6.4.1</flowable.version>
    <spring-cloud-dependencies.version>Greenwich.SR5</spring-cloud-dependencies.version>
    <httpclient.version>4.5.13</httpclient.version>
    <zxing.version>3.3.3</zxing.version>
    <elasticsearch.version>7.6.2</elasticsearch.version>
    <aviator.version>5.2.6</aviator.version>
    <easyexcel.version>3.1.0</easyexcel.version>
  </properties>

  <modules>
    <module>kbc-algo-entity</module>
    <module>kbc-algo-service</module>
    <module>kbc-algo-web</module>
    <module>kbc-algo-web-client</module>
    <module>kbc-algo-common</module>
  </modules>

  <dependencies>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-collections4</artifactId>
      <version>${commons-collections4.version}</version>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>${fastjson.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>

    <dependency>
      <groupId>com.googlecode.aviator</groupId>
      <artifactId>aviator</artifactId>
      <version>${aviator.version}</version>
    </dependency>

    <!-- Micrometer Prometheus registry -->
    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-registry-prometheus</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
    </dependency>
  </dependencies>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring-boot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.spring.platform</groupId>
        <artifactId>platform-bom</artifactId>
        <version>${spring-platform.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-dependencies</artifactId>
        <version>${spring-cloud-dependencies.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <!-- 与spring无关的工具包-->
      <dependency>
        <groupId>com.kbao</groupId>
        <artifactId>tool-spring-boot-starter</artifactId>
        <version>${core.version}</version>
      </dependency>

      <!--httpclient-->
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>${httpclient.version}</version>
      </dependency>


      <!-- 公共实体类模块 -->
      <dependency>
        <groupId>com.kbao</groupId>
        <artifactId>common-spring-boot-starter</artifactId>
        <version>${core.version}</version>
      </dependency>
      <!-- 关系型和非关系型数据库配置 -->
      <dependency>
        <groupId>com.kbao</groupId>
        <artifactId>db-spring-boot-starter</artifactId>
        <version>${core.version}</version>
      </dependency>
      <!-- 非关系型数据库配置 -->
      <dependency>
        <groupId>com.kbao</groupId>
        <artifactId>redis-spring-boot-starter</artifactId>
        <version>${core.version}</version>
      </dependency>
      <!-- 日志配置 -->
      <dependency>
        <groupId>com.kbao</groupId>
        <artifactId>log-spring-boot-starter</artifactId>
        <version>${core.version}</version>
      </dependency>

      <!-- 业务实体-->
      <dependency>
        <groupId>com.kbao</groupId>
        <artifactId>kbc-algo-common</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.kbao</groupId>
        <artifactId>kbc-algo-entity</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.kbao</groupId>
        <artifactId>kbc-algo-service</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!--client-->
      <dependency>
        <groupId>com.kbao</groupId>
        <artifactId>kbc-algo-web-client</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- BSC -->
      <dependency>
        <groupId>com.kbao</groupId>
        <artifactId>kbc-bsc-web-client</artifactId>
        <version>1.0.0${env.version}SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.kbao</groupId>
        <artifactId>kbc-bsc-api-client</artifactId>
        <version>1.0.0${env.version}SNAPSHOT</version>
      </dependency>
      <!-- 统一消息平台 web接口服务包 -->
      <dependency>
        <groupId>com.kbao</groupId>
        <artifactId>kbc-ums-web-client</artifactId>
        <version>1.0.0${env.version}SNAPSHOT</version>
      </dependency>

      <!-- 云服核心 -->
      <dependency>
        <groupId>com.kbao</groupId>
        <artifactId>kbc-cbs-ism-web-client</artifactId>
        <version>1.0.0${env.version}SNAPSHOT</version>
      </dependency>

      <dependency>
        <groupId>com.kbao</groupId>
        <artifactId>kbc-uoc-web-client</artifactId>
        <version>1.0.0${env.version}SNAPSHOT</version>
      </dependency>

      <dependency>
        <groupId>com.kbao</groupId>
        <artifactId>kbc-ucs-web-client</artifactId>
        <version>1.0.0${env.version}SNAPSHOT</version>
      </dependency>

      <!-- 日志平台 -->
      <dependency>
        <groupId>org.elasticsearch</groupId>
        <artifactId>elasticsearch</artifactId>
        <version>${elasticsearch.version}</version>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch.client</groupId>
        <artifactId>elasticsearch-rest-client</artifactId>
        <version>${elasticsearch.version}</version>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch.client</groupId>
        <artifactId>elasticsearch-rest-high-level-client</artifactId>
        <version>${elasticsearch.version}</version>
      </dependency>

      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-validator</artifactId>
        <version>${hibernate-validator.verion}</version>
      </dependency>

      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>easyexcel</artifactId>
        <version>${easyexcel.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>poi-ooxml-schemas</artifactId>
            <groupId>org.apache.poi</groupId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>com.kbao</groupId>
        <artifactId>config-spring-boot-starter</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
    </dependencies>

  </dependencyManagement>

  <distributionManagement>
    <repository>
      <id>releases</id>
      <url>http://10.176.1.3:8081/nexus/content/repositories/releases</url>
    </repository>
    <snapshotRepository>
      <id>snapshots</id>
      <url>http://10.176.1.3:8081/nexus/content/repositories/snapshots</url>
    </snapshotRepository>
  </distributionManagement>

  <repositories>
    <repository>
      <id>nexus</id>
      <name>nexus</name>
      <url>http://10.176.1.3:8081/nexus/content/groups/public/</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </snapshots>
    </repository>
  </repositories>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-maven-plugin</artifactId>
          <version>2.4.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <configuration>
            <target>${java.version}</target>
            <source>${java.version}</source>
            <encoding>UTF-8</encoding>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <configuration>
            <archive>
              <addMavenDescriptor>false</addMavenDescriptor>
            </archive>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <includes>
          <include>**/*.properties</include>
          <include>**/*.yml</include>
          <include>**/*.xml</include>
          <include>**/*.tld</include>
          <include>**/*.p12</include>
          <include>**/*.conf</include>
          <include>**/*.txt</include>
          <include>**/*.wsdl</include>
          <include>**/*.xsd</include>
          <include>**/*.ftl</include>
          <include>**/*.lua</include>
          <include>**/*.json</include>
          <include>processes/*</include>
          <include>**/spring.factories</include>
          <include>**/*.xlsx</include>
          <include>**/*.jpg</include>
        </includes>
        <filtering>false</filtering>
      </resource>
      <resource>
        <directory>src/main/java</directory>
        <includes>
          <include>**/*.properties</include>
          <include>**/*.xml</include>
          <include>**/*.tld</include>
        </includes>
        <filtering>false</filtering>
      </resource>
    </resources>
  </build>
  <profiles>
    <profile>
      <id>dev</id>
      <properties>
        <profiles.active>dev</profiles.active>
        <env.version>-sta-</env.version>
      </properties>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
    </profile>
    <profile>
      <id>local</id>
      <properties>
        <profiles.active>local</profiles.active>
        <env.version>-sta-</env.version>
      </properties>
    </profile>
    <profile>
      <id>sta</id>
      <properties>
        <profiles.active>sta</profiles.active>
        <env.version>-sta-</env.version>
      </properties>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
    </profile>
    <profile>
      <id>uat</id>
      <properties>
        <profiles.active>uat</profiles.active>
        <env.version>-uat-</env.version>
      </properties>
    </profile>
    <profile>
      <id>prod</id>
      <properties>
        <profiles.active>prod</profiles.active>
        <env.version>-</env.version>
      </properties>

      <distributionManagement>
        <repository>
          <id>releases</id>
          <url>http://10.176.1.3:8082/nexus/content/repositories/releases</url>
        </repository>
        <snapshotRepository>
          <id>snapshots</id>
          <url>http://10.176.1.3:8082/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
      </distributionManagement>


      <repositories>
        <repository>
          <id>nexus</id>
          <name>nexus</name>
          <url>http://10.176.1.3:8082/nexus/content/groups/public/</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>true</enabled>
            <updatePolicy>always</updatePolicy>
          </snapshots>
        </repository>
      </repositories>
    </profile>
  </profiles>
</project>
